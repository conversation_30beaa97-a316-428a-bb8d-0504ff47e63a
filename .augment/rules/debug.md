---
type: "always_apply"
---

## PostgreSQL解析器插件编译和部署规则

### 编译环境配置

1. **编译环境**: 代码运行在远程服务器的容器环境中
2. **编译工具**: 使用gwhw_mcp工具集中的相关API进行编译操作
3. **目标插件**: PostgreSQL协议解析器插件（postgre_parser.so）

### 编译部署流程

#### 1. 插件编译
   ```json
   start_build_plugin_api_v1_build_plugin_post_gwhw_mcp({
     "plugin_name": "postgre_parser.so",
     "remote_ip": "",
     "ssh_user": "",
     "container_name": ""
   })
   ```

**参数说明**:
- `plugin_name`: 插件名称，默认为 `postgre_parser.so`
- `remote_ip`, `ssh_user`, `container_name`: 可选参数，使用默认值


#### 2. 任务监控
- **单个任务监控**: 使用 `get_task_api_v1_tasks__task_id__get_gwhw_mcp` 根据返回的task_id监控编译状态
```json
{
  "task_id": "",  
}
```

**参数说明**:
- `task_id`: 任务ID，由上一步插件编译任务返回

#### 3. 错误处理流程
1. **分析错误信息**: 如果编译出现警告或错误，仔细分析错误信息
2. **修改源代码**: 根据错误信息修改对应的源代码文件
3. **重新编译**: 修改完成后重新执行编译流程
4. **迭代修复**: 重复此过程直到编译成功

#### 4. 验证步骤
- 确认任务状态为"completed"或"success"
- 检查是否有编译警告需要处理

### 常见编译问题处理

#### 编译错误类型
1. **语法错误**: 检查C++语法，特别是新增代码的语法正确性
2. **链接错误**: 检查函数声明和定义是否匹配
3. **头文件错误**: 确保所有必要的头文件都已包含
4. **类型错误**: 检查数据类型定义和使用是否一致

#### 修复优先级
1. **编译错误**: 优先修复阻止编译的错误
2. **编译警告**: 处理可能影响功能的警告
3. **代码风格**: 确保遵循现有的代码风格规范

### 注意事项

- **代码风格一致性**: 在修改代码后，确保遵循现有的代码风格和错误处理模式
- **向后兼容性**: 保持与现有PostgreSQL解析器功能的向后兼容性
- **内存安全**: 注意内存分配和释放，防止内存泄漏

### 示例工作流程

```bash
# 1. 编译插件
start_build_plugin_api_v1_build_plugin_post_gwhw_mcp({
     "plugin_name": "postgre_parser.so",
     "remote_ip": "",
     "ssh_user": "",
     "container_name": ""
})

# 2. 监控编译状态
get_task_api_v1_tasks__task_id__get_gwhw_mcp(task_id)

```

### PostgreSQL解析器特定注意事项

#### COPY协议相关修改
- 确保COPY状态管理函数正确实现
- 验证COPY_BOTH_RESPONSE消息处理
- 检查资源清理逻辑的正确性

#### 数据类型扩展相关修改
- 验证新增数据类型的OID定义正确
- 确保数组类型处理函数的内存安全
- 检查网络地址类型的格式化输出

#### 内存管理
- 特别注意COPY请求的内存分配和释放
- 确保错误情况下的资源清理
- 验证数组处理中的内存边界检查
