/*
 * Oracle认证处理器实现
 * 处理完整的Oracle认证流程
 * <AUTHOR> @date 2025
 */

#include "oracle_auth_handler.h"
#include "oracle_parser_common.h"
#include "gw_logger.h"
#include <string.h>
#include <stdlib.h>
#include <map>

// 简化的日志宏定义
#define AUTH_LOG_DEBUG(fmt, ...) printf("[AUTH-DEBUG] " fmt "\n", ##__VA_ARGS__)
#define AUTH_LOG_INFO(fmt, ...)  printf("[AUTH-INFO] " fmt "\n", ##__VA_ARGS__)
#define AUTH_LOG_WARN(fmt, ...)  printf("[AUTH-WARN] " fmt "\n", ##__VA_ARGS__)
#define AUTH_LOG_ERROR(fmt, ...) printf("[AUTH-ERROR] " fmt "\n", ##__VA_ARGS__)

// 大端序读取函数
static uint16_t read_uint16_be(const char *data) {
    return (((uint8_t)data[0]) << 8) | ((uint8_t)data[1]);
}

static uint32_t read_uint32_be(const char *data) {
    return (((uint8_t)data[0]) << 24) | (((uint8_t)data[1]) << 16) | 
           (((uint8_t)data[2]) << 8) | ((uint8_t)data[3]);
}

OracleAuthHandler::OracleAuthHandler()
    : m_debug_enabled(false)
    , m_max_sessions(1000)
    , m_session_timeout(300)
    , m_total_auth_attempts(0)
    , m_successful_auths(0)
    , m_failed_auths(0)
    , m_password_auths(0)
    , m_o3logon_auths(0)
    , m_o5logon_auths(0)
    , m_kerberos_auths(0)
    , m_ssl_auths(0)
    , m_strong_auths(0)
    , m_weak_auths(0)
{
    AUTH_LOG_INFO("Oracle Auth Handler initialized");
}

OracleAuthHandler::~OracleAuthHandler()
{
    // 清理所有认证会话
    for (auto& pair : m_auth_sessions) {
        free_auth_session(pair.second);
    }
    m_auth_sessions.clear();
    
    AUTH_LOG_INFO("Oracle Auth Handler destroyed, processed %lu auth attempts",
                 (unsigned long)m_total_auth_attempts);
}

int OracleAuthHandler::handle_auth_request(const char *data, size_t data_len, uint8_t message_type,
                                         oracle_status_t *status, oracle_parsed_data_t *result)
{
    if (!data || data_len < 8 || !status || !result) {
        return AUTH_PARSE_ERROR;
    }

    m_total_auth_attempts++;

    // 获取或创建认证会话
    oracle_auth_session_t *auth_session = nullptr;
    int ret = get_or_create_auth_session(status->session_id, &auth_session);
    if (ret != AUTH_PARSE_SUCCESS || !auth_session) {
        AUTH_LOG_ERROR("Failed to get authentication session");
        return ret;
    }

    // 根据消息类型和内容确定认证类型
    uint8_t auth_type = detect_auth_type(data, data_len, message_type);
    
    switch (auth_type) {
        case ORACLE_AUTH_TYPE_PASSWORD:
            ret = handle_password_auth(data, data_len, auth_session, status, result);
            m_password_auths++;
            break;
            
        case ORACLE_AUTH_TYPE_O3LOGON:
            ret = handle_o3logon_auth(data, data_len, auth_session, status, result);
            m_o3logon_auths++;
            break;
            
        case ORACLE_AUTH_TYPE_O5LOGON:
            ret = handle_o5logon_auth(data, data_len, auth_session, status, result);
            m_o5logon_auths++;
            break;
            
        case ORACLE_AUTH_TYPE_KERBEROS:
            ret = handle_kerberos_auth(data, data_len, auth_session, status, result);
            m_kerberos_auths++;
            m_strong_auths++;
            break;
            
        case ORACLE_AUTH_TYPE_SSL:
            ret = handle_ssl_auth(data, data_len, auth_session, status, result);
            m_ssl_auths++;
            m_strong_auths++;
            break;
            
        default:
            AUTH_LOG_WARN("Unknown authentication type: %u", auth_type);
            ret = AUTH_PARSE_UNSUPPORTED;
            break;
    }

    if (ret == AUTH_PARSE_SUCCESS) {
        m_successful_auths++;
        status->is_authenticated = 1;
        
        // 设置用户信息
        if (auth_session->username[0] != '\0') {
            result->user = &status->user;
            status->user.s = auth_session->username;
            status->user.len = strlen(auth_session->username);
        }
        
        AUTH_LOG_INFO("Authentication successful: type=%u, user=%s",
                     auth_type,
                     auth_session->username[0] != '\0' ? auth_session->username : "unknown");
        
        // 记录成功的认证
        audit_auth_attempt(auth_session, true);
    } else {
        m_failed_auths++;
        AUTH_LOG_WARN("Authentication failed: type=%u, error=%d", auth_type, ret);
        audit_auth_attempt(auth_session, false);
    }

    return ret;
}

int OracleAuthHandler::handle_password_auth(const char *data, size_t data_len,
                                          oracle_auth_session_t *auth_session,
                                          oracle_status_t *status, oracle_parsed_data_t *result)
{
    size_t offset = 0;
    
    // 解析用户名
    if (offset + 2 > data_len) {
        return AUTH_PARSE_NEED_MORE_DATA;
    }
    
    uint16_t username_len = read_uint16_be(data + offset);
    offset += 2;
    
    if (offset + username_len > data_len || username_len >= sizeof(auth_session->username)) {
        return AUTH_PARSE_ERROR;
    }
    
    memcpy(auth_session->username, data + offset, username_len);
    auth_session->username[username_len] = '\0';
    offset += username_len;
    
    // 解析密码长度（不存储实际密码）
    if (offset + 2 > data_len) {
        return AUTH_PARSE_NEED_MORE_DATA;
    }
    
    uint16_t password_len = read_uint16_be(data + offset);
    offset += 2 + password_len;
    
    auth_session->auth_type = ORACLE_AUTH_TYPE_PASSWORD;
    auth_session->auth_status = ORACLE_AUTH_STATUS_CHALLENGE;
    
    AUTH_LOG_DEBUG("Password auth for user: %s", auth_session->username);
    
    return AUTH_PARSE_SUCCESS;
}

int OracleAuthHandler::handle_o3logon_auth(const char *data, size_t data_len,
                                         oracle_auth_session_t *auth_session,
                                         oracle_status_t *status, oracle_parsed_data_t *result)
{
    // 简化的O3LOGON处理
    auth_session->auth_type = ORACLE_AUTH_TYPE_O3LOGON;
    auth_session->auth_status = ORACLE_AUTH_STATUS_CHALLENGE;
    
    AUTH_LOG_DEBUG("O3LOGON authentication processed");
    
    return AUTH_PARSE_SUCCESS;
}

int OracleAuthHandler::handle_o5logon_auth(const char *data, size_t data_len,
                                         oracle_auth_session_t *auth_session,
                                         oracle_status_t *status, oracle_parsed_data_t *result)
{
    // 简化的O5LOGON处理
    auth_session->auth_type = ORACLE_AUTH_TYPE_O5LOGON;
    auth_session->auth_status = ORACLE_AUTH_STATUS_RESPONSE;
    
    AUTH_LOG_DEBUG("O5LOGON authentication processed");
    
    return AUTH_PARSE_SUCCESS;
}

int OracleAuthHandler::handle_kerberos_auth(const char *data, size_t data_len,
                                          oracle_auth_session_t *auth_session,
                                          oracle_status_t *status, oracle_parsed_data_t *result)
{
    // 简化的Kerberos处理
    auth_session->auth_type = ORACLE_AUTH_TYPE_KERBEROS;
    auth_session->auth_status = ORACLE_AUTH_STATUS_RESPONSE;
    
    AUTH_LOG_DEBUG("Kerberos authentication processed");
    
    return AUTH_PARSE_SUCCESS;
}

int OracleAuthHandler::handle_ssl_auth(const char *data, size_t data_len,
                                     oracle_auth_session_t *auth_session,
                                     oracle_status_t *status, oracle_parsed_data_t *result)
{
    // 简化的SSL处理
    auth_session->auth_type = ORACLE_AUTH_TYPE_SSL;
    auth_session->auth_status = ORACLE_AUTH_STATUS_RESPONSE;
    
    AUTH_LOG_DEBUG("SSL authentication processed");
    
    return AUTH_PARSE_SUCCESS;
}

uint8_t OracleAuthHandler::detect_auth_type(const char *data, size_t data_len, uint8_t message_type)
{
    // 简化的认证类型检测
    if (data_len < 4) {
        return ORACLE_AUTH_TYPE_UNKNOWN;
    }
    
    // 根据消息特征判断认证类型
    if (data[0] == 0x01 && data[1] == 0x00) {
        return ORACLE_AUTH_TYPE_PASSWORD;
    } else if (data[0] == 0x03 && data[1] == 0x00) {
        return ORACLE_AUTH_TYPE_O3LOGON;
    } else if (data[0] == 0x05 && data[1] == 0x00) {
        return ORACLE_AUTH_TYPE_O5LOGON;
    }
    
    return ORACLE_AUTH_TYPE_UNKNOWN;
}

int OracleAuthHandler::get_or_create_auth_session(uint32_t session_id, oracle_auth_session_t **auth_session)
{
    auto it = m_auth_sessions.find(session_id);
    if (it != m_auth_sessions.end()) {
        *auth_session = it->second;
        return AUTH_PARSE_SUCCESS;
    }
    
    // 创建新的认证会话
    oracle_auth_session_t *new_session = (oracle_auth_session_t*)calloc(1, sizeof(oracle_auth_session_t));
    if (!new_session) {
        return AUTH_PARSE_ERROR;
    }
    
    new_session->session_id = session_id;
    new_session->auth_status = ORACLE_AUTH_STATUS_INIT;
    new_session->created_time = time(nullptr);
    
    m_auth_sessions[session_id] = new_session;
    *auth_session = new_session;
    
    return AUTH_PARSE_SUCCESS;
}

void OracleAuthHandler::audit_auth_attempt(oracle_auth_session_t *auth_session, bool success)
{
    // 简化的审计实现
    AUTH_LOG_INFO("Auth audit: session=%u, user=%s, success=%s",
                 auth_session->session_id,
                 auth_session->username[0] != '\0' ? auth_session->username : "unknown",
                 success ? "true" : "false");
}

void OracleAuthHandler::free_auth_session(oracle_auth_session_t *auth_session)
{
    if (auth_session) {
        free(auth_session);
    }
}

void OracleAuthHandler::get_auth_statistics(oracle_auth_stats_t *stats)
{
    if (!stats) {
        return;
    }
    
    stats->total_attempts = m_total_auth_attempts;
    stats->successful_auths = m_successful_auths;
    stats->failed_auths = m_failed_auths;
    stats->password_auths = m_password_auths;
    stats->o3logon_auths = m_o3logon_auths;
    stats->o5logon_auths = m_o5logon_auths;
    stats->kerberos_auths = m_kerberos_auths;
    stats->ssl_auths = m_ssl_auths;
    stats->strong_auths = m_strong_auths;
    stats->weak_auths = m_weak_auths;
    
    if (m_total_auth_attempts > 0) {
        stats->success_rate = (double)m_successful_auths / m_total_auth_attempts * 100.0;
    } else {
        stats->success_rate = 0.0;
    }
}

// ===== 缺失的认证处理方法实现 =====

// 解析SSL认证
int OracleAuthHandler::parse_ssl_auth(const char *data, size_t data_len, oracle_auth_session_t *auth_session)
{
    if (!data || data_len < 8 || !auth_session) {
        AUTH_LOG_ERROR("Invalid parameters for SSL auth parsing");
        return AUTH_PARSE_ERROR;
    }

    AUTH_LOG_DEBUG("Parsing SSL authentication, data_len: %zu", data_len);

    size_t offset = 0;

    // 解析SSL版本 (2字节)
    if (offset + 2 > data_len) {
        AUTH_LOG_ERROR("Insufficient data for SSL version");
        return AUTH_PARSE_NEED_MORE_DATA;
    }

    uint16_t ssl_version = read_uint16_be(data + offset);
    offset += 2;

    // 解析证书长度 (4字节)
    if (offset + 4 > data_len) {
        AUTH_LOG_ERROR("Insufficient data for certificate length");
        return AUTH_PARSE_NEED_MORE_DATA;
    }

    uint32_t cert_length = read_uint32_be(data + offset);
    offset += 4;

    AUTH_LOG_DEBUG("SSL auth: version=0x%x, cert_length=%u", ssl_version, cert_length);

    // 验证证书长度合理性
    if (cert_length > data_len - offset || cert_length > 65536) {
        AUTH_LOG_ERROR("Invalid certificate length: %u", cert_length);
        return AUTH_PARSE_ERROR;
    }

    // 跳过证书数据
    if (cert_length > 0) {
        offset += cert_length;
        AUTH_LOG_DEBUG("SSL certificate data skipped (%u bytes)", cert_length);
    }

    // 更新认证会话
    auth_session->auth_type = ORACLE_AUTH_TYPE_SSL;
    auth_session->auth_status = ORACLE_AUTH_STATUS_RESPONSE;
    auth_session->ssl_version = ssl_version;

    AUTH_LOG_DEBUG("SSL authentication parsed successfully");
    return AUTH_PARSE_SUCCESS;
}

// 大端序读取函数（成员方法版本）
uint16_t OracleAuthHandler::read_uint16_be(const char *data)
{
    return (uint16_t)((((uint8_t)data[0]) << 8) | ((uint8_t)data[1]));
}

uint32_t OracleAuthHandler::read_uint32_be(const char *data)
{
    return (uint32_t)((((uint8_t)data[0]) << 24) |
                     (((uint8_t)data[1]) << 16) |
                     (((uint8_t)data[2]) << 8) |
                     ((uint8_t)data[3]));
}

// 审计认证尝试（重载版本）
int OracleAuthHandler::audit_auth_attempt(const oracle_auth_session_t *auth_session, bool success)
{
    if (!auth_session) {
        AUTH_LOG_ERROR("Invalid auth session for audit");
        return -1;
    }

    // 记录详细的审计信息
    AUTH_LOG_INFO("Auth audit: session=%u, user=%s, type=%u, success=%s, time=%ld",
                 auth_session->session_id,
                 auth_session->username[0] != '\0' ? auth_session->username : "unknown",
                 auth_session->auth_type,
                 success ? "true" : "false",
                 auth_session->created_time);

    // 这里可以添加更详细的审计逻辑，如写入审计日志文件等
    return 0;
}

// 解析Kerberos认证
int OracleAuthHandler::parse_kerberos_auth(const char *data, size_t data_len, oracle_auth_session_t *auth_session)
{
    if (!data || data_len < 12 || !auth_session) {
        AUTH_LOG_ERROR("Invalid parameters for Kerberos auth parsing");
        return AUTH_PARSE_ERROR;
    }

    AUTH_LOG_DEBUG("Parsing Kerberos authentication, data_len: %zu", data_len);

    size_t offset = 0;

    // 解析Kerberos版本 (2字节)
    if (offset + 2 > data_len) {
        AUTH_LOG_ERROR("Insufficient data for Kerberos version");
        return AUTH_PARSE_NEED_MORE_DATA;
    }

    uint16_t krb_version = read_uint16_be(data + offset);
    offset += 2;

    // 解析票据长度 (4字节)
    if (offset + 4 > data_len) {
        AUTH_LOG_ERROR("Insufficient data for ticket length");
        return AUTH_PARSE_NEED_MORE_DATA;
    }

    uint32_t ticket_length = read_uint32_be(data + offset);
    offset += 4;

    // 解析主体名长度 (2字节)
    if (offset + 2 > data_len) {
        AUTH_LOG_ERROR("Insufficient data for principal name length");
        return AUTH_PARSE_NEED_MORE_DATA;
    }

    uint16_t principal_length = read_uint16_be(data + offset);
    offset += 2;

    AUTH_LOG_DEBUG("Kerberos auth: version=%u, ticket_len=%u, principal_len=%u",
                  krb_version, ticket_length, principal_length);

    // 验证长度合理性
    if (ticket_length > data_len - offset || ticket_length > 65536) {
        AUTH_LOG_ERROR("Invalid ticket length: %u", ticket_length);
        return AUTH_PARSE_ERROR;
    }

    // 跳过票据数据
    if (ticket_length > 0) {
        offset += ticket_length;
    }

    // 解析主体名
    if (principal_length > 0 && offset + principal_length <= data_len) {
        if (principal_length < sizeof(auth_session->username)) {
            memcpy(auth_session->username, data + offset, principal_length);
            auth_session->username[principal_length] = '\0';
            AUTH_LOG_DEBUG("Kerberos principal: %s", auth_session->username);
        }
        offset += principal_length;
    }

    // 更新认证会话
    auth_session->auth_type = ORACLE_AUTH_TYPE_KERBEROS;
    auth_session->auth_status = ORACLE_AUTH_STATUS_RESPONSE;

    AUTH_LOG_DEBUG("Kerberos authentication parsed successfully");
    return AUTH_PARSE_SUCCESS;
}

// 解析密码认证
int OracleAuthHandler::parse_password_auth(const char *data, size_t data_len, oracle_auth_session_t *auth_session)
{
    if (!data || data_len < 8 || !auth_session) {
        AUTH_LOG_ERROR("Invalid parameters for password auth parsing");
        return AUTH_PARSE_ERROR;
    }

    AUTH_LOG_DEBUG("Parsing password authentication, data_len: %zu", data_len);

    size_t offset = 0;

    // 解析用户名长度 (2字节)
    if (offset + 2 > data_len) {
        AUTH_LOG_ERROR("Insufficient data for username length");
        return AUTH_PARSE_NEED_MORE_DATA;
    }

    uint16_t username_length = read_uint16_be(data + offset);
    offset += 2;

    // 验证用户名长度
    if (username_length == 0 || username_length >= sizeof(auth_session->username) ||
        offset + username_length > data_len) {
        AUTH_LOG_ERROR("Invalid username length: %u", username_length);
        return AUTH_PARSE_ERROR;
    }

    // 解析用户名
    memcpy(auth_session->username, data + offset, username_length);
    auth_session->username[username_length] = '\0';
    offset += username_length;

    // 解析密码长度 (2字节)
    if (offset + 2 > data_len) {
        AUTH_LOG_ERROR("Insufficient data for password length");
        return AUTH_PARSE_NEED_MORE_DATA;
    }

    uint16_t password_length = read_uint16_be(data + offset);
    offset += 2;

    // 验证密码长度并跳过密码数据（出于安全考虑不存储）
    if (password_length > 0 && offset + password_length <= data_len) {
        offset += password_length;
        AUTH_LOG_DEBUG("Password data skipped (%u bytes)", password_length);
    }

    AUTH_LOG_DEBUG("Password auth: username=%s, password_len=%u",
                  auth_session->username, password_length);

    // 更新认证会话
    auth_session->auth_type = ORACLE_AUTH_TYPE_PASSWORD;
    auth_session->auth_status = ORACLE_AUTH_STATUS_CHALLENGE;

    AUTH_LOG_DEBUG("Password authentication parsed successfully");
    return AUTH_PARSE_SUCCESS;
}
