/*
 * Oracle会话管理器实现
 * 管理Oracle会话的生命周期和状态
 * <AUTHOR> @date 2025
 */

#include "oracle_session_manager.h"
#include "oracle_parser_common.h"
#include "gw_logger.h"
#include <string.h>
#include <stdlib.h>
#include <time.h>
#include <map>

// 简化的日志宏定义
#define SESS_LOG_DEBUG(fmt, ...) printf("[SESS-DEBUG] " fmt "\n", ##__VA_ARGS__)
#define SESS_LOG_INFO(fmt, ...)  printf("[SESS-INFO] " fmt "\n", ##__VA_ARGS__)
#define SESS_LOG_WARN(fmt, ...)  printf("[SESS-WARN] " fmt "\n", ##__VA_ARGS__)
#define SESS_LOG_ERROR(fmt, ...) printf("[SESS-ERROR] " fmt "\n", ##__VA_ARGS__)

// 大端序读取函数
static uint16_t read_uint16_be(const char *data) {
    return (((uint8_t)data[0]) << 8) | ((uint8_t)data[1]);
}

static uint32_t read_uint32_be(const char *data) {
    return (((uint8_t)data[0]) << 24) | (((uint8_t)data[1]) << 16) | 
           (((uint8_t)data[2]) << 8) | ((uint8_t)data[3]);
}

OracleSessionManager::OracleSessionManager()
    : m_debug_enabled(false)
    , m_max_sessions(10000)
    , m_session_timeout(1800)  // 30分钟
    , m_cleanup_interval(300)  // 5分钟
    , m_next_session_id(1)
    , m_total_sessions_created(0)
    , m_total_sessions_destroyed(0)
    , m_active_sessions(0)
    , m_peak_sessions(0)
    , m_session_timeouts(0)
    , m_last_cleanup_time(0)
{
    SESS_LOG_INFO("Oracle Session Manager initialized");
}

OracleSessionManager::~OracleSessionManager()
{
    // 清理所有活跃会话
    for (auto& pair : m_active_session_map) {
        free_oracle_session(pair.second);
    }
    m_active_session_map.clear();
    
    SESS_LOG_INFO("Oracle Session Manager destroyed, created %lu sessions, destroyed %lu sessions",
                 (unsigned long)m_total_sessions_created, (unsigned long)m_total_sessions_destroyed);
}

oracle_session_t* OracleSessionManager::create_session(const char *client_ip, int client_port,
                                                      const char *server_ip, int server_port)
{
    if (!client_ip || !server_ip) {
        return nullptr;
    }

    oracle_session_t *session = (oracle_session_t*)calloc(1, sizeof(oracle_session_t));
    if (!session) {
        SESS_LOG_ERROR("Failed to allocate memory for Oracle session");
        return nullptr;
    }

    // 初始化会话基本信息
    session->session_id = m_next_session_id++;
    session->state = ORACLE_SESSION_STATE_INIT;
    session->created_time = time(nullptr);
    session->last_activity_time = session->created_time;
    
    // 设置网络信息
    strncpy(session->client_ip, client_ip, sizeof(session->client_ip) - 1);
    session->client_port = client_port;
    strncpy(session->server_ip, server_ip, sizeof(session->server_ip) - 1);
    session->server_port = server_port;
    
    // 初始化连接状态
    session->connection_status = ORACLE_CONN_INIT;
    session->is_authenticated = false;
    session->in_transaction = false;
    
    // 添加到活跃会话映射
    m_active_session_map[session->session_id] = session;
    m_active_sessions++;
    m_total_sessions_created++;
    
    if (m_active_sessions > m_peak_sessions) {
        m_peak_sessions = m_active_sessions;
    }
    
    SESS_LOG_DEBUG("Created Oracle session: id=%u, client=%s:%d, server=%s:%d",
                  session->session_id, client_ip, client_port, server_ip, server_port);
    
    return session;
}

int OracleSessionManager::destroy_session(uint32_t session_id)
{
    auto it = m_active_session_map.find(session_id);
    if (it == m_active_session_map.end()) {
        SESS_LOG_WARN("Attempt to destroy non-existent session: %u", session_id);
        return SESSION_MGT_ERROR;
    }
    
    oracle_session_t *session = it->second;
    
    SESS_LOG_DEBUG("Destroying Oracle session: id=%u, user=%s, duration=%lu seconds",
                  session->session_id,
                  session->username[0] != '\0' ? session->username : "unknown",
                  (unsigned long)(time(nullptr) - session->created_time));
    
    // 清理会话资源
    free_oracle_session(session);
    
    // 从映射中移除
    m_active_session_map.erase(it);
    m_active_sessions--;
    m_total_sessions_destroyed++;
    
    return SESSION_MGT_SUCCESS;
}

oracle_session_t* OracleSessionManager::get_session(uint32_t session_id)
{
    auto it = m_active_session_map.find(session_id);
    if (it != m_active_session_map.end()) {
        // 更新最后活动时间
        it->second->last_activity_time = time(nullptr);
        return it->second;
    }
    
    return nullptr;
}

int OracleSessionManager::update_session_state(uint32_t session_id, oracle_session_state_t new_state)
{
    oracle_session_t *session = get_session(session_id);
    if (!session) {
        return SESSION_MGT_ERROR;
    }
    
    oracle_session_state_t old_state = session->state;
    session->state = new_state;
    session->last_activity_time = time(nullptr);
    
    SESS_LOG_DEBUG("Session %u state changed: %d -> %d", session_id, old_state, new_state);
    
    return SESSION_MGT_SUCCESS;
}

int OracleSessionManager::update_connection_status(uint32_t session_id, oracle_conn_status new_status)
{
    oracle_session_t *session = get_session(session_id);
    if (!session) {
        return SESSION_MGT_ERROR;
    }
    
    oracle_conn_status old_status = session->connection_status;
    session->connection_status = new_status;
    session->last_activity_time = time(nullptr);
    
    SESS_LOG_DEBUG("Session %u connection status changed: %d -> %d", session_id, old_status, new_status);
    
    return SESSION_MGT_SUCCESS;
}

int OracleSessionManager::set_session_user(uint32_t session_id, const char *username)
{
    if (!username) {
        return SESSION_MGT_ERROR;
    }
    
    oracle_session_t *session = get_session(session_id);
    if (!session) {
        return SESSION_MGT_ERROR;
    }
    
    strncpy(session->username, username, sizeof(session->username) - 1);
    session->username[sizeof(session->username) - 1] = '\0';
    session->is_authenticated = true;
    session->last_activity_time = time(nullptr);
    
    SESS_LOG_DEBUG("Session %u authenticated as user: %s", session_id, username);
    
    return SESSION_MGT_SUCCESS;
}

int OracleSessionManager::start_transaction(uint32_t session_id, uint32_t transaction_id)
{
    oracle_session_t *session = get_session(session_id);
    if (!session) {
        return SESSION_MGT_ERROR;
    }
    
    session->in_transaction = true;
    session->transaction_id = transaction_id;
    session->transaction_start_time = time(nullptr);
    session->last_activity_time = session->transaction_start_time;
    
    SESS_LOG_DEBUG("Session %u started transaction: %u", session_id, transaction_id);
    
    return SESSION_MGT_SUCCESS;
}

int OracleSessionManager::end_transaction(uint32_t session_id, bool committed)
{
    oracle_session_t *session = get_session(session_id);
    if (!session) {
        return SESSION_MGT_ERROR;
    }
    
    if (session->in_transaction) {
        session->in_transaction = false;
        session->last_activity_time = time(nullptr);
        
        if (committed) {
            session->total_commits++;
        } else {
            session->total_rollbacks++;
        }
        
        SESS_LOG_DEBUG("Session %u ended transaction: %u (%s)", 
                      session_id, session->transaction_id, committed ? "COMMIT" : "ROLLBACK");
    }
    
    return SESSION_MGT_SUCCESS;
}

int OracleSessionManager::cleanup_expired_sessions()
{
    time_t current_time = time(nullptr);
    
    // 检查是否需要清理
    if (current_time - m_last_cleanup_time < m_cleanup_interval) {
        return SESSION_MGT_SUCCESS;
    }
    
    m_last_cleanup_time = current_time;
    
    std::vector<uint32_t> expired_sessions;
    
    // 查找过期会话
    for (auto& pair : m_active_session_map) {
        oracle_session_t *session = pair.second;
        if (current_time - session->last_activity_time > m_session_timeout) {
            expired_sessions.push_back(session->session_id);
        }
    }
    
    // 清理过期会话
    for (uint32_t session_id : expired_sessions) {
        SESS_LOG_INFO("Cleaning up expired session: %u", session_id);
        destroy_session(session_id);
        m_session_timeouts++;
    }
    
    if (!expired_sessions.empty()) {
        SESS_LOG_INFO("Cleaned up %zu expired sessions", expired_sessions.size());
    }
    
    return SESSION_MGT_SUCCESS;
}

void OracleSessionManager::get_session_statistics(oracle_session_stats_t *stats)
{
    if (!stats) {
        return;
    }
    
    stats->total_sessions_created = m_total_sessions_created;
    stats->total_sessions_destroyed = m_total_sessions_destroyed;
    stats->active_sessions = m_active_sessions;
    stats->peak_sessions = m_peak_sessions;
    stats->session_timeouts = m_session_timeouts;
    
    // 计算认证会话数
    stats->authenticated_sessions = 0;
    stats->sessions_in_transaction = 0;
    
    for (auto& pair : m_active_session_map) {
        oracle_session_t *session = pair.second;
        if (session->is_authenticated) {
            stats->authenticated_sessions++;
        }
        if (session->in_transaction) {
            stats->sessions_in_transaction++;
        }
    }
    
    // 计算平均会话持续时间
    if (m_total_sessions_destroyed > 0) {
        stats->average_session_duration = 0; // 简化实现
    } else {
        stats->average_session_duration = 0;
    }
}

void OracleSessionManager::free_oracle_session(oracle_session_t *session)
{
    if (session) {
        // 清理会话相关资源
        // 这里可以添加更多的清理逻辑
        free(session);
    }
}

void OracleSessionManager::dump_session_info(uint32_t session_id)
{
    oracle_session_t *session = get_session(session_id);
    if (!session) {
        SESS_LOG_WARN("Session %u not found for dump", session_id);
        return;
    }
    
    SESS_LOG_INFO("Session %u Info:", session_id);
    SESS_LOG_INFO("  State: %d", session->state);
    SESS_LOG_INFO("  Connection Status: %d", session->connection_status);
    SESS_LOG_INFO("  User: %s", session->username[0] != '\0' ? session->username : "not authenticated");
    SESS_LOG_INFO("  Client: %s:%d", session->client_ip, session->client_port);
    SESS_LOG_INFO("  Server: %s:%d", session->server_ip, session->server_port);
    SESS_LOG_INFO("  Created: %lu", (unsigned long)session->created_time);
    SESS_LOG_INFO("  Last Activity: %lu", (unsigned long)session->last_activity_time);
    SESS_LOG_INFO("  In Transaction: %s", session->in_transaction ? "yes" : "no");
    SESS_LOG_INFO("  Total Commits: %lu", (unsigned long)session->total_commits);
    SESS_LOG_INFO("  Total Rollbacks: %lu", (unsigned long)session->total_rollbacks);
}
