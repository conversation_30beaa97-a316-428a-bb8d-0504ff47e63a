
CFLAGS         += -Wall
ifeq ("$(BUILD_SCHEME)", "Debug")
CFLAGS          += -g3
CFLAGS          += -O0
CFLAGS += -D_DEBUG
else ifeq ("$(BUILD_SCHEME)", "Test")
CFLAGS          += -g
CFLAGS          += -O2
# CFLAGS += -D_DEBUG
else ifeq ("$(BUILD_SCHEME)", "UnitTest")
CFLAGS          += -g
CFLAGS          += -O2
# CFLAGS += -D_DEBUG
LDFLAGS += -ltcmalloc
CFLAGS += -D_ENABLE_FLUSH_FILE=1
else ifeq ("$(BUILD_SCHEME)", "CiUnitTest")
CFLAGS          += -g
CFLAGS          += -O2
# CFLAGS += -D_DEBUG
LDFLAGS += -ltcmalloc
CFLAGS += -D_ENABLE_FLUSH_FILE=1
CFLAGS += -D_DISABLE_LICENSE_=1
CFLAGS += -DNDEBUG
else ifeq ("$(BUILD_SCHEME)", "Profile")
CFLAGS          += -g
CFLAGS          += -O3
CFLAGS += -DNDEBUG
LDFLAGS += -ltcmalloc
LDFLAGS += -lprofiler
else ifeq ("$(BUILD_SCHEME)", "Analyze")
CFLAGS          += -g
CFLAGS          += -O2
CFLAGS += -DNDEBUG
else ifeq ("$(BUILD_SCHEME)", "Release")
CFLAGS          += -g3
CFLAGS          += -O0
CFLAGS += -DNDEBUG
LDFLAGS += -ltcmalloc
else ifeq ("$(BUILD_SCHEME)", "Release_x86")
CFLAGS          += -g3
CFLAGS          += -O0
CFLAGS += -DNDEBUG
LDFLAGS += -ltcmalloc
else ifeq ("$(BUILD_SCHEME)", "Release_arm")
CFLAGS          += -g
CFLAGS          += -O3
CFLAGS += -DNDEBUG
CFLAGS += -DARM
CFLAGS += -march=armv8-a
else ifeq ("$(BUILD_SCHEME)", "RiskEval")
CFLAGS          += -g
CFLAGS          += -O3
CFLAGS += -DNDEBUG
CFLAGS += -D_RISK_EVAL_CORE_=1
CFLAGS += -D_DISABLE_LICENSE_=1
LDFLAGS += -ltcmalloc
else ifeq ("$(BUILD_SCHEME)", "asan")
CFLAGS          += -g3
CFLAGS          += -O0
CFLAGS          += -fsanitize=address
CFLAGS          += -fno-omit-frame-pointer
CFLAGS          += -D_DISABLE_LICENSE_=1
CFLAGS += -D_ENABLE_FLUSH_FILE=1
CFLAGS += -DNDEBUG
LDFLAGS += -lasan
else ifeq ("$(BUILD_SCHEME)", "gdb")
CFLAGS          += -g3
CFLAGS          += -O0
CFLAGS += -DNDEBUG
CFLAGS += -D_DISABLE_LICENSE_=1
CFLAGS += -D_ENABLE_FLUSH_FILE=1
#LDFLAGS += -ltcmalloc
else
CFLAGS          += -O2
# ifneq ("$(BUILD_OS)", "Darwin")
# LDFLAGS += -ltcmalloc
# endif
endif

CPPFLAGS          = $(CFLAGS) -std=c++0x


# 用于测试 限制到4个worker
ifeq ("$(BUILD_TEST)", "1")
CFLAGS += -D__FOR_TEST__=1
endif
# 用于测试 限制到8个worker
ifeq ("$(BUILD_DEV_VIRENV)", "1")
CFLAGS += -D__FOR_DEV_VIRENV__=1
endif
# 用于mac测试 限制到4个worker
ifeq ("$(BUILD_DEV_MAC)", "1")
CFLAGS += -D__FOR_DEV_MAC__=1
endif

# 用于编译测试工具
ifeq ("$(BUILD_TOOLS)", "1")
CFLAGS += -D_DISABLE_LICENSE_=1
endif
