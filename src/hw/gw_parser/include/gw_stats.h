/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#ifndef __GW_STATS_H__
#define __GW_STATS_H__

#include <stdint.h>
#include <pthread.h>

#include <vector>
#include <map>
#include <string>
#include <utility>

#include "cap_data.h"
#include "addr_port_collect.h"
#include "ipfilter_rule.h"
#include "worker_queue.h"

#define MAX_WORK_PORTS (8)
#define MAX_NIC_DEVICE_NUM (4)

#define MAX_SESSION_PROTO_TYPE_NUM_2 16

#define SOURCE_FLAG_DPDK    0x00000001
#define SOURCE_FLAG_AGENT   0x00000002
#define SOURCE_FLAG_NIC     0x00000004
#define SOURCE_FLAG_PCAP    0x00000008

struct cJSON;

typedef void (*CALLBACK_STATS)(void *);

// 队列
typedef struct StatsQueueData
{
  int cb; // 当前结构体大小

  volatile uint64_t cnt_q_num;     // 队列当前数量
  volatile uint64_t cnt_q_succ;    // 成功数量
  volatile uint64_t cnt_q_fail;    // 入队列失败
  volatile uint64_t cnt_q_full;    // 队列满
  volatile uint64_t cnt_q_no_mem;  // 内存不足
  volatile uint64_t cnt_q_max_num; // 队列出现过的最大数量
} stats_queue_t;

// 状态统计
struct StatsTaskData
{
  int cb; // 当前结构体大小

  volatile uint64_t cnt; // 总数

  // 队列
  volatile stats_queue_t q;

  // 处理
  volatile uint64_t cnt_succ;  // 成功数量
  volatile uint64_t cnt_fail;  // 失败总数量
  volatile uint64_t cnt_fail2; // 失败2数量  gzip 解压失败, upload 上传失败
  volatile uint64_t cnt_fail3; // 失败3数量  内存不足
  volatile uint64_t cnt_fail4; // 失败4数量  gzip 字符串太长,
};

#define STATS_QUEUE_NUM(stats, num)               \
  do                                              \
  {                                               \
    (stats)->q.cnt_q_num = (num);                 \
    if (unlikely((stats)->q.cnt_q_max_num < num)) \
    {                                             \
      (stats)->q.cnt_q_max_num = num;             \
    }                                             \
  } while (0)



struct StatsQpsData
{
  int cb;

  // STATS_QPS_UNIT
  volatile uint64_t cnt;         // 总数量
  volatile float qps;            // 总共 qps
  volatile float qps_last_2;     // 最近2秒 qps
  volatile float qps_last_30;    // 最近30秒 qps
  volatile float qps_last_60;    // 最近1分钟 qps
  volatile float qps_last_300;   // 最近5分钟 qps
  volatile float qps_last_1800;  // 最近30分钟 qps
  volatile float qps_last_3600;  // 最近1小时 qps
  volatile float qps_last_7200;  // 最近2小时 qps
  volatile float qps_last_28800; // 最近8小时 qps
  volatile float qps_last_43200; // 最近12小时 qps
  volatile float qps_last_86400; // 最近1天 qps

  StatsQpsData():cb(0),
  cnt(0), 
  qps(0.0),
  qps_last_2(0.0),
  qps_last_30(0.0),
  qps_last_60(0.0),
  qps_last_300(0.0),
  qps_last_1800(0.0),
  qps_last_3600(0.0),
  qps_last_7200(0.0),
  qps_last_28800(0.0),
  qps_last_43200(0.0),
  qps_last_86400(0.0)
  {}

};

struct Stats_Data_t
{
  const volatile uint64_t* cur_value;
  std::vector<uint64_t> cache_value;
  std::map<std::string,Stats_Data_t> branches;
};

typedef struct gw_parser_stats_t
{
  uint64_t u64_time_val; /* 获取状态的时间戳 */
  uint32_t u32_eth_num;  /* dpdk 抓包网口的数量 */
  uint32_t u32_nic_num;  /* nic  抓包网口数量*/

  eth_info_t a_st_eth_info[MAX_WORK_PORTS]; /* 网口信息 */
  pcap_probe_info_t st_pcap_probe_info;     /* pcap模式下状态信息 */
  nic_info_t a_st_nic_info[MAX_NIC_DEVICE_NUM];

  float f_recv_bytes_speed;         /* 接收数据的速率 */
  uint64_t u64_parse_http_cnt;      /* 解析http包的个数 */
  uint64_t u64_succ_parse_http_cnt; /* 成功解析http包的数量 */
  float f_parse_http_speed;         /* 解析http包的速率 */
  float f_parse_postgre_speed;      /* 解析PostgreSQL包的速率 */
  uint64_t u64_http_gzip_num;       /* 解压http gzip包的数量 */
  float f_http_gzip_speed;          /* 解压http gzip的速率 */
  uint64_t u64_up_kafka_cnt;        /* 上传kafka的数据量 */
  uint64_t u64_up_kafak_succ_cnt;   /* 上传kafka成功的数据量 */
  float f_up_kafka_succ_speed;      /* 上传kafka成功的速率 */
  float f_up_kafka_succ_bytes_speed;/* 上传kafka成功的字节速率 */
  float f_cpu_usage;                /* 当前CPU使用率 */
  float f_mem_usage;                /* 当前使用内存占用比 */
  // uint64_t u64_ip_flow_total;                                           /* ip层流量总量 */
  // uint64_t u64_ip_flow_fwd;                                          /* ip层转发流量 */
  // uint64_t u64_ip_flow_drop;                                             /* ip层丢弃流量 */
  struct AddrPortCount addr_port_cnts[SHOW_ADDR_PORT_NUM];              /* 展示目的IP:PORT及其数目 */

  // uint64_t u64_http_cnt_m;                            // 解析总数
  // uint64_t u64_http_cnt_m_succ;                       // 解析成功数量
  // uint64_t u64_http_cnt_m_fail;                       // 解析失败数量
  // uint64_t u64_http_cnt_match_by_trunk_failed;      // 因为trunk导致的失败
  // uint64_t u64_http_cnt_match_by_trunk_too_large;    // 因为trunk导致的失败
  // uint64_t u64_http_cnt_match_by_ungzip_failed;     // 因为gzip解压导致的失败
  // uint64_t u64_http_cnt_match_by_payload_too_large;  // 因为内容过长导致的失败
  // uint64_t u64_http_cnt_match_by_packet_lost;       // 因为内容过长导致的失败

  // uint64_t u64_app_cnt;                                      // 数据总量
  // uint64_t u64_app_cnt_parsed;                               // 解析数量
  // uint64_t u64_app_cnt_drop_by_http_trunk_failed;            // 因为trunk失败导致的丢弃
  // uint64_t u64_app_cnt_drop_by_http_trunk_too_large;         // 因为trunk过长导致的丢弃
  // uint64_t u64_app_cnt_drop_by_http_payload_too_large;       // 因为内容过长导致的丢弃
  // uint64_t u64_app_cnt_drop_by_http_parsed_failed;           // 因为解析失败导致的丢弃
  // uint64_t u64_app_cnt_drop_by_packet_lost;                  // 因为丢包导致的丢弃
  // uint64_t u64_app_cnt_drop_by_cache_too_large;              // 因为缓存过大导致的丢弃
  // uint64_t u64_app_cnt_drop_by_port_filter_hit;              // 因为端口过滤命中导致的丢弃
  // uint64_t u64_app_cnt_drop_by_urg;                          // 因为紧急数据而导致的丢弃
  // uint64_t u64_app_cnt_drop_by_other;                        // 其他原因导致的丢弃

} gw_parser_stats_t;

typedef struct
{
  uint64_t u64_in_total_packets;           /* 进入网口的包数 */
  uint64_t u64_in_err_packets;             /* 进入网卡出现错误的包数 */
  uint64_t u64_in_drop_packets;            /* 由硬件网卡丢弃的包数 */
  uint64_t u64_in_total_bytes;             /* 进入网卡的字节数 */
  uint64_t u64_avg_bytes_per_packet;       /* 每个包的平均字节数 */
  uint64_t u64_in_drop_bytes;              /* 从硬件网卡丢弃的字节数 */
  uint64_t u64_in_err_bytes;               /* 进入网卡错误的字节数 */
}eth_statstic_info_t;


class CGwCommon;
class CSource;
class CParser;

// class CUpload;
struct UploadMsg;

class CIpfilterRule;
class CSingleStreamCollect;
class CGwStats
{
public:
  CGwStats(void);
  virtual ~CGwStats(void);

public:
  // /**
  //  * 显示输出
  //  */
  // virtual void show(void);

  virtual void init();

  virtual void fini();

  virtual void run();

  /**
   * 触发退出信号时处理
   */
  virtual void set_quit_signal(void);

  /**
   * 等待运行结束
   */
  virtual void wait_for_stop(void);

  /**
   * 设置全局公共类对象实例。
   * @param CGwCommon *comm
   */
  virtual void set_gw_common(CGwCommon *comm);

  // /**
  //  * 设置显示工作线程状态调用函数
  //  * @param CALLBACK_STATS cb
  //  * @param void *ud
  //  */
  // virtual void set_worker_callback(CALLBACK_STATS cb, void *ud);

  // /**
  //  * 设置显示IP drop信息调用函数
  //  * @param CALLBACK_STATS cb
  //  * @param void *ud
  //  */
  // virtual void set_drop_callback(CALLBACK_STATS cb, void *ud);

  /**
   * 设置状态输出回调函数
   * @param const char *name
   * @param CALLBACK_STATS cb
   * @param void *ud
   * @param int priority 数字越小越优先显示
   */
  virtual void set_stats_callback(const char *name, CALLBACK_STATS cb, void *ud, int priority = 100);

  // /**
  //   * 设置转发状态输出对象
  //   * @param const char *name
  //   * @param const stats_forward_t*
  //   * @param int priority 数字越小越优先显示
  //   */
  // virtual void set_forward(const char *name, const stats_forward_t *, int priority = 100);

  // /**
  //   * 设置转发字节数状态输出对象
  //   * @param const char *name
  //   * @param const stats_forward_t*
  //   * @param int priority 数字越小越优先显示
  //   */
  // virtual void set_forward_bytes(const char *name, const stats_forward_t *, int priority = 100);


  /**
    * 设置队列任务状态输出对象
    * @param const char *name
    * @param const StatsTaskData*
    * @param int priority 数字越小越优先显示
    */
  //virtual void set_task(const char *name, const StatsTaskData *, int priority = 100);
  virtual void set_task(const char *name, CWorkerQueue *pwq, int priority = 100);

  /**
   * 设置QPS统计对象，
   * 更新计算值在状态线程中统一处理。
   * @param const char *name
   * @param const volatile uint64_t *
   * @param int priority 数字越小越优先显示
   */
  virtual void set_qps(const char *name, const volatile uint64_t *, int priority = 100);

  /**
   * 设置内存使用统计对象，
   * @param const char *name
   * @param const volatile uint64_t *
   * @param const volatile uint64_t *
   * @param int priority 数字越小越优先显示
   */
  virtual void set_mem_stat(const char *name, const volatile uint64_t *mem, const volatile uint64_t *max_mem, int priority = 100);

  /**
   * 根据name获取qps值 
   * @param const char *name
   */
  virtual uint32_t get_name_qps(const char *name);



  /**
   * 计算一段时间内的统计值差值
   * @param name 统计值名称
   * @param branch_name NULL表示总值，非NULL表示分量值，内容为分量值的名称
   * @param value 统计值变量
   * @param priority 优先级，数字越小越优先显示
   */
  virtual void set_stats(const char* name,const char* branch_name,const volatile uint64_t* value,int priority = 100);
  virtual void set_byte_stats(const char* name,const char* branch_name,const volatile uint64_t* value,int priority = 100);

  virtual void set_souce(CSource *csource);

  virtual void set_http_parser(CParser *cparser);

  /**
   * 加载配置参数（Json字符串，支持动态）。
   * @param const char *
   */
  virtual bool load_conf(const char *);

  /**
   *  获取限流参数 
   */
  virtual int get_stats_speed_limit();

  virtual void put_event_msg(bool is_request,const char* content,int content_len);


  virtual void put_upload_msg(UploadMsg *);


  virtual bool check_analyze_log(uint32_t source_ip,uint32_t dest_ip,uint16_t dest_port);

  virtual uint32_t get_analyze_flag();

  void get_queue_info (char* log_buf, size_t log_buf_len);
  void get_task_info(char* log_buf, size_t log_buf_len);
  void get_qps_info(char* log_buf, size_t log_buf_len);
  void get_mem_info(char* log_buf, size_t log_buf_len);
  void set_tcp_parser(CTcpParser *tcp_parser);
protected:
  /**
   * qps更新计算。
   */
  void update(void);

  void gwp_print_stats(void);

  /**
   * 计算count值
   */
  void calculate(void);

protected:
  pthread_t m_thread_report_stat; 
  int m_thread_report_stat_stats; /* report stat 状态 */
  static int report_stat_run(void *arg_ptr);
  int report_stat();
  pthread_t m_thread_qps_stat;
  int m_pthread_qps_stat_stats; /* qps stat 状态 */
  static int qps_stat_run(void *arg_ptr);
  int qps_stat();

protected:
  CGwCommon *m_comm;
  volatile int m_quit_signal;

  // forward
  // typedef std::pair<int, std::string> forward_name_priority_t;
  // std::vector<forward_name_priority_t> m_vec_forward_name;
  // std::map<std::string, const stats_forward_t*> m_mp_forward_data;

  // // forward bytes
  // typedef std::pair<int, std::string> forward_bytes_priority_t;
  // std::vector<forward_bytes_priority_t> m_vec_forward_bytes;
  // std::map<std::string, const stats_forward_t*> m_mp_forward_bytes;

  // task queue 状态
  typedef std::pair<int, std::string> task_name_priority_t;
  std::vector<task_name_priority_t> m_vec_task_name;
  //std::map<std::string, const StatsTaskData *> m_map_task_data;
  std::map<std::string, CWorkerQueue *> m_map_task_data;
  // qps 统计
  typedef std::pair<int, std::string> qps_name_priority_t;
  std::vector<task_name_priority_t> m_vec_qps_name;
  std::map<std::string, const volatile uint64_t *> m_map_qps_count;
  std::map<std::string, StatsQpsData *> m_map_qps_data;
  // custom 显示
  typedef std::pair<int, std::string> custom_show_name_priority_t;
  std::vector<custom_show_name_priority_t> m_vec_custom_show_name;
  typedef std::pair<CALLBACK_STATS, void *> custom_show_cb_t;
  std::map<std::string, custom_show_cb_t> m_map_custom_show_cb;
  // mem 统计
  typedef std::pair<int, std::string> mem_name_priority_t;
  std::vector<mem_name_priority_t> m_vec_mem_name;
  std::map<std::string, const volatile uint64_t *> m_map_mem_data;
  std::map<std::string, const volatile uint64_t *> m_map_max_mem_data;


  typedef std::pair<int,std::string> count_name_priority_t;
  std::vector<count_name_priority_t> m_vec_stats_name;
  std::vector<count_name_priority_t> m_vec_bytes_stats_name;
  std::map<std::string,Stats_Data_t> m_map_stats_data;

private:
  // void print_forward_stats(void);
  // void print_ip_forward_stats(const stats_forward_t *p_stats_forward);
  // void print_forward_bytes_stats(void);
  // void print_worker_stats(void);
  // void print_drop_stats(void);
  void print_task_stats(void);
  void print_qps_stats(void);
  void print_mem_info(void);
  void print_single_stream_stats(void);
  // CALLBACK_STATS m_worker_cb;
  // void *m_worker_data;
  // CALLBACK_STATS m_drop_info_cb;
  // void *m_drop_info_data;

private:
  // qps 计算变量
  int m_qps_cur;
  int m_qps_show_idx;
  time_t m_gw_time_base;

// protected:
//   void collect_http_match_status(gw_parser_stats_t & st_gw_parser_stats);
//   void collect_app_cnt_status(gw_parser_stats_t & st_gw_parser_stats);

//   void print_http_match_status();
//   void print_app_cnt_status();
  void update_eth_info(gw_parser_stats_t & st_gw_parser_stats);

protected:
  int pre_collect_gw_parser_stats(void);
  int collect_gw_parser_stats(int delta);
  cJSON *struct_to_json(const gw_parser_stats_t *p_st_gw_parser_info);
  void stats_data_to_json(cJSON* root);
  int get_timeval(uint64_t *p_u64_timaval);
  void cut_stats_file(bool is_del_stats_file);
  int m_i_source_flag;
  std::vector<CSource*> m_vec_source;
  CParser *m_parser;

protected:
  int m_conf_show_stats; // TODO
  int m_collect_stats;
  std::string m_str_stats_path;
  std::string m_str_stats_file;
  FILE *m_stats_fp;

protected:
  int m_conf_limit_mbps;
  int m_conf_part_limit_mbps;
  int m_conf_white_part_limit_mbps;
  int m_stats_speed_limit;
  int m_conf_stream_debug;
  int m_conf_stream_order_field;
  int m_conf_addr_port_max_num;

  int m_stats_data_interval;          //上传产品端的数据变化的时间范围，默认600秒，计算10分钟内各统计数值的差值
  int m_stats_run_days;               //状态文件运行的天数，每天切割一个文件，最后保留两个星期的文件
  const int m_stats_file_hold_days;   //状态文件保存的天数，默认14天
  eth_statstic_info_t m_last_eth_info[MAX_WORK_PORTS];

  static CIpfilterRule st_collect_flow_ip_white;
  CTcpParser *m_tcp_parser;
  //static int st_only_collect_ip_white;
public:
  static void add_addr_port(void * psm, uint32_t dst_addr, uint16_t dst_port, uint32_t src_addr, uint16_t src_port, uint32_t flow, int first_link);
  static void send_single_msg(void *psm, void * addr, int trigger_condition, int only_where, int reverse);
  int collect_top_addr_port_items(struct AddrPortCount* items, int num, int order_field);
  void clear_addr_port_items();
};

#endif // __GW_STATS_H__
