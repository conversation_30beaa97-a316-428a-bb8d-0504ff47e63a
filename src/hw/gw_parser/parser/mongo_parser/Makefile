
ifeq ("$(BUILD_CC_TOOL)","clang++")
CC              = clang++ -fsanitize=address -fno-omit-frame-pointer -D_CC_CLANG_PP
else ifeq ("$(BUILD_CC_TOOL)","clang")
CC              = clang -fsanitize=address -fno-omit-frame-pointer -D_CC_CLANG
else ifeq ("$(BUILD_CC_TOOL)","g++")
CC              = g++ -D_CC_GNU_PP
else ifeq ("$(BUILD_CC_TOOL)", "aarch64-linux-gnu-gcc")
CC              = aarch64-linux-gnu-gcc
else
CC              = gcc
endif


CFLAGS          = -std=c++11 -g -fvisibility=hidden -fPIC -I. -I../.././include -I../.././utils/queue/ -I../.././utils/cjson/ -I/usr/include/libbson-1.0


LDFLAGS         = -shared 
LDFLAGS        += -lstdc++ -lpthread -lbson-1.0  -lssl -lcrypto  

CFLAGS         += -I/opt/openssl/include/
LDFLAGS        += -L/opt/openssl/lib/


include ../../flags.make

O_FILES = mongo_parser.o 
O_FILES += mongo_parser_deal_probe.o
O_FILES += mongo_parser_deal_parser.o
O_FILES += mongo_parser_wire_protocol_task_worker.o mongo_parser_task_worker.o 

O_FILES += module_mgt_mongo_parser.o
O_FILES += cJSON.o cJSON_Utils.o 

.PHONY: all clean


all: mongo_parser.so 

%.o:%.cpp
	$(CC) -c $(CFLAGS)  $(LIBS_CFLAGS) $<

%.o:%.c
	$(CC) -c $(CFLAGS)  $(LIBS_CFLAGS) $<

cJSON.o: ../.././utils/cjson/cJSON.c ../.././utils/cjson/cJSON.h
	$(CC) -c $(CFLAGS)  $(LIBS_CFLAGS) $<

cJSON_Utils.o: ../.././utils/cjson/cJSON_Utils.c ../.././utils/cjson/cJSON_Utils.h
	$(CC) -c $(CFLAGS)  $(LIBS_CFLAGS) $<

mongo_parser.so: $(O_FILES) 
	$(CC) -o $@ $^ $(LDFLAGS) $(LIBS) $(LIB)

clean:
	rm -f *.o *~ mongo_parser.so


