/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <memory.h>
#include <inttypes.h>

#include "mongo_parser.h"
#include "mongo_parser_common.h"

#include "utils.h"

#include "gw_common.h"
#include "gw_logger.h"
#include "gw_config.h"
#include "session_mgt.h"
#include "session.h"

#include "worker_queue.h"
#include "task_worker.h"
#include "gw_stats.h"

#include "gw_i_upload.h"
#include "display_stats_define.h"
#include "cJSON.h"

/**
 * CMongoParser implementation
 *
 * Mongodb 协议解析
 */
CMongoParser::CMongoParser(void)
{
  m_quit_signal = 0;
  m_comm = NULL;
  memset(m_name, 0, sizeof(m_name));
  snprintf(m_name, COUNTOF(m_name) - 1, "CMongoParser-%" PRIu64 "", ((uint64_t)this) & 0xffff);

  memset(&m_stats_mongo, 0, sizeof(m_stats_mongo));

  memset(&m_p_wq, 0, sizeof(m_p_wq));
  memset(&m_p_tw, 0, sizeof(m_p_tw));

  m_conf_upload_name = "log";

  m_conf_mongo_wire_protocol_mode = 1;
  m_conf_mongo_wire_protocol_thread_num = 2;
  m_conf_mongo_wire_protocol_queue_max_num = 1000;
  m_conf_mongo_wire_protocol_queue_memory_max_size_bytes = 100 * 1024ULL * 1024ULL;

  m_event_prior = 1;
  m_content_prior = 2;

  for (size_t i = 0; i < COUNTOF(m_wire_protocol_lock); i++)
  {
    SPIN_IMPL_INIT(m_wire_protocol_lock[i]);
    spin_init(&(m_wire_protocol_lock[i]), PTHREAD_PROCESS_SHARED);
  }
}

CMongoParser::~CMongoParser(void)
{

  del_session_mongo_map_merge_data(&m_map_session);

  for (size_t i = 0; i < COUNTOF(m_p_wq); i++)
  {
    free_worker_queue(m_p_wq[i]);
  }
  for (size_t i = 0; i < COUNTOF(m_p_tw); i++)
  {
    free_task_worker(m_p_tw[i]);
    m_p_tw[i] = NULL;
  }

  for (size_t i = 0; i < COUNTOF(m_wire_protocol_lock); i++)
  {
    spin_destroy(&(m_wire_protocol_lock[i]));
  }
}

void CMongoParser::cache_clean() 
{
  for (int i(0); i<MONGOPARSER_WQ_MAX_NUM; i++)
  {
    if (m_p_wq[i]) 
    {
      m_p_wq[i]->flush_queue();
    }
  }
  
}

/**
 * 获取当前流解析出来的数据。
 * @param struct StreamData *psd
 * @param int dir
 * @param int *data_len
 * @param int *offset_out
 */
const char *CMongoParser::get_data(const struct StreamData *psd, int dir, int *data_len, int *offset_out)
{
  *data_len = 0;
  *offset_out = 0;
  return NULL;
}

/**
 * 已解析处理字节数。
 * @param struct StreamData *
 * @param int dir
 * @param int num
 */
bool CMongoParser::discard(struct StreamData *, int dir, int num)
{
  return false;
}

/**
 * 已处理字节数，同时更新数据。
 * @param struct StreamData *
 * @param int dir
 * @param int num
 */
bool CMongoParser::discard_and_update(struct StreamData *, int dir, int num)
{
  return false;
}

// /**
//  * 删除解析对象中在会话管理中的单边数据。
//  * @param HalfStreamData*
//  */
// void CMongoParser::del_session_half_stream(HalfStreamData *)
// {
//   return;
// }

/**
 * @param StreamData*
 */
void CMongoParser::del_session_stream(StreamData *psd)
{
  struct mongo_stream *pms = psd->p_mongo_stream;

  if (pms->p_mongo_server != NULL)
  {
    del_session_mongo_half_stream(pms->p_mongo_server);
    free(pms->p_mongo_server);
  }
  if (pms->p_mongo_client != NULL)
  {
    del_session_mongo_half_stream(pms->p_mongo_client);
    free(pms->p_mongo_client);
  }

  if (pms->map_session_data != NULL)
  {
    del_session_mongo_map_merge_data(pms->map_session_data);

    delete pms->map_session_data;
  }

  delete psd->p_mongo_stream;
  delete psd;
}

/**
 * @param SessionMgtData*
 */
void CMongoParser::del_session_param(SessionMgtData *)
{
}

void CMongoParser::init()
{
  ASSERT(m_comm != NULL);
  m_quit_signal = 0;

  load_conf(NULL);

  m_comm->get_gw_stats()->set_stats_callback(MONGO_SHOW, print_mongo_stats_callback, this);
  m_comm->get_gw_stats()->set_qps(MONGO_SESSION_QPS, &m_stats_mongo.cnt_session, 90);

  new_wq_wire_protocol();
}

void CMongoParser::fini()
{
  ASSERT(m_comm != NULL);
  for (size_t i = 0; i < COUNTOF(m_p_wq); i++)
  {
    free_worker_queue(m_p_wq[i]);
    m_p_wq[i] = NULL;
  }
  for (size_t i = 0; i < COUNTOF(m_p_tw); i++)
  {
    free_task_worker(m_p_tw[i]);
    m_p_tw[i] = NULL;
  }

  del_session_mongo_map_merge_data(&m_map_session);
}

void CMongoParser::run()
{
  ASSERT(m_comm != NULL);
  for (size_t i = 0; i < COUNTOF(m_p_wq); i++)
  {
    if (m_p_wq[i] == NULL)
    {
      continue;
    }
    m_p_wq[i]->run();
  }
}

/**
 * 获取对象名。以-为分隔符，前半部分为类名，后半部分为实例地址尾部分。
 */
const char *CMongoParser::get_name(void) const
{
  return m_name;
}

/**
 * 获取版本号。
 */
const char *CMongoParser::get_version(void) const
{
  return MONGOARSER_VER;
}

/**
 * 设置全局公共类对象实例。
 * @param CGwCommon *comm
 */
void CMongoParser::set_gw_common(CGwCommon *comm)
{
  m_comm = comm;
}

/**
 * 加载配置参数（Json字符串，支持动态）。
 * @param const char *
 */
bool CMongoParser::load_conf(const char *json_string)
{
  CGwConfig *pgwc = m_comm->get_gw_config();

  // if (json_string != NULL)
  // {
  //   // TODO 动态加载配置参数
  //   pgwc->load_string(json_string);
  // }

  m_conf_upload_name = pgwc->read_conf_string("parser", "upload_mode");
  m_conf_mongo_wire_protocol_mode = pgwc->read_conf_int("mongo_parser", "mongo_wire_protocol_mode", m_conf_mongo_wire_protocol_mode);
  m_conf_mongo_wire_protocol_thread_num = pgwc->read_conf_int("mongo_parser", "mongo_wire_protocol_thread_num", m_conf_mongo_wire_protocol_thread_num);
  m_conf_mongo_wire_protocol_queue_max_num = pgwc->read_conf_int("mongo_parser", "mongo_wire_protocol_queue_num", m_conf_mongo_wire_protocol_queue_max_num);
  m_conf_mongo_wire_protocol_queue_memory_max_size_bytes = pgwc->read_conf_int("mongo_parser", "queue_mongo_wire_protocol_memory_size", m_conf_mongo_wire_protocol_queue_memory_max_size_bytes / (1024ULL * 1024ULL)) * 1024ULL * 1024ULL;

  return true;
}

/**
 * 触发退出信号时处理
 */
void CMongoParser::set_quit_signal(void)
{
  m_quit_signal = 1;

  for (size_t i = 0; i < COUNTOF(m_p_wq); i++)
  {
    if (m_p_wq[i] == NULL)
    {
      continue;
    }
    m_p_wq[i]->set_quit_signal();
  }
}

/**
 * 等待运行结束
 */
void CMongoParser::wait_for_stop(void)
{
  for (size_t i = 0; i < COUNTOF(m_p_wq); i++)
  {
    if (m_p_wq[i] == NULL)
    {
      continue;
    }
    m_p_wq[i]->wait_for_stop();
  }
}

/**
 * 设置过滤规则。
 * @param CFilterRule*rule
 */
void CMongoParser::set_url_filter_rule(CFilterRule *rule)
{
}

/**
 * 设置账号过滤规则
 * @param CFilterRule*rule
 */
void CMongoParser::set_accout_filter_rule(CFilterRule *rule)
{

}

void CMongoParser::set_upload_filter_rule(CFilterRule *client_rule, CFilterRule *server_rule) {}

void CMongoParser::free_worker_queue(CWorkerQueue *p)
{
  if (p == NULL)
  {
    return;
  }
  p->set_quit_signal();
  p->wait_for_stop();

  p->delete_queue();

  p->fini();

  if (m_comm != NULL)
  {
    m_comm->destory_worker_queue(p);
  }
}

void CMongoParser::free_task_worker(CTaskWorker *p)
{
  if (p == NULL)
  {
    return;
  }
  p->release();
}

/**
 * 增加上层协议解析对象。
 * @param CParser *parser
 */
void CMongoParser::add_upstream(CParser *parser)
{
}

/**
 * 清空上层协议解析对象
 */
void CMongoParser::reset_upstream(void)
{
}

/**
 * 推送到上层消息(异步方式, Json序列化数据)
 * @param char *s
 * @param size_t *length
 */
void CMongoParser::push_upstream_msg(char *s, size_t length)
{
  GWLOG_TEST(m_comm, "mongodb test s=%p length=%u\n", s, length);
}

/**
 * 是否使用当前协议解析流数据
 * @param StreamData*
 */
bool CMongoParser::is_parsed(const struct StreamData *) const
{
  return false;
}

/**
 * 克隆会话流数据到队列中使用(预留)
 * @param struct StreamData*
 */
struct StreamData *CMongoParser::clone_stream_data(const struct StreamData *)
{
  return NULL;
}

/**
 *  获取解析http数量(针对http parser) 
 */
uint64_t CMongoParser::get_parser_http_cnt()
{
    return 0;
}

/**
 *  获取解析http成功的数量(针对http parser) 
 */
uint64_t CMongoParser::get_succ_parser_http_cnt()
{
    return 0;
}

/**
 *  获取解析parser的状态数据，以便于进行查看Parser内部状态
 */
void* CMongoParser::get_parser_status()
{
  return NULL;
}

void CMongoParser::print_mongo_stats_callback(void *p)
{
  const CMongoParser *pThis = (const CMongoParser *)p;
  ASSERT(pThis != NULL);
  pThis->print_mongo_stats();
}

void CMongoParser::print_mongo_stats(void) const
{

  /*
  stats mongo                 total      success      failure
  mongo session:                  0
  mongo parser:                   0            0            0
  mongo request parser:           0            0            0
  mongo reponse parser:           0            0            0
  mongo match:                    0            0            0
  mongo request match:            0            0            0
  mongo reponse match:            0            0            0
  */

  printf("\n%-21s %12s %12s %12s\n", "stats mongo ", "total", "success", "failure");
  printf("mongo session:        %12" PRIu64 "\n",
         m_stats_mongo.cnt_session);

  printf("mongo parser:         %12" PRIu64 " %12" PRIu64 " %12" PRIu64 "\n",
         m_stats_mongo.p.cnt_p,
         m_stats_mongo.p.cnt_p_succ,
         m_stats_mongo.p.cnt_p_fail);
  if (unlikely(m_comm->get_verbose()))
  {
    printf("mongo request parser: %12" PRIu64 " %12" PRIu64 " %12" PRIu64 "\n",
           m_stats_mongo.req.cnt_p,
           m_stats_mongo.req.cnt_p_succ,
           m_stats_mongo.req.cnt_p_fail);
    printf("mongo reponse parser: %12" PRIu64 " %12" PRIu64 " %12" PRIu64 "\n",
           m_stats_mongo.rsp.cnt_p,
           m_stats_mongo.rsp.cnt_p_succ,
           m_stats_mongo.rsp.cnt_p_fail);
  }

  printf("mongo match:          %12" PRIu64 " %12" PRIu64 " %12" PRIu64 "\n",
         m_stats_mongo.m.cnt_m,
         m_stats_mongo.m.cnt_m_succ,
         m_stats_mongo.m.cnt_m_fail);
  if (unlikely(m_comm->get_verbose()))
  {
    printf("mongo request match:  %12" PRIu64 " %12" PRIu64 " %12" PRIu64 "\n",
           m_stats_mongo.req_match_rsp.cnt_m,
           m_stats_mongo.req_match_rsp.cnt_m_succ,
           m_stats_mongo.req_match_rsp.cnt_m_fail);
    printf("mongo reponse match:  %12" PRIu64 " %12" PRIu64 " %12" PRIu64 "\n",
           m_stats_mongo.rsp_match_req.cnt_m,
           m_stats_mongo.rsp_match_req.cnt_m_succ,
           m_stats_mongo.rsp_match_req.cnt_m_fail);
  }

  printf("mongo COMMAND:        %12" PRIu64 " %12" PRIu64 " %12" PRIu64 "\n",
         m_stats_mongo.op[STATS_OP_COMMAND].cnt_p,
         m_stats_mongo.op[STATS_OP_COMMAND].cnt_p_succ,
         m_stats_mongo.op[STATS_OP_COMMAND].cnt_p_fail);
  printf("mongo COMMANDREPLY:   %12" PRIu64 " %12" PRIu64 " %12" PRIu64 "\n",
         m_stats_mongo.op[STATS_OP_COMMANDREPLY].cnt_p,
         m_stats_mongo.op[STATS_OP_COMMANDREPLY].cnt_p_succ,
         m_stats_mongo.op[STATS_OP_COMMANDREPLY].cnt_p_fail);
  printf("mongo QUERY:          %12" PRIu64 " %12" PRIu64 " %12" PRIu64 "\n",
         m_stats_mongo.op[STATS_OP_QUERY].cnt_p,
         m_stats_mongo.op[STATS_OP_QUERY].cnt_p_succ,
         m_stats_mongo.op[STATS_OP_QUERY].cnt_p_fail);
  printf("mongo REPLY:          %12" PRIu64 " %12" PRIu64 " %12" PRIu64 "\n",
         m_stats_mongo.op[STATS_OP_REPLY].cnt_p,
         m_stats_mongo.op[STATS_OP_REPLY].cnt_p_succ,
         m_stats_mongo.op[STATS_OP_REPLY].cnt_p_fail);
  if (unlikely(m_comm->get_verbose()))
  {
    printf("mongo MSG:            %12" PRIu64 " %12" PRIu64 " %12" PRIu64 "\n",
           m_stats_mongo.op[STATS_OP_MSG].cnt_p,
           m_stats_mongo.op[STATS_OP_MSG].cnt_p_succ,
           m_stats_mongo.op[STATS_OP_MSG].cnt_p_fail);
    printf("mongo UPDATE:         %12" PRIu64 " %12" PRIu64 " %12" PRIu64 "\n",
           m_stats_mongo.op[STATS_OP_UPDATE].cnt_p,
           m_stats_mongo.op[STATS_OP_UPDATE].cnt_p_succ,
           m_stats_mongo.op[STATS_OP_UPDATE].cnt_p_fail);
    printf("mongo INSERT:         %12" PRIu64 " %12" PRIu64 " %12" PRIu64 "\n",
           m_stats_mongo.op[STATS_OP_INSERT].cnt_p,
           m_stats_mongo.op[STATS_OP_INSERT].cnt_p_succ,
           m_stats_mongo.op[STATS_OP_INSERT].cnt_p_fail);
    printf("mongo DELETE:         %12" PRIu64 " %12" PRIu64 " %12" PRIu64 "\n",
           m_stats_mongo.op[STATS_OP_DELETE].cnt_p,
           m_stats_mongo.op[STATS_OP_DELETE].cnt_p_succ,
           m_stats_mongo.op[STATS_OP_DELETE].cnt_p_fail);
    printf("mongo GET_MORE:       %12" PRIu64 " %12" PRIu64 " %12" PRIu64 "\n",
           m_stats_mongo.op[STATS_OP_GET_MORE].cnt_p,
           m_stats_mongo.op[STATS_OP_GET_MORE].cnt_p_succ,
           m_stats_mongo.op[STATS_OP_GET_MORE].cnt_p_fail);
    printf("mongo KILL_CURSORS:   %12" PRIu64 " %12" PRIu64 " %12" PRIu64 "\n",
           m_stats_mongo.op[STATS_OP_KILL_CURSORS].cnt_p,
           m_stats_mongo.op[STATS_OP_KILL_CURSORS].cnt_p_succ,
           m_stats_mongo.op[STATS_OP_KILL_CURSORS].cnt_p_fail);
    printf("mongo RESERVED:       %12" PRIu64 " %12" PRIu64 " %12" PRIu64 "\n",
           m_stats_mongo.op[STATS_OP_RESERVED].cnt_p,
           m_stats_mongo.op[STATS_OP_RESERVED].cnt_p_succ,
           m_stats_mongo.op[STATS_OP_RESERVED].cnt_p_fail);
  }
}

void CMongoParser::wire_protocol_lock(int no)
{
  if (unlikely(!(m_conf_mongo_wire_protocol_mode == 1)))
  {
    return;
  }

  if (unlikely(!(no >= 0 && no < COUNTOF(m_wire_protocol_lock))))
  {
    no = 0;
  }
  spin_lock(&m_wire_protocol_lock[no]);
}

void CMongoParser::wire_protocol_unlock(int no)
{
  if (unlikely(!(m_conf_mongo_wire_protocol_mode == 1)))
  {
    return;
  }

  if (unlikely(!(no >= 0 && no < COUNTOF(m_wire_protocol_lock))))
  {
    no = 0;
  }
  spin_unlock(&m_wire_protocol_lock[no]);
}

void CMongoParser::send_upload_msg(const char *s, const char* msgtype, std::string pdata)
{
  size_t length = strlen(s);
  // send_upstream(s, length);

  CUpload *p_upload = m_comm->get_upload_from_parser(this, m_conf_upload_name.c_str());
  if (p_upload == NULL)
  {
    GWLOG_INFO(m_comm, "upload null(%s)\n", m_conf_upload_name.c_str());
    cJSON_free((void *)s);
    return;
  }

  UploadMsg *pum = new UploadMsg;
  memset(pum, 0, sizeof(UploadMsg));

  pum->cb = sizeof(UploadMsg);
  pum->destroy_func = free_upload_msg;
  pum->parser = this;
  pum->length = length;
  pum->s = s;
  pum->mem_size = sizeof(UploadMsg) + pum->length;

  int len = (pdata.length()>=sizeof(pum->context))?(sizeof(pum->context)-1):pdata.length();
  memcpy(pum->context,pdata.c_str(),len);

  pum->msgtype = msgtype;

  p_upload->put_msg(pum);
}

void CMongoParser::free_upload_msg(const struct UploadMsg *pum)
{
  CMongoParser *pThis = (CMongoParser *)pum->parser;
  ASSERT(pThis != NULL);

  cJSON_free((void *)pum->s);

  delete pum;
}
