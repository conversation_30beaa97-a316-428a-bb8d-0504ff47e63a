/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#ifndef __MONGO_PARSER_COMMON_H__
#define __MONGO_PARSER_COMMON_H__

#include <string.h>
#include <inttypes.h>

#include <string>
#include <map>
#include <vector>

#include "mongo_parser_op.h"

#define BSON_SAFE_FREE(x)        \
  do                             \
  {                              \
    if (likely(NULL != (x).str)) \
    {                            \
      bson_free((x).str);        \
    }                            \
  } while (0)

typedef struct
{
  size_t json_length;
  char *str;
} bson_json_data_t;

typedef struct
{
  time_t to_last; // 超时时间

  int32 message_id;
  int32 op_code;
  int64_t tm;

  union {

    // OP_QUERY
    struct
    {
      int32 flags;
      const char *fullCollectionName;
      int32 numberToSkip;
      int32 numberToReturn;
      bson_json_data_t bjd_query;
      bson_json_data_t bjd_return_fields;
    } us_op_query;

    // OP_COMMAND
    struct
    {
      const char *database;
      const char *commandName;
      bson_json_data_t bjd_metadata;
      bson_json_data_t bjd_command_args;
      size_t bjd_documents_size;
      bson_json_data_t *bjd_documents;
    } us_op_command;

    // OP_REPLY
    struct
    {
      int32 responseFlags;  // bit vector - see details below
      int64 cursorID;       // cursor id if client needs to do get more's
      int32 startingFrom;   // where in the cursor this reply is starting
      int32 numberReturned; // number of documents in the reply
      size_t bjd_documents_size;
      bson_json_data_t *bjd_documents;
    } us_op_reply;

    // OP_COMMANDREPLY
    struct
    {
      bson_json_data_t bjd_metadata;
      bson_json_data_t bjd_command_reply;
      bson_json_data_t bjd_output_docs;
    } us_op_commandreply;

    // OP_MSG
    struct
    {
      uint32 flagBits; // message flags
      uint8_t kind;
      bson_json_data_t bjd_body;
      uint32 checksum; // optional<uint32> checksum; // optional CRC-32C checksum
    } us_op_msg;
  };

} mongo_session_merge_op_data_t;

typedef struct
{
  std::vector<mongo_session_merge_op_data_t *> vec_req_op_data;
  std::vector<mongo_session_merge_op_data_t *> vec_rsp_op_data;
} mongo_session_merge_data_t;

class mongo_map_session_func
{
public:
  inline bool operator()(const ConnData &cd1, const ConnData &cd2) volatile const
  {
    return memcmp(&cd1, &cd2, sizeof(ConnData)) < 0;
  }
};

// conn,message_id
typedef std::map<ConnData, mongo_session_merge_data_t, mongo_map_session_func> mongo_map_merge_data_t;

typedef struct mongo_half_stream
{
  int dummy;
} mongo_half_stream_t;

typedef struct mongo_stream
{
  mongo_half_stream_t *p_mongo_server; // 服务器侧接收到的数据
  mongo_half_stream_t *p_mongo_client; // 客户端侧接收到的数据

  mongo_map_merge_data_t *map_session_data;

  int is_mongo; // 是否为MONGO连接 =0 未决; =1 MONGO连接; =2 非MONGO连接;


  std::string sessionid;
  std::string username;
  std::string version;
  std::string database_name;

} mongo_stream_t;

#endif // __MONGO_PARSER_COMMON_H__
