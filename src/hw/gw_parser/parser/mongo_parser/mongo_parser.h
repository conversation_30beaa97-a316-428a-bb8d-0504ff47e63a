/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#ifndef __MONGO_PARSER_H__
#define __MONGO_PARSER_H__

#include <inttypes.h>

#include <map>
#include <string>

#include "gw_i_parser.h"

#include "gw_ver.h"

#include "utils.h"

#include "mongo_parser_common.h"

#define MONGOARSER_VER GW_VER_STRING(GW_VER_MAJOR, GW_VER_MINOR, GW_VER_REVISION)

#define MONGOPARSER_WQ_WIRE_PROTOCOL 0
#define MONGOPARSER_WQ_MAX_NUM 2

class CWorkerQueue;
class CTaskWorker;
class CTask<PERSON>orker<PERSON>ireProtocol;
struct mongo_wire_protocol;

class CSession;
struct StreamData;
struct mongo_half_stream;

struct cJSON;

struct MsgHeader;

#define STATS_OP_COMMANDREPLY (0)
#define STATS_OP_COMMAND (1)
#define STATS_OP_REPLY (2)
#define STATS_OP_QUERY (3)
#define STATS_OP_MSG (4)
#define STATS_OP_UPDATE (4 + 1)
#define STATS_OP_INSERT (4 + 2)
#define STATS_OP_RESERVED (4 + 3)
// #define STATS_OP_QUERY (4 + 4)
#define STATS_OP_GET_MORE (4 + 5)
#define STATS_OP_DELETE (4 + 6)
#define STATS_OP_KILL_CURSORS (4 + 7)

#define STATS_OP_MAX_NUM (16)

typedef struct
{
  volatile uint64_t cnt_p; // 解析总数
  // volatile uint64_t cnt_p_bytes; // 字节总数
  volatile uint64_t cnt_p_succ; // 解析成功数量
  volatile uint64_t cnt_p_fail; // 解析失败数量
} stats_mongo_parser_t;

typedef struct
{
  volatile uint64_t cnt_m;      // 解析总数
  volatile uint64_t cnt_m_succ; // 解析成功数量
  volatile uint64_t cnt_m_fail; // 解析失败数量
} stats_mongo_match_t;

typedef struct
{
  // session
  volatile uint64_t cnt_session;       // 数量
  volatile uint64_t cnt_session_bytes; // 字节总数量

  // parser
  volatile stats_mongo_parser_t p;
  // match
  volatile stats_mongo_match_t m;

  // request
  volatile stats_mongo_parser_t req;
  volatile stats_mongo_match_t req_match_rsp; // 匹配respone 成功数量

  // response
  volatile stats_mongo_parser_t rsp;
  volatile stats_mongo_match_t rsp_match_req; // 匹配request

  // op stats
  volatile stats_mongo_parser_t op[STATS_OP_MAX_NUM];

} stats_mongo_t;

class CMongoParser : public CParser
{
public:
  CMongoParser(void);
  virtual ~CMongoParser(void);

public:
  virtual void cache_clean();
  /**
   * 在接收数据时，探测数据流协议。
   * @param CSessionMgt *
   * @param app_stream *
   * @param struct conn *
   */
  virtual bool probe(CSessionMgt *, const app_stream *, const struct conn *);

  /**
   * 在连接关闭时，探测数据流协议。
   * @param CSessionMgt *
   * @param app_stream *
   * @param struct conn *
   */
  virtual bool probe_on_close(CSessionMgt *, const app_stream *, const struct conn *);

  /**
   * 在连接重置时，探测数据流协议。
   * @param CSessionMgt *
   * @param app_stream *
   * @param struct conn *
   */
  virtual bool probe_on_reset(CSessionMgt *, const app_stream *, const struct conn *);

  /**
   * 在接收数据时，解析数据流协议。
   * @param CSessionMgt *
   * @param app_stream *
   * @param struct conn *
   */
  virtual int parse(CSessionMgt *, const app_stream *, const struct conn *);

  virtual int parse_clear(CSessionMgt *, const app_stream *, const struct conn *);

  /**
   * 在连接关闭时，解析数据流协议。
   * @param CSessionMgt *
   * @param app_stream *
   * @param struct conn *
   */
  virtual int parse_on_close(CSessionMgt *, const app_stream *, const struct conn *);

  /**
   * 在连接重置时，解析数据流协议。
   * @param CSessionMgt *
   * @param app_stream *
   * @param struct conn *
   */
  virtual int parse_on_reset(CSessionMgt *, const app_stream *, const struct conn *);

  /**
   * 获取当前流解析出来的数据。
   * @param struct StreamData *psd
   * @param int dir
   * @param int *data_len
   * @param int *offset_out
   */
  virtual const char *get_data(const struct StreamData *psd, int dir, int *data_len, int *offset_out);

  /**
   * 已处理字节数。
   * @param struct StreamData *
   * @param int dir
   * @param int num
   */
  virtual bool discard(struct StreamData *, int dir, int num);

  /**
   * 已处理字节数，同时更新数据。
   * @param struct StreamData *
   * @param int dir
   * @param int num
   */
  virtual bool discard_and_update(struct StreamData *, int dir, int num);

  // /**
  //  * 删除解析对象中在会话管理中的单边数据。
  //  * @param HalfStreamData*
  //  */
  // virtual void del_session_half_stream(HalfStreamData *);

  /**
   * @param StreamData*
   */
  virtual void del_session_stream(StreamData *);

  /**
   * @param SessionMgtData*
   */
  virtual void del_session_param(SessionMgtData *);

  virtual void init();

  virtual void fini();

  virtual void run();

  /**
   * 获取对象名。以-为分隔符，前半部分为类名，后半部分为实例地址尾部分。
   */
  virtual const char *get_name(void) const;

  /**
   * 获取版本号。
   */
  virtual const char *get_version(void) const;

  /**
   * 设置全局公共类对象实例。
   * @param CGwCommon *comm
   */
  virtual void set_gw_common(CGwCommon *comm);

  /**
   * 加载配置参数（Json字符串，支持动态）。
   * @param const char *
   */
  virtual bool load_conf(const char *);

  /**
   * 触发退出信号时处理
   */
  virtual void set_quit_signal(void);

  /**
   * 等待运行结束
   */
  virtual void wait_for_stop(void);

  /**
   * 设置账号过滤规则
   * @param CFilterRule*rule
   */
  virtual void set_url_filter_rule(CFilterRule *rule);

  /**
   * 设置过滤规则。
   * @param CFilterRule*rule
   */
  virtual void set_accout_filter_rule(CFilterRule *rule);

  virtual void set_upload_filter_rule(CFilterRule *client_rule, CFilterRule *server_rule);

  /**
   * 增加上层协议解析对象。
   * @param CParser *parser
   */
  virtual void add_upstream(CParser *parser);

  /**
   * 清空上层协议解析对象
   */
  virtual void reset_upstream(void);

  /**
   * 推送到上层消息(异步方式, Json序列化数据)
   * @param char *s
   * @param size_t *length
   */
  virtual void push_upstream_msg(char *s, size_t length);

  /**
   * 是否使用当前协议解析流数据
   * @param struct StreamData*
   */
  virtual bool is_parsed(const struct StreamData *) const;

  /**
   * 克隆会话流数据到队列中使用(预留)
   * @param struct StreamData*
   */
  virtual struct StreamData *clone_stream_data(const struct StreamData *);

  /**
   *  获取解析http数量(针对http parser) 
   */
  virtual uint64_t get_parser_http_cnt();

  /**
   *  获取解析http成功的数量(针对http parser) 
   */
  virtual uint64_t get_succ_parser_http_cnt();

  /**
   *  获取解析parser的状态数据，以便于进行查看Parser内部状态
   */
  virtual void* get_parser_status();

protected:
  CGwCommon *m_comm;
  volatile int m_quit_signal;
  char m_name[32];

protected:
  mongo_half_stream *get_mongo_from_session(CSession *p_session, int dir, StreamData *&psd_out);
  void del_session_mongo_half_stream(const mongo_half_stream *p_mhs) const;
  void del_session_mongo_map_merge_data(mongo_map_merge_data_t *p_mmmd) const;
  void del_session_merge_op_data(const mongo_session_merge_op_data_t *p_msmod) const;

protected:
  int parse_content(mongo_map_merge_data_t *, int dir, const struct conn *pcon, CSession *p_session, const MsgHeader *);
  int parse_content_deal(mongo_map_merge_data_t *, int dir, const struct conn *pcon, CSession *p_session, const MsgHeader *);
  int parse_content_deal_op_update(mongo_map_merge_data_t *, int dir, const struct conn *pcon, const MsgHeader *);
  int parse_content_deal_op_insert(mongo_map_merge_data_t *, int dir, const struct conn *pcon, const MsgHeader *);
  int parse_content_deal_op_reserved(mongo_map_merge_data_t *, int dir, const struct conn *pcon, const MsgHeader *);
  int parse_content_deal_op_query(mongo_map_merge_data_t *, int dir, const struct conn *pcon, const MsgHeader *);
  int parse_content_deal_op_get_more(mongo_map_merge_data_t *, int dir, const struct conn *pcon, const MsgHeader *);
  int parse_content_deal_op_delete(mongo_map_merge_data_t *, int dir, const struct conn *pcon, const MsgHeader *);
  int parse_content_deal_op_kill_cursors(mongo_map_merge_data_t *, int dir, const struct conn *pcon, const MsgHeader *);
  int parse_content_deal_op_msg(mongo_map_merge_data_t *, int dir, const struct conn *pcon, const MsgHeader *);
  int parse_content_deal_op_command(mongo_map_merge_data_t *, int dir, const struct conn *pcon, const MsgHeader *);
  int parse_content_deal_op_reply(mongo_map_merge_data_t *, int dir, const struct conn *pcon, const MsgHeader *);
  int parse_content_deal_op_commandreply(mongo_map_merge_data_t *, int dir, const struct conn *pcon, const MsgHeader *);
  // 合并会话
  int parse_content_deal_merge(mongo_map_merge_data_t *, int dir, const struct conn *pcon, CSession *p_session, const MsgHeader *);
  int parse_content_deal_merge_msg(const struct conn *pcon, CSession *p_session, const mongo_session_merge_op_data_t *req, const mongo_session_merge_op_data_t *rsp);
  cJSON *parse_content_deal_merge_msg_op_data(const mongo_session_merge_op_data_t *);

protected:
  inline CWorkerQueue *get_wq_wire_protocol(void) const
  {
    return m_p_wq[MONGOPARSER_WQ_WIRE_PROTOCOL];
  }
  CWorkerQueue *new_wq_wire_protocol(void);

protected:
  CWorkerQueue *m_p_wq[MONGOPARSER_WQ_MAX_NUM];
  CTaskWorker *m_p_tw[MONGOPARSER_WQ_MAX_NUM];

  void free_task_worker(CTaskWorker *p);
  void free_worker_queue(CWorkerQueue *p);

  friend class CTaskWorkerWireProtocol;

protected:
  // 读取int8数据并移动指针
  bool read_uint8_data_and_move(const unsigned char *&p, const unsigned char *p_end, uint8_t &data) const volatile;
  // 读取uint32数据并移动指针
  bool read_uint32_data_and_move(const unsigned char *&p, const unsigned char *p_end, uint32_t &data) const volatile;

  // 读取int32数据并移动指针
  bool read_int32_data_and_move(const unsigned char *&p, const unsigned char *p_end, int32_t &data) const volatile;
  // 读取int32数据
  bool read_int32_data(const unsigned char *&p, const unsigned char *p_end, int32_t &data) const volatile;

  // 读取int64数据并移动指针
  bool read_int64_data_and_move(const unsigned char *&p, const unsigned char *p_end, int64_t &data) const volatile;

  // 读取cstring数据并移动指针
  bool read_cstring_data_and_move(const unsigned char *&p, const unsigned char *p_end, const char *&s) const volatile;

  // 读取Document数据并移动指针
  bool read_document_data_and_move(const unsigned char *&p, const unsigned char *p_end, int &document_length, bson_json_data_t &bjd) const volatile;

protected:
  stats_mongo_t m_stats_mongo; // 状态
  static void print_mongo_stats_callback(void *p);
  void print_mongo_stats(void) const;

protected:
  bool send_mongo_wire_protocol_content(int dir, const struct conn *pcon, CSession *p_session,  const MsgHeader *pmh);
  int worker_routine_mongo_wire_protocol_inner(const void *p);
  void free_mongo_wire_protocol_inner(const mongo_wire_protocol *) volatile const;

protected:
  void send_upload_msg(const char *s, const char* msgtype, std::string pdata);
  static void free_upload_msg(const struct UploadMsg *); // this包含在结构体中

protected:
  std::string m_conf_upload_name;

  int m_conf_mongo_wire_protocol_mode;
  int m_conf_mongo_wire_protocol_thread_num;
  int m_conf_mongo_wire_protocol_queue_max_num;
  uint64_t m_conf_mongo_wire_protocol_queue_memory_max_size_bytes;

protected:
  spinlock_t SPIN_DECL(m_wire_protocol_lock[16]);
  void wire_protocol_lock(int = 0);
  void wire_protocol_unlock(int = 0);

private:
  mongo_map_merge_data_t m_map_session;
  // std::map<std::string, > m_map_session;

private:
  mongo_session_merge_op_data_t *new_map_merge_op_data(mongo_map_merge_data_t *, int dir, const struct conn *pcon, const MsgHeader *);
  bool add_map_merge_op_data(mongo_map_merge_data_t *, int dir, const struct conn *pcon, const MsgHeader *, mongo_session_merge_op_data_t *&);

private:  
  int m_event_prior;
  int m_content_prior;
};

#endif // __MONGO_PARSER_H__
