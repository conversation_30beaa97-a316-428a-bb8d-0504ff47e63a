/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <memory.h>
#include <inttypes.h>

#include "mongo_parser.h"
#include "mongo_parser_common.h"

#include "utils.h"

#include "gw_common.h"
#include "gw_logger.h"
#include "gw_config.h"
#include "session_mgt.h"
#include "session.h"

#include "worker_queue.h"
#include "task_worker.h"

#include <openssl/md5.h>

/**
 * 在接收数据时，探测数据流协议。
 * @param CSessionMgt *
 * @param app_stream_t *
 * @param struct conn *
 */
bool CMongoParser::probe(CSessionMgt *psm, const app_stream_t *a_app, const struct conn *pcon)
{
  CSession *p_session = psm->find_session(pcon);
  CSession *p_session_first = p_session;
  unsigned char *p;
  StreamData *psd;
  mongo_stream_t *p_ms = NULL;
  mongo_half_stream_t *mhs;
  const char *data;
  int dir = a_app->dir;
  int data_len;
  int offset_out;

  // GWLOG_TEST(m_comm, "mongo\n");
  if (p_session == NULL)
  {
    p_session = psm->new_session(pcon);
    if (p_session == NULL)
    {
      return false;
    }
  }

  mhs = get_mongo_from_session(p_session, dir, psd);
  if (mhs == NULL)
  {
    return false;
  }
  p_ms = psd->p_mongo_stream;

  if (p_ms->is_mongo == 1)
  {
    return true;
  }
  else if (p_ms->is_mongo == 2)
  {
    return false;
  }

  data = p_session->get_data(this, dir, &data_len, &offset_out);
  p = (unsigned char *)data;

  //printf("data_len=%d\n", data_len);
  if (!(p != NULL && data_len > 32))
  {
    // 等待新的数据
    p_session->discard(this, dir, 0);
    return false;
  }

  if (unlikely(0))
  {
    char buf[256] = {0};
    for (size_t i = 0; i < MIN(24, data_len); i++)
    {
      snprintf(buf + strlen(buf), COUNTOF(buf) - 1 - strlen(buf), " %.2X", p[i]);
    }
    GWLOG_TEST(m_comm, "probe buf=%s\n", buf);
  }

  p_ms->is_mongo = 2; // 不是MONGO连接
  const msg_header_t *pmh = (const msg_header_t *)data;

  // GWLOG_TEST(m_comm, "msg header messageLength=%d\n", pmh->messageLength);
  // GWLOG_TEST(m_comm, "msg header requestID=%x\n", pmh->requestID);
  // GWLOG_TEST(m_comm, "msg header responseTo=%x\n", pmh->responseTo);
  // GWLOG_TEST(m_comm, "msg header opCode=%d\n", pmh->opCode);
  // GWLOG_TEST(m_comm, "data_len=%d msg header messageLength=%d requestID=%x responseTo=%x opCode=%d\n", data_len, pmh->messageLength, pmh->requestID, pmh->responseTo, pmh->opCode);

  if (pmh->messageLength > 0 && pmh->messageLength < 0x20000)
  {
    switch (pmh->opCode)
    {
    default:
      break;

    // for request
    case OP_UPDATE:
    case OP_INSERT:
    case OP_RESERVED:
    case OP_QUERY:
    case OP_GET_MORE:
    case OP_DELETE:
    case OP_KILL_CURSORS:
    case OP_COMMAND:
    case OP_MSG:

    // for response
    case OP_REPLY:
    case OP_COMMANDREPLY:
      p_ms->is_mongo = 1; // 是MONGO连接
      break;
    }
  }

  if (p_ms->is_mongo == 1)
  {
    return true;
  }

  return false;
}

/**
 * 在连接关闭时，探测数据流协议。
 * @param CSessionMgt *
 * @param app_stream_t *
 * @param struct conn *
 */
bool CMongoParser::probe_on_close(CSessionMgt *psm, const app_stream_t *a_app, const struct conn *pcon)
{
  bool res = true;

  res = probe(psm, a_app, pcon);
  return res;
}

/**
 * 在连接重置时，探测数据流协议。
 * @param CSessionMgt *
 * @param app_stream_t *
 * @param struct conn *
 */
bool CMongoParser::probe_on_reset(CSessionMgt *psm, const app_stream_t *a_app, const struct conn *pcon)
{
  return probe_on_close(psm, a_app, pcon);
}

mongo_half_stream_t *CMongoParser::get_mongo_from_session(CSession *p_session, int dir, StreamData *&psd_out)
{
  mongo_stream_t *p_ms;
  mongo_half_stream_t *p_mhs;
  StreamData *psd;

  if ((psd = p_session->get_stream_data_from_parser(this)) == NULL)
  {
    // 创建 StreamData
    psd = new StreamData();
    if (!p_session->set_parser(this, psd))
    {
      delete psd;
      return NULL;
    }
    psd->p_mongo_stream = new mongo_stream_t();


    unsigned char id[16];
    char id_str[40] = {0};
    std::string uni_id = "";
    const ConnData* pcon = p_session->get_conn();
    uni_id += std::to_string(pcon->client.port);
    uni_id += std::to_string(pcon->client.ipv4);
    uni_id += std::to_string(pcon->server.port);
    uni_id += std::to_string(pcon->server.ipv4);
    uni_id += std::to_string(m_comm->gw_time());
    MD5((const unsigned char*)uni_id.c_str(),uni_id.length(),id);
    for(int i=0;i<16;++i){
        sprintf(id_str+(i*2),"%02x",id[i]);
    }

    psd->p_mongo_stream->sessionid = std::string(id_str);
    // SessionMgtData *psmd = p_session->get_session_mgt()->get_session_data_from_parser(this);
    // if (psmd == NULL || psmd->p_ssl_data == NULL)
    // {
    //   psmd = new SessionMgtData;
    //   psmd->p_ssl_data = new ssl_session_mgt_t;

    //   init_ssl_session_param(&psmd->p_ssl_data->ssp);

    //   if (!p_session->get_session_mgt()->set_parser_data(this, psmd))
    //   {
    //     free_ssl_session_param(&psmd->p_ssl_data->ssp);
    //     delete psmd->p_ssl_data;
    //     delete psmd;
    //   }
    // }

    // psmd = p_session->get_session_mgt()->get_session_data_from_parser(this);
    // // GWLOG_TEST(m_comm, "psmd=%p psmd->p_ssl_data=%p\n", psmd, psmd->p_ssl_data);
    // if (psmd != NULL || psmd->p_ssl_data != NULL)
    // {
    //   psd->p_mongo_stream->pssp = &psmd->p_ssl_data->ssp;
    // }

    psd->p_mongo_stream->map_session_data = new mongo_map_merge_data_t();
  }
  p_ms = psd->p_mongo_stream;
  // GWLOG_TEST(m_comm, "psd=%p p_ms=%p\n", psd, p_ms);

  if (dir)
  {
    p_mhs = p_ms->p_mongo_client;
  }
  else
  {
    p_mhs = p_ms->p_mongo_server;
  }
  if (p_mhs != NULL)
  {
    goto end;
  }

  p_mhs = (mongo_half_stream_t *)malloc(sizeof(mongo_half_stream_t));
  if (unlikely(p_mhs == NULL))
  {
    // out of memory
    // no mem
    return NULL;
  }

  memset(p_mhs, 0, sizeof(mongo_half_stream_t));
  if (dir)
  {
    p_ms->p_mongo_client = p_mhs;
  }
  else
  {
    p_ms->p_mongo_server = p_mhs;
  }

end:
  p_session->update_time(); // 更新当前会话时间
  psd_out = psd;

  return p_mhs;
}

void CMongoParser::del_session_mongo_half_stream(const mongo_half_stream_t *p_mhs) const
{
  if (p_mhs == NULL)
  {
    return;
  }

  return;
}
