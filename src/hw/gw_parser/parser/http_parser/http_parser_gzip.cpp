/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <string.h>
#include <memory.h>
#include <unistd.h>
#include <arpa/inet.h>

#include "http_parser.h"
#include "http_parser_common.h"
#include "http_filter.h"

#include "proto_http_parser.h"
#include "utils.h"

#include "gw_common.h"
#include "gw_logger.h"
#include "gw_config.h"
#include "session_mgt.h"
#include "session.h"

#include "worker_queue.h"
#include "http_parser_gzip_task_worker.h"
#include "gw_stats.h"

#include "cJSON.h"
#include "simple_json.h"
#include "display_stats_define.h"
#include "get_file_type.h"

#define BODY_STRUCT_SIZE (128)
#define FILE_EXTENSION_LEN (256)

void CHttpParser::free_upload_gzip_req_info(http_req_info_t *p_gzip_req_info)
{
  BSTR_SAFE_FREE(p_gzip_req_info->p_body);
  BSTR_SAFE_FREE(p_gzip_req_info->p_header);
  BSTR_SAFE_FREE(p_gzip_req_info->p_req_header);
}

void CHttpParser::free_upload_gzip_rsp_info(http_rsp_info_t *p_gzip_rsp_info)
{
  BSTR_SAFE_FREE(p_gzip_rsp_info->p_header);
  BSTR_SAFE_FREE(p_gzip_rsp_info->p_rsp_header);
  SAFE_FREE(p_gzip_rsp_info->set_cookies_list);
}

void CHttpParser::free_upload_gzip_file_info(http_file_info_t *p_gzip_file_info)
{
  SAFE_FREE(p_gzip_file_info->p_file_type);
  SAFE_FREE(p_gzip_file_info->p_file_name);
}

void CHttpParser::free_upload_gzip_info(upload_http_info_t *p_upload_gzip_info)
{
  if (NULL == p_upload_gzip_info)
  {
    return ;
  }

  free_upload_gzip_req_info(&(p_upload_gzip_info->http_req_info));
  free_upload_gzip_rsp_info(&(p_upload_gzip_info->http_rsp_info));
  free_upload_gzip_file_info(&(p_upload_gzip_info->http_file_info));

  free(p_upload_gzip_info);
}

CWorkerQueue *CHttpParser::new_wq_http_gzip(void)
{
  m_p_wq[HTTPPARSER_WQ_HTTP_GZIP] = m_comm->create_worker_queue();
  CWorkerQueue *pwq = get_wq_http_gzip();
  if (pwq == NULL)
  {
    return NULL;
  }

  CTaskWorkerGzip *ptw = new CTaskWorkerGzip();
  ptw->set_parser(this);
  ptw->set_wq(pwq);
  m_p_tw[HTTPPARSER_WQ_HTTP_GZIP] = ptw;

  pwq->set_gw_common(m_comm);
  pwq->set_watchdog(m_comm->get_watchdog());
  pwq->set_task_worker(ptw);

  pwq->set_queue_num_and_bytes(m_conf_http_gzip_queue_max_num, m_conf_http_gzip_queue_memory_max_size_bytes);
  pwq->set_queue_name(HTTP_GZIP_QUEUE);
  pwq->init();
  pwq->create_queue();
  pwq->adjust_worker_thread_num(m_conf_http_gzip_thread_num);

  m_comm->get_gw_stats()->set_task(pwq->get_queue_name(), pwq, 51);
  m_comm->get_gw_stats()->set_qps(HTTP_GZIP_QPS, &pwq->get_stats_task_data()->cnt);
  m_comm->get_gw_stats()->set_mem_stat(pwq->get_queue_name(), &pwq->get_queue_mem_size(), &pwq->get_queue_max_mem_size());

  return pwq;
}

CWorkerQueue *CHttpParser::new_wq_http_gzip_parser(void)
{
  m_p_wq[HTTPPARSER_WQ_HTTP_GZIP_PARSER] = m_comm->create_worker_queue();
  CWorkerQueue *pwq = get_wq_http_gzip_parser();
  if (NULL == pwq)
  {
    return NULL;
  }
  CGwStats *pgws = m_comm->get_gw_stats();
  ASSERT(pgws != NULL);

  CTaskWorkerGzipParser *ptw = new CTaskWorkerGzipParser();
  ptw->set_parser(this);
  ptw->set_wq(pwq);
  m_p_tw[HTTPPARSER_WQ_HTTP_GZIP_PARSER] = ptw;

  pwq->set_gw_common(m_comm);
  pwq->set_watchdog(m_comm->get_watchdog());
  pwq->set_task_worker(ptw);

  pwq->set_queue_num_and_bytes(m_conf_http_gzip_parser_queue_max_num, m_conf_http_gzip_parser_queue_memory_max_size_bytes);
  pwq->set_queue_name(HTTP_GZIP_PARSER_QUEUE);
  pwq->init();
  pwq->create_queue();
  pwq->adjust_worker_thread_num(m_conf_http_gzip_parser_thread_num);

  pgws->set_task(pwq->get_queue_name(), pwq, 51);
  pgws->set_qps(HTTP_GZIP_PARSER_QPS, &pwq->get_stats_task_data()->cnt);
  pgws->set_mem_stat(pwq->get_queue_name(), &pwq->get_queue_mem_size(), &pwq->get_queue_max_mem_size());

  return pwq;
}

void CHttpParser::free_http_gzip_inner(const http_gzip_t *p)
{
  bstr_del_string(p->s);
  if (!m_conf_http_gzip_parser_mode && p->p_upload_http_info && p->p_upload_http_info->is_file_event != 1)
  {
    free_upload_gzip_info(p->p_upload_http_info);
  }
}

void CHttpParser::http_cb_http_gzip(upload_http_info_t *p_upload_http_info, http_parser_ext_data_t *phped, int unkown_rule, int uploadflag, bool analyze)
{
  //int i_ret = 0;
  size_t mem_size = sizeof(http_gzip_t)
                  + phped->length
                  + sizeof(upload_http_info_t)
                  + sizeof(keyvalue_info_t) * 2
                  + p_upload_http_info->http_req_info.header_length
                  + p_upload_http_info->http_req_info.body.length
                  + p_upload_http_info->http_rsp_info.header_length;

  http_gzip_t *phg = new http_gzip_t;

  phg->length = phped->length;           // gzip uncompress data length
  phg->s = phped->pstr;                  // gzip uncompress data
  phg->deep = phped->gzip_deep;          // gzip deep
  phg->unkown_rule = unkown_rule; // unkown rule
  phg->upload_flag = uploadflag;
  phg->p_upload_http_info = (upload_http_info_t*)malloc(sizeof(upload_http_info_t));
  memset(phg->p_upload_http_info, 0, sizeof(upload_http_info_t));
  memcpy(phg->p_upload_http_info, p_upload_http_info, sizeof(upload_http_info_t));
  phg->log_to_analyze=analyze;

  if (!get_wq_http_gzip()->queue_put_data(phg, mem_size))
  {
    goto failed;
  }

  return;

  failed:
    bstr_del_string(phg->s);
    free_upload_gzip_info(phg->p_upload_http_info);
    delete phg;
    return ;
}

void CHttpParser::free_http_gzip_parser_data(const http_gzip_parser_t *phgp)
{

  if(!phgp)
  {
    return;
  }

  // 检查 p_upload_http_info 是否为 NULL（可能已在 http_gzip_upload_msg 中被释放）
  if (phgp->p_upload_http_info != NULL)
  {
    if (phgp->p_upload_http_info->is_file_event == 1)
    {
        //TODO 只释放new出来的结构，解压的数据不释放
        delete phgp;
        return;
    }

    if (phgp->p_upload_http_info->is_file_event != 1)
    {
      free_upload_gzip_info(phgp->p_upload_http_info);
    }
  }
  //SAFE_FREE(phgp->s);

  SAFE_FREE(phgp->s);

  delete phgp;
}

void CHttpParser::http_cb_http_gzip_parser(http_gzip_t *phg, char *s, size_t length)
{
  char *p_gzip_data = NULL;
  http_gzip_parser_t *phgp = NULL;
  size_t mem_size = 0;

  if (length == 0)
  {
    if (phg->length == 0)
    {
        goto end;
    }
    else
    {
        s = phg->s;
        length = phg->length;
    }
  }

  p_gzip_data = (char *)malloc(length + 1);
  if (NULL == p_gzip_data)
  {
    goto end;
  }

  memcpy(p_gzip_data, s, length);
  p_gzip_data[length] = '\0';
  mem_size = sizeof(http_gzip_parser_t) + length;
  phgp = new http_gzip_parser_t();
  phgp->p_upload_http_info = phg->p_upload_http_info;
  phgp->unknown_rule = phg->unkown_rule;
  phgp->upload_flag = phg->upload_flag;
  phgp->log_to_analyze = phg->log_to_analyze;
  phgp->length = length;
  phgp->s = p_gzip_data;

  if (!get_wq_http_gzip_parser()->queue_put_data(phgp, mem_size))
  {
    free_http_gzip_parser_data(phgp);
  }

  return;

end:
  get_wq_http_gzip_parser()->status_count(WQ_QUEUE_FAIL, 1);   /* 记录入队列失败 */
  SAFE_FREE(p_gzip_data);
  free_upload_gzip_info(phg->p_upload_http_info);

  return;
}

int CHttpParser::worker_routine_http_gzip_inner(thread_local_gzip_data_t *ptlgd, const void *p, z_streamp p_zs)
{
  http_gzip_t *phg = (http_gzip_t *)p;
  const size_t buf_maxsize = ptlgd->buf_maxsize;
  const size_t body_buf_size = ptlgd->body_buf_size;
  size_t buf_size;
  char *body = ptlgd->body;
  char *body2 = ptlgd->body2;

  if (!(phg->s != NULL && (int)phg->length >= 0 && phg->p_upload_http_info != NULL))
  {
    /* 避免内存泄漏 */
    if (m_conf_http_gzip_parser_mode)
    {
      free_upload_gzip_info(phg->p_upload_http_info);
    }
    return 0;
  }

  //printf ("phg->length=%d\n", phg->length);
  char *buf = phg->s;
  buf_size = 0;

  // 执行解压操作并将结果放到 事件消息上传
  if (phg->length > 0)
  {
    int gzip_st;
    int gzip_deep = phg->deep;
    char *buf_src = phg->s;
    size_t len_src = phg->length;
    char *buf_dst = body;
    char *pp;

    // gzip 支持嵌套解压，解压嵌套的深度
    while (gzip_deep >= 1)
    {
      gzip_st = http_gzip_routine(gzip_deep, &buf_size, buf_dst, body_buf_size, buf_maxsize, buf_src, len_src, p_zs);
      buf = buf_dst;
      if (gzip_st)
      {
        if (buf_size > 0)
        {
          // 截断
          if (gzip_deep == 1)
          {
            // 最后一层，仍使用解压后的数据
            break;
          }
        }
        else
        {
          // 异常退出
          buf_size = 0;
          break;
        }
      }

      if (gzip_deep == 1)
      {
        // 最后一层无需交换
        break;
      }
      if (buf_src == phg->s)
      {
        buf_src = body;
        buf_dst = body2;
      }
      else
      {
        pp = buf_src;
        buf_src = buf_dst;
        buf_dst = pp;
      }
      len_src = buf_size;

      gzip_deep--;
    }
  }
  else if (phg->s != NULL && phg->length == 0)
  {
    get_wq_http_gzip()->status_count(WQ_STATUS_FAIL, 1);
  }

  //printf ("buf_size = %d\n", buf_size);
  if ((int)buf_size >= 0)
  {
    if (m_conf_http_gzip_parser_mode)
    {
      http_cb_http_gzip_parser(phg, buf, buf_size);
    }
    else
    {
      http_gzip_upload_msg(phg->p_upload_http_info, phg->upload_flag, phg->unkown_rule, phg->log_to_analyze, buf, buf_size);
    }
  }
  return 1;
}

int CHttpParser::http_gzip_routine(int gzip_deep, size_t *p_buf_size_out, char *body, size_t body_buf_size, const size_t buf_maxsize, char *pstr, size_t length, z_streamp p_zs)
{
  size_t maxsize;
  size_t buf_size = 0;
  int ret = 0;

  maxsize = MIN(buf_maxsize, (size_t)m_conf_http_response_body_max_size);
  buf_size = gzip_uncompress(pstr, length, body, body_buf_size, p_zs);

  if ((int)buf_size < 0)
  {
    // 解压失败
    get_wq_http_gzip()->status_count(WQ_STATUS_FAIL2, 1);
    get_wq_http_gzip()->status_count(WQ_STATUS_FAIL, 1);
    //body[0] = '\0';
    buf_size = 0;
    ret = 1;
  }
  else if (buf_size > maxsize)
  {
    // 解压数据过长
    get_wq_http_gzip()->status_count(WQ_STATUS_FAIL4, 1);
    get_wq_http_gzip()->status_count(WQ_STATUS_FAIL, 1);
    //body[maxsize] = '\0';
    /* 黄浩修改，返回实际解压的数据长度，不一定是完全解压之后的长度，最大能解压到100MB */
    //buf_size = maxsize;
    ret = 2;
  }
  else
  {
    if (gzip_deep == 1)
    {
      // 解压到最后一层，可认为解成功
      get_wq_http_gzip()->status_count(WQ_STATUS_SUCC, 1);
    }
  }

  *p_buf_size_out = buf_size;

  return ret;
}

int CHttpParser::http_gzip_parser_routine(const void* p)
{
  http_gzip_parser_t *phgp = (http_gzip_parser_t*)p;

  int result = http_gzip_upload_msg(phgp->p_upload_http_info, phgp->upload_flag, phgp->unknown_rule, phgp->log_to_analyze, phgp->s, phgp->length);

  // 如果 http_gzip_upload_msg 返回 1，表示内存已被释放，需要标记以避免重复释放
  if (result == 1)
  {
    // 将指针设为 NULL，避免在 free_http_gzip_parser_data 中重复释放
    ((http_gzip_parser_t*)p)->p_upload_http_info = NULL;
  }

  return 0;  // 总是返回成功，因为内存管理问题已经处理
}

int  CHttpParser::http_gzip_upload_msg(upload_http_info_t *p_upload_gzip_info, int upload_flag, int unknown_rule, bool log_to_analyze, char *body, size_t buf_size)
{
  char *ss = NULL;
  size_t ss_len = 0;
  const char *p_req_url = NULL;                       /* url */
  char *p_content_type = NULL;                 /* response content-type内容 */
  char *p_content_disposition = NULL;
  char *p_content_range = NULL;
  char *p_file_name = NULL;
  char a_body_struct[BODY_STRUCT_SIZE];        /* 用于获取文件类型，取文件内容的前127字节，解析文件的头结构 */
  memset(a_body_struct, 0, BODY_STRUCT_SIZE);
  int i_body_len = 0;
  i_body_len = MIN(BODY_STRUCT_SIZE, buf_size);
  memcpy(a_body_struct, body, i_body_len);
  size_t file_len = 0;

  p_req_url = p_upload_gzip_info->http_req_info.full_url;
    /* 获取request url字段，response content-type、content-disposition字段 */
  get_http_gzip_info(&(p_upload_gzip_info->http_rsp_info), p_req_url, &p_content_type, &p_content_disposition, &p_content_range, &file_len, &p_file_name);
  // upload_flag默认为0
  if (p_upload_gzip_info->is_file_event != 1)
  {
    file_len = buf_size;

    bstring st_body;
    st_body.bstr = body;
    st_body.length = buf_size;
    char mime[256] = {0};

    if (p_content_type && strlen(p_content_type) > 0)
    {
        memcpy(mime, p_content_type, MIN(strlen(p_content_type),255));
    }
    probe_file_event(&st_body, mime, p_content_disposition, p_upload_gzip_info);

    if (p_upload_gzip_info->is_file_event == 1)
    {
        p_upload_gzip_info->http_file_info.p_file_direction = "download";
        p_upload_gzip_info->http_file_info.file_len = st_body.length;
        p_upload_gzip_info->rw_flag = 0; // RW_FLAG_READ
        get_http_file_type(&p_upload_gzip_info->http_file_info, mime);
        get_file_name(&p_upload_gzip_info->http_file_info, p_content_disposition, p_upload_gzip_info->http_req_info.full_url);
    }

    if (p_upload_gzip_info->http_file_info.file_len >= (size_t)m_conf_http_response_body_max_size)
    {
      p_upload_gzip_info->http_file_info.p_file_warn = g_p_file_warn;
    }

    free_param(p_content_type, p_content_disposition, p_content_range, p_file_name);
  }


  p_upload_gzip_info->http_rsp_info.body.p_value = body;
  //p_upload_gzip_info->http_rsp_info.body.length = MIN(buf_size, (size_t)m_conf_http_response_body_max_size);
  p_upload_gzip_info->http_rsp_info.body.length = buf_size;

  char s[1024] = "\0";

  // 保存原始的 is_file_event 和 found_gzip 状态，用于判断 simple_json_encode 是否会释放内存
  int original_is_file_event = p_upload_gzip_info->is_file_event;
  bool original_found_gzip = p_upload_gzip_info->found_gzip;

  ss = simple_json_encode(*p_upload_gzip_info, unknown_rule, log_to_analyze, &ss_len, s);
  if (ss != NULL)
  {
    if (1 == original_is_file_event)
    {
      unknown_rule = 2;
    }

    http_cb_upload_msg(ss, unknown_rule, log_to_analyze, ss_len,s);
  }

  // 检查 simple_json_encode 是否释放了内存
  // 如果 is_file_event == 2 且 !found_gzip，则 simple_json_encode 会调用 free_upload_http_info
  // 这种情况下，我们需要标记内存已被释放，避免在 free_http_gzip_parser_data 中重复释放
  if (original_is_file_event == 2 && !original_found_gzip)
  {
    // 内存已在 simple_json_encode 中被释放，返回特殊值表示不需要再次释放
    return 1;
  }

  return 0 ;
}
