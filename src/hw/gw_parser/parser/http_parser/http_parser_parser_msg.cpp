/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <string.h>
#include <strings.h>
#include <memory.h>
#include <unistd.h>
#include <inttypes.h>
#include <arpa/inet.h>

#include "http_parser.h"
#include "http_parser_common.h"
#include "http_filter.h"

#include "utils.h"

#include "gw_common.h"
#include "gw_logger.h"
#include "gw_config.h"
#include "session_mgt.h"
#include "session.h"

#include "worker_queue.h"
#include "gw_stats.h"

#include "cJSON.h"
#include "simple_json.h"

#include "gw_i_upload.h"

#include "http_parser_msg_task_worker.h"
#include "urlfilter_rule.h"
#include "accoutfilter_rule.h"
#include "display_stats_define.h"
#include "get_file_type.h"

#include "pp.h"

#define REQUEST_CT_FORM "multipart/form-data"

#define CONTENT_TYPE_MAX_LENGTH 256
#define CONTENT_DISPOSITION_MAX_LENGTH 1024
#define FILENAME_MAX_LENGTH 1024
const char *g_p_file_warn = "large";
const char *g_p_file_upload = "upload";
const char *g_p_file_download = "download";

static const char msg_unknown_rule_type[] = "http_unknown_rule";
static const char msg_ruled_type[] = "http_ruled";
static const char msg_drop_file_type[] = "http_drop_file";
static const char msg_file_type[] = "http_file";

thread_local uint64_t g_u64_http_upload_ms = 0;
thread_local uint32_t g_u32_http_upload_index = 0;
thread_local magic_t g_magic = NULL;

//有些文件类型是无法通过文件头来确定的，比如tar文件，还要进行content-type判断
std::map<std::string, std::string> magic_upload = {
    {"255044462d312e", "pdf"},
    {"7b5c727466", "rtf"},
    {"d0cf11e0a1b11ae1", "doc,xls,ppt"},
    {"504b0304", "docx,xlsx,pptx,zip,jar,doc,apk"},
    {"1f8b", "gz"},
    {"52617221", "rar"},
    {"425a68", "bz2"},
    {"fd377a585a00", "xz"},
};

//有些文件类型是无法通过文件头来确定的，比如svg，wmv，mp3，还要进行content-type判断
std::map<std::string, std::string> magic_not_upload = {
    {"ffd8ff", "jpg,jpe,jpeg"},
    {"89504e470d0a1a0a", "png"},
    {"47494638", "gif"},
    {"424d", "bmp"},
    {"464c56", "flv"},
    {"41564920", "avi"},
    {"0000001c6674797069736f6d30303030", "mp4"},
    {"0000001466747970717420", "mov"},
    {"664c6143", "flac"},
};

std::map<std::string, std::string> content_type_upload_map = {
    {"application/msword", "doc"},
    {"application/pdf", "pdf"},
    {"application/vnd.adobe.pdx", "pdx"},
    {"application/vnd.ms-powerpoint", "ppt"},
    {"application/x-ppt", "ppt"},
    {"text/vnd.rn-realtext", "rt"},
    {"application/x-rtf", "rtf"},
    {"application/vnd.ms-excel", "xls"},
    {"application/x-xls", "xls"},
    {"application/vnd.openxmlformats-officedocument.wordprocessingml.document", "docx"},
    {"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "xlsx"},
    {"application/vnd.openxmlformats-officedocument.presentationml.presentation", "pptx"},
    {"application/x-tar", "tar"},
    {"application/x-gzip", "gz"},
    {"application/x-bzip2", "bz2"},
    {"application/x-xz", "xz"},
    {"application/zip", "zip"},
    {"application/x-rar", "rar"},
    {"application/java-archive", "jar"},
    {"text/plain", "txt"},
    {"text/csv", "csv"},
};

std::map<std::string, std::string> content_type_not_upload_map = {
    {"audio/mpeg", "mp3"},
    {"image/jpeg","jpeg"},
    {"image/png","png"},
    {"image/gif","gif"},
    {"image/bmp","bmp"},
    {"image/svg+xml","svg"},
    {"video/x-flv","flv"},
    {"video/x-msvideo","avi"},
    {"video/mp4","mp4"},
    {"video/quicktime","mov"},
    {"video/x-ms-wmv","wmv"},
    {"audio/flac","flac"},
};

static void add_header_array_to_json(keyvalue_info_t **pp_header_info, int *p_header_info_num, http_header_item_t *phi, ssize_t header_item_num)
{
  http_header_item_t *header = phi;
  ssize_t k = header_item_num;
  ssize_t i = 0;
  ssize_t j = 0;

  keyvalue_info_t *p_header_info = NULL;
  p_header_info = (keyvalue_info_t *)malloc(sizeof(keyvalue_info_t) * k);
  if (NULL == p_header_info)
  {
    return ;
  }
  memset(p_header_info, 0, sizeof(keyvalue_info_t) * k);

  while (k > 0)
  {
    int duplicate_flag = 0;
    for (j = 0; j < (header_item_num - k); j++)
    {
      if (p_header_info[j].key.length == header->field.length && !strncasecmp(p_header_info[j].key.p_value, header->field.bstr, header->field.length))
      {
        p_header_info[j].value.p_value = header->value.bstr;
        p_header_info[j].value.length = header->value.length;
        duplicate_flag = 1;
        break;
      }
    }
    if (!duplicate_flag)
    {
      p_header_info[i].key.p_value = header->field.bstr;
      p_header_info[i].key.length = header->field.length;
      p_header_info[i].value.p_value = header->value.bstr;
      p_header_info[i].value.length = header->value.length;
      i++;
    }
    k--;
    header++;
  }
  *p_header_info_num = i;
  *pp_header_info = p_header_info;
}

static void get_addr_str(u_int addr, char *buf, size_t size)
{
  buf[size - 1] = '\0';
  unsigned char *bytes = (unsigned char *)&addr;
  size_t offset = 0;
  int i = 0;
  for (i = 0; i < 4; i++)
  {
    ntos(bytes[i], buf + offset, &offset);
    if (i != 3)
    {
      *(buf + offset++) = '.';
    }
  }
}

/*
static void parser_content_range(char *p_content_range_value, size_t *p_content_len, int *p_i_start_index)
{
  if (p_content_range_value == NULL || p_content_len == NULL || p_i_start_index == NULL)
  {
    return;
  }
  size_t content_size = 0;
  int i_start_index = 0;

//   获取内容的总长度
  char *p_value_len = strrchr(p_content_range_value, '/');
  if (p_value_len == NULL)
  {
    return;
  }
  content_size = strtol(p_value_len + 1, NULL, 10);

//   判断是否从索引0获取数据
  char *p = strchr(p_content_range_value, ' ');
  if (p == NULL)
  {
    return;
  }
  p++;

  while (*p != '-' && p < p_value_len)
  {
    if (*p < '0' || *p > '9')
    {
      break;
    }
    i_start_index = (i_start_index * 10) + (*p - '0');
    p++;
  }

  if (*p != '-' || p >= p_value_len)
  {
    return;
  }

  *p_content_len = content_size;
  *p_i_start_index = i_start_index;
  return;
}*/

/* 获取multipart的content-disposition 和 content-type 内容  */
static void get_multipart_info(const char *p_multipart, size_t multipart_len, char **pp_req_content_disposition, char **pp_req_content_type)
{
  #define DISPOSITION "Content-Disposition: "
  #define CONTENT_TYPE "Content-Type: "
  const char *p_req_content_disposition = NULL;
  char *p_content_disposition = NULL;
  size_t disposition_value_len = 0;

  const char *p_req_content_type = NULL;
  char *p_content_type = NULL;
  size_t content_type_value_len = 0;

  const char *p_start = p_multipart;
  const char *p_end = NULL;
  size_t len = multipart_len;
  size_t disposition_len = strlen(DISPOSITION);
  size_t content_type_len = strlen(CONTENT_TYPE);

  while (len > 0 && (p_end = my_strstr(p_start, len, "\r\n", strlen("\r\n"))) != NULL)
  {
    if (!memcmp(p_start, DISPOSITION, disposition_len))
    {
      /* 查找disposition字段是否有"filename=" */
      disposition_value_len = (p_end - p_start) - disposition_len;
      if (!my_strstr(p_start + disposition_len, disposition_value_len, "filename=", strlen("filename=")))
      {
        goto next;
      }

      p_req_content_disposition = p_start + disposition_len;
      goto next;
    }

    if (!memcmp(p_start, CONTENT_TYPE, content_type_len))
    {
      content_type_value_len = (p_end - p_start) - content_type_len;
      p_req_content_type = p_start + content_type_len;
      goto next;
    }

  next:
    len -= (p_end - p_start + 2);
    p_start += (p_end - p_start + 2);

    if (strncmp(p_end + 2, "\r\n", 2) == 0)
    {
      break;
    }
  }

  if (p_req_content_disposition == NULL || p_req_content_type == NULL)
  {
    return;
  }

  p_content_disposition = (char *)malloc(disposition_value_len + 1);
  if (NULL == p_content_disposition)
  {
    return;
  }

  p_content_type = (char *)malloc(content_type_value_len + 1);
  if (NULL == p_content_type)
  {
    free(p_content_disposition);
    return;
  }

  memcpy(p_content_disposition, p_req_content_disposition, disposition_value_len);
  memcpy (p_content_type, p_req_content_type, content_type_value_len);
  p_content_disposition[disposition_value_len] = '\0';
  p_content_type[content_type_value_len] = '\0';

  *pp_req_content_disposition = p_content_disposition;
  *pp_req_content_type = p_content_type;
  return;
}

const char *CHttpParser::find_real_multipart(const char *p_first_boundary    /* first boundary */
                                                  , size_t first_boundary_len       /* first boundary len */
                                                  , const char *p_req_body
                                                  , uint64_t req_content_length
                                                  , size_t *body_len
                                                  , char **pp_req_content_disposition
                                                  , char **pp_req_content_type)
{
  const char *p_start = p_req_body;
  const char *p_end = NULL;
  const char *p_multipart_end = NULL;
  char *p_req_content_disposition = NULL;
  char *p_req_content_type = NULL;
  size_t len = *body_len;
  while (  (p_end = my_strstr(p_start, len, p_first_boundary, first_boundary_len)) != NULL)
  {
    /* boudary \r\n---------xxxx--\r\n  last boundary \r\n---------xxxx----\r\n */
    if (p_end - p_start < 2 || memcmp(p_end - 2, "\r\n", 2) || (memcmp(p_end + first_boundary_len, "\r\n", 2) && memcmp(p_end + first_boundary_len, "--\r\n", 4)))  /* \r\n */
    {
      return NULL;
    }

    p_multipart_end = my_strstr(p_start, (p_end - p_start -2), "\r\n\r\n", 4);
    if (NULL == p_multipart_end)
    {
      fprintf (stderr, "find multipart failed\n");
      return NULL;
    }

    get_multipart_info(p_start, (p_multipart_end - p_start + 4), &p_req_content_disposition, &p_req_content_type);
    if (p_req_content_disposition != NULL && p_req_content_type != NULL)
    {
      *pp_req_content_disposition = p_req_content_disposition;
      *pp_req_content_type = p_req_content_type;
      /* 文件数据长度 */
      *body_len = (p_end - p_multipart_end - 2 /* \r\n */ - 4 /*\r\n\r\n*/);
      /* 文件首地址 */
      return (p_multipart_end + 4);
    }

    len -= (p_end + first_boundary_len + 2 - p_start);
    p_start = p_end + first_boundary_len + 2;
  }

  /* 大于 HTTP_REQUEST_BODY_MAX_LENGTH boundary 会被截断*/
  if (req_content_length >= (uint64_t)m_conf_http_request_body_max_size)
  {
    p_multipart_end = my_strstr(p_start, len, "\r\n\r\n", 4);
    if (NULL == p_multipart_end)
    {
      fprintf (stderr, "find last multipart failed\n");
      return NULL;
    }

    get_multipart_info(p_start, (p_multipart_end - p_start + 4), &p_req_content_disposition, &p_req_content_type);
    if (p_req_content_disposition != NULL && p_req_content_type != NULL)
    {
      *pp_req_content_disposition = p_req_content_disposition;
      *pp_req_content_type = p_req_content_type;
      /* 文件数据长度 */
      *body_len = (len - (p_multipart_end + 4 - p_start));
      /* 文件首地址 */
      return (p_multipart_end + 4);
    }
  }
  return NULL;
}

static inline void free_req_value(char *p_req_disposition, char *p_req_content_type)
{
  SAFE_FREE(p_req_disposition);
  SAFE_FREE(p_req_content_type);
  return;
}

int CHttpParser::get_form_data_info(http_parser_msg *msg, char *content_type, char *content_disposition, bstring_t *body_value)
{
  const char *p_boundary = NULL;  /* request boundary字段 */
  size_t content_boundary_len = 0; /* request boundary 长度 */
  bstring_t *p_st_req_content_type = &msg->req_hdr_content_type;
  bstring_t *p_st_req_body = &msg->req_msg.body;
  size_t req_content_length = msg->req_msg.content_length;
  p_boundary = get_req_ct_boundary(p_st_req_content_type->bstr, p_st_req_content_type->length, &content_boundary_len);
  if (p_boundary == NULL)
  {
    /* 不认为是上传文件 */
    GWLOG_DEBUG(m_comm, "[request] content-type boundary error, content type len = %zu, content type = %.*s\n\n", p_st_req_content_type->length
                                                                                                               , (int)p_st_req_content_type->length
                                                                                                               , p_st_req_content_type->bstr);
  }
  else
  {

    /* 在request body中查找 First boundary*/
    size_t req_body_len = p_st_req_body->length;
    const char *p_start = p_st_req_body->bstr;

    /* 不解析 -- \r\n */
    if (content_boundary_len + 2 + 2 >= req_body_len)
    {
      return -2;
    }

    /* first boundary 比 p_boundary多两个字符("--") */
    const char *p_first_boundary = NULL;
    if ((p_first_boundary = my_strstr(p_start, p_st_req_body->length, p_boundary, content_boundary_len)) != NULL)
    {
      ASSERT((p_first_boundary - p_start) >= 2);
      if (!strncmp(p_first_boundary - 2, "--", 2) && !strncmp(p_first_boundary + content_boundary_len, "\r\n", 2))
      {
        req_body_len -= (p_first_boundary - p_start + content_boundary_len + 2);
        p_start = p_first_boundary + content_boundary_len + 2; /* 指针后移first_boundary长度 + 2 */
        char *disposition = NULL;
        char *type = NULL;
        p_start = find_real_multipart(p_first_boundary - 2, content_boundary_len + 2, p_start, req_content_length, &req_body_len, &disposition, &type);
        if (p_start == NULL)
        {
          return -1;
        }

        if(disposition)
        {
            memcpy(content_disposition, disposition, MIN(CONTENT_DISPOSITION_MAX_LENGTH - 1, strlen(disposition)));
            SAFE_FREE(disposition);
        }

        if(type)
        {
            memcpy(content_type, type, MIN(CONTENT_TYPE_MAX_LENGTH - 1, strlen(type)));
            SAFE_FREE(type);
        }

        body_value->bstr = p_start;
        body_value->length = req_body_len;
      }
    }
  }
  return 0;
}

char* CHttpParser::get_http_file_type(http_file_info *file_info, const char *mime)
{
    if (mime && strlen(mime) > 0)
    {
        // 从对应的MIME列表中找到文件类型
        std::map<std::string, std::string>::iterator it = content_type_upload_map.find(mime);
        if (it != content_type_upload_map.end())
        {
            // 先释放之前可能分配的内存，避免内存泄漏
            if (file_info->p_file_type != NULL)
            {
                free(file_info->p_file_type);
                file_info->p_file_type = NULL;
            }

            file_info->p_file_type = (char*)malloc(it->second.length() + 1);
            if (file_info->p_file_type != NULL)
            {
                memcpy(file_info->p_file_type, it->second.c_str(), it->second.length());
                *(file_info->p_file_type + it->second.length()) = '\0';
            }
            else
            {
                // 内存分配失败，记录错误但不崩溃
                // 这里可以添加日志记录
                return NULL;
            }
        }
    }

    return NULL;

}

int CHttpParser::get_filename_from_content_disposition(const char *p_content_disposition, char *p_filename)
{
    //char *p_file_extension = NULL;
    int key_len = 0;
    char *p = strcasestr((char*)p_content_disposition, "filename");
    if (p == NULL)
    {
        return -1;
    }
    key_len = strlen("filename");
    /* 查找最后一个'.' */
    int i_double_quote = 0;
    int i_single_quote = 0;
    char *p_last_dot = NULL;
    p = p + key_len;
    // filename=;filename*=;filename =;filename* = ,'='后面的空格不处理
    p = strstr(p, "=");
    if (p == NULL)
    {
        return -1;
    }
    p++;
    p_last_dot = p;
    while (*p != '\0')
    {
        /* response content-disposition字段，经过C++转码, filename="的双引号变成 \" */
        if (*p == '\\' && *(p + 1) == '"')
        {
            if (i_double_quote == 0)
            {
                i_double_quote++;
                p = p + 2;
                p_last_dot = p;
                continue;
            }
            else
            {
                break;
            }
        }

        /* request content-disposition字段，没有经过json转码 */
        if (*p == '"')
        {
            if (i_double_quote == 0)
            {
                i_double_quote ++;
                p = p + 1;
                p_last_dot = p;
                continue;
            }
            else
            {
                break;
            }
        }

        // filename*=UTF-8''xxx.docx 以及 filename*=UTF-8'zh_cn'xxx.docx
        if (*p == '\'')
        {
            if (i_single_quote == 0)
            {
                i_single_quote ++;
            }

            if (i_single_quote == 1)
            {
                p = p + 1;
                p_last_dot = p;
            }
            continue;
        }

        // 跳过路径
        if (*p == '/')
        {
            p = p + 1;
            p_last_dot = p;
            continue;
        }

        // filename=xxx.docx
        if (*p == ';')
        {
            break;
        }

        ++p;
    }

    if (!p_last_dot || *p_last_dot == '\0' || *p_last_dot == ';')
    {
        return -1;
    }

    memcpy(p_filename, p_last_dot, MIN((p - p_last_dot), FILENAME_MAX_LENGTH - 1));

    return 0;
}

int CHttpParser::get_filename_from_url(const char *p_url, char *p_filename)
{
    if (p_url == NULL)
    {
        return -1;
    }
    char p_dot = '.';
    char p_slash = '/';
    char p_arg_flag = '?';
    char p_arg_flag1 = ';';
    //printf ("p_url = %s\n", p_url);

    /* 判断url是否携带参数 */
    char *p_arg = strchr((char*)p_url, p_arg_flag);
    if (p_arg == NULL)
    {
        p_arg = strchr((char*)p_url, p_arg_flag1);
    }

    if (p_arg == NULL)
    {
        /* 寻找最后一个‘/’ */
        char *p = NULL;
        p = strrchr((char*)p_url, p_slash);
        if (p == NULL)
        {
            return -1;
        }

        /* url没有携带参数，从位置开始寻找最后一个'.'的位置 */
        char *p_extension = strrchr(p, p_dot);
        if (p_extension != NULL)
        {
            int length = strlen(p_url) - (p - p_url +1);
            //printf ("url extension len = %p, len = %u, p_extension = %p, len = %u\n", p_url_extension, (FILE_EXTENSION_LEN - 1), (p_extension + 1), strlen(p_extension + 1));
            memcpy(p_filename, p + 1, MIN(length, FILENAME_MAX_LENGTH - 1));
            return 0;
        }
        return -1;
    }
    else
    {
        /* 寻找最后一个‘/’ */
        char *p = NULL;
        p = strrchr((char*)p_url, p_slash);
        if (p == NULL)
        {
            return -1;
        }

        char *q = strchr((char*)p + 1, p_dot);

        if (q == NULL)
        {
            return -1;
        }

        if (q >= p_arg)
        {
            return -1;
        }

        memcpy(p_filename, p + 1, MIN((p_arg - p - 1), FILENAME_MAX_LENGTH - 1));
        return 0;
    }
    return 0;
}

char* CHttpParser::get_file_name(http_file_info *file_info, const char *p_content_disposition, const char *url)
{
    // 从disposition中提取文件名
    // 从url中提取文件名
    // 以sha256[0:7]和url作为文件名,未实现
    char filename[FILENAME_MAX_LENGTH] = {0};

    // 先释放之前可能分配的内存，避免内存泄漏
    if (file_info->p_file_name != NULL)
    {
        free(file_info->p_file_name);
        file_info->p_file_name = NULL;
    }

    if (p_content_disposition && strlen(p_content_disposition) > 0)
    {
        get_filename_from_content_disposition(p_content_disposition, filename);

        if (strlen(filename) > 0)
        {
            file_info->p_file_name = (char*)malloc(strlen(filename) + 1);
            if (file_info->p_file_name != NULL)
            {
                memcpy(file_info->p_file_name, filename, strlen(filename));
                *(file_info->p_file_name + strlen(filename)) = '\0';
            }
            return NULL;
        }
    }

    // url可能会没有？响应单边
    if (url)
    {
        get_filename_from_url(url, filename);

        if (strlen(filename) > 0)
        {
            file_info->p_file_name = (char*)malloc(strlen(filename) + 1);
            if (file_info->p_file_name != NULL)
            {
                memcpy(file_info->p_file_name, filename, strlen(filename));
                *(file_info->p_file_name + strlen(filename)) = '\0';
            }
            return NULL;
        }
    }

    // 设置默认文件名
    file_info->p_file_name = (char*)malloc(strlen("unknown") + 1);
    if (file_info->p_file_name != NULL)
    {
        memcpy(file_info->p_file_name, "unknown", strlen("unknown"));
        *(file_info->p_file_name + strlen("unknown")) = '\0';
    }
    return NULL;
}

void CHttpParser::probe_file_event_by_mime(bstring_t *body, const char *p_content_type, int flag, upload_http_info_t *st_upload_http_info)
{
    if (strlen(p_content_type) == 0)
    {
        return;
    }
    std::map<std::string, std::string>::iterator it = content_type_upload_map.find(p_content_type);

    if (it != content_type_upload_map.end())
    {
        //命中白名单
        if (flag == 0)
        {
            if (it->first == "text/plain" || it->first == "text/csv")
            {
                return;
            }
        }
        st_upload_http_info->is_file_event = 1;
        return;
    }

    it = content_type_not_upload_map.find(p_content_type);
    if (it != content_type_not_upload_map.end())
    {
        //命中黑名单,事件需要过滤
        st_upload_http_info->is_file_event = 2;
        return;
    }
    return;
}

const char* CHttpParser::get_mime_from_body(bstring_t *body)
{
    if (!g_magic)
    {
        g_magic = magic_open(MAGIC_MIME_TYPE); // TODO, 未close

        if (!g_magic)
        {
            // TODO
            return NULL;
        }
        if (magic_load(g_magic, "/opt/data/apigw/gwhw/magic.mgc") != 0)
        {
            // TODO
            return NULL;
        }
    }
    // 只需要传入前16KB
    return (magic_buffer(g_magic, body->bstr, body->length < 16384? body->length:16384));
}

int CHttpParser::probe_file_event(bstring *body, char *p_content_type, const char *p_content_disposition, upload_http_info_t *st_upload_http_info)
{
    // 表单提交的内容也需要重新提取content-disposition,content-type,filename
    if (p_content_disposition && strlen(p_content_disposition) > 0)
    {
        if (!strncasecmp(p_content_disposition, "attachment", strlen("attachment")))
        {

            if (strlen(p_content_type) == 0 || !strncasecmp(p_content_type, "application/octet-stream", strlen("application/octet-stream")) || m_file_type_from_body)
            {
                const char *ct = get_mime_from_body(body);
                if(ct && strlen(ct) > 0)
                {
                    memcpy(p_content_type, ct, MIN(CONTENT_TYPE_MAX_LENGTH - 1, strlen(ct)));
                    *(p_content_type + strlen(ct)) = '\0';
                }
                else
                {
                    return 0;
                }
            }

            probe_file_event_by_mime(body, p_content_type, 1, st_upload_http_info);
            if (st_upload_http_info->is_file_event == 0)
            {
                st_upload_http_info->is_file_event = 3;
            }
            return 0;
        }

        //包括form-data和inline两种情况
        const char *p = NULL;
        p = my_strstr(p_content_disposition, strlen(p_content_disposition), "filename", strlen("filename"));
        if (p)
        {
            if (strlen(p_content_type) == 0 || !strncasecmp(p_content_type, "application/octet-stream", strlen("application/octet-stream")) || m_file_type_from_body)
            {
                const char *ct = get_mime_from_body(body);
                if(ct && strlen(ct) > 0)
                {
                    memcpy(p_content_type, ct, MIN(CONTENT_TYPE_MAX_LENGTH - 1, strlen(ct)));
                    *(p_content_type + strlen(ct)) = '\0';
                }
                else
                {
                    return 0;
                }
            }

            probe_file_event_by_mime(body, p_content_type, 1, st_upload_http_info);
            if (st_upload_http_info->is_file_event == 0)
            {
                st_upload_http_info->is_file_event = 3;
            }
            return 0;
        }
    }

    if (p_content_type && strlen(p_content_type) > 0)
    {
        probe_file_event_by_mime(body, p_content_type, 0, st_upload_http_info);
        return 0;
    }

    return 0;
}

int CHttpParser::get_fileinfo(upload_http_info_t *upload_i, http_parser_msg *phpm, bstring *body)
{
    char p_content_type[CONTENT_TYPE_MAX_LENGTH] = {0};
    char p_content_disposition[CONTENT_DISPOSITION_MAX_LENGTH] = {0};

    // 处理2xx的响应
    if (phpm->status_code < 200 || phpm->status_code >= 300)
    {
        return -1;
    }

    // 考虑到两个情况,一个是POST即是下载也是响应以及大部分都是下载的事件,因此先检测响应
    if (phpm->rsp_hdr_content_type.length > 0)
    {

        const char* p = my_strchr(phpm->rsp_hdr_content_type.bstr, phpm->rsp_hdr_content_type.length, ';');
        if (p)
        {
            memcpy(p_content_type, phpm->rsp_hdr_content_type.bstr, MIN(CONTENT_TYPE_MAX_LENGTH - 1, p - phpm->rsp_hdr_content_type.bstr));
        }
        else
        {
            memcpy(p_content_type, phpm->rsp_hdr_content_type.bstr, MIN(CONTENT_TYPE_MAX_LENGTH - 1, phpm->rsp_hdr_content_type.length));
        }
    }

    if (phpm->rsp_hdr_content_disposition.length > 0)
    {
        memcpy(p_content_disposition, phpm->rsp_hdr_content_disposition.bstr, MIN(CONTENT_DISPOSITION_MAX_LENGTH - 1, phpm->rsp_hdr_content_disposition.length));
    }
    // 断点续传处理
    // TODO

    probe_file_event(&phpm->rsp_msg.body, p_content_type, p_content_disposition, upload_i);

    if (upload_i->is_file_event == 1)
    {
        upload_i->http_file_info.p_file_direction = "download";
        upload_i->http_file_info.file_len = phpm->rsp_msg.body.length;
        upload_i->rw_flag = 0; // RW_FLAG_READ
        get_http_file_type(&upload_i->http_file_info, p_content_type);
        get_file_name(&upload_i->http_file_info, p_content_disposition, phpm->full_uri);
        body = &phpm->rsp_msg.body;
        return 0;
    }


    memset(p_content_disposition, 0, CONTENT_DISPOSITION_MAX_LENGTH);
    memset(p_content_type, 0, CONTENT_TYPE_MAX_LENGTH);

    if (upload_i->is_file_event != 0)
    {
        return 0;
    }

    if ((phpm->method == HTTP_POST || phpm->method == HTTP_PUT) && phpm->req_msg.body.bstr && phpm->req_msg.body.length > 0)
    {
        // 获取文件内容,http的信息
        if (phpm->req_hdr_content_type.bstr && strncasecmp(phpm->req_hdr_content_type.bstr, REQUEST_CT_FORM, strlen(REQUEST_CT_FORM)) == 0)
        {
            get_form_data_info(phpm, p_content_type, p_content_disposition, body);

            if (!body->bstr)
            {
                upload_i->is_file_event = 3;
                return 0;
            }
        }
        else
        {
            body = &phpm->req_msg.body;
            if (!body->bstr)
            {
                upload_i->is_file_event = 3;
                return 0;
            }

            const char* p = my_strchr(phpm->req_hdr_content_type.bstr, phpm->req_hdr_content_type.length, ';');
            if (p)
            {
                memcpy(p_content_type, phpm->req_hdr_content_type.bstr, MIN(CONTENT_TYPE_MAX_LENGTH - 1 , p - phpm->req_hdr_content_type.bstr));
            }
            else
            {
                memcpy(p_content_type, phpm->req_hdr_content_type.bstr, MIN(CONTENT_TYPE_MAX_LENGTH - 1, phpm->req_hdr_content_type.length));
            }
        }

        probe_file_event(body, p_content_type, p_content_disposition, upload_i);

        // 0也会返回,这种不确定的情况也会当作普通事件处理,主要是为了适配POST可能是上传也可能是下载的情况
        if (upload_i->is_file_event != 1)
        {
            return 0;
        }

        upload_i->http_file_info.p_file_direction = "upload";
        upload_i->rw_flag = 1; //RW_FLAG_WRITE
        if (body->bstr)
        {
            upload_i->http_file_info.file_len = body->length;
        }
        else
        {
            upload_i->http_file_info.file_len = phpm->req_msg.body.length;
        }

        get_http_file_type(&upload_i->http_file_info, p_content_type);
        get_file_name(&upload_i->http_file_info, p_content_disposition, phpm->full_uri);

        return 0;
    }
    upload_i->http_file_info.p_file_direction = "unknown";
    upload_i->is_file_event = 3;

    return 0;
}

void CHttpParser::add_event_id(char *p_event_id)
{
    if (NULL == p_event_id)
    {
      return;
    }

    char a_unique_code[64] = {0};
    uint64_t u64_time_val = 0;
    get_ms_timeval(&u64_time_val);

    if (g_u64_http_upload_ms == 0)
    {
        g_u64_http_upload_ms = u64_time_val;
        g_u32_http_upload_index = 1;
    }
    else
    {
        if (u64_time_val == g_u64_http_upload_ms)
        {
            g_u32_http_upload_index ++;
        }
        else
        {
            g_u64_http_upload_ms = u64_time_val;
            g_u32_http_upload_index = 1;
        }
    }

    /* 获取唯一标识ID */
    get_unique_event_id(m_str_gw_ip.c_str(), g_u64_http_upload_ms, g_u32_http_upload_index, a_unique_code, sizeof(a_unique_code) - 1);

    /* 将unique_code进行base64编码 */
    base64_encode((unsigned char*)p_event_id, (unsigned char*)a_unique_code, strlen(a_unique_code));

    return;
}

void CHttpParser::free_http_parser_msg_inner(const http_parser_msg_t *p)
{
  SAFE_FREE(p->req_msg.header_item);
  SAFE_FREE(p->rsp_msg.header_item);

  BSTR_SAFE_FREE(p->req_msg.header.bstr);
  BSTR_SAFE_FREE(p->req_msg.body.bstr);
  BSTR_SAFE_FREE(p->rsp_msg.header.bstr);
  BSTR_SAFE_FREE(p->rsp_msg.body.bstr);

}

void CHttpParser::calc_match_fail_cnt(http_parser_ext_t* p_req, http_parser_ext_t* p_rsp)
{
  if (p_req == NULL || p_rsp == NULL) {
    return ;
  }

  if (p_req->error_code == 0 && p_rsp->error_code == 0) {
    return ;
  }

  int error_code = 0;
  if (p_rsp->error_code != 0) {
    error_code = p_rsp->error_code;
  }
  else {
    error_code = p_req->error_code;
  }

  switch(error_code) {
    case TCP_DROP_BY_HTTP_TRUNK_FAILED:
      __sync_fetch_and_add(&m_stats_http.m.cnt_match_by_trunk_failed, 1);
      break;
    case TCP_DROP_BY_HTTP_TRUNK_TOO_LARGE:
      __sync_fetch_and_add(&m_stats_http.m.cnt_match_by_trunk_too_large, 1);
      break;
    case TCP_DROP_BY_HTTP_PARSED_FAILED:
      break;
    case TCP_DROP_BY_HTTP_PAYLOAD_TOO_LARGE:
      __sync_fetch_and_add(&m_stats_http.m.cnt_match_by_payload_too_large, 1);
      break;

    case TCP_DROP_BY_PACKET_LOST:
      __sync_fetch_and_add(&m_stats_http.m.cnt_match_by_packet_lost, 1);
      break;
    default:
      break;
  }
}

void CHttpParser::http_cb_parser_send_data_new(parser_request_response_params_t *p_req_rsp_param, double pcap_ts, const ConnData *conn, CSession *p_session, http_parser_ext_t *p_req_parser, http_parser_ext_t *p_rsp_parser, int unkown_rule)
{
  http_parser_msg_t hpm[1] = {0};
  http_parser_ext_t parser[1] ={0};
  UrlDropReason url_drop_reason = UrlDropReason::SUCCESS;
  parser[0].error_code = TCP_DROP_BY_PACKET_LOST;
  // GWLOG_INFO(m_comm, "http send data\n");
  if (p_req_parser == NULL && p_rsp_parser == NULL) {
    return ;
  }
  std::string str_http_host;
  std::string content_type;

  http_parser_ext_t* req_parser = p_req_parser;
  http_parser_ext_t* rsp_parser = p_rsp_parser;

  if (req_parser == NULL) {
    if (m_conf_send_empty_http_req_enable)
    {
      req_parser = &parser[0];
    }
    else
    {
      goto end;
    }
    //req_parser = &parser[0];
    //goto end;
  }

  if (rsp_parser == NULL) {
    if (!m_drop_empty_rsp)
    {
      rsp_parser = &parser[0];
    }
    else
    {
      goto end;
    }
  }

  str_http_host = std::move(std::string(req_parser->hdr_host.bstr, req_parser->hdr_host.length));
  content_type = std::move(std::string(rsp_parser->hdr_content_type.bstr, rsp_parser->hdr_content_type.length));

  // if (!(req_parser->parsed_complete && rsp_parser->parsed_complete))
  // {
  //   //return;
  // }

  if(!req_parser->header_complete && !rsp_parser->header_complete) {
    goto end ;
  }

  calc_match_fail_cnt(req_parser, rsp_parser);
  strncpy(hpm->full_uri, req_parser->full_uri, COUNTOF(hpm->full_uri));
  strncpy(hpm->host, req_parser->hdr_host.bstr, MIN(COUNTOF(hpm->host) - 1, req_parser->hdr_host.length));
  hpm->mem_size = sizeof(http_parser_msg_t);
  if (p_session->get_reverse()) {
    memcpy(&hpm->addr.client, &conn->server, sizeof(conn->server));
    memcpy(&hpm->addr.server, &conn->client, sizeof(conn->client));
    hpm->addr.p_client_mac = conn->p_server_mac;
    hpm->addr.p_server_mac = conn->p_client_mac;
  }else {
    hpm->addr = *conn;
  }


  do
  {
    unsigned int client_ip = 0;
    unsigned int server_ip = 0;

    client_ip = ntohl(hpm->addr.client.ipv4);
    server_ip = ntohl(hpm->addr.server.ipv4);

    if (m_upload_client_ipfilter_rule->ip_white_hit(client_ip)) {
      break;
    }

    if (m_upload_server_ipfilter_rule->ip_white_hit(server_ip)) {
      break;
    }

    if (m_upload_client_ipfilter_rule->ip_filter_hit(client_ip)) {
      goto end;
    }

    if (m_upload_server_ipfilter_rule->ip_filter_hit(server_ip)) {
      goto end;
    }
  } while(0);

  if (m_drop_enable && m_drop_percent > 0)
  {
    int magic = m_drop_percent * DROP_MOD / 100;
    if ((conn->client.ipv4 ^ conn->client.port ^ conn->server.ipv4 ^ conn->server.port) % DROP_MOD <  (unsigned)magic)
    {
      // GWLOG_INFO(m_comm, "drop http event\n");
      url_drop_reason = UrlDropReason::UPLOAD_LIMIT;
      goto end;
    }
  }

  /* 不完整流量过滤,只过滤不进行统计 */
  if (!m_upload_incomplete_events_enable)
  {
      if (!req_parser->complete && req_parser->error_code != 2000)
      {
          goto end;
      }

      if (!rsp_parser->complete && rsp_parser->error_code != 2000)
      {
          goto end;
      }
  }

  /* host过滤 */
  __sync_fetch_and_add(&(m_stats_http.host_filter.cnt_op), 1);
  if (!m_host_white->hit_white_host(str_http_host))
  {
    if (m_host_filter->hit_filter_host(str_http_host))
    {
      url_drop_reason = UrlDropReason::HOST_FILTER;
      __sync_fetch_and_add(&(m_stats_http.host_filter.cnt_op_filter), 1);
      goto end;
    }
  }
  __sync_fetch_and_add(&(m_stats_http.host_filter.cnt_op_pass), 1);

  __sync_fetch_and_add(&(m_stats_http.content_type_filter.cnt_op), 1);
  if (m_content_type_filter->hit_content_type_filters(content_type))
  {
    url_drop_reason = UrlDropReason::CONTENT_TYPE_FILTER;
    __sync_fetch_and_add(&(m_stats_http.content_type_filter.cnt_op_filter), 1);
    goto end;
  }
  __sync_fetch_and_add(&(m_stats_http.content_type_filter.cnt_op_pass), 1);

  /* ip-method 过滤 */
  __sync_fetch_and_add(&(m_stats_http.ip_method_filter.cnt_op), 1);
  if (m_ipmethod_filter->get_ip_method_filters_size() > 0)
  {

    bool is_hit = false;
    if (hpm->addr.client.v == 4)
    {
      is_hit = m_ipmethod_filter->hit_filter_ipv4_method(req_parser->hp.method, hpm->addr.client.ipv4);
    }
    else
    {
      is_hit = m_ipmethod_filter->hit_filter_ipv6_method(req_parser->hp.method, (uint16_t*)hpm->addr.client.ipv6);
    }

    if (is_hit)
    {
      url_drop_reason = UrlDropReason::IP_METHOD_FILTER;
      __sync_fetch_and_add(&(m_stats_http.ip_method_filter.cnt_op_filter), 1);
      goto end;
    }
  }
  __sync_fetch_and_add(&(m_stats_http.ip_method_filter.cnt_op_pass), 1);

  // url fiter
  __sync_fetch_and_add(&m_stats_http.url_filter.cnt_op, 1);
  if (!m_url_white->have_filter() || !m_url_white->hit_url_filter(req_parser->full_uri))
  {
    if(m_interactive_url_filter->have_filter() && m_interactive_url_filter->hit_url_filter(req_parser->full_uri))
    {
      url_drop_reason = UrlDropReason::URL_FILTER;
      __sync_fetch_and_add(&m_stats_http.url_filter.cnt_op_filter, 1);
      goto end;
    }
    if (m_url_filter->have_filter() && m_url_filter->hit_url_filter(req_parser->full_uri)){
      url_drop_reason = UrlDropReason::URL_FILTER;
      __sync_fetch_and_add(&m_stats_http.url_filter.cnt_op_filter, 1);
      goto end;
    }
  }
  __sync_fetch_and_add(&m_stats_http.url_filter.cnt_op_pass, 1);

  if (conn->pcap_filename)
  {
    memcpy(hpm->pcap_filename, conn->pcap_filename, MIN(COUNTOF(hpm->pcap_filename), strlen(conn->pcap_filename)));
  }

  //total both+single_req
  __sync_fetch_and_add(&m_stats_http.event_complete.cnt_total, 1);
  if (req_parser->complete&&rsp_parser->complete)
  {
      __sync_fetch_and_add(&m_stats_http.event_complete.cnt_complete, 1);
  }
  else if(rsp_parser->error_code == 2000 && req_parser->complete)
  {
      __sync_fetch_and_add(&m_stats_http.event_complete.cnt_complete, 1);
  }
  else if(req_parser->error_code == 2000 && rsp_parser->complete)
  {
      __sync_fetch_and_add(&m_stats_http.event_complete.cnt_complete, 1);
  }
  else
  {
      __sync_fetch_and_add(&m_stats_http.event_complete.cnt_uncomplete, 1);
  }

  //both
  if (rsp_parser->error_code != 2000 && req_parser->error_code != 2000)
  {
      __sync_fetch_and_add(&m_stats_http.event_complete.cnt_both, 1);
      if (req_parser->complete&&rsp_parser->complete)
      {
          __sync_fetch_and_add(&m_stats_http.event_complete.cnt_both_complete, 1);
      }
      else
      {
          __sync_fetch_and_add(&m_stats_http.event_complete.cnt_both_uncomplete, 1);
      }
  }
  else if (rsp_parser->error_code == 2000)//single_req
  {
      __sync_fetch_and_add(&m_stats_http.event_complete.cnt_single_req, 1);
      if (req_parser->complete)
      {
          __sync_fetch_and_add(&m_stats_http.event_complete.cnt_single_req_complete, 1);
      }
      else
      {
          __sync_fetch_and_add(&m_stats_http.event_complete.cnt_single_req_uncomplete, 1);
      }
  }
  else if (req_parser->error_code == 2000)//single_rsp
  {
      __sync_fetch_and_add(&m_stats_http.event_complete.cnt_single_rsp, 1);
      if (rsp_parser->complete)
      {
          __sync_fetch_and_add(&m_stats_http.event_complete.cnt_single_rsp_complete, 1);
      }
      else
      {
          __sync_fetch_and_add(&m_stats_http.event_complete.cnt_single_rsp_uncomplete, 1);
      }
  }
  else
  {

  }

  hpm->pcap_ts = pcap_ts;
  hpm->req_hdr_cookie = req_parser->hdr_cookie;
  // hpm->mem_size += http_cb_http_parser_get_msg_data(&hpm->req_msg, p_req_parser);
  // hpm->mem_size += http_cb_http_parser_get_msg_data(&hpm->rsp_msg, p_rsp_parser);
  hpm->req_msg = req_parser->http_parser_msg;
  hpm->rsp_msg = rsp_parser->http_parser_msg;
  memset(&req_parser->http_parser_msg, 0, sizeof(req_parser->http_parser_msg));
  memset(&rsp_parser->http_parser_msg, 0, sizeof(rsp_parser->http_parser_msg));
  hpm->mem_size += hpm->req_msg.mem_size;
  hpm->mem_size += hpm->rsp_msg.mem_size;

  hpm->method = req_parser->hp.method;
  hpm->http_req_ver = {req_parser->hp.http_major, req_parser->hp.http_minor};
  hpm->http_rsp_ver = {rsp_parser->hp.http_major, rsp_parser->hp.http_minor};

  hpm->status_code = rsp_parser->hp.status_code;
  hpm->req_hdr_content_type = req_parser->hdr_content_type;
  hpm->rsp_hdr_content_encoding = rsp_parser->hdr_content_encoding;
  hpm->rsp_hdr_content_type = rsp_parser->hdr_content_type;
  hpm->rsp_hdr_content_disposition = rsp_parser->hdr_content_disposition;
  hpm->rsp_hdr_content_range = rsp_parser->hdr_content_range;
  hpm->rsp_content_length = rsp_parser->hp.content_length;

  hpm->unkown_rule = unkown_rule;
  hpm->http_gzip_mode = m_conf_http_gzip_mode;

  hpm->req_error_code = req_parser->error_code;
  hpm->rsp_error_code = rsp_parser->error_code;

  hpm->req_seq = req_parser->seq;
  hpm->req_ack = req_parser->ack;

  hpm->rsp_seq = rsp_parser->seq;
  hpm->rsp_ack = rsp_parser->ack;

  hpm->vlan_id = p_session->m_vlan_id;
    hpm->req_complete = req_parser->complete;
    hpm->rsp_complete = rsp_parser->complete;

  http_cb_http_parser_msg(hpm);

end:
  // 从当前节点中，删除节点信息；
  //http_stream_t *phs = p_session->get_stream_data_from_parser(this)->p_http_stream;

  if (m_url_abnormal_check_enable)
  {
    if (req_parser->error_code == 2000 || rsp_parser->error_code == 2000)
    {
      url_drop_reason = UrlDropReason::PACKET_LOST;
    }

    if (strstr(req_parser->full_uri, m_abnormal_check_url.c_str()) != NULL &&
        UrlDropReason::SUCCESS < url_drop_reason &&
        url_drop_reason < UrlDropReason::URL_DROP_REASON_NUM)

    GWLOG_WARN(m_comm, "url=%s drop, reason=%s\n", m_abnormal_check_url.c_str(), m_drop_reason_msg[url_drop_reason]);
  }

  http_stream_t *phs = p_session->get_stream_data_from_type(m_http_type)->p_http_stream;
  free_session_http_parser(phs, HTTP_REQUEST, p_req_parser);
  free_session_http_parser(phs, HTTP_RESPONSE, p_rsp_parser);
  p_req_rsp_param->free_header_chunk_data = 0;
}

CWorkerQueue *CHttpParser::new_wq_http_parser_msg(void)
{
  m_p_wq[HTTPPARSER_WQ_HTTP_PARSER_MSG] = m_comm->create_worker_queue();
  CWorkerQueue *pwq = get_wq_http_parser_msg();
  if (pwq == NULL)
  {
    return NULL;
  }

  CTaskWorkerMsg *ptw = new CTaskWorkerMsg();
  ptw->set_parser(this);
  ptw->set_wq(pwq);
  m_p_tw[HTTPPARSER_WQ_HTTP_PARSER_MSG] = ptw;

  pwq->set_gw_common(m_comm);
  pwq->set_watchdog(m_comm->get_watchdog());
  pwq->set_task_worker(ptw);

  pwq->set_queue_num_and_bytes(m_conf_http_parser_queue_max_num, m_conf_http_parser_queue_memory_max_size_bytes);
  pwq->set_queue_name(HTTP_MSG_QUEUE);
  // pwq->set_queue_destroy_callback((q_destroy_func_t)free_http_parser_msg);
  pwq->init();
  pwq->create_queue();
  // pwq->create_thread(m_conf_http_parser_thread_num, worker_routine_http_parser, this);
  pwq->adjust_worker_thread_num(m_conf_http_parser_thread_num);

  //m_comm->get_gw_stats()->set_task(pwq->get_queue_name(), pwq->get_stats_task_data(), 50);
  m_comm->get_gw_stats()->set_task(pwq->get_queue_name(), pwq, 50);
  m_comm->get_gw_stats()->set_mem_stat(pwq->get_queue_name(), &pwq->get_queue_mem_size(), &pwq->get_queue_max_mem_size());

  return pwq;
}

void CHttpParser::http_cb_http_parser_msg(http_parser_msg_t *a_phpm)
{
  http_parser_msg_t *phpm = new http_parser_msg_t;
  memcpy(phpm, a_phpm, sizeof(http_parser_msg_t));
  // phpm->parser = this;

  // GWLOG_TEST(m_comm, "a_phpm->mem_size=%u\n", a_phpm->mem_size);

  if (!get_wq_http_parser_msg()->queue_put_data(phpm, a_phpm->mem_size))
  {
    free_http_parser_msg_inner(a_phpm);
    delete phpm;
  }

  return;
}

int CHttpParser::worker_routine_http_parser_inner(const void *p)
{
  if (m_i_fast_message == 1)
  {
    return http_parser_fast_msg_routine((http_parser_msg_t *)p);
  }
  else
  {
    return http_parser_msg_routine((http_parser_msg_t *)p);
  }
}

// HTTP解析快速数据组装
int CHttpParser::http_parser_fast_msg_routine(http_parser_msg_t *phpm)
{
  char *s = NULL;
  int unkown_rule = phpm->unkown_rule;
  http_parser_ext_data_t hped[1] = {0};
  http_parser_ext_data_t *phped = &hped[0];
  upload_http_fast_info_t st_upload_http_fast_info;
  memset(&st_upload_http_fast_info, 0, sizeof(upload_http_fast_info_t));

  st_upload_http_fast_info.req_url = phpm->full_uri;
  if (phpm->addr.client.v == 4)
  {
    get_addr_str(phpm->addr.server.ipv4, st_upload_http_fast_info.dst_ip, COUNTOF(st_upload_http_fast_info.dst_ip));
  }
  else
  {
    get_ip6addr_str((uint32_t*)phpm->addr.server.ipv6, st_upload_http_fast_info.dst_ip, COUNTOF(st_upload_http_fast_info.dst_ip));
  }

  http_parser_fast_msg_routine_rsp(phpm, &(st_upload_http_fast_info.rsp_content_type), phped);

  if (1 == m_conf_pcap_timestamp)
  {
    // 取自PCAP结构里的时间信息
    st_upload_http_fast_info.meta_tm = phpm->pcap_ts;
  }
  else
  {
    st_upload_http_fast_info.meta_tm = time(NULL);
  }

  bool analyze = m_comm->get_gw_stats()->check_analyze_log(phpm->addr.client.ipv4,phpm->addr.server.ipv4,phpm->addr.server.port);
  if (analyze)
  {
    st_upload_http_fast_info.analyze_flag = m_comm->get_gw_stats()->get_analyze_flag();
  }

  if (s != NULL)
  {
    // 上传数据

    if (m_conf_upload_mode)
    {
      http_cb_upload_msg(s, unkown_rule,analyze, strlen(s), nullptr);
    }
    else
    {
      cJSON_free(s);
    }
  }

  return 0;
}

void CHttpParser::free_upload_http_info(upload_http_info_t *p_upload_http_info)
{
  if (NULL == p_upload_http_info)
  {
    return ;
  }

  http_req_info_t *p_req_http_info = &(p_upload_http_info->http_req_info);
  http_rsp_info_t *p_rsp_http_info = &(p_upload_http_info->http_rsp_info);
  http_file_info_t *p_http_file_info = &(p_upload_http_info->http_file_info);

  SAFE_FREE(p_req_http_info->p_req_header);
  SAFE_FREE(p_rsp_http_info->p_rsp_header);
  SAFE_FREE(p_rsp_http_info->set_cookies_list);
  SAFE_FREE(p_http_file_info->p_file_type);
  SAFE_FREE(p_http_file_info->p_file_name);
}

// HTTP解析数据的组装
int CHttpParser::http_parser_msg_routine(http_parser_msg_t *phpm)
{
  if (m_conf_upload_mode == 0)
  {
    GWLOG_ERROR(m_comm, "upload moudle not exist\n");
  }

  char *s = NULL;
  size_t s_len = 0;

  int i_upload_file = 0;
  bstring_t st_body_value;
  memset(&st_body_value, 0, sizeof(bstring_t));

  int unkown_rule = phpm->unkown_rule;//0 正常传，1 丢弃文件事件，2 文件事件，其他值 unknown_rule
  http_parser_ext_data_t hped[1] = {0};
  http_parser_ext_data_t *phped = &hped[0];
  http_header_info_t st_header_info;
  memset(&st_header_info, 0, sizeof(http_header_info_t));

  upload_http_info_t st_upload_http_info;
  memset(&st_upload_http_info, 0, sizeof(upload_http_info_t));
  st_upload_http_info.http_file_info.p_file_direction = "unknown";
  if (phped->found_gzip == 0)
  {
    // 文件解析
    get_fileinfo(&st_upload_http_info, phpm, &st_body_value);
  }

  st_header_info.p_req_url = phpm->full_uri;
  http_parser_msg_routine_req(phpm, &st_upload_http_info.http_req_info, &st_body_value);
  http_parser_msg_routine_rsp(phpm, &st_upload_http_info.http_rsp_info, phped, &st_header_info);
  st_upload_http_info.found_gzip = phped->found_gzip;

  /* 添加net信息 */
  if (phpm->addr.client.v == 4)
  {
    strcpy(st_upload_http_info.http_net_info.a_src_ip, st_upload_http_info.http_req_info.remote_addr);
    get_addr_str(phpm->addr.server.ipv4, st_upload_http_info.http_net_info.a_dst_ip, sizeof(st_upload_http_info.http_net_info.a_dst_ip));
  }
  else
  {
    strcpy(st_upload_http_info.http_net_info.a_src_ip, st_upload_http_info.http_req_info.remote_addr);
    get_ip6addr_str((uint32_t*)phpm->addr.server.ipv6, st_upload_http_info.http_net_info.a_dst_ip, sizeof(st_upload_http_info.http_net_info.a_dst_ip));
  }
  st_upload_http_info.http_net_info.src_port = phpm->addr.client.port;
  st_upload_http_info.http_net_info.dst_port = phpm->addr.server.port;

  st_upload_http_info.http_net_info.vlan_id = phpm->vlan_id;

  if (phpm->pcap_filename)
  {
    memcpy(st_upload_http_info.http_net_info.pcap_filename, phpm->pcap_filename, 255);
  }

    if (phpm->req_complete == 1 && phpm->rsp_complete == 1)
    {
        memcpy(st_upload_http_info.http_net_info.complete,"both",sizeof("both"));
    }
    else if (phpm->req_complete == 1 && phpm->rsp_complete == 0)
    {
        memcpy(st_upload_http_info.http_net_info.complete,"req",sizeof("req"));
    }
    else if (phpm->rsp_complete == 1 && phpm->req_complete == 0)
    {
        memcpy(st_upload_http_info.http_net_info.complete,"rsp",sizeof("rsp"));
    }
    else
    {
        memcpy(st_upload_http_info.http_net_info.complete,"none",sizeof("none"));
    }


  /* 添加unique_id */
  add_event_id(st_upload_http_info.a_unique_id);

  /* 添加meta信息 */
  if (1 == m_conf_pcap_timestamp)
  {
    // 取自PCAP结构里的时间信息
    st_upload_http_info.http_meta_info.ts = phpm->pcap_ts;
  }
  else
  {
    uint64_t u64_real_ms = m_comm->gw_real_time_ms();
    //st_upload_http_info.http_meta_info.ts = time(NULL);
    st_upload_http_info.http_meta_info.ts = ((double)u64_real_ms / 1000);
  }

  st_upload_http_info.http_meta_info.req_seq = phpm->req_seq;
  st_upload_http_info.http_meta_info.req_ack = phpm->req_ack;
  st_upload_http_info.http_meta_info.rsp_seq = phpm->rsp_seq;
  st_upload_http_info.http_meta_info.rsp_ack = phpm->rsp_ack;

  bool analyze = m_comm->get_gw_stats()->check_analyze_log(phpm->addr.client.ipv4,phpm->addr.server.ipv4,phpm->addr.server.port);
  if (analyze)
  {
    st_upload_http_info.analyze_flag = m_comm->get_gw_stats()->get_analyze_flag();
  }

    char str[1024] = "\0";
    if (!phped->found_gzip)
    {
      s = simple_json_encode(st_upload_http_info, unkown_rule, analyze, &s_len, str);
      phpm->is_file_event = st_upload_http_info.is_file_event;
      if (phpm->is_file_event == 1) {
        // 内存交给 下一个线程(mini_upload)来释放
        phpm->req_msg.header = {0};
        phpm->req_msg.body = {0};
        phpm->rsp_msg.header = {0};
        phpm->rsp_msg.body = {0};
      }
    }
    else
    {
        /* gzip数据不进行格式化，将结构体传到gzip队列里 */
        if (m_u32_http_gzip_deep == 0 || phped->gzip_deep <= m_u32_http_gzip_deep)
        {
            http_cb_http_gzip(&st_upload_http_info, phped, unkown_rule, i_upload_file, analyze);
            phped->pstr = NULL;
            // 内存交给 gzip解压线程来释放
            phpm->req_msg.header = {0};
            phpm->req_msg.body = {0};
            phpm->rsp_msg.header = {0};
            phpm->rsp_msg.body = {0};
        }
    }

  if (s != NULL)
  {
    // 上传数据

    if (m_is_drop_file_event)
    {
      unkown_rule = 1;
    }

    if (1 == st_upload_http_info.is_file_event)
    {
      unkown_rule = 2;
    }

    // 数据放到内部上传队列中
    free_upload_http_info(&st_upload_http_info);
    http_cb_upload_msg(s, unkown_rule, analyze, s_len, str);
  }

  // SAFE_FREE(phped->pstr);
  // phped->pstr = NULL;

  return 0;
}

int CHttpParser::http_parser_msg_routine_req(http_parser_msg_t *phpm, http_req_info_t *p_http_req_info, bstring_t *p_body_value)
{
  http_parser_half_msg_t *p_req_msg = &phpm->req_msg;
  //char *url_full = phpm->full_uri;

  /* 添加method信息 */
  p_http_req_info->p_method = http_method_str((enum http_method)phpm->method);
  p_http_req_info->http_major = phpm->http_req_ver.major;
  p_http_req_info->http_minor = phpm->http_req_ver.minor;
  /* 添加remote addr 信息 */
  if (likely(phpm->addr.client.v == 4))
  {
    get_addr_str(phpm->addr.client.ipv4, p_http_req_info->remote_addr, COUNTOF(p_http_req_info->remote_addr));
  }
  else
  {
    get_ip6addr_str((uint32_t*)phpm->addr.client.ipv6, p_http_req_info->remote_addr, COUNTOF(p_http_req_info->remote_addr));
  }

  /* 添加error code 信息 */
  p_http_req_info->error_code = phpm->req_error_code;

  /* 添加url 信息 */
  //p_http_req_info->full_url = url_full;
  memcpy(p_http_req_info->full_url, phpm->full_uri, strlen(phpm->full_uri));
  memcpy(p_http_req_info->host, phpm->host, strlen(phpm->host));
  p_http_req_info->p_header = p_req_msg->header.bstr;
  p_http_req_info->header_length = p_req_msg->header.length;
   p_http_req_info->p_body = p_req_msg->body.bstr;

  if (m_i_insert_original_req_body == 1 || p_body_value->length == 0)
  {
    p_http_req_info->body.p_value = p_req_msg->body.bstr;
    p_http_req_info->body.length = p_req_msg->body.length;
  }
  else
  {
    p_http_req_info->body.p_value = p_body_value->bstr;
    p_http_req_info->body.length = p_body_value->length;
  }

  /* 添加 request header 信息 */
  add_header_array_to_json(&(p_http_req_info->p_req_header),  &(p_http_req_info->req_header_num), p_req_msg->header_item, p_req_msg->header_item_num);

  return 0;
}

int CHttpParser::http_parser_fast_msg_routine_rsp(http_parser_msg_t *phpm, bstr_t *p_rsp_content_type, http_parser_ext_data_t *phped)
{
  http_parser_half_msg_t *p_rsp_msg = &phpm->rsp_msg;
  if (p_rsp_msg->header_item != NULL && p_rsp_msg->header_item_num > 0)
  {
    ssize_t header_item_num = p_rsp_msg->header_item_num;
    http_header_item_t *header = p_rsp_msg->header_item;

    while (header_item_num > 0)
    {
      if (0 == my_strnicmp(&header->field, "Content-Type"))
      {
        p_rsp_content_type->p_value = header->value.bstr;
        p_rsp_content_type->length = header->value.length;
        break;
      }

      header_item_num--;
      header++;
    }
  }

  return 0;
}

int CHttpParser::http_parser_msg_routine_rsp(http_parser_msg *phpm, http_rsp_info *p_http_rsp_info, http_parser_ext_data *phped, http_header_info *p_http_header_info)
{
  http_parser_half_msg_t *p_rsp_msg = &phpm->rsp_msg;
  const int http_gzip_mode = phpm->http_gzip_mode; // m_conf_http_gzip_mode;
  size_t length = 0;
  int found_gzip = 0;
  int gzip_deep = 1;
  int malloc_size = 16;

  p_http_rsp_info->status_code = phpm->status_code;
  p_http_rsp_info->http_major = phpm->http_rsp_ver.major;
  p_http_rsp_info->http_minor = phpm->http_rsp_ver.minor;

  p_http_rsp_info->error_code = phpm->rsp_error_code;
  p_http_rsp_info->p_header = p_rsp_msg->header.bstr;
  p_http_rsp_info->header_length = p_rsp_msg->header.length;
  add_header_array_to_json(&(p_http_rsp_info->p_rsp_header), &(p_http_rsp_info->rsp_header_num), p_rsp_msg->header_item, p_rsp_msg->header_item_num);

  if (p_rsp_msg->header_item != NULL && p_rsp_msg->header_item_num > 0)
  {
    ssize_t header_item_num = p_rsp_msg->header_item_num;
    http_header_item_t *header = p_rsp_msg->header_item;

    while (header_item_num > 0)
    {
      if (0 == my_strnicmp(&header->field, "Set-Cookie"))
      {
        if (p_http_rsp_info->set_cookies_list == NULL)
        {
          p_http_rsp_info->set_cookies_list = (bstr_t *)malloc(sizeof(bstr_t) * malloc_size);
          memset(p_http_rsp_info->set_cookies_list, 0, sizeof(bstr_t) * malloc_size);
        }

        if (p_http_rsp_info->cookies_list_num == malloc_size)
        {
          malloc_size += malloc_size;
          bstr_t *cookies_list_tmp = (bstr_t *)malloc(sizeof(bstr_t) * malloc_size);
          memset(cookies_list_tmp, 0, sizeof(bstr_t) * malloc_size);
          memcpy(cookies_list_tmp,  p_http_rsp_info->set_cookies_list, p_http_rsp_info->cookies_list_num * sizeof(bstr_t));
          free(p_http_rsp_info->set_cookies_list);
          p_http_rsp_info->set_cookies_list = cookies_list_tmp;
        }
        p_http_rsp_info->set_cookies_list[p_http_rsp_info->cookies_list_num].p_value = header->value.bstr;
        p_http_rsp_info->set_cookies_list[p_http_rsp_info->cookies_list_num].length = header->value.length;
        p_http_rsp_info->cookies_list_num++;
      }
      header_item_num--;
      header++;
    }
  }

  if (phpm->rsp_hdr_content_type.bstr)
  {
    memcpy(p_http_header_info->a_rsp_content_type, phpm->rsp_hdr_content_type.bstr, MIN(phpm->rsp_hdr_content_type.length, sizeof(p_http_header_info->a_rsp_content_type) - 1));
  }

  if (phpm->rsp_hdr_content_disposition.bstr)
  {
    memcpy(p_http_header_info->a_rsp_disposition, phpm->rsp_hdr_content_disposition.bstr, MIN(phpm->rsp_hdr_content_disposition.length, sizeof(p_http_header_info->a_rsp_disposition) - 1));
  }

  if (phpm->rsp_hdr_content_range.bstr)
  {
    memcpy(p_http_header_info->a_rsp_content_range, phpm->rsp_hdr_content_range.bstr, MIN(phpm->rsp_hdr_content_range.length, sizeof(p_http_header_info->a_rsp_content_range) - 1));
  }
    // 封装数据结构，并发送给服务器
    length = p_rsp_msg->body.length;

    if (p_rsp_msg->is_chunked == 0)
    {
        if (p_rsp_msg->content_length > 0 && p_rsp_msg->content_length != ULLONG_MAX)
        {
            // u64_content_length = p_rsp_msg->content_length;
        }
    }

    if (0 == bstr_icmp_string(&phpm->rsp_hdr_content_encoding, "gzip") || 0 == bstr_icmp_string(&phpm->rsp_hdr_content_encoding, "deflate"))
    {
        gzip_deep = 1;
        found_gzip = 1;
    }
    else
    {
        // gzip 支持嵌套解压，解压嵌套的深度
        int deep = bstr_get_count_string(&phpm->rsp_hdr_content_encoding, "gzip");
        if (deep > 0)
        {
            gzip_deep = deep;
            found_gzip = 1;
        }

        if (http_gzip_mode == 2) /* 只支持解压一层 */
        {
            gzip_deep = 1;
        }
    }

    if (found_gzip)
    {
        phpm->found_gzip = 1;
    }

    if (found_gzip == 0 || (found_gzip == 1 && http_gzip_mode == 0)) /* 是gzip数据但是不解压 */
    {
        p_http_rsp_info->body.p_value = p_rsp_msg->body.bstr;
        p_http_rsp_info->body.length = length;
    }


  if (found_gzip == 1 && http_gzip_mode > 0)
  {
    phped->found_gzip = found_gzip;
    phped->gzip_deep = gzip_deep;
  }
  else
  {
    phped->found_gzip = 0;
    phped->gzip_deep = 0;
  }
  phped->pstr = (char*)p_rsp_msg->body.bstr;
//   phped->length = u64_content_length > 0 ? u64_content_length : length;
  phped->length = length;

  return 0;
}

void CHttpParser::http_cb_upload_msg(const char *s, int unkown_rule,bool analyze, size_t s_len, char *str)
{
  //size_t length = strlen(s);
  size_t length  = s_len;
  http_cb_upstream(s, length);

  if (unlikely(m_p_upload == NULL))
  {
    // GWLOG_INFO(m_comm, "upload null(%s)\n", m_conf_upload_name.c_str());
    cJSON_free((void*)s);
    return;
  }

  UploadMsg *pum = new UploadMsg;
  memset(pum, 0, sizeof(UploadMsg));

  pum->cb = sizeof(UploadMsg);
  pum->destroy_func = free_upload_msg;
  pum->parser = this;
  pum->length = length;
  pum->s = s;
  pum->log_for_analyze = analyze;

  if(str)
  {
    memset(pum->context,0,sizeof(pum->context));
    strcpy(pum->context,str);
  }

  switch (unkown_rule)
  {
  case 0:
    pum->msgtype = msg_ruled_type;
    break;
  case 1:
    pum->msgtype = msg_drop_file_type;
    break;
  case 2:
    pum->msgtype = msg_file_type;
    break;
  default:
    pum->msgtype = msg_unknown_rule_type;
  }

  pum->mem_size = sizeof(UploadMsg) + pum->length;

  m_p_upload->put_msg(pum);
}

void CHttpParser::free_upload_msg(const struct UploadMsg *pum)
{
  CHttpParser *pThis = (CHttpParser *)pum->parser;
  ASSERT(pThis != NULL);
  (void)pThis;

  //cJSON_free((void*)pum->s);

  delete pum;
}

int CHttpParser::http_cb_check_forward(parser_request_response_params *p_req_rsp_param, CSession *p_session, http_parser_ext *p_req_parser, http_parser_ext *p_rsp_parser, int *p_unkown_rule)
{
  int forward_reason = 0;
  int forward = 1; //0 不转发 1 转发

  // __sync_fetch_and_add(&g_stats_url_fwd.cnt, 1);

  if (NULL == p_req_parser || NULL == p_rsp_parser)
  {
    if (m_conf_multi_queue_forward_enable)
    {
      if (p_unkown_rule != NULL)
      {
        // 未配置过规则，全部转发到未配置规则的队列中
        *p_unkown_rule = 1;
      }
    }
    // __sync_fetch_and_add(&g_stats_url_fwd.cnt_fwd, 1);
    return forward;
  }

  if (p_req_parser->message_complete && p_rsp_parser->message_complete)
  {
    //__sync_fetch_and_add(&g_stats_url_fwd.cnt, 1);
  }

  //char suburl[255] = {0};
  // snprintf(suburl, sizeof(suburl)-1, "%s://%.*s", p_req_parser->schema,
  //           p_req_parser->host.length,
  //           p_req_parser->host.bstr);

  forward = m_urlfilter_rule->check_forward(p_req_parser->full_uri, p_req_parser->sub_url, 0, &forward_reason);

  if (1 == forward)
  {
#ifdef DEBUG
    printf("fullurl:%s, suburl:%s, forward:%d\n", p_req_parser->full_uri, p_req_parser->sub_url, forward);
    printf("forward_reason:%d\n", forward_reason);
#endif
    if (p_req_parser->message_complete && p_rsp_parser->message_complete)
    {
      //__sync_fetch_and_add(&g_stats_url_fwd.cnt_fwd, 1);
    }
    if (forward_reason == MATCH_FORWARD_RULE)
    {
      // 命中规则
      if (p_req_parser->message_complete && p_rsp_parser->message_complete)
      {
        //__sync_fetch_and_add(&g_stats_url_fwd.cnt_fwd_hit, 1);
      }
      if (m_conf_multi_queue_forward_enable && (p_unkown_rule != NULL))
      {
        *p_unkown_rule = 0;
      }
    }
    else
    {
      // 未命中规则
      if (p_req_parser->message_complete && p_rsp_parser->message_complete)
      {
        //__sync_fetch_and_add(&g_stats_url_fwd.cnt_fwd_unknown, 1);
      }
      if (m_conf_multi_queue_forward_enable && (p_unkown_rule != NULL))
      {

        *p_unkown_rule = 1;
      }
    }

    return forward;
  }
  else
  {

#ifdef DEBUG
    printf("fullurl:%s, suburl:%s, forward:%d\n", p_req_parser->full_uri, p_req_parser->sub_url, forward);
#endif
  }

  // 从当前会话中，删除节点信息；
  // 从当前节点中，删除节点信息；
  //http_stream_t *phs = p_session->get_stream_data_from_parser(this)->p_http_stream;
  http_stream_t *phs = p_session->get_stream_data_from_type(m_http_type)->p_http_stream;
  free_session_http_parser(phs, HTTP_REQUEST, p_req_parser);
  free_session_http_parser(phs, HTTP_RESPONSE, p_rsp_parser);
  p_req_rsp_param->free_header_chunk_data = 0;

  return forward;
}

void CHttpParser::http_cb_parser_user_info_inner(char *full_uri, bstring_t *p_hdr_cookie, cJSON *session, cJSON *c_name, cJSON *c_uid)
{
  int i;
  user_info_rule_t *p = NULL;
  user_info_rule_t *pp;
  rule_user_info_t *rule_user_info_ptr = m_accoutfilter_rule->get_accout_info();

  if (rule_user_info_ptr == NULL)
  {
    return;
  }

  if (rule_user_info_ptr->rule_user_info_num <= 0)
  {
    return;
  }

  // 解析用户账号信息规则 check url
  for (i = 0; i < rule_user_info_ptr->rule_user_info_num; ++i)
  {
    pp = &rule_user_info_ptr->rule_user_info[i];
    // // url字符串完整匹配
    // if (pp != NULL && pp->url != NULL && 0 == strcasecmp(full_uri, pp->url))
    // {
    //   p = pp;
    //   break;
    // }
    // url字符串前缀匹配
    if (pp != NULL && pp->url != NULL && 0 == strncasecmp(full_uri, pp->url, strlen(pp->url)))
    {
      p = pp;
      break;
    }
  }

  if (p != NULL)
  {
    // 解析访问接口行为时的用户信息 （从cookie中获取)
    size_t length = p_hdr_cookie->length;
    const char *ss = p_hdr_cookie->bstr;
    const char *r_keys = p->keys;           // const char r_keys[] = "JSESSIONID\0" "\0\0";
    const char *r_keys_name = p->keys_name; // const char r_keys_name[] = "nickname\0" "\0\0";
    const char *r_keys_uid = p->keys_uid;   //  const char r_keys_uid[] = "uid\0" "\0\0";
    rule_key_t rk[] = {
        {session, r_keys},
        {c_name, r_keys_name},
        {c_uid, r_keys_uid},
        {NULL, NULL},
    };

    m_accoutfilter_rule->rule_user_extract(ss, length, rk);
  }
}

void CHttpParser::parser_error_check(http_req_info_t* req_info, http_rsp_info_t* rsp_info, http_meta_info_t* meta)
{

    char* url = req_info->full_url;
    //先进行黑白名单判断
    if (!m_post_file_url->have_filter() || !m_post_file_url->hit_url_filter(url))
    {
       if (m_url_check_error_filter->have_filter() && m_url_check_error_filter->hit_url_filter(url))
        {
            return;
        }
    }

    if (m_check_session_match_enable == 1)
    {
        check_session_match_err(req_info, rsp_info, meta);
    }

    if (m_check_exist_chunk_flag_enable == 1)
    {
        check_exist_chunk_flag(req_info, rsp_info, meta);
    }

    if (m_check_multi_rsp_header_enable == 1)
    {
        check_multi_rsp_headers(req_info, rsp_info, meta);
    }

    if(m_check_header_parse_err_enable == 1)
    {
        check_headers_parse_err(req_info, rsp_info, meta);
    }
}

void CHttpParser::check_session_match_err(http_req_info_t* req_info, http_rsp_info_t* rsp_info, http_meta_info_t* meta)
{
   const char *url = req_info->full_url;
   int req_ack_num = meta->req_ack;
   int req_header_num = req_info->req_header_num;
   keyvalue_info_t* req_header_item = req_info->p_req_header;
   int rsp_header_num = rsp_info->rsp_header_num;
   keyvalue_info_t* rsp_header_item = rsp_info->p_rsp_header;

   int i = 0;
   const char* p_content_type = NULL;
   //先找到响应中的content-type
   unsigned int key_len = strlen("Content-Type");
   unsigned int content_type_len = 0;
   for (i = 0; i < rsp_header_num&&rsp_header_item; i++, rsp_header_item++)
   {
      if (rsp_header_item->key.length != key_len)
      {
        continue;
      }

      if (strncasecmp(rsp_header_item->key.p_value, "Content-Type", key_len) == 0)
      {
        p_content_type = rsp_header_item->value.p_value;
        content_type_len = rsp_header_item->value.length;
        break;
      }
   }

   if ( !p_content_type || content_type_len == 0 )
   {
      return;
   }

   //content-type还需要做处理,如果类似application/json;charset=UTF-8则需要看下;的位置重新计算长度
   const char* semicolon = NULL;
   semicolon = my_strstr(p_content_type, content_type_len, ";", 1);
   if (semicolon)
   {
        content_type_len = semicolon - p_content_type;
   }

   const char* p_accept = NULL;
   //再查找请求中是否存在Accept
   key_len = strlen("Accept");
   unsigned int accept_len = 0;
   for (i = 0; i < req_header_num&&req_header_num; i++, req_header_item++)
   {
      if (req_header_item->key.length != key_len)
      {
        continue;
      }

      if (strncasecmp(req_header_item->key.p_value, "Accept", key_len) == 0)
      {
        p_accept = req_header_item->value.p_value;
        accept_len = req_header_item->value.length;
        break;
      }
   }

   if ( !p_accept || accept_len == 0 )
   {
        return;
   }

   //将content-type的内容去accept中查找，如果未找到则认为不匹配
   if (accept_len < content_type_len)
   {
        //accept可能有多个类型，长度会大于等于content-type长度
        return;
   }

   const char* p = NULL;
   p = my_strstr(p_accept, accept_len, p_content_type, content_type_len);

   if (p)
   {
        return;
   }

   GWLOG_INFO(m_comm, "请求响应匹配错误,url:%s, req_ack:%u\n", url, req_ack_num);
   return;
}

void CHttpParser::check_exist_chunk_flag(http_req_info_t* req_info, http_rsp_info_t* rsp_info, http_meta_info_t* meta)
{
    const char *url = req_info->full_url;
    int req_ack_num = meta->req_ack;
    const char* body = rsp_info->body.p_value;
    unsigned int len = rsp_info->body.length;
    const char* start = NULL;
    start = my_strstr(body , len, "\r\n", strlen("\r\n"));

    if (!start)
    {
        return;
    }

    int delta = start - body;
    // 设定chunk标志位数在2-4,body整体长度不小于20
    if ( delta > 4 || delta <= 1 || len <= 20 )
    {
        return;
    }

    const char* p = body;
    //判断body中首行是否只有4个或4个以下的字符
    //判断字符为小写字母或者数字，并且字母小于等于f，排除PNG,\r\n \*\等特殊情况
    for(; p != start; p++)
    {
        if ((*p >= '0' && *p <= '9') || (*p >= 'a'&& *p <= 'f'))
        {
            continue;
        }
        return;
    }

    GWLOG_INFO(m_comm, "响应中存在未解析的chunk的数据,url:%s, req_ack:%u\n", url, req_ack_num);

}

void CHttpParser::check_multi_rsp_headers(http_req_info_t* req_info, http_rsp_info_t* rsp_info, http_meta_info_t* meta)
{
    const char *url = req_info->full_url;
    int req_ack_num = meta->req_ack;
    const char* rsp_start = NULL;
    const char* body = rsp_info->body.p_value;
    unsigned int len = rsp_info->body.length;
    rsp_start = my_strstr(body , len, "HTTP/1.1", strlen("HTTP/1.1"));

    if (rsp_start&&(rsp_start == body))
    {
        GWLOG_INFO(m_comm, "响应中包含多个响应头,url:%s, req_ack:%u\n", url, req_ack_num);
    }

    return;
}

void CHttpParser::check_headers_parse_err(http_req_info_t* req_info, http_rsp_info_t* rsp_info, http_meta_info_t* meta)
{

   const char *url = req_info->full_url;
   int req_ack_num = meta->req_ack;
   int header_num = rsp_info->rsp_header_num;
   keyvalue_info_t* header_item = rsp_info->p_rsp_header;

   for (int i = 0; i < header_num&&header_item; i++, header_item++)
   {
     char first_char = header_item->key.p_value[0];
     if (first_char == ':')
     {
        GWLOG_INFO(m_comm, "响应头存在数据异常,url:%s, req_ack:%u\n", url, req_ack_num);
     }

   }

   return;
}

bool CHttpParser::is_req_content_type_in_white(bstring_t *p_st_req_content_type)
{
    if (NULL == p_st_req_content_type) {return false;}

    const char* file_type = get_content_type_file_type(p_st_req_content_type->bstr, p_st_req_content_type->length);
    if (NULL == file_type)
    {
        return false;
    }

    char buf[16] = {0};
    const char* p_head = file_type;
    const char* p_tail = NULL;
    int len = 0;

    while ((p_tail = strchr(p_head, ',')) != NULL)
    {
        len = p_tail - p_head;
        strncpy(buf, p_head, len);
        if (m_file_type_filter->hit_file_type_white(buf))
        {
            return true;
        }

        p_head = p_tail + 1;
    }

    if (m_file_type_filter->hit_file_type_white(p_head))
    {
        return true;
    }

    return false;
}
