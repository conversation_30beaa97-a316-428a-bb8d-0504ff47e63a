/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#ifndef __HTTP_PARSER_GZIP_TASK_WORKER_H__
#define __HTTP_PARSER_GZIP_TASK_WORKER_H__
#include <zlib.h>
#include "http_parser_task_worker.h"
#include "simple_json.h"

typedef struct thread_local_gzip_data
{
  size_t buf_maxsize;
  size_t body_buf_size;
  char *body;
  char *body2;
} thread_local_gzip_data_t;

// gzip解压数据结构
typedef struct http_gzip
{
  TaskWorkerData twd;

  size_t length;       // 未解压gzip数据长度
  char *s;             // 未解压gzip数据
  //char *json_dump_str; // 除了GZIP解压数据之外的所有结构的DUMP字符串。
  upload_http_info_t *p_upload_http_info;
  int deep;            // 嵌套解压的深度。
  int unkown_rule;     // 
  int upload_flag;     // 上传文件标识(1 表示上传文件， 0 表示下载文件)
  bool log_to_analyze;
} http_gzip_t;

class CTaskWorkerGzip : public CTaskWorkerHttp
{
public:
  virtual ~CTaskWorkerGzip();
  virtual int deal_data(const TaskWorkerData *ptwd);
  virtual void free_data(const TaskWorkerData *ptwd);
  virtual void init(void);
  virtual void fini(void);

protected:
  static thread_local thread_local_gzip_data_t m_tlgd;
  static thread_local z_stream m_zs;
  static thread_local bool m_initialized;
};

typedef struct http_gzip_parser
{
  TaskWorkerData twd;
  //char *json_dump_str; /* 除了GZIP解压数据之外的所有结构的DUMP字符串 */
  upload_http_info_t *p_upload_http_info;
  int unknown_rule;
  int upload_flag;
  bool log_to_analyze;
  size_t length;       /* 解压之后的数据长度 */
  char *s;             /* 解压的数据 */     
}http_gzip_parser_t;

class CTaskWorkerGzipParser : public CTaskWorkerHttp
{
public:
  virtual int deal_data(const TaskWorkerData *ptwd);
  virtual void free_data(const TaskWorkerData *ptwd);
};

#endif // __HTTP_PARSER_GZIP_TASK_WORKER_H__
