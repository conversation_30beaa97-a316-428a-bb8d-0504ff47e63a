#include "postgre_parser.h"
#include "postgre_parser_upload_task_worker.hpp"
#include <cstdio>

int CTaskWorkerUploadMsg::deal_data(const TaskWorkerData *data)
{
    upload_postgre_info_t *p = (upload_postgre_info_t *)data;
    return m_parser->worker_routine_postgre_upload_data_inner(p);
}

void CTaskWorkerUploadMsg::free_data(const TaskWorkerData *data)
{
    upload_postgre_info_t *p = (upload_postgre_info_t *)data;
    m_parser->free_postgre_upload_data(p);
} 