// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ProtobufRawPostgreEvent.proto

#include "ProtobufRawPostgreEvent.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG

namespace _pb = ::PROTOBUF_NAMESPACE_ID;
namespace _pbi = _pb::internal;

namespace com {
namespace quanzhi {
namespace audit_core {
namespace common {
namespace model {
PROTOBUF_CONSTEXPR ProtobufRawPostgreEvent::ProtobufRawPostgreEvent(
    ::_pbi::ConstantInitialized)
  : req_(nullptr)
  , rsp_(nullptr)
  , meta_(nullptr)
  , net_(nullptr)
  , uniqueid_(nullptr)
  , source_(nullptr)
  , mac_(nullptr){}
struct ProtobufRawPostgreEventDefaultTypeInternal {
  PROTOBUF_CONSTEXPR ProtobufRawPostgreEventDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~ProtobufRawPostgreEventDefaultTypeInternal() {}
  union {
    ProtobufRawPostgreEvent _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 ProtobufRawPostgreEventDefaultTypeInternal _ProtobufRawPostgreEvent_default_instance_;
PROTOBUF_CONSTEXPR Meta::Meta(
    ::_pbi::ConstantInitialized)
  : type_(&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{})
  , app_name_(&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{})
  , server_version_(&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{})
  , tm_(uint64_t{0u}){}
struct MetaDefaultTypeInternal {
  PROTOBUF_CONSTEXPR MetaDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~MetaDefaultTypeInternal() {}
  union {
    Meta _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 MetaDefaultTypeInternal _Meta_default_instance_;
PROTOBUF_CONSTEXPR UniqueId::UniqueId(
    ::_pbi::ConstantInitialized)
  : eventid_(&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}){}
struct UniqueIdDefaultTypeInternal {
  PROTOBUF_CONSTEXPR UniqueIdDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~UniqueIdDefaultTypeInternal() {}
  union {
    UniqueId _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 UniqueIdDefaultTypeInternal _UniqueId_default_instance_;
PROTOBUF_CONSTEXPR PostgreRequest::PostgreRequest(
    ::_pbi::ConstantInitialized)
  : db_user_(&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{})
  , db_name_(&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{})
  , db_password_(&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{})
  , cmd_type_(&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{})
  , sql_(&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{})
  , errcode_(0){}
struct PostgreRequestDefaultTypeInternal {
  PROTOBUF_CONSTEXPR PostgreRequestDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~PostgreRequestDefaultTypeInternal() {}
  union {
    PostgreRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 PostgreRequestDefaultTypeInternal _PostgreRequest_default_instance_;
PROTOBUF_CONSTEXPR PostgreResponse::PostgreResponse(
    ::_pbi::ConstantInitialized)
  : result_set_(nullptr)
  , start_time_(uint64_t{0u})
  , status_(0)
  , row_count_(0)
  , close_time_(uint64_t{0u})
  , errcode_(0){}
struct PostgreResponseDefaultTypeInternal {
  PROTOBUF_CONSTEXPR PostgreResponseDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~PostgreResponseDefaultTypeInternal() {}
  union {
    PostgreResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 PostgreResponseDefaultTypeInternal _PostgreResponse_default_instance_;
PROTOBUF_CONSTEXPR ResultSet::ResultSet(
    ::_pbi::ConstantInitialized)
  : field_names_()
  , rows_()
  , field_count_(0){}
struct ResultSetDefaultTypeInternal {
  PROTOBUF_CONSTEXPR ResultSetDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~ResultSetDefaultTypeInternal() {}
  union {
    ResultSet _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 ResultSetDefaultTypeInternal _ResultSet_default_instance_;
PROTOBUF_CONSTEXPR ResultRow::ResultRow(
    ::_pbi::ConstantInitialized)
  : field_values_(){}
struct ResultRowDefaultTypeInternal {
  PROTOBUF_CONSTEXPR ResultRowDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~ResultRowDefaultTypeInternal() {}
  union {
    ResultRow _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 ResultRowDefaultTypeInternal _ResultRow_default_instance_;
PROTOBUF_CONSTEXPR Net::Net(
    ::_pbi::ConstantInitialized)
  : srcip_(&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{})
  , dstip_(&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{})
  , flowsource_(&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{})
  , srcport_(0)
  , dstport_(0){}
struct NetDefaultTypeInternal {
  PROTOBUF_CONSTEXPR NetDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~NetDefaultTypeInternal() {}
  union {
    Net _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 NetDefaultTypeInternal _Net_default_instance_;
PROTOBUF_CONSTEXPR Mac::Mac(
    ::_pbi::ConstantInitialized)
  : mac_(&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}){}
struct MacDefaultTypeInternal {
  PROTOBUF_CONSTEXPR MacDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~MacDefaultTypeInternal() {}
  union {
    Mac _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 MacDefaultTypeInternal _Mac_default_instance_;
PROTOBUF_CONSTEXPR Source::Source(
    ::_pbi::ConstantInitialized)
  : taskid_(&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{})
  , app_(&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{})
  , sourcetype_(0)
{}
struct SourceDefaultTypeInternal {
  PROTOBUF_CONSTEXPR SourceDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~SourceDefaultTypeInternal() {}
  union {
    Source _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 SourceDefaultTypeInternal _Source_default_instance_;
}  // namespace model
}  // namespace common
}  // namespace audit_core
}  // namespace quanzhi
}  // namespace com
static ::_pb::Metadata file_level_metadata_ProtobufRawPostgreEvent_2eproto[10];
static const ::_pb::EnumDescriptor* file_level_enum_descriptors_ProtobufRawPostgreEvent_2eproto[1];
static constexpr ::_pb::ServiceDescriptor const** file_level_service_descriptors_ProtobufRawPostgreEvent_2eproto = nullptr;

const uint32_t TableStruct_ProtobufRawPostgreEvent_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::ProtobufRawPostgreEvent, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::ProtobufRawPostgreEvent, req_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::ProtobufRawPostgreEvent, rsp_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::ProtobufRawPostgreEvent, meta_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::ProtobufRawPostgreEvent, net_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::ProtobufRawPostgreEvent, uniqueid_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::ProtobufRawPostgreEvent, source_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::ProtobufRawPostgreEvent, mac_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::Meta, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::Meta, tm_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::Meta, type_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::Meta, app_name_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::Meta, server_version_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::UniqueId, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::UniqueId, eventid_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::PostgreRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::PostgreRequest, db_user_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::PostgreRequest, db_name_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::PostgreRequest, db_password_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::PostgreRequest, cmd_type_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::PostgreRequest, sql_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::PostgreRequest, errcode_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::PostgreResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::PostgreResponse, status_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::PostgreResponse, start_time_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::PostgreResponse, close_time_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::PostgreResponse, row_count_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::PostgreResponse, result_set_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::PostgreResponse, errcode_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::ResultSet, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::ResultSet, field_names_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::ResultSet, rows_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::ResultSet, field_count_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::ResultRow, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::ResultRow, field_values_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::Net, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::Net, srcip_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::Net, srcport_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::Net, dstip_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::Net, dstport_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::Net, flowsource_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::Mac, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::Mac, mac_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::Source, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::Source, sourcetype_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::Source, taskid_),
  PROTOBUF_FIELD_OFFSET(::com::quanzhi::audit_core::common::model::Source, app_),
};
static const ::_pbi::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::com::quanzhi::audit_core::common::model::ProtobufRawPostgreEvent)},
  { 13, -1, -1, sizeof(::com::quanzhi::audit_core::common::model::Meta)},
  { 23, -1, -1, sizeof(::com::quanzhi::audit_core::common::model::UniqueId)},
  { 30, -1, -1, sizeof(::com::quanzhi::audit_core::common::model::PostgreRequest)},
  { 42, -1, -1, sizeof(::com::quanzhi::audit_core::common::model::PostgreResponse)},
  { 54, -1, -1, sizeof(::com::quanzhi::audit_core::common::model::ResultSet)},
  { 63, -1, -1, sizeof(::com::quanzhi::audit_core::common::model::ResultRow)},
  { 70, -1, -1, sizeof(::com::quanzhi::audit_core::common::model::Net)},
  { 81, -1, -1, sizeof(::com::quanzhi::audit_core::common::model::Mac)},
  { 88, -1, -1, sizeof(::com::quanzhi::audit_core::common::model::Source)},
};

static const ::_pb::Message* const file_default_instances[] = {
  &::com::quanzhi::audit_core::common::model::_ProtobufRawPostgreEvent_default_instance_._instance,
  &::com::quanzhi::audit_core::common::model::_Meta_default_instance_._instance,
  &::com::quanzhi::audit_core::common::model::_UniqueId_default_instance_._instance,
  &::com::quanzhi::audit_core::common::model::_PostgreRequest_default_instance_._instance,
  &::com::quanzhi::audit_core::common::model::_PostgreResponse_default_instance_._instance,
  &::com::quanzhi::audit_core::common::model::_ResultSet_default_instance_._instance,
  &::com::quanzhi::audit_core::common::model::_ResultRow_default_instance_._instance,
  &::com::quanzhi::audit_core::common::model::_Net_default_instance_._instance,
  &::com::quanzhi::audit_core::common::model::_Mac_default_instance_._instance,
  &::com::quanzhi::audit_core::common::model::_Source_default_instance_._instance,
};

const char descriptor_table_protodef_ProtobufRawPostgreEvent_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\035ProtobufRawPostgreEvent.proto\022#com.qua"
  "nzhi.audit_core.common.model\"\303\003\n\027Protobu"
  "fRawPostgreEvent\022@\n\003req\030\001 \001(\01323.com.quan"
  "zhi.audit_core.common.model.PostgreReque"
  "st\022A\n\003rsp\030\002 \001(\01324.com.quanzhi.audit_core"
  ".common.model.PostgreResponse\0227\n\004meta\030\003 "
  "\001(\0132).com.quanzhi.audit_core.common.mode"
  "l.Meta\0225\n\003net\030\004 \001(\0132(.com.quanzhi.audit_"
  "core.common.model.Net\022\?\n\010uniqueId\030\005 \001(\0132"
  "-.com.quanzhi.audit_core.common.model.Un"
  "iqueId\022;\n\006source\030\006 \001(\0132+.com.quanzhi.aud"
  "it_core.common.model.Source\0225\n\003mac\030\007 \001(\013"
  "2(.com.quanzhi.audit_core.common.model.M"
  "ac\"J\n\004Meta\022\n\n\002tm\030\001 \001(\004\022\014\n\004type\030\002 \001(\t\022\020\n\010"
  "app_name\030\003 \001(\t\022\026\n\016server_version\030\004 \001(\t\"\033"
  "\n\010UniqueId\022\017\n\007eventId\030\001 \001(\t\"w\n\016PostgreRe"
  "quest\022\017\n\007db_user\030\001 \001(\t\022\017\n\007db_name\030\002 \001(\t\022"
  "\023\n\013db_password\030\003 \001(\t\022\020\n\010cmd_type\030\004 \001(\t\022\013"
  "\n\003sql\030\005 \001(\t\022\017\n\007errCode\030\006 \001(\005\"\261\001\n\017Postgre"
  "Response\022\016\n\006status\030\001 \001(\005\022\022\n\nstart_time\030\002"
  " \001(\004\022\022\n\nclose_time\030\003 \001(\004\022\021\n\trow_count\030\004 "
  "\001(\005\022B\n\nresult_set\030\005 \001(\0132..com.quanzhi.au"
  "dit_core.common.model.ResultSet\022\017\n\007errCo"
  "de\030\006 \001(\005\"s\n\tResultSet\022\023\n\013field_names\030\001 \003"
  "(\t\022<\n\004rows\030\002 \003(\0132..com.quanzhi.audit_cor"
  "e.common.model.ResultRow\022\023\n\013field_count\030"
  "\003 \001(\005\"!\n\tResultRow\022\024\n\014field_values\030\001 \003(\t"
  "\"Y\n\003Net\022\r\n\005srcIp\030\001 \001(\t\022\017\n\007srcPort\030\002 \001(\005\022"
  "\r\n\005dstIp\030\003 \001(\t\022\017\n\007dstPort\030\004 \001(\005\022\022\n\nflowS"
  "ource\030\005 \001(\t\"\022\n\003Mac\022\013\n\003mac\030\001 \001(\t\"n\n\006Sourc"
  "e\022G\n\nsourceType\030\001 \001(\01623.com.quanzhi.audi"
  "t_core.common.model.SourceTypeEnum\022\016\n\006ta"
  "skId\030\002 \001(\t\022\013\n\003app\030\003 \001(\t*\'\n\016SourceTypeEnu"
  "m\022\013\n\007app_har\020\000\022\010\n\004flow\020\001B&B$ProtobufRawP"
  "ostgreEvent$$ByJProtobufb\006proto3"
  ;
static ::_pbi::once_flag descriptor_table_ProtobufRawPostgreEvent_2eproto_once;
const ::_pbi::DescriptorTable descriptor_table_ProtobufRawPostgreEvent_2eproto = {
    false, false, 1392, descriptor_table_protodef_ProtobufRawPostgreEvent_2eproto,
    "ProtobufRawPostgreEvent.proto",
    &descriptor_table_ProtobufRawPostgreEvent_2eproto_once, nullptr, 0, 10,
    schemas, file_default_instances, TableStruct_ProtobufRawPostgreEvent_2eproto::offsets,
    file_level_metadata_ProtobufRawPostgreEvent_2eproto, file_level_enum_descriptors_ProtobufRawPostgreEvent_2eproto,
    file_level_service_descriptors_ProtobufRawPostgreEvent_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::_pbi::DescriptorTable* descriptor_table_ProtobufRawPostgreEvent_2eproto_getter() {
  return &descriptor_table_ProtobufRawPostgreEvent_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY2 static ::_pbi::AddDescriptorsRunner dynamic_init_dummy_ProtobufRawPostgreEvent_2eproto(&descriptor_table_ProtobufRawPostgreEvent_2eproto);
namespace com {
namespace quanzhi {
namespace audit_core {
namespace common {
namespace model {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* SourceTypeEnum_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_ProtobufRawPostgreEvent_2eproto);
  return file_level_enum_descriptors_ProtobufRawPostgreEvent_2eproto[0];
}
bool SourceTypeEnum_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
      return true;
    default:
      return false;
  }
}


// ===================================================================

class ProtobufRawPostgreEvent::_Internal {
 public:
  static const ::com::quanzhi::audit_core::common::model::PostgreRequest& req(const ProtobufRawPostgreEvent* msg);
  static const ::com::quanzhi::audit_core::common::model::PostgreResponse& rsp(const ProtobufRawPostgreEvent* msg);
  static const ::com::quanzhi::audit_core::common::model::Meta& meta(const ProtobufRawPostgreEvent* msg);
  static const ::com::quanzhi::audit_core::common::model::Net& net(const ProtobufRawPostgreEvent* msg);
  static const ::com::quanzhi::audit_core::common::model::UniqueId& uniqueid(const ProtobufRawPostgreEvent* msg);
  static const ::com::quanzhi::audit_core::common::model::Source& source(const ProtobufRawPostgreEvent* msg);
  static const ::com::quanzhi::audit_core::common::model::Mac& mac(const ProtobufRawPostgreEvent* msg);
};

const ::com::quanzhi::audit_core::common::model::PostgreRequest&
ProtobufRawPostgreEvent::_Internal::req(const ProtobufRawPostgreEvent* msg) {
  return *msg->req_;
}
const ::com::quanzhi::audit_core::common::model::PostgreResponse&
ProtobufRawPostgreEvent::_Internal::rsp(const ProtobufRawPostgreEvent* msg) {
  return *msg->rsp_;
}
const ::com::quanzhi::audit_core::common::model::Meta&
ProtobufRawPostgreEvent::_Internal::meta(const ProtobufRawPostgreEvent* msg) {
  return *msg->meta_;
}
const ::com::quanzhi::audit_core::common::model::Net&
ProtobufRawPostgreEvent::_Internal::net(const ProtobufRawPostgreEvent* msg) {
  return *msg->net_;
}
const ::com::quanzhi::audit_core::common::model::UniqueId&
ProtobufRawPostgreEvent::_Internal::uniqueid(const ProtobufRawPostgreEvent* msg) {
  return *msg->uniqueid_;
}
const ::com::quanzhi::audit_core::common::model::Source&
ProtobufRawPostgreEvent::_Internal::source(const ProtobufRawPostgreEvent* msg) {
  return *msg->source_;
}
const ::com::quanzhi::audit_core::common::model::Mac&
ProtobufRawPostgreEvent::_Internal::mac(const ProtobufRawPostgreEvent* msg) {
  return *msg->mac_;
}
ProtobufRawPostgreEvent::ProtobufRawPostgreEvent(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  // @@protoc_insertion_point(arena_constructor:com.quanzhi.audit_core.common.model.ProtobufRawPostgreEvent)
}
ProtobufRawPostgreEvent::ProtobufRawPostgreEvent(const ProtobufRawPostgreEvent& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_req()) {
    req_ = new ::com::quanzhi::audit_core::common::model::PostgreRequest(*from.req_);
  } else {
    req_ = nullptr;
  }
  if (from._internal_has_rsp()) {
    rsp_ = new ::com::quanzhi::audit_core::common::model::PostgreResponse(*from.rsp_);
  } else {
    rsp_ = nullptr;
  }
  if (from._internal_has_meta()) {
    meta_ = new ::com::quanzhi::audit_core::common::model::Meta(*from.meta_);
  } else {
    meta_ = nullptr;
  }
  if (from._internal_has_net()) {
    net_ = new ::com::quanzhi::audit_core::common::model::Net(*from.net_);
  } else {
    net_ = nullptr;
  }
  if (from._internal_has_uniqueid()) {
    uniqueid_ = new ::com::quanzhi::audit_core::common::model::UniqueId(*from.uniqueid_);
  } else {
    uniqueid_ = nullptr;
  }
  if (from._internal_has_source()) {
    source_ = new ::com::quanzhi::audit_core::common::model::Source(*from.source_);
  } else {
    source_ = nullptr;
  }
  if (from._internal_has_mac()) {
    mac_ = new ::com::quanzhi::audit_core::common::model::Mac(*from.mac_);
  } else {
    mac_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:com.quanzhi.audit_core.common.model.ProtobufRawPostgreEvent)
}

inline void ProtobufRawPostgreEvent::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&req_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&mac_) -
    reinterpret_cast<char*>(&req_)) + sizeof(mac_));
}

ProtobufRawPostgreEvent::~ProtobufRawPostgreEvent() {
  // @@protoc_insertion_point(destructor:com.quanzhi.audit_core.common.model.ProtobufRawPostgreEvent)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void ProtobufRawPostgreEvent::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete req_;
  if (this != internal_default_instance()) delete rsp_;
  if (this != internal_default_instance()) delete meta_;
  if (this != internal_default_instance()) delete net_;
  if (this != internal_default_instance()) delete uniqueid_;
  if (this != internal_default_instance()) delete source_;
  if (this != internal_default_instance()) delete mac_;
}

void ProtobufRawPostgreEvent::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ProtobufRawPostgreEvent::Clear() {
// @@protoc_insertion_point(message_clear_start:com.quanzhi.audit_core.common.model.ProtobufRawPostgreEvent)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && req_ != nullptr) {
    delete req_;
  }
  req_ = nullptr;
  if (GetArenaForAllocation() == nullptr && rsp_ != nullptr) {
    delete rsp_;
  }
  rsp_ = nullptr;
  if (GetArenaForAllocation() == nullptr && meta_ != nullptr) {
    delete meta_;
  }
  meta_ = nullptr;
  if (GetArenaForAllocation() == nullptr && net_ != nullptr) {
    delete net_;
  }
  net_ = nullptr;
  if (GetArenaForAllocation() == nullptr && uniqueid_ != nullptr) {
    delete uniqueid_;
  }
  uniqueid_ = nullptr;
  if (GetArenaForAllocation() == nullptr && source_ != nullptr) {
    delete source_;
  }
  source_ = nullptr;
  if (GetArenaForAllocation() == nullptr && mac_ != nullptr) {
    delete mac_;
  }
  mac_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ProtobufRawPostgreEvent::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .com.quanzhi.audit_core.common.model.PostgreRequest req = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_req(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .com.quanzhi.audit_core.common.model.PostgreResponse rsp = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_rsp(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .com.quanzhi.audit_core.common.model.Meta meta = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_meta(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .com.quanzhi.audit_core.common.model.Net net = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_net(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .com.quanzhi.audit_core.common.model.UniqueId uniqueId = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_uniqueid(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .com.quanzhi.audit_core.common.model.Source source = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          ptr = ctx->ParseMessage(_internal_mutable_source(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .com.quanzhi.audit_core.common.model.Mac mac = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 58)) {
          ptr = ctx->ParseMessage(_internal_mutable_mac(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ProtobufRawPostgreEvent::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:com.quanzhi.audit_core.common.model.ProtobufRawPostgreEvent)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .com.quanzhi.audit_core.common.model.PostgreRequest req = 1;
  if (this->_internal_has_req()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, _Internal::req(this),
        _Internal::req(this).GetCachedSize(), target, stream);
  }

  // .com.quanzhi.audit_core.common.model.PostgreResponse rsp = 2;
  if (this->_internal_has_rsp()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, _Internal::rsp(this),
        _Internal::rsp(this).GetCachedSize(), target, stream);
  }

  // .com.quanzhi.audit_core.common.model.Meta meta = 3;
  if (this->_internal_has_meta()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(3, _Internal::meta(this),
        _Internal::meta(this).GetCachedSize(), target, stream);
  }

  // .com.quanzhi.audit_core.common.model.Net net = 4;
  if (this->_internal_has_net()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(4, _Internal::net(this),
        _Internal::net(this).GetCachedSize(), target, stream);
  }

  // .com.quanzhi.audit_core.common.model.UniqueId uniqueId = 5;
  if (this->_internal_has_uniqueid()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(5, _Internal::uniqueid(this),
        _Internal::uniqueid(this).GetCachedSize(), target, stream);
  }

  // .com.quanzhi.audit_core.common.model.Source source = 6;
  if (this->_internal_has_source()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(6, _Internal::source(this),
        _Internal::source(this).GetCachedSize(), target, stream);
  }

  // .com.quanzhi.audit_core.common.model.Mac mac = 7;
  if (this->_internal_has_mac()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(7, _Internal::mac(this),
        _Internal::mac(this).GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:com.quanzhi.audit_core.common.model.ProtobufRawPostgreEvent)
  return target;
}

size_t ProtobufRawPostgreEvent::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.quanzhi.audit_core.common.model.ProtobufRawPostgreEvent)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .com.quanzhi.audit_core.common.model.PostgreRequest req = 1;
  if (this->_internal_has_req()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *req_);
  }

  // .com.quanzhi.audit_core.common.model.PostgreResponse rsp = 2;
  if (this->_internal_has_rsp()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *rsp_);
  }

  // .com.quanzhi.audit_core.common.model.Meta meta = 3;
  if (this->_internal_has_meta()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *meta_);
  }

  // .com.quanzhi.audit_core.common.model.Net net = 4;
  if (this->_internal_has_net()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *net_);
  }

  // .com.quanzhi.audit_core.common.model.UniqueId uniqueId = 5;
  if (this->_internal_has_uniqueid()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *uniqueid_);
  }

  // .com.quanzhi.audit_core.common.model.Source source = 6;
  if (this->_internal_has_source()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *source_);
  }

  // .com.quanzhi.audit_core.common.model.Mac mac = 7;
  if (this->_internal_has_mac()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *mac_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ProtobufRawPostgreEvent::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ProtobufRawPostgreEvent::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ProtobufRawPostgreEvent::GetClassData() const { return &_class_data_; }

void ProtobufRawPostgreEvent::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ProtobufRawPostgreEvent *>(to)->MergeFrom(
      static_cast<const ProtobufRawPostgreEvent &>(from));
}


void ProtobufRawPostgreEvent::MergeFrom(const ProtobufRawPostgreEvent& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.quanzhi.audit_core.common.model.ProtobufRawPostgreEvent)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_req()) {
    _internal_mutable_req()->::com::quanzhi::audit_core::common::model::PostgreRequest::MergeFrom(from._internal_req());
  }
  if (from._internal_has_rsp()) {
    _internal_mutable_rsp()->::com::quanzhi::audit_core::common::model::PostgreResponse::MergeFrom(from._internal_rsp());
  }
  if (from._internal_has_meta()) {
    _internal_mutable_meta()->::com::quanzhi::audit_core::common::model::Meta::MergeFrom(from._internal_meta());
  }
  if (from._internal_has_net()) {
    _internal_mutable_net()->::com::quanzhi::audit_core::common::model::Net::MergeFrom(from._internal_net());
  }
  if (from._internal_has_uniqueid()) {
    _internal_mutable_uniqueid()->::com::quanzhi::audit_core::common::model::UniqueId::MergeFrom(from._internal_uniqueid());
  }
  if (from._internal_has_source()) {
    _internal_mutable_source()->::com::quanzhi::audit_core::common::model::Source::MergeFrom(from._internal_source());
  }
  if (from._internal_has_mac()) {
    _internal_mutable_mac()->::com::quanzhi::audit_core::common::model::Mac::MergeFrom(from._internal_mac());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ProtobufRawPostgreEvent::CopyFrom(const ProtobufRawPostgreEvent& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.quanzhi.audit_core.common.model.ProtobufRawPostgreEvent)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ProtobufRawPostgreEvent::IsInitialized() const {
  return true;
}

void ProtobufRawPostgreEvent::InternalSwap(ProtobufRawPostgreEvent* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ProtobufRawPostgreEvent, mac_)
      + sizeof(ProtobufRawPostgreEvent::mac_)
      - PROTOBUF_FIELD_OFFSET(ProtobufRawPostgreEvent, req_)>(
          reinterpret_cast<char*>(&req_),
          reinterpret_cast<char*>(&other->req_));
}

::PROTOBUF_NAMESPACE_ID::Metadata ProtobufRawPostgreEvent::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_ProtobufRawPostgreEvent_2eproto_getter, &descriptor_table_ProtobufRawPostgreEvent_2eproto_once,
      file_level_metadata_ProtobufRawPostgreEvent_2eproto[0]);
}

// ===================================================================

class Meta::_Internal {
 public:
};

Meta::Meta(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  // @@protoc_insertion_point(arena_constructor:com.quanzhi.audit_core.common.model.Meta)
}
Meta::Meta(const Meta& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  type_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    type_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_type().empty()) {
    type_.Set(from._internal_type(), 
      GetArenaForAllocation());
  }
  app_name_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    app_name_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_app_name().empty()) {
    app_name_.Set(from._internal_app_name(), 
      GetArenaForAllocation());
  }
  server_version_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    server_version_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_server_version().empty()) {
    server_version_.Set(from._internal_server_version(), 
      GetArenaForAllocation());
  }
  tm_ = from.tm_;
  // @@protoc_insertion_point(copy_constructor:com.quanzhi.audit_core.common.model.Meta)
}

inline void Meta::SharedCtor() {
type_.InitDefault();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  type_.Set("", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
app_name_.InitDefault();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  app_name_.Set("", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
server_version_.InitDefault();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  server_version_.Set("", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
tm_ = uint64_t{0u};
}

Meta::~Meta() {
  // @@protoc_insertion_point(destructor:com.quanzhi.audit_core.common.model.Meta)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Meta::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  type_.Destroy();
  app_name_.Destroy();
  server_version_.Destroy();
}

void Meta::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void Meta::Clear() {
// @@protoc_insertion_point(message_clear_start:com.quanzhi.audit_core.common.model.Meta)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  type_.ClearToEmpty();
  app_name_.ClearToEmpty();
  server_version_.ClearToEmpty();
  tm_ = uint64_t{0u};
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Meta::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint64 tm = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          tm_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string type = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_type();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "com.quanzhi.audit_core.common.model.Meta.type"));
        } else
          goto handle_unusual;
        continue;
      // string app_name = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_app_name();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "com.quanzhi.audit_core.common.model.Meta.app_name"));
        } else
          goto handle_unusual;
        continue;
      // string server_version = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          auto str = _internal_mutable_server_version();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "com.quanzhi.audit_core.common.model.Meta.server_version"));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Meta::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:com.quanzhi.audit_core.common.model.Meta)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint64 tm = 1;
  if (this->_internal_tm() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt64ToArray(1, this->_internal_tm(), target);
  }

  // string type = 2;
  if (!this->_internal_type().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_type().data(), static_cast<int>(this->_internal_type().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "com.quanzhi.audit_core.common.model.Meta.type");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_type(), target);
  }

  // string app_name = 3;
  if (!this->_internal_app_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_app_name().data(), static_cast<int>(this->_internal_app_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "com.quanzhi.audit_core.common.model.Meta.app_name");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_app_name(), target);
  }

  // string server_version = 4;
  if (!this->_internal_server_version().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_server_version().data(), static_cast<int>(this->_internal_server_version().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "com.quanzhi.audit_core.common.model.Meta.server_version");
    target = stream->WriteStringMaybeAliased(
        4, this->_internal_server_version(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:com.quanzhi.audit_core.common.model.Meta)
  return target;
}

size_t Meta::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.quanzhi.audit_core.common.model.Meta)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string type = 2;
  if (!this->_internal_type().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_type());
  }

  // string app_name = 3;
  if (!this->_internal_app_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_app_name());
  }

  // string server_version = 4;
  if (!this->_internal_server_version().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_server_version());
  }

  // uint64 tm = 1;
  if (this->_internal_tm() != 0) {
    total_size += ::_pbi::WireFormatLite::UInt64SizePlusOne(this->_internal_tm());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Meta::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    Meta::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Meta::GetClassData() const { return &_class_data_; }

void Meta::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<Meta *>(to)->MergeFrom(
      static_cast<const Meta &>(from));
}


void Meta::MergeFrom(const Meta& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.quanzhi.audit_core.common.model.Meta)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_type().empty()) {
    _internal_set_type(from._internal_type());
  }
  if (!from._internal_app_name().empty()) {
    _internal_set_app_name(from._internal_app_name());
  }
  if (!from._internal_server_version().empty()) {
    _internal_set_server_version(from._internal_server_version());
  }
  if (from._internal_tm() != 0) {
    _internal_set_tm(from._internal_tm());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Meta::CopyFrom(const Meta& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.quanzhi.audit_core.common.model.Meta)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Meta::IsInitialized() const {
  return true;
}

void Meta::InternalSwap(Meta* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &type_, lhs_arena,
      &other->type_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &app_name_, lhs_arena,
      &other->app_name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &server_version_, lhs_arena,
      &other->server_version_, rhs_arena
  );
  swap(tm_, other->tm_);
}

::PROTOBUF_NAMESPACE_ID::Metadata Meta::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_ProtobufRawPostgreEvent_2eproto_getter, &descriptor_table_ProtobufRawPostgreEvent_2eproto_once,
      file_level_metadata_ProtobufRawPostgreEvent_2eproto[1]);
}

// ===================================================================

class UniqueId::_Internal {
 public:
};

UniqueId::UniqueId(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  // @@protoc_insertion_point(arena_constructor:com.quanzhi.audit_core.common.model.UniqueId)
}
UniqueId::UniqueId(const UniqueId& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  eventid_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    eventid_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_eventid().empty()) {
    eventid_.Set(from._internal_eventid(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:com.quanzhi.audit_core.common.model.UniqueId)
}

inline void UniqueId::SharedCtor() {
eventid_.InitDefault();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  eventid_.Set("", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

UniqueId::~UniqueId() {
  // @@protoc_insertion_point(destructor:com.quanzhi.audit_core.common.model.UniqueId)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void UniqueId::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  eventid_.Destroy();
}

void UniqueId::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void UniqueId::Clear() {
// @@protoc_insertion_point(message_clear_start:com.quanzhi.audit_core.common.model.UniqueId)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  eventid_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* UniqueId::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string eventId = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_eventid();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "com.quanzhi.audit_core.common.model.UniqueId.eventId"));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* UniqueId::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:com.quanzhi.audit_core.common.model.UniqueId)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string eventId = 1;
  if (!this->_internal_eventid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_eventid().data(), static_cast<int>(this->_internal_eventid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "com.quanzhi.audit_core.common.model.UniqueId.eventId");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_eventid(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:com.quanzhi.audit_core.common.model.UniqueId)
  return target;
}

size_t UniqueId::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.quanzhi.audit_core.common.model.UniqueId)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string eventId = 1;
  if (!this->_internal_eventid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_eventid());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData UniqueId::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    UniqueId::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*UniqueId::GetClassData() const { return &_class_data_; }

void UniqueId::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<UniqueId *>(to)->MergeFrom(
      static_cast<const UniqueId &>(from));
}


void UniqueId::MergeFrom(const UniqueId& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.quanzhi.audit_core.common.model.UniqueId)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_eventid().empty()) {
    _internal_set_eventid(from._internal_eventid());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void UniqueId::CopyFrom(const UniqueId& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.quanzhi.audit_core.common.model.UniqueId)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool UniqueId::IsInitialized() const {
  return true;
}

void UniqueId::InternalSwap(UniqueId* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &eventid_, lhs_arena,
      &other->eventid_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata UniqueId::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_ProtobufRawPostgreEvent_2eproto_getter, &descriptor_table_ProtobufRawPostgreEvent_2eproto_once,
      file_level_metadata_ProtobufRawPostgreEvent_2eproto[2]);
}

// ===================================================================

class PostgreRequest::_Internal {
 public:
};

PostgreRequest::PostgreRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  // @@protoc_insertion_point(arena_constructor:com.quanzhi.audit_core.common.model.PostgreRequest)
}
PostgreRequest::PostgreRequest(const PostgreRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  db_user_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    db_user_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_db_user().empty()) {
    db_user_.Set(from._internal_db_user(), 
      GetArenaForAllocation());
  }
  db_name_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    db_name_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_db_name().empty()) {
    db_name_.Set(from._internal_db_name(), 
      GetArenaForAllocation());
  }
  db_password_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    db_password_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_db_password().empty()) {
    db_password_.Set(from._internal_db_password(), 
      GetArenaForAllocation());
  }
  cmd_type_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    cmd_type_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_cmd_type().empty()) {
    cmd_type_.Set(from._internal_cmd_type(), 
      GetArenaForAllocation());
  }
  sql_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    sql_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_sql().empty()) {
    sql_.Set(from._internal_sql(), 
      GetArenaForAllocation());
  }
  errcode_ = from.errcode_;
  // @@protoc_insertion_point(copy_constructor:com.quanzhi.audit_core.common.model.PostgreRequest)
}

inline void PostgreRequest::SharedCtor() {
db_user_.InitDefault();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  db_user_.Set("", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
db_name_.InitDefault();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  db_name_.Set("", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
db_password_.InitDefault();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  db_password_.Set("", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
cmd_type_.InitDefault();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  cmd_type_.Set("", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
sql_.InitDefault();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  sql_.Set("", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
errcode_ = 0;
}

PostgreRequest::~PostgreRequest() {
  // @@protoc_insertion_point(destructor:com.quanzhi.audit_core.common.model.PostgreRequest)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void PostgreRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  db_user_.Destroy();
  db_name_.Destroy();
  db_password_.Destroy();
  cmd_type_.Destroy();
  sql_.Destroy();
}

void PostgreRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void PostgreRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:com.quanzhi.audit_core.common.model.PostgreRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  db_user_.ClearToEmpty();
  db_name_.ClearToEmpty();
  db_password_.ClearToEmpty();
  cmd_type_.ClearToEmpty();
  sql_.ClearToEmpty();
  errcode_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* PostgreRequest::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string db_user = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_db_user();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "com.quanzhi.audit_core.common.model.PostgreRequest.db_user"));
        } else
          goto handle_unusual;
        continue;
      // string db_name = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_db_name();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "com.quanzhi.audit_core.common.model.PostgreRequest.db_name"));
        } else
          goto handle_unusual;
        continue;
      // string db_password = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_db_password();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "com.quanzhi.audit_core.common.model.PostgreRequest.db_password"));
        } else
          goto handle_unusual;
        continue;
      // string cmd_type = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          auto str = _internal_mutable_cmd_type();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "com.quanzhi.audit_core.common.model.PostgreRequest.cmd_type"));
        } else
          goto handle_unusual;
        continue;
      // string sql = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          auto str = _internal_mutable_sql();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "com.quanzhi.audit_core.common.model.PostgreRequest.sql"));
        } else
          goto handle_unusual;
        continue;
      // int32 errCode = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 48)) {
          errcode_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* PostgreRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:com.quanzhi.audit_core.common.model.PostgreRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string db_user = 1;
  if (!this->_internal_db_user().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_db_user().data(), static_cast<int>(this->_internal_db_user().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "com.quanzhi.audit_core.common.model.PostgreRequest.db_user");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_db_user(), target);
  }

  // string db_name = 2;
  if (!this->_internal_db_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_db_name().data(), static_cast<int>(this->_internal_db_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "com.quanzhi.audit_core.common.model.PostgreRequest.db_name");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_db_name(), target);
  }

  // string db_password = 3;
  if (!this->_internal_db_password().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_db_password().data(), static_cast<int>(this->_internal_db_password().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "com.quanzhi.audit_core.common.model.PostgreRequest.db_password");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_db_password(), target);
  }

  // string cmd_type = 4;
  if (!this->_internal_cmd_type().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_cmd_type().data(), static_cast<int>(this->_internal_cmd_type().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "com.quanzhi.audit_core.common.model.PostgreRequest.cmd_type");
    target = stream->WriteStringMaybeAliased(
        4, this->_internal_cmd_type(), target);
  }

  // string sql = 5;
  if (!this->_internal_sql().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_sql().data(), static_cast<int>(this->_internal_sql().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "com.quanzhi.audit_core.common.model.PostgreRequest.sql");
    target = stream->WriteStringMaybeAliased(
        5, this->_internal_sql(), target);
  }

  // int32 errCode = 6;
  if (this->_internal_errcode() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(6, this->_internal_errcode(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:com.quanzhi.audit_core.common.model.PostgreRequest)
  return target;
}

size_t PostgreRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.quanzhi.audit_core.common.model.PostgreRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string db_user = 1;
  if (!this->_internal_db_user().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_db_user());
  }

  // string db_name = 2;
  if (!this->_internal_db_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_db_name());
  }

  // string db_password = 3;
  if (!this->_internal_db_password().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_db_password());
  }

  // string cmd_type = 4;
  if (!this->_internal_cmd_type().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_cmd_type());
  }

  // string sql = 5;
  if (!this->_internal_sql().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_sql());
  }

  // int32 errCode = 6;
  if (this->_internal_errcode() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_errcode());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData PostgreRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    PostgreRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*PostgreRequest::GetClassData() const { return &_class_data_; }

void PostgreRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<PostgreRequest *>(to)->MergeFrom(
      static_cast<const PostgreRequest &>(from));
}


void PostgreRequest::MergeFrom(const PostgreRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.quanzhi.audit_core.common.model.PostgreRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_db_user().empty()) {
    _internal_set_db_user(from._internal_db_user());
  }
  if (!from._internal_db_name().empty()) {
    _internal_set_db_name(from._internal_db_name());
  }
  if (!from._internal_db_password().empty()) {
    _internal_set_db_password(from._internal_db_password());
  }
  if (!from._internal_cmd_type().empty()) {
    _internal_set_cmd_type(from._internal_cmd_type());
  }
  if (!from._internal_sql().empty()) {
    _internal_set_sql(from._internal_sql());
  }
  if (from._internal_errcode() != 0) {
    _internal_set_errcode(from._internal_errcode());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void PostgreRequest::CopyFrom(const PostgreRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.quanzhi.audit_core.common.model.PostgreRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PostgreRequest::IsInitialized() const {
  return true;
}

void PostgreRequest::InternalSwap(PostgreRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &db_user_, lhs_arena,
      &other->db_user_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &db_name_, lhs_arena,
      &other->db_name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &db_password_, lhs_arena,
      &other->db_password_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &cmd_type_, lhs_arena,
      &other->cmd_type_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &sql_, lhs_arena,
      &other->sql_, rhs_arena
  );
  swap(errcode_, other->errcode_);
}

::PROTOBUF_NAMESPACE_ID::Metadata PostgreRequest::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_ProtobufRawPostgreEvent_2eproto_getter, &descriptor_table_ProtobufRawPostgreEvent_2eproto_once,
      file_level_metadata_ProtobufRawPostgreEvent_2eproto[3]);
}

// ===================================================================

class PostgreResponse::_Internal {
 public:
  static const ::com::quanzhi::audit_core::common::model::ResultSet& result_set(const PostgreResponse* msg);
};

const ::com::quanzhi::audit_core::common::model::ResultSet&
PostgreResponse::_Internal::result_set(const PostgreResponse* msg) {
  return *msg->result_set_;
}
PostgreResponse::PostgreResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  // @@protoc_insertion_point(arena_constructor:com.quanzhi.audit_core.common.model.PostgreResponse)
}
PostgreResponse::PostgreResponse(const PostgreResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_result_set()) {
    result_set_ = new ::com::quanzhi::audit_core::common::model::ResultSet(*from.result_set_);
  } else {
    result_set_ = nullptr;
  }
  ::memcpy(&start_time_, &from.start_time_,
    static_cast<size_t>(reinterpret_cast<char*>(&errcode_) -
    reinterpret_cast<char*>(&start_time_)) + sizeof(errcode_));
  // @@protoc_insertion_point(copy_constructor:com.quanzhi.audit_core.common.model.PostgreResponse)
}

inline void PostgreResponse::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&result_set_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&errcode_) -
    reinterpret_cast<char*>(&result_set_)) + sizeof(errcode_));
}

PostgreResponse::~PostgreResponse() {
  // @@protoc_insertion_point(destructor:com.quanzhi.audit_core.common.model.PostgreResponse)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void PostgreResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete result_set_;
}

void PostgreResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void PostgreResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:com.quanzhi.audit_core.common.model.PostgreResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && result_set_ != nullptr) {
    delete result_set_;
  }
  result_set_ = nullptr;
  ::memset(&start_time_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&errcode_) -
      reinterpret_cast<char*>(&start_time_)) + sizeof(errcode_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* PostgreResponse::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 status = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          status_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint64 start_time = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          start_time_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint64 close_time = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          close_time_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 row_count = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          row_count_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .com.quanzhi.audit_core.common.model.ResultSet result_set = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_result_set(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 errCode = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 48)) {
          errcode_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* PostgreResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:com.quanzhi.audit_core.common.model.PostgreResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 status = 1;
  if (this->_internal_status() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(1, this->_internal_status(), target);
  }

  // uint64 start_time = 2;
  if (this->_internal_start_time() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt64ToArray(2, this->_internal_start_time(), target);
  }

  // uint64 close_time = 3;
  if (this->_internal_close_time() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt64ToArray(3, this->_internal_close_time(), target);
  }

  // int32 row_count = 4;
  if (this->_internal_row_count() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(4, this->_internal_row_count(), target);
  }

  // .com.quanzhi.audit_core.common.model.ResultSet result_set = 5;
  if (this->_internal_has_result_set()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(5, _Internal::result_set(this),
        _Internal::result_set(this).GetCachedSize(), target, stream);
  }

  // int32 errCode = 6;
  if (this->_internal_errcode() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(6, this->_internal_errcode(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:com.quanzhi.audit_core.common.model.PostgreResponse)
  return target;
}

size_t PostgreResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.quanzhi.audit_core.common.model.PostgreResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .com.quanzhi.audit_core.common.model.ResultSet result_set = 5;
  if (this->_internal_has_result_set()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *result_set_);
  }

  // uint64 start_time = 2;
  if (this->_internal_start_time() != 0) {
    total_size += ::_pbi::WireFormatLite::UInt64SizePlusOne(this->_internal_start_time());
  }

  // int32 status = 1;
  if (this->_internal_status() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_status());
  }

  // int32 row_count = 4;
  if (this->_internal_row_count() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_row_count());
  }

  // uint64 close_time = 3;
  if (this->_internal_close_time() != 0) {
    total_size += ::_pbi::WireFormatLite::UInt64SizePlusOne(this->_internal_close_time());
  }

  // int32 errCode = 6;
  if (this->_internal_errcode() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_errcode());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData PostgreResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    PostgreResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*PostgreResponse::GetClassData() const { return &_class_data_; }

void PostgreResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<PostgreResponse *>(to)->MergeFrom(
      static_cast<const PostgreResponse &>(from));
}


void PostgreResponse::MergeFrom(const PostgreResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.quanzhi.audit_core.common.model.PostgreResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_result_set()) {
    _internal_mutable_result_set()->::com::quanzhi::audit_core::common::model::ResultSet::MergeFrom(from._internal_result_set());
  }
  if (from._internal_start_time() != 0) {
    _internal_set_start_time(from._internal_start_time());
  }
  if (from._internal_status() != 0) {
    _internal_set_status(from._internal_status());
  }
  if (from._internal_row_count() != 0) {
    _internal_set_row_count(from._internal_row_count());
  }
  if (from._internal_close_time() != 0) {
    _internal_set_close_time(from._internal_close_time());
  }
  if (from._internal_errcode() != 0) {
    _internal_set_errcode(from._internal_errcode());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void PostgreResponse::CopyFrom(const PostgreResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.quanzhi.audit_core.common.model.PostgreResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PostgreResponse::IsInitialized() const {
  return true;
}

void PostgreResponse::InternalSwap(PostgreResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(PostgreResponse, errcode_)
      + sizeof(PostgreResponse::errcode_)
      - PROTOBUF_FIELD_OFFSET(PostgreResponse, result_set_)>(
          reinterpret_cast<char*>(&result_set_),
          reinterpret_cast<char*>(&other->result_set_));
}

::PROTOBUF_NAMESPACE_ID::Metadata PostgreResponse::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_ProtobufRawPostgreEvent_2eproto_getter, &descriptor_table_ProtobufRawPostgreEvent_2eproto_once,
      file_level_metadata_ProtobufRawPostgreEvent_2eproto[4]);
}

// ===================================================================

class ResultSet::_Internal {
 public:
};

ResultSet::ResultSet(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  field_names_(arena),
  rows_(arena) {
  SharedCtor();
  // @@protoc_insertion_point(arena_constructor:com.quanzhi.audit_core.common.model.ResultSet)
}
ResultSet::ResultSet(const ResultSet& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      field_names_(from.field_names_),
      rows_(from.rows_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  field_count_ = from.field_count_;
  // @@protoc_insertion_point(copy_constructor:com.quanzhi.audit_core.common.model.ResultSet)
}

inline void ResultSet::SharedCtor() {
field_count_ = 0;
}

ResultSet::~ResultSet() {
  // @@protoc_insertion_point(destructor:com.quanzhi.audit_core.common.model.ResultSet)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void ResultSet::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void ResultSet::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ResultSet::Clear() {
// @@protoc_insertion_point(message_clear_start:com.quanzhi.audit_core.common.model.ResultSet)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  field_names_.Clear();
  rows_.Clear();
  field_count_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ResultSet::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated string field_names = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_field_names();
            ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(ptr);
            CHK_(::_pbi::VerifyUTF8(str, "com.quanzhi.audit_core.common.model.ResultSet.field_names"));
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated .com.quanzhi.audit_core.common.model.ResultRow rows = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_rows(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      // int32 field_count = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          field_count_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ResultSet::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:com.quanzhi.audit_core.common.model.ResultSet)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated string field_names = 1;
  for (int i = 0, n = this->_internal_field_names_size(); i < n; i++) {
    const auto& s = this->_internal_field_names(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "com.quanzhi.audit_core.common.model.ResultSet.field_names");
    target = stream->WriteString(1, s, target);
  }

  // repeated .com.quanzhi.audit_core.common.model.ResultRow rows = 2;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_rows_size()); i < n; i++) {
    const auto& repfield = this->_internal_rows(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(2, repfield, repfield.GetCachedSize(), target, stream);
  }

  // int32 field_count = 3;
  if (this->_internal_field_count() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(3, this->_internal_field_count(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:com.quanzhi.audit_core.common.model.ResultSet)
  return target;
}

size_t ResultSet::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.quanzhi.audit_core.common.model.ResultSet)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated string field_names = 1;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(field_names_.size());
  for (int i = 0, n = field_names_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      field_names_.Get(i));
  }

  // repeated .com.quanzhi.audit_core.common.model.ResultRow rows = 2;
  total_size += 1UL * this->_internal_rows_size();
  for (const auto& msg : this->rows_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // int32 field_count = 3;
  if (this->_internal_field_count() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_field_count());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ResultSet::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ResultSet::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ResultSet::GetClassData() const { return &_class_data_; }

void ResultSet::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ResultSet *>(to)->MergeFrom(
      static_cast<const ResultSet &>(from));
}


void ResultSet::MergeFrom(const ResultSet& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.quanzhi.audit_core.common.model.ResultSet)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  field_names_.MergeFrom(from.field_names_);
  rows_.MergeFrom(from.rows_);
  if (from._internal_field_count() != 0) {
    _internal_set_field_count(from._internal_field_count());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ResultSet::CopyFrom(const ResultSet& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.quanzhi.audit_core.common.model.ResultSet)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ResultSet::IsInitialized() const {
  return true;
}

void ResultSet::InternalSwap(ResultSet* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  field_names_.InternalSwap(&other->field_names_);
  rows_.InternalSwap(&other->rows_);
  swap(field_count_, other->field_count_);
}

::PROTOBUF_NAMESPACE_ID::Metadata ResultSet::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_ProtobufRawPostgreEvent_2eproto_getter, &descriptor_table_ProtobufRawPostgreEvent_2eproto_once,
      file_level_metadata_ProtobufRawPostgreEvent_2eproto[5]);
}

// ===================================================================

class ResultRow::_Internal {
 public:
};

ResultRow::ResultRow(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  field_values_(arena) {
  SharedCtor();
  // @@protoc_insertion_point(arena_constructor:com.quanzhi.audit_core.common.model.ResultRow)
}
ResultRow::ResultRow(const ResultRow& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      field_values_(from.field_values_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:com.quanzhi.audit_core.common.model.ResultRow)
}

inline void ResultRow::SharedCtor() {
}

ResultRow::~ResultRow() {
  // @@protoc_insertion_point(destructor:com.quanzhi.audit_core.common.model.ResultRow)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void ResultRow::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void ResultRow::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ResultRow::Clear() {
// @@protoc_insertion_point(message_clear_start:com.quanzhi.audit_core.common.model.ResultRow)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  field_values_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ResultRow::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated string field_values = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_field_values();
            ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(ptr);
            CHK_(::_pbi::VerifyUTF8(str, "com.quanzhi.audit_core.common.model.ResultRow.field_values"));
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ResultRow::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:com.quanzhi.audit_core.common.model.ResultRow)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated string field_values = 1;
  for (int i = 0, n = this->_internal_field_values_size(); i < n; i++) {
    const auto& s = this->_internal_field_values(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "com.quanzhi.audit_core.common.model.ResultRow.field_values");
    target = stream->WriteString(1, s, target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:com.quanzhi.audit_core.common.model.ResultRow)
  return target;
}

size_t ResultRow::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.quanzhi.audit_core.common.model.ResultRow)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated string field_values = 1;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(field_values_.size());
  for (int i = 0, n = field_values_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      field_values_.Get(i));
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ResultRow::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ResultRow::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ResultRow::GetClassData() const { return &_class_data_; }

void ResultRow::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ResultRow *>(to)->MergeFrom(
      static_cast<const ResultRow &>(from));
}


void ResultRow::MergeFrom(const ResultRow& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.quanzhi.audit_core.common.model.ResultRow)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  field_values_.MergeFrom(from.field_values_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ResultRow::CopyFrom(const ResultRow& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.quanzhi.audit_core.common.model.ResultRow)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ResultRow::IsInitialized() const {
  return true;
}

void ResultRow::InternalSwap(ResultRow* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  field_values_.InternalSwap(&other->field_values_);
}

::PROTOBUF_NAMESPACE_ID::Metadata ResultRow::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_ProtobufRawPostgreEvent_2eproto_getter, &descriptor_table_ProtobufRawPostgreEvent_2eproto_once,
      file_level_metadata_ProtobufRawPostgreEvent_2eproto[6]);
}

// ===================================================================

class Net::_Internal {
 public:
};

Net::Net(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  // @@protoc_insertion_point(arena_constructor:com.quanzhi.audit_core.common.model.Net)
}
Net::Net(const Net& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  srcip_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    srcip_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_srcip().empty()) {
    srcip_.Set(from._internal_srcip(), 
      GetArenaForAllocation());
  }
  dstip_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    dstip_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_dstip().empty()) {
    dstip_.Set(from._internal_dstip(), 
      GetArenaForAllocation());
  }
  flowsource_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    flowsource_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_flowsource().empty()) {
    flowsource_.Set(from._internal_flowsource(), 
      GetArenaForAllocation());
  }
  ::memcpy(&srcport_, &from.srcport_,
    static_cast<size_t>(reinterpret_cast<char*>(&dstport_) -
    reinterpret_cast<char*>(&srcport_)) + sizeof(dstport_));
  // @@protoc_insertion_point(copy_constructor:com.quanzhi.audit_core.common.model.Net)
}

inline void Net::SharedCtor() {
srcip_.InitDefault();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  srcip_.Set("", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
dstip_.InitDefault();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  dstip_.Set("", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
flowsource_.InitDefault();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  flowsource_.Set("", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&srcport_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&dstport_) -
    reinterpret_cast<char*>(&srcport_)) + sizeof(dstport_));
}

Net::~Net() {
  // @@protoc_insertion_point(destructor:com.quanzhi.audit_core.common.model.Net)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Net::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  srcip_.Destroy();
  dstip_.Destroy();
  flowsource_.Destroy();
}

void Net::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void Net::Clear() {
// @@protoc_insertion_point(message_clear_start:com.quanzhi.audit_core.common.model.Net)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  srcip_.ClearToEmpty();
  dstip_.ClearToEmpty();
  flowsource_.ClearToEmpty();
  ::memset(&srcport_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&dstport_) -
      reinterpret_cast<char*>(&srcport_)) + sizeof(dstport_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Net::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string srcIp = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_srcip();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "com.quanzhi.audit_core.common.model.Net.srcIp"));
        } else
          goto handle_unusual;
        continue;
      // int32 srcPort = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          srcport_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string dstIp = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_dstip();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "com.quanzhi.audit_core.common.model.Net.dstIp"));
        } else
          goto handle_unusual;
        continue;
      // int32 dstPort = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          dstport_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string flowSource = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          auto str = _internal_mutable_flowsource();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "com.quanzhi.audit_core.common.model.Net.flowSource"));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Net::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:com.quanzhi.audit_core.common.model.Net)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string srcIp = 1;
  if (!this->_internal_srcip().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_srcip().data(), static_cast<int>(this->_internal_srcip().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "com.quanzhi.audit_core.common.model.Net.srcIp");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_srcip(), target);
  }

  // int32 srcPort = 2;
  if (this->_internal_srcport() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(2, this->_internal_srcport(), target);
  }

  // string dstIp = 3;
  if (!this->_internal_dstip().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_dstip().data(), static_cast<int>(this->_internal_dstip().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "com.quanzhi.audit_core.common.model.Net.dstIp");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_dstip(), target);
  }

  // int32 dstPort = 4;
  if (this->_internal_dstport() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(4, this->_internal_dstport(), target);
  }

  // string flowSource = 5;
  if (!this->_internal_flowsource().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_flowsource().data(), static_cast<int>(this->_internal_flowsource().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "com.quanzhi.audit_core.common.model.Net.flowSource");
    target = stream->WriteStringMaybeAliased(
        5, this->_internal_flowsource(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:com.quanzhi.audit_core.common.model.Net)
  return target;
}

size_t Net::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.quanzhi.audit_core.common.model.Net)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string srcIp = 1;
  if (!this->_internal_srcip().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_srcip());
  }

  // string dstIp = 3;
  if (!this->_internal_dstip().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_dstip());
  }

  // string flowSource = 5;
  if (!this->_internal_flowsource().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_flowsource());
  }

  // int32 srcPort = 2;
  if (this->_internal_srcport() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_srcport());
  }

  // int32 dstPort = 4;
  if (this->_internal_dstport() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_dstport());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Net::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    Net::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Net::GetClassData() const { return &_class_data_; }

void Net::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<Net *>(to)->MergeFrom(
      static_cast<const Net &>(from));
}


void Net::MergeFrom(const Net& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.quanzhi.audit_core.common.model.Net)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_srcip().empty()) {
    _internal_set_srcip(from._internal_srcip());
  }
  if (!from._internal_dstip().empty()) {
    _internal_set_dstip(from._internal_dstip());
  }
  if (!from._internal_flowsource().empty()) {
    _internal_set_flowsource(from._internal_flowsource());
  }
  if (from._internal_srcport() != 0) {
    _internal_set_srcport(from._internal_srcport());
  }
  if (from._internal_dstport() != 0) {
    _internal_set_dstport(from._internal_dstport());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Net::CopyFrom(const Net& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.quanzhi.audit_core.common.model.Net)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Net::IsInitialized() const {
  return true;
}

void Net::InternalSwap(Net* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &srcip_, lhs_arena,
      &other->srcip_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &dstip_, lhs_arena,
      &other->dstip_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &flowsource_, lhs_arena,
      &other->flowsource_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Net, dstport_)
      + sizeof(Net::dstport_)
      - PROTOBUF_FIELD_OFFSET(Net, srcport_)>(
          reinterpret_cast<char*>(&srcport_),
          reinterpret_cast<char*>(&other->srcport_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Net::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_ProtobufRawPostgreEvent_2eproto_getter, &descriptor_table_ProtobufRawPostgreEvent_2eproto_once,
      file_level_metadata_ProtobufRawPostgreEvent_2eproto[7]);
}

// ===================================================================

class Mac::_Internal {
 public:
};

Mac::Mac(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  // @@protoc_insertion_point(arena_constructor:com.quanzhi.audit_core.common.model.Mac)
}
Mac::Mac(const Mac& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  mac_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    mac_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_mac().empty()) {
    mac_.Set(from._internal_mac(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:com.quanzhi.audit_core.common.model.Mac)
}

inline void Mac::SharedCtor() {
mac_.InitDefault();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  mac_.Set("", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

Mac::~Mac() {
  // @@protoc_insertion_point(destructor:com.quanzhi.audit_core.common.model.Mac)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Mac::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  mac_.Destroy();
}

void Mac::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void Mac::Clear() {
// @@protoc_insertion_point(message_clear_start:com.quanzhi.audit_core.common.model.Mac)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  mac_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Mac::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string mac = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_mac();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "com.quanzhi.audit_core.common.model.Mac.mac"));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Mac::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:com.quanzhi.audit_core.common.model.Mac)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string mac = 1;
  if (!this->_internal_mac().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_mac().data(), static_cast<int>(this->_internal_mac().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "com.quanzhi.audit_core.common.model.Mac.mac");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_mac(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:com.quanzhi.audit_core.common.model.Mac)
  return target;
}

size_t Mac::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.quanzhi.audit_core.common.model.Mac)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string mac = 1;
  if (!this->_internal_mac().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_mac());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Mac::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    Mac::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Mac::GetClassData() const { return &_class_data_; }

void Mac::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<Mac *>(to)->MergeFrom(
      static_cast<const Mac &>(from));
}


void Mac::MergeFrom(const Mac& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.quanzhi.audit_core.common.model.Mac)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_mac().empty()) {
    _internal_set_mac(from._internal_mac());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Mac::CopyFrom(const Mac& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.quanzhi.audit_core.common.model.Mac)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Mac::IsInitialized() const {
  return true;
}

void Mac::InternalSwap(Mac* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &mac_, lhs_arena,
      &other->mac_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata Mac::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_ProtobufRawPostgreEvent_2eproto_getter, &descriptor_table_ProtobufRawPostgreEvent_2eproto_once,
      file_level_metadata_ProtobufRawPostgreEvent_2eproto[8]);
}

// ===================================================================

class Source::_Internal {
 public:
};

Source::Source(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  // @@protoc_insertion_point(arena_constructor:com.quanzhi.audit_core.common.model.Source)
}
Source::Source(const Source& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  taskid_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    taskid_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_taskid().empty()) {
    taskid_.Set(from._internal_taskid(), 
      GetArenaForAllocation());
  }
  app_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    app_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_app().empty()) {
    app_.Set(from._internal_app(), 
      GetArenaForAllocation());
  }
  sourcetype_ = from.sourcetype_;
  // @@protoc_insertion_point(copy_constructor:com.quanzhi.audit_core.common.model.Source)
}

inline void Source::SharedCtor() {
taskid_.InitDefault();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  taskid_.Set("", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
app_.InitDefault();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  app_.Set("", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
sourcetype_ = 0;
}

Source::~Source() {
  // @@protoc_insertion_point(destructor:com.quanzhi.audit_core.common.model.Source)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Source::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  taskid_.Destroy();
  app_.Destroy();
}

void Source::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void Source::Clear() {
// @@protoc_insertion_point(message_clear_start:com.quanzhi.audit_core.common.model.Source)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  taskid_.ClearToEmpty();
  app_.ClearToEmpty();
  sourcetype_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Source::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .com.quanzhi.audit_core.common.model.SourceTypeEnum sourceType = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_sourcetype(static_cast<::com::quanzhi::audit_core::common::model::SourceTypeEnum>(val));
        } else
          goto handle_unusual;
        continue;
      // string taskId = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_taskid();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "com.quanzhi.audit_core.common.model.Source.taskId"));
        } else
          goto handle_unusual;
        continue;
      // string app = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_app();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "com.quanzhi.audit_core.common.model.Source.app"));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Source::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:com.quanzhi.audit_core.common.model.Source)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .com.quanzhi.audit_core.common.model.SourceTypeEnum sourceType = 1;
  if (this->_internal_sourcetype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteEnumToArray(
      1, this->_internal_sourcetype(), target);
  }

  // string taskId = 2;
  if (!this->_internal_taskid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_taskid().data(), static_cast<int>(this->_internal_taskid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "com.quanzhi.audit_core.common.model.Source.taskId");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_taskid(), target);
  }

  // string app = 3;
  if (!this->_internal_app().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_app().data(), static_cast<int>(this->_internal_app().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "com.quanzhi.audit_core.common.model.Source.app");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_app(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:com.quanzhi.audit_core.common.model.Source)
  return target;
}

size_t Source::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.quanzhi.audit_core.common.model.Source)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string taskId = 2;
  if (!this->_internal_taskid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_taskid());
  }

  // string app = 3;
  if (!this->_internal_app().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_app());
  }

  // .com.quanzhi.audit_core.common.model.SourceTypeEnum sourceType = 1;
  if (this->_internal_sourcetype() != 0) {
    total_size += 1 +
      ::_pbi::WireFormatLite::EnumSize(this->_internal_sourcetype());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Source::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    Source::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Source::GetClassData() const { return &_class_data_; }

void Source::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<Source *>(to)->MergeFrom(
      static_cast<const Source &>(from));
}


void Source::MergeFrom(const Source& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.quanzhi.audit_core.common.model.Source)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_taskid().empty()) {
    _internal_set_taskid(from._internal_taskid());
  }
  if (!from._internal_app().empty()) {
    _internal_set_app(from._internal_app());
  }
  if (from._internal_sourcetype() != 0) {
    _internal_set_sourcetype(from._internal_sourcetype());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Source::CopyFrom(const Source& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.quanzhi.audit_core.common.model.Source)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Source::IsInitialized() const {
  return true;
}

void Source::InternalSwap(Source* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &taskid_, lhs_arena,
      &other->taskid_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &app_, lhs_arena,
      &other->app_, rhs_arena
  );
  swap(sourcetype_, other->sourcetype_);
}

::PROTOBUF_NAMESPACE_ID::Metadata Source::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_ProtobufRawPostgreEvent_2eproto_getter, &descriptor_table_ProtobufRawPostgreEvent_2eproto_once,
      file_level_metadata_ProtobufRawPostgreEvent_2eproto[9]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace model
}  // namespace common
}  // namespace audit_core
}  // namespace quanzhi
}  // namespace com
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::com::quanzhi::audit_core::common::model::ProtobufRawPostgreEvent*
Arena::CreateMaybeMessage< ::com::quanzhi::audit_core::common::model::ProtobufRawPostgreEvent >(Arena* arena) {
  return Arena::CreateMessageInternal< ::com::quanzhi::audit_core::common::model::ProtobufRawPostgreEvent >(arena);
}
template<> PROTOBUF_NOINLINE ::com::quanzhi::audit_core::common::model::Meta*
Arena::CreateMaybeMessage< ::com::quanzhi::audit_core::common::model::Meta >(Arena* arena) {
  return Arena::CreateMessageInternal< ::com::quanzhi::audit_core::common::model::Meta >(arena);
}
template<> PROTOBUF_NOINLINE ::com::quanzhi::audit_core::common::model::UniqueId*
Arena::CreateMaybeMessage< ::com::quanzhi::audit_core::common::model::UniqueId >(Arena* arena) {
  return Arena::CreateMessageInternal< ::com::quanzhi::audit_core::common::model::UniqueId >(arena);
}
template<> PROTOBUF_NOINLINE ::com::quanzhi::audit_core::common::model::PostgreRequest*
Arena::CreateMaybeMessage< ::com::quanzhi::audit_core::common::model::PostgreRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::com::quanzhi::audit_core::common::model::PostgreRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::com::quanzhi::audit_core::common::model::PostgreResponse*
Arena::CreateMaybeMessage< ::com::quanzhi::audit_core::common::model::PostgreResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::com::quanzhi::audit_core::common::model::PostgreResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::com::quanzhi::audit_core::common::model::ResultSet*
Arena::CreateMaybeMessage< ::com::quanzhi::audit_core::common::model::ResultSet >(Arena* arena) {
  return Arena::CreateMessageInternal< ::com::quanzhi::audit_core::common::model::ResultSet >(arena);
}
template<> PROTOBUF_NOINLINE ::com::quanzhi::audit_core::common::model::ResultRow*
Arena::CreateMaybeMessage< ::com::quanzhi::audit_core::common::model::ResultRow >(Arena* arena) {
  return Arena::CreateMessageInternal< ::com::quanzhi::audit_core::common::model::ResultRow >(arena);
}
template<> PROTOBUF_NOINLINE ::com::quanzhi::audit_core::common::model::Net*
Arena::CreateMaybeMessage< ::com::quanzhi::audit_core::common::model::Net >(Arena* arena) {
  return Arena::CreateMessageInternal< ::com::quanzhi::audit_core::common::model::Net >(arena);
}
template<> PROTOBUF_NOINLINE ::com::quanzhi::audit_core::common::model::Mac*
Arena::CreateMaybeMessage< ::com::quanzhi::audit_core::common::model::Mac >(Arena* arena) {
  return Arena::CreateMessageInternal< ::com::quanzhi::audit_core::common::model::Mac >(arena);
}
template<> PROTOBUF_NOINLINE ::com::quanzhi::audit_core::common::model::Source*
Arena::CreateMaybeMessage< ::com::quanzhi::audit_core::common::model::Source >(Arena* arena) {
  return Arena::CreateMessageInternal< ::com::quanzhi::audit_core::common::model::Source >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
