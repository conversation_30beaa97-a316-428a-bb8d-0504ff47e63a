#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <arpa/inet.h>
#include <sys/time.h>
#include <ctype.h>
#include <unordered_map>
#include <memory>

#ifndef _GNU_SOURCE
#define _GNU_SOURCE
#endif

#include "postgre_parser.h"
#include "gw_common.h"
#include "gw_logger.h"
#include "session_mgt.h"
#include "session.h"
#include "utils.h"

// 获取秒级时间戳
uint64_t CPostgreParser::get_time_ts(CSession *p_session, int use_pcap_ts)
{
    if (1 == use_pcap_ts){
        return p_session->get_ts();
    }
    else{
        struct timeval tv;
        gettimeofday(&tv, NULL);
        return tv.tv_sec;
    }
}

// 获取毫秒级时间戳
uint64_t CPostgreParser::get_time_ts_ms(CSession *p_session, int use_pcap_ts)
{
    if (1 == use_pcap_ts){
        return p_session->get_ts_ms();
    }
    else{
        struct timeval tv;
        gettimeofday(&tv, NULL);
        return tv.tv_sec * 1000ULL + tv.tv_usec / 1000ULL;
    }
}
// 辅助函数：验证SQL链表完整性
bool CPostgreParser::validate_sql_list_integrity(sql_statement_t *sql_list, const char *context)
{
    if (sql_list == NULL) {
        return true; // 空链表是有效的
    }
    
    sql_statement_t *current = sql_list;
    int depth = 0;
    
    while (current) {
        depth++;
        
        // 检查深度是否过深（可能的循环链表）
        if (depth > 10000) {
            GWLOG_ERROR(m_comm, "[PostgreSQL][ListIntegrity] %s: List depth exceeds 10000, possible circular reference\n", context ? context : "Unknown");
            return false;
        }
        
        // 检查指针是否合理
        if ((uintptr_t)current < 0x1000) {
            GWLOG_ERROR(m_comm, "[PostgreSQL][ListIntegrity] %s: Invalid node pointer at depth %d: %p\n", context ? context : "Unknown", depth, current);
            return false;
        }
        
        // 检查SQL字符串的有效性
        if (current->sql.s && current->sql.len > 0) {
            // 基本的字符串有效性检查
            if ((uintptr_t)current->sql.s < 0x1000) {
                GWLOG_ERROR(m_comm, "[PostgreSQL][ListIntegrity] %s: Invalid SQL string pointer at depth %d: %p\n", context ? context : "Unknown", depth, current->sql.s);
                return false;
            }
        }
        
        current = current->next;
    }
    
    return true;
}

// 辅助函数：清理行数据资源
void CPostgreParser::cleanup_row_data(postgre_row_data_t *row, int field_count) 
{
    if (!row) {
        return;
    }
    
    // 优先使用行数据结构中保存的字段数量，如果没有则使用传入的参数
    int actual_field_count = (row->field_count > 0) ? row->field_count : field_count;
    
    if (row->row) {
        // 添加边界检查，防止访问无效内存
        if (actual_field_count <= 0 || actual_field_count > 10000) { // 合理的最大列数限制
            GWLOG_ERROR(m_comm, "[PostgreSQL] Invalid field count %d in cleanup_row_data, skipping cleanup\n", actual_field_count);
            // 只释放行数组本身，不访问其内容
            delete[] row->row;
            row->row = NULL;
        } else {
            for (int i = 0; i < actual_field_count; i++) {
                // 检查数组索引和指针有效性
                if (row->row[i]) {
                    // 然后清理字符串内存
                    if (row->row[i]->s) {
                        free((void*)row->row[i]->s);
                        row->row[i]->s = NULL;
                    }
                    // 最后删除结构体
                    delete row->row[i];
                    row->row[i] = NULL;
                }
            }
            // 删除指针数组
            delete[] row->row;
            row->row = NULL;
        }
    }
    // 删除行结构体
    delete row;
}

// 理列定义链表的辅助函数
void CPostgreParser::cleanup_column_definitions(column_def_t *col_def)
{
    while (col_def) {
        column_def_t *temp = col_def;
        col_def = col_def->next;
        
        // 清理列名字符串
        if (temp->name.s) {
            free((void*)temp->name.s);
            temp->name.s = NULL;
        }
        
        // 重置所有字段
        temp->name.len = 0;
        temp->table_oid = 0;
        temp->column_id = 0;
        temp->type_oid = 0;
        temp->type_len = 0;
        temp->type_mod = 0;
        temp->format_code = 0;
        temp->next = NULL;
        
        // 删除结构体
        delete temp;
    }
}

// SQL参数实例化函数
char* CPostgreParser::instantiate_sql_with_parameters(const char* parameterized_sql, size_t sql_len, 
                                                     char** param_values, uint16_t param_count, 
                                                     size_t* result_len)
{
    if (!parameterized_sql || sql_len == 0 || !result_len) {
        return NULL;
    }
    
    // 如果没有参数，直接复制原SQL
    if (param_count == 0 || !param_values) {
        char* result = (char*)malloc(sql_len + 1);
        if (result) {
            memcpy(result, parameterized_sql, sql_len);
            result[sql_len] = '\0';
            *result_len = sql_len;
        }
        return result;
    }
    
    // 估算结果长度：原SQL长度 + 所有参数长度之和 + 额外缓冲
    size_t estimated_len = sql_len;
    for (uint16_t i = 0; i < param_count; i++) {
        if (param_values[i]) {
            estimated_len += strlen(param_values[i]) + 10; // 额外10字节用于引号等
        } else {
            estimated_len += 4; // "NULL"
        }
    }
    
    // 分配结果缓冲区
    char* result = (char*)malloc(estimated_len + 1);
    if (!result) {
        GWLOG_ERROR(m_comm, "[PostgreSQL] Failed to allocate memory for instantiated SQL\n");
        return NULL;
    }
    
    size_t result_pos = 0;
    size_t sql_pos = 0;
    
    while (sql_pos < sql_len) {
        // 检查是否遇到参数占位符 $n
        if (parameterized_sql[sql_pos] == '$' && sql_pos + 1 < sql_len) {
            // 解析参数编号
            size_t param_start = sql_pos + 1;
            size_t param_end = param_start;
            
            // 找到参数编号的结束位置
            while (param_end < sql_len && 
                   parameterized_sql[param_end] >= '0' && 
                   parameterized_sql[param_end] <= '9') {
                param_end++;
            }
            
            if (param_end > param_start) {
                // 提取参数编号
                char param_num_str[16] = {0};
                size_t param_num_len = param_end - param_start;
                if (param_num_len < sizeof(param_num_str)) {
                    memcpy(param_num_str, parameterized_sql + param_start, param_num_len);
                    int param_num = atoi(param_num_str);
                    
                    // 检查参数编号是否有效
                    if (param_num > 0 && param_num <= param_count) {
                        const char* param_value = param_values[param_num - 1]; // 数组从0开始
                        
                        if (param_value) {
                            // 添加参数值，如果是字符串类型则加引号
                            bool needs_quotes = true;
                            
                            // 简单判断是否为数字（不需要引号）
                            if (strlen(param_value) > 0) {
                                bool is_number = true;
                                const char* p = param_value;
                                if (*p == '-' || *p == '+') p++; // 跳过符号
                                while (*p) {
                                    if ((*p < '0' || *p > '9') && *p != '.') {
                                        is_number = false;
                                        break;
                                    }
                                    p++;
                                }
                                needs_quotes = !is_number;
                            }
                            
                            // 检查缓冲区空间
                            size_t param_len = strlen(param_value);
                            size_t needed_space = param_len + (needs_quotes ? 2 : 0);
                            
                            if (result_pos + needed_space >= estimated_len) {
                                // 扩展缓冲区
                                estimated_len = (result_pos + needed_space + 1024) * 2;
                                char* new_result = (char*)realloc(result, estimated_len + 1);
                                if (!new_result) {
                                    free(result);
                                    GWLOG_ERROR(m_comm, "[PostgreSQL] Failed to reallocate memory for instantiated SQL\n");
                                    return NULL;
                                }
                                result = new_result;
                            }
                            
                            // 添加参数值
                            if (needs_quotes) {
                                result[result_pos++] = '\'';
                            }
                            memcpy(result + result_pos, param_value, param_len);
                            result_pos += param_len;
                            if (needs_quotes) {
                                result[result_pos++] = '\'';
                            }
                        } else {
                            // NULL参数
                            const char* null_str = "NULL";
                            size_t null_len = 4;
                            
                            if (result_pos + null_len >= estimated_len) {
                                estimated_len = (result_pos + null_len + 1024) * 2;
                                char* new_result = (char*)realloc(result, estimated_len + 1);
                                if (!new_result) {
                                    free(result);
                                    return NULL;
                                }
                                result = new_result;
                            }
                            
                            memcpy(result + result_pos, null_str, null_len);
                            result_pos += null_len;
                        }
                        
                        sql_pos = param_end; // 跳过整个参数占位符
                        continue;
                    }
                }
            }
        }
        
        // 检查缓冲区空间
        if (result_pos >= estimated_len) {
            estimated_len *= 2;
            char* new_result = (char*)realloc(result, estimated_len + 1);
            if (!new_result) {
                free(result);
                return NULL;
            }
            result = new_result;
        }
        
        // 复制普通字符
        result[result_pos++] = parameterized_sql[sql_pos++];
    }
    
    result[result_pos] = '\0';
    *result_len = result_pos;
    
    return result;
}

// 处理SASL响应消息
void CPostgreParser::handle_sasl_response_message(postgre_stream_t *pgs, const char *data, size_t len)
{   
    // 清除之前可能记录的密码信息
    if (pgs->pg_stat.password.s) {
        free((void*)pgs->pg_stat.password.s);
        pgs->pg_stat.password.s = NULL;
        pgs->pg_stat.password.len = 0;
    }

    // 对于SASL认证，尝试从响应中提取密码哈希
    if (len > 0 && data) {
        // 将数据转换为字符串进行解析
        char *sasl_data = (char*)malloc(len + 1);
        if (sasl_data) {
            memcpy(sasl_data, data, len);
            sasl_data[len] = '\0';
            
            // 查找 "p=" 参数
            char *p_param = strstr(sasl_data, "p=");
            if (p_param) {
                p_param += 2; // 跳过 "p="
                
                // 查找参数结束位置（逗号或字符串结尾）
                char *param_end = strchr(p_param, ',');
                size_t password_len;
                if (param_end) {
                    password_len = param_end - p_param;
                } else {
                    password_len = strlen(p_param);
                }
                
                // 限制密码长度，防止过长密码
                if (password_len > 256) {
                    password_len = 256;
                }
                
                if (password_len > 0) {
                    // 分配内存并保存密码哈希
                    char *password_value = (char*)malloc(password_len + 1);
                    if (password_value) {
                        memcpy(password_value, p_param, password_len);
                        password_value[password_len] = '\0';
                        
                        pgs->pg_stat.password.s = password_value;
                        pgs->pg_stat.password.len = password_len;
                        
                    }
                }
            }
            
            free(sasl_data);
        }
    }
    
}

// 处理传统密码消息
void CPostgreParser::handle_traditional_password_message(postgre_stream_t *pgs, const char *data, size_t len)
{
    // 对于传统认证方式，尝试提取密码
    const char *password = data;
    size_t password_len = strnlen(password, len);
    
    // 验证密码格式
    if (password_len == len) {
        // 没有null终止符，可能是格式错误
        GWLOG_WARN(m_comm, "[PostgreSQL] Password message without null terminator\n");
        return;
    }
    
    // 限制密码长度，防止过长密码
    if (password_len > 256) {
        password_len = 256;
    }
    
    // 分配内存并保存密码
    char *password_value = (char*)malloc(password_len + 1);
    if (password_value) {
        memcpy(password_value, password, password_len);
        password_value[password_len] = '\0';
        
        // 释放之前的密码
        if (pgs->pg_stat.password.s) {
            free((void*)pgs->pg_stat.password.s);
        }
        
        pgs->pg_stat.password.s = password_value;
        pgs->pg_stat.password.len = password_len;
        
    }
}

/**
 * 解析PostgreSQL ErrorResponse消息中的各个字段
 * @param error_data 错误数据指针（跳过消息类型和长度）
 * @param data_len 错误数据长度
 * @param detailed_message 输出参数：组装后的详细错误消息
 * @param error_code 输出参数：错误代码数字部分
 */
// 辅助函数：安全释放错误字段内存
void CPostgreParser::cleanup_error_fields(char *err_msg, char *err_severity, char *err_detail,
                                         char *err_hint, char *err_position, char *err_code_str)
{
    if (err_msg) free(err_msg);
    if (err_severity) free(err_severity);
    if (err_detail) free(err_detail);
    if (err_hint) free(err_hint);
    if (err_position) free(err_position);
    if (err_code_str) free(err_code_str);
}

void CPostgreParser::parse_error_response_fields(const char *error_data, size_t data_len, char **detailed_message, int *error_code)
{
    *detailed_message = NULL;
    *error_code = 0;

    char *err_msg = NULL;
    char *err_severity = NULL;
    char *err_detail = NULL;
    char *err_hint = NULL;
    char *err_position = NULL;
    char *err_code_str = NULL;
    int err_code = 0;

    const char *field_data = error_data;
    size_t remaining_len = data_len;

    // 解析所有错误字段
    while (remaining_len > 0 && *field_data != '\0') {
        char field_type = *field_data++;
        remaining_len--;

        if (remaining_len == 0) break;

        // 查找字符串结束符
        const char *field_end = (const char*)memchr(field_data, '\0', remaining_len);
        if (!field_end) {
            // 如果没有找到结束符，使用剩余长度
            field_end = field_data + remaining_len;
        }

        size_t field_len = field_end - field_data;

        switch (field_type) {
            case 'S': // Severity
                if (field_len > 0) {
                    err_severity = strndup(field_data, field_len);
                    if (!err_severity) {
                        cleanup_error_fields(err_msg, NULL, err_detail, err_hint, err_position, err_code_str);
                        return;
                    }
                }
                break;
            case 'C': // Code
                if (field_len > 0) {
                    err_code_str = strndup(field_data, field_len);
                    if (!err_code_str) {
                        cleanup_error_fields(err_msg, err_severity, err_detail, err_hint, err_position, NULL);
                        return;
                    }
                    // 解析错误代码数字部分
                    const char *p = field_data;
                    const char *p_end = field_data + field_len;
                    while (p < p_end && *p >= '0' && *p <= '9') {
                        err_code = err_code * 10 + (*p - '0');
                        p++;
                    }
                }
                break;
            case 'M': // Message
                if (field_len > 0) {
                    err_msg = strndup(field_data, field_len);
                    if (!err_msg) {
                        cleanup_error_fields(NULL, err_severity, err_detail, err_hint, err_position, err_code_str);
                        return;
                    }
                }
                break;
            case 'D': // Detail
                if (field_len > 0) {
                    err_detail = strndup(field_data, field_len);
                    if (!err_detail) {
                        cleanup_error_fields(err_msg, err_severity, NULL, err_hint, err_position, err_code_str);
                        return;
                    }
                }
                break;
            case 'H': // Hint
                if (field_len > 0) {
                    err_hint = strndup(field_data, field_len);
                    if (!err_hint) {
                        cleanup_error_fields(err_msg, err_severity, err_detail, NULL, err_position, err_code_str);
                        return;
                    }
                }
                break;
            case 'P': // Position
                if (field_len > 0) {
                    err_position = strndup(field_data, field_len);
                    if (!err_position) {
                        cleanup_error_fields(err_msg, err_severity, err_detail, err_hint, NULL, err_code_str);
                        return;
                    }
                }
                break;
            default:
                // 其他字段暂时忽略
                break;
        }

        // 移动到下一个字段
        if (field_end < field_data + remaining_len) {
            field_data = field_end + 1;
            remaining_len -= (field_len + 1);
        } else {
            break;
        }
    }

    // 组装详细的错误消息
    char *detailed_err_msg = NULL;
    if (err_msg) {
        // 计算所需的缓冲区大小，增加额外的安全边距
        size_t total_len = strlen(err_msg) + 100; // 基本消息 + 额外安全边距
        if (err_severity) total_len += strlen(err_severity) + 15; // " [Severity: ]" + 边距
        if (err_code_str) total_len += strlen(err_code_str) + 15; // " [Code: ]" + 边距
        if (err_detail) total_len += strlen(err_detail) + 15; // " [Detail: ]" + 边距
        if (err_hint) total_len += strlen(err_hint) + 15; // " [Hint: ]" + 边距
        if (err_position) total_len += strlen(err_position) + 20; // " [Position: ]" + 边距

        detailed_err_msg = (char*)malloc(total_len);
        if (detailed_err_msg) {
            // 使用更简单和安全的字符串拼接方法
            strcpy(detailed_err_msg, err_msg);

            if (err_severity) {
                strcat(detailed_err_msg, " [Severity: ");
                strcat(detailed_err_msg, err_severity);
                strcat(detailed_err_msg, "]");
            }
            if (err_code_str) {
                strcat(detailed_err_msg, " [Code: ");
                strcat(detailed_err_msg, err_code_str);
                strcat(detailed_err_msg, "]");
            }
            if (err_detail) {
                strcat(detailed_err_msg, " [Detail: ");
                strcat(detailed_err_msg, err_detail);
                strcat(detailed_err_msg, "]");
            }
            if (err_hint) {
                strcat(detailed_err_msg, " [Hint: ");
                strcat(detailed_err_msg, err_hint);
                strcat(detailed_err_msg, "]");
            }
            if (err_position) {
                strcat(detailed_err_msg, " [Position: ");
                strcat(detailed_err_msg, err_position);
                strcat(detailed_err_msg, "]");
            }
        }
    }

    // 设置返回结果
    *detailed_message = detailed_err_msg ? detailed_err_msg : (err_msg ? strdup(err_msg) : NULL);
    *error_code = err_code;

    // 清理临时分配的内存
    cleanup_error_fields(err_msg, err_severity, err_detail, err_hint, err_position, err_code_str);
}

// 解析startup消息
int CPostgreParser::parse_startup_msg(postgre_stream_t *pgs, const char *data, int len, const struct conn *pcon)
{
    // 检查数据指针有效性
    if (data == NULL)
    {
        GWLOG_ERROR(m_comm, "[PostgreSQL] Invalid data pointer for startup message\n");
        return PARSER_STATUS_DROP_DATA;
    }
    
    // 检查PostgreSQL流结构是否有效
    if (pgs == NULL)
    {
        GWLOG_WARN(m_comm, "[PostgreSQL] Invalid PostgreSQL stream for startup message\n");
        return PARSER_STATUS_DROP_DATA;
    }

    // 检查消息长度
    if (len < POSTGRE_STARTUP_MIN_LEN)
    {
        return PARSER_STATUS_CONTINUE;
    }

    // 检查消息总长度
    uint32_t msg_len = GET_MSG_LEN(data);
    if (msg_len < POSTGRE_STARTUP_MIN_LEN)
    {
        GWLOG_WARN(m_comm, "[PostgreSQL] Invalid startup message length: %u, smaller than minimum %d\n", 
                     msg_len, POSTGRE_STARTUP_MIN_LEN);
        return PARSER_STATUS_DROP_DATA;
    }
    
    if (msg_len > (uint32_t)len)
    {
        return PARSER_STATUS_CONTINUE;
    }

    // 检查是否为SSLRequest消息
    if (msg_len == POSTGRE_SSL_REQUEST_LEN && GET_MSG_LEN(data + 4) == POSTGRE_SSL_REQUEST_CODE)
    {
        // SSLRequest不改变连接状态，只是请求开启SSL
        // SSL协商由TLS层处理，这里只是识别消息
        return PARSER_STATUS_FINISH;
    }

    // 检查是否为GSSENCRequest消息
    if (msg_len == POSTGRE_SSL_REQUEST_LEN && GET_MSG_LEN(data + 4) == POSTGRE_GSSENC_REQUEST_CODE)
    {
        // GSSENCRequest不改变连接状态，只是请求开启GSSAPI加密
        return PARSER_STATUS_FINISH;
    }

    // 检查协议版本
    uint32_t protocol_version = GET_MSG_LEN(data + 4);
    if (protocol_version != POSTGRE_PROTOCOL_VERSION_3)
    {
        GWLOG_WARN(m_comm, "[PostgreSQL] Unsupported protocol version: 0x%08x, expected 0x%08x\n", 
                     protocol_version, POSTGRE_PROTOCOL_VERSION_3);
        return PARSER_STATUS_DROP_DATA;
    }

    // 解析参数
    const char *param_start = data + 8;
    const char *param_end = data + msg_len;
    
    // 参数区域安全性检查
    if (param_start >= param_end)
    {
        GWLOG_WARN(m_comm, "[PostgreSQL] Invalid startup message format: no space for parameters\n");
        return PARSER_STATUS_DROP_DATA;
    }

    // 逐个解析键值对参数
    while (param_start < param_end)
    {
        // 解析参数名
        const char *param_name = param_start;
        size_t param_name_len = strnlen(param_name, param_end - param_name);
        
        // 检查参数名是否有效
        if (param_name_len == 0)
        {
            // 遇到空字符串，可能是参数区域的结束标志
            param_start += 1;
            break;
        }
        
        if (param_name_len == (size_t)(param_end - param_name))
        {
            // 参数名没有结束符，格式错误
            GWLOG_WARN(m_comm, "[PostgreSQL] Malformed startup message: parameter name without null terminator\n");
            return PARSER_STATUS_DROP_DATA;
        }
        
        // 移动到参数值开始位置
        param_start += param_name_len + 1;
        
        // 安全检查：确保参数值开始位置在消息范围内
        if (param_start >= param_end)
        {
            GWLOG_WARN(m_comm, "[PostgreSQL] Malformed startup message: parameter '%s' has no value\n", param_name);
            return PARSER_STATUS_DROP_DATA;
        }

        // 解析参数值
        const char *param_value = param_start;
        size_t param_value_len = strnlen(param_value, param_end - param_value);
        
        // 检查参数值是否有效
        if (param_value_len == (size_t)(param_end - param_value))
        {
            GWLOG_WARN(m_comm, "[PostgreSQL] Malformed startup message: parameter '%s' value without null terminator\n", param_name);
            return PARSER_STATUS_DROP_DATA;
        }
        
        // 移动到下一个参数开始位置
        param_start += param_value_len + 1;

        // 检查流复制协议参数
        if (strcmp(param_name, "replication") == 0) {
            if (ensure_copy_context(pgs)) {
                pgs->copy_context->is_replication_mode = true;
            }
        }

        // 保存重要参数
        if (strcmp(param_name, "user") == 0)
        {
            // 检查参数值长度是否合理
            if (param_value_len > 256)
            {
                GWLOG_WARN(m_comm, "[PostgreSQL] User parameter too long: %zu bytes\n", param_value_len);
                return PARSER_STATUS_DROP_DATA;
            }
            
            // 分配新内存并拷贝数据
            char *user_value = (char *)malloc(param_value_len + 1);
            if (!user_value) {
                GWLOG_WARN(m_comm, "[PostgreSQL] Failed to allocate memory for user parameter\n");
                return PARSER_STATUS_DROP_DATA;
            }
            
            memcpy(user_value, param_value, param_value_len);
            user_value[param_value_len] = '\0';  // 确保字符串结束
            
            // 如果之前已有用户名，先释放
            if (pgs->pg_stat.user.s) {
                free((void*)pgs->pg_stat.user.s);
            }
            
            pgs->pg_stat.user.s = user_value;
            pgs->pg_stat.user.len = param_value_len;
        }
        else if (strcmp(param_name, "database") == 0)
        {
            // 检查参数值长度是否合理
            if (param_value_len > 256)
            {
                GWLOG_WARN(m_comm, "[PostgreSQL] Database parameter too long: %zu bytes\n", param_value_len);
                return PARSER_STATUS_DROP_DATA;
            }
            
            char *db_value = (char *)malloc(param_value_len + 1);
            if (!db_value) {
                GWLOG_WARN(m_comm, "[PostgreSQL] Failed to allocate memory for database parameter\n");
                return PARSER_STATUS_DROP_DATA;
            }
            
            memcpy(db_value, param_value, param_value_len);
            db_value[param_value_len] = '\0';
            
            // 释放之前的数据库名
            if (pgs->pg_stat.db_name.s) {
                free((void*)pgs->pg_stat.db_name.s);
            }
            
            pgs->pg_stat.db_name.s = db_value;
            pgs->pg_stat.db_name.len = param_value_len;
        }
        else if (strcmp(param_name, "application_name") == 0)
        {
            // 检查参数值长度是否合理
            if (param_value_len > 256)
            {
                GWLOG_WARN(m_comm, "[PostgreSQL] Application name parameter too long: %zu bytes\n", param_value_len);
                // 对于应用名称过长，我们可以选择截断而非拒绝
                param_value_len = 256;
            }
            
            char *app_value = (char *)malloc(param_value_len + 1);
            if (!app_value) {
                GWLOG_WARN(m_comm, "[PostgreSQL] Failed to allocate memory for application_name parameter\n");
                return PARSER_STATUS_DROP_DATA;
            }
            
            memcpy(app_value, param_value, param_value_len);
            app_value[param_value_len] = '\0';
            
            // 释放之前的应用名称
            if (pgs->pg_stat.application_name.s) {
                free((void*)pgs->pg_stat.application_name.s);
            }
            
            pgs->pg_stat.application_name.s = app_value;
            pgs->pg_stat.application_name.len = param_value_len;
        }
    }

    pgs->pg_stat.protocol_version = protocol_version;

    return PARSER_STATUS_FINISH;
}

// 解析认证消息
int CPostgreParser::parse_auth_msg(postgre_stream_t *pgs, const char *data, int len, const struct conn *pcon, CSession *p_session)
{
    // 检查基本参数
    if (!data || !pgs || len <= 0) {
        return PARSER_STATUS_DROP_DATA;
    }

    int offset = 0;
    while (offset < len)
    {
        // 检查剩余数据是否足够构成一个消息头部
        if (len - offset < POSTGRE_NORMAL_MIN_LEN)
        {
            return PARSER_STATUS_CONTINUE;
        }

        // 获取并验证消息长度
        char msg_type = data[offset];
        uint32_t msg_len = GET_MSG_LEN(data + offset + 1);
        
        if (msg_len < POSTGRE_MSG_HEADER_LEN || msg_len + 1 > (uint32_t)(len - offset)) {
            return PARSER_STATUS_CONTINUE;
        }

        // 根据消息类型进行处理
        switch (msg_type)
        {
            case POSTGRE_MSG_AUTHENTICATION:
            {
                // 确保有足够的数据读取认证类型
                if (msg_len < 8) // 4字节头部 + 4字节认证类型
                {
                    GWLOG_WARN(m_comm, "[PostgreSQL] Authentication message too short: %u bytes\n", msg_len);
                    return PARSER_STATUS_DROP_DATA;
                }
                
                uint32_t auth_type = GET_MSG_LEN(data + offset + 5);
                // 初始化认证上下文
                if (!pgs->auth_context) {
                    pgs->auth_context = new postgre_auth_context_t();
                    memset(pgs->auth_context, 0, sizeof(postgre_auth_context_t));
                }

                pgs->auth_context->current_auth_type = auth_type;

                switch (auth_type)
                {
                    case POSTGRE_AUTH_SASL_FINAL:
                    case POSTGRE_AUTH_OK:
                        break;
                    case POSTGRE_AUTH_CLEARTEXT_PASSWORD:
                    case POSTGRE_AUTH_MD5_PASSWORD:
                        pgs->auth_context->is_sasl_flow = false;
                        break;
                    case POSTGRE_AUTH_SASL:
                        pgs->auth_context->is_sasl_flow = true;
                        break;
                    case POSTGRE_AUTH_SASL_CONTINUE:
                        pgs->auth_context->auth_round++;
                        break;
                    default:
                        GWLOG_WARN(m_comm, "[PostgreSQL] Unsupported authentication type: %u\n", auth_type);
                        return PARSER_STATUS_DROP_DATA;
                }
                break;
            }
            case POSTGRE_MSG_PARAMETER_STATUS:
            {
                // 确保有足够的数据读取参数
                if (msg_len <= 5) // 至少需要包含一个参数名和空值
                {
                    GWLOG_WARN(m_comm, "[PostgreSQL] Parameter status message too short: %u bytes\n", msg_len);
                    return PARSER_STATUS_DROP_DATA;
                }
                
                // 处理参数状态消息
                const char *param_name = data + offset + 5;
                const char *param_end = data + offset + msg_len + 1; // 消息结束位置
                
                // 检查参数名是否有效
                size_t param_name_len = strnlen(param_name, param_end - param_name);
                if (param_name_len == (size_t)(param_end - param_name))
                {
                    GWLOG_WARN(m_comm, "[PostgreSQL] Parameter name without null terminator\n");
                    return PARSER_STATUS_DROP_DATA;
                }
                
                // 获取参数值
                const char *param_value = param_name + param_name_len + 1;
                
                // 检查参数值位置是否有效
                if (param_value >= param_end)
                {
                    GWLOG_WARN(m_comm, "[PostgreSQL] Parameter '%s' without value\n", param_name);
                    return PARSER_STATUS_DROP_DATA;
                }
                
                // 检查参数值是否有效
                size_t param_value_len = strnlen(param_value, param_end - param_value);
                if (param_value_len == (size_t)(param_end - param_value))
                {
                    GWLOG_WARN(m_comm, "[PostgreSQL] Parameter '%s' value without null terminator\n", param_name);
                    return PARSER_STATUS_DROP_DATA;
                }
                
                // 特别处理服务器版本参数
                if (strncmp(param_name, "server_version", param_name_len) == 0)
                {
                    if (param_value_len > 256)
                    {
                        GWLOG_WARN(m_comm, "[PostgreSQL] Server version too long: %zu bytes\n", param_value_len);
                        param_value_len = 256;  // 截断过长的版本字符串
                    }
                    
                    char *version_value = (char *)malloc(param_value_len + 1);
                    if (!version_value)
                    {
                        GWLOG_WARN(m_comm, "[PostgreSQL] Failed to allocate memory for server_version\n");
                        return PARSER_STATUS_DROP_DATA;
                    }
                    
                    memcpy(version_value, param_value, param_value_len);
                    version_value[param_value_len] = '\0';
                    
                    // 释放之前的版本字符串
                    if (pgs->pg_stat.server_version.s)
                    {
                        free((void*)pgs->pg_stat.server_version.s);
                    }
                    
                    pgs->pg_stat.server_version.s = version_value;
                    pgs->pg_stat.server_version.len = param_value_len;
                }
                break;
            }
            case POSTGRE_MSG_BACKEND_KEY_DATA:
            {
                // 检查消息长度
                if (msg_len != 12) // 4字节头部 + 4字节PID + 4字节密钥
                {
                    GWLOG_WARN(m_comm, "[PostgreSQL] Invalid backend key data length: %u, expected 12\n", msg_len);
                    return PARSER_STATUS_DROP_DATA;
                }
                
                // 处理后端密钥数据
                pgs->pg_stat.backend_pid = GET_MSG_LEN(data + offset + 5);
                pgs->pg_stat.backend_key = GET_MSG_LEN(data + offset + 9);
                break;
            }
            case POSTGRE_MSG_ERROR_RESPONSE:
            {                
                // 解析错误消息并创建结果集
                postgre_half_stream_t *p_new_stream = new postgre_half_stream_t();
                memset(p_new_stream, 0, sizeof(postgre_half_stream_t));
                
                // 使用公共函数解析错误消息字段
                char *detailed_err_msg = NULL;
                int err_code = 0;
                parse_error_response_fields(data + offset + 5, msg_len - 4, &detailed_err_msg, &err_code);
                // 设置错误信息到流状态
                if (detailed_err_msg) {
                    p_new_stream->data.err_msg = detailed_err_msg;
                    p_new_stream->data.err_code = err_code;

                    // 创建包含错误信息的message列结果集
                    column_def_t *col = new column_def_t();
                    if (col) {
                        memset(col, 0, sizeof(column_def_t));
                        char *name_copy = strdup("message");
                        if (name_copy) {
                            col->name.s = name_copy;
                            col->name.len = strlen(name_copy);
                            col->next = NULL;

                            result_set_t *current_rs = NULL;
                            add_result_set(&p_new_stream->data, col, 1, &current_rs);

                            // 创建错误消息行
                            postgre_row_data_t *row = new postgre_row_data_t();
                            if (row) {
                                row->row = new b_string_t*[1];
                                if (row->row) {
                                    row->field_count = 1;
                                    row->row[0] = new b_string_t();
                                    if (row->row[0]) {
                                        row->row[0]->s = strdup(detailed_err_msg);
                                        row->row[0]->len = strlen(detailed_err_msg);

                                        postgre_row_data_t **new_rows = new postgre_row_data_t*[1];
                                        if (new_rows) {
                                            new_rows[0] = row;
                                            update_result_set(current_rs, new_rows, 1);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                // 设置认证失败状态并上传登录事件
                pgs->pg_stat.transaction_status = POSTGRE_TRANS_ERROR;
                if (pgs->pg_stat.startup_close_time == 0) {
                    pgs->pg_stat.startup_close_time = get_time_ts_ms(p_session, m_conf_pcap_timestamp);
                }
                
                // 上传认证失败的登录事件
                handle_login_event(pgs, pcon, &p_new_stream->data);

                del_postgre_merged_data(&p_new_stream->data);
                delete p_new_stream;
                
                return PARSER_STATUS_FINISH;
            }
            case POSTGRE_MSG_READY_FOR_QUERY:
            {
                // 检查消息长度
                if (msg_len != 5) // 4字节头部 + 1字节事务状态
                {
                    GWLOG_WARN(m_comm, "[PostgreSQL] Invalid ready for query message length: %u, expected 5\n", msg_len);
                    return PARSER_STATUS_DROP_DATA;
                }
                
                char trans_status = data[offset + 5];
                if (trans_status == POSTGRE_TRANS_IDLE ||
                    trans_status == POSTGRE_TRANS_IN_TRANSACTION ||
                    trans_status == POSTGRE_TRANS_ERROR)
                {
                    pgs->pg_stat.transaction_status = trans_status;
                    if (pgs->pg_stat.startup_close_time == 0){
                        pgs->pg_stat.startup_close_time = get_time_ts_ms(p_session, m_conf_pcap_timestamp);
                    }
                    // 在startup阶段结束后上传登录事件
                    handle_login_event(pgs, pcon);
                }
                else
                {
                    GWLOG_WARN(m_comm, "[PostgreSQL] Invalid transaction status: %c\n", trans_status);
                    return PARSER_STATUS_DROP_DATA;
                }
                break;
            }
            default:
                // 处理其他类型的认证阶段消息
                break;
        }

        // 移动到下一个消息
        offset += msg_len + 1;
    }

    return PARSER_STATUS_FINISH;
}

// 解析客户端认证响应消息
int CPostgreParser::parse_client_auth_msg(postgre_stream_t *pgs, const char *data, int len)
{
    if (!data || !pgs || len <= 0) {
        return PARSER_STATUS_DROP_DATA;
    }

    // 对于客户端认证消息，需要至少有消息类型和长度
    if (len < POSTGRE_NORMAL_MIN_LEN)
    {
        return PARSER_STATUS_CONTINUE;
    }

    // 获取消息长度并验证
    uint32_t msg_len = GET_MSG_LEN(data + 1);
    if (msg_len < POSTGRE_MSG_HEADER_LEN || msg_len + 1 > (uint32_t)len) {
        return PARSER_STATUS_CONTINUE;
    }

    // 检查消息类型
    char msg_type = data[0];
    switch (msg_type)
    {
        case POSTGRE_MSG_PASSWORD:
        {
            // 确保密码消息的长度合理
            if (msg_len <= 4)
            {
                return PARSER_STATUS_DROP_DATA;
            }

            // 根据认证上下文决定如何处理密码消息
            if (pgs->auth_context && pgs->auth_context->is_sasl_flow) {
                // SASL认证流程
                handle_sasl_response_message(pgs, data + 5, msg_len - 4);
            } else {
                // 传统密码认证，提取密码
                handle_traditional_password_message(pgs, data + 5, msg_len - 4);
            }

            break;
        }
        case POSTGRE_MSG_TERMINATE:
        {
            // 终止消息，通常不需要特殊处理
            if (msg_len != 4)  // 终止消息应该只有4字节头部
            {
                return PARSER_STATUS_DROP_DATA;
            }
            break;
        }
        case POSTGRE_MSG_SASL_CONTINUE:
        {
            // SASL认证响应
            // 这里只需验证基本格式，具体SASL认证内容不做解析
            if (msg_len <= 4)
            {
                return PARSER_STATUS_DROP_DATA;
            }
            break;
        }
        default:
        {
            // 不支持的客户端认证消息类型
            return PARSER_STATUS_DROP_DATA;
        }
    }

    return PARSER_STATUS_FINISH;
}

// 统一的SQL语句添加函数，支持丢包信息
void CPostgreParser::add_sql_statement(postgre_parsed_data_t *p_data, const char *sql, size_t sql_len,
                                      bool is_incomplete, int lost_bytes)
{
    if (!p_data || !sql || sql_len == 0)
    {
        GWLOG_WARN(m_comm, "[PostgreSQL][AddSQL] Invalid parameters: p_data=%p, sql=%p, sql_len=%zu\n", p_data, sql, sql_len);
        return;
    }

    sql_statement_t *new_sql = new sql_statement_t();
    if (!new_sql)
    {
        GWLOG_ERROR(m_comm, "[PostgreSQL][AddSQL] Failed to allocate SQL statement structure\n");
        return;
    }
    memset(new_sql, 0, sizeof(sql_statement_t));

    // 分配并复制SQL文本
    char *sql_text = (char *)malloc(sql_len + 1);
    if (!sql_text)
    {
        GWLOG_ERROR(m_comm, "[PostgreSQL][AddSQL] Failed to allocate SQL text buffer, length=%zu\n", sql_len);
        delete new_sql;
        return;
    }
    memcpy(sql_text, sql, sql_len);
    sql_text[sql_len] = '\0';

    new_sql->sql.s = sql_text;
    new_sql->sql.len = sql_len;
    new_sql->next = NULL;
    new_sql->is_incomplete = is_incomplete;
    new_sql->lost_bytes = lost_bytes;

    // 添加到链表末尾（使用安全的链表操作）
    if (!p_data->sql_list)
    {
        p_data->sql_list = new_sql;
    }
    else
    {
        sql_statement_t *last = p_data->sql_list;
        while (last->next)
        {
            last = last->next;
        }
        last->next = new_sql;
    }
    p_data->sql_count++;
}

void CPostgreParser::free_sql_list(sql_statement_t *sql_list)
{
    while (sql_list)
    {
        // 防御性检查：确保当前节点有效
        if (sql_list == NULL) {
            GWLOG_WARN(m_comm, "[PostgreSQL][MemorySafety] Null pointer encountered in SQL list\n");
            break;
        }
        
        // 提前保存下一个节点，避免在释放当前节点后访问已释放内存
        sql_statement_t *next = sql_list->next;
        
        // 防御性检查：验证next指针的合理性
        if (next != NULL && (uintptr_t)next < 0x1000) {
            // next指针看起来不合理（太小的地址），可能已损坏
            GWLOG_ERROR(m_comm, "[PostgreSQL][MemorySafety] Corrupted next pointer detected: %p, breaking list traversal\n", next);
            next = NULL; // 停止链表遍历，避免继续访问损坏的内存
        }
        
        // 释放SQL字符串内存
        if (sql_list->sql.s)
        {
            free((void*)sql_list->sql.s);
            sql_list->sql.s = NULL;  // 防止double free
        }
        
        // 清理其他字段
        sql_list->sql.len = 0;
        sql_list->next = NULL;  // 防止悬空指针
        
        // 释放节点结构体
        delete sql_list;
        
        // 移动到下一个节点
        sql_list = next;
    }
}

// 添加prepared statement管理函数
prepared_statement_t* CPostgreParser::find_prepared_statement(postgre_stream_t *pgs, const char *name)
{
    if (!pgs || !name) return NULL;
    
    prepared_statement_t *stmt = pgs->prepared_statements;
    while (stmt) {
        if (strcmp(stmt->name, name) == 0) {
            return stmt;
        }
        stmt = stmt->next;
    }
    return NULL;
}

portal_t* CPostgreParser::find_portal(postgre_stream_t *pgs, const char *name)
{
    if (!pgs || !name) return NULL;
    
    portal_t *portal = pgs->portals;
    while (portal) {
        if (strcmp(portal->name, name) == 0) {
            return portal;
        }
        portal = portal->next;
    }
    return NULL;
}

void CPostgreParser::add_prepared_statement(postgre_stream_t *pgs, const char *name, sql_statement_t *sql)
{
    if (!pgs || !name) {
        GWLOG_WARN(m_comm, "[PostgreSQL][PreparedStmt] Invalid parameters: pgs=%p, name=%p\n", pgs, name);
        return;
    }
    
    bool is_unnamed = (strlen(name) == 0);
    uint64_t current_time = time(NULL) * 1000ULL;
    
    // 如果是unnamed statement，先清理现有的unnamed statement
    if (is_unnamed) {
        prepared_statement_t *stmt = pgs->prepared_statements;
        prepared_statement_t *prev = NULL;
        
        while (stmt) {
            if (stmt->is_unnamed) {
                // 移除现有的unnamed statement
                if (prev) {
                    prev->next = stmt->next;
                } else {
                    pgs->prepared_statements = stmt->next;
                }
                
                // 在释放statement前，清理所有portal中对它的引用
                clear_portal_references_to_statement(pgs, stmt);
                cleanup_single_prepared_statement(stmt);
                break;
            }
            prev = stmt;
            stmt = stmt->next;
        }
    } else {
        // 如果已存在同名statement，先删除旧的SQL
        prepared_statement_t *existing = find_prepared_statement(pgs, name);
        if (existing) {
            if (existing->sql_list) {
                free_sql_list(existing->sql_list);
                existing->sql_list = NULL;  // 防止悬空指针
            }
            existing->sql_list = sql;
            existing->state = STMT_STATE_PREPARED;
            existing->create_time = current_time;
            return;
        }
    }
    
    // 创建新的statement
    prepared_statement_t *stmt = new prepared_statement_t();
    if (!stmt) {
        GWLOG_ERROR(m_comm, "[PostgreSQL][PreparedStmt] Failed to allocate prepared statement structure\n");
        return;
    }
    
    // 初始化所有字段
    memset(stmt, 0, sizeof(prepared_statement_t));
    
    // 使用防御性的字符串复制
    stmt->name = strdup(name);
    if (!stmt->name) {
        GWLOG_ERROR(m_comm, "[PostgreSQL][PreparedStmt] Failed to duplicate statement name\n");
        delete stmt;
        return;
    }
    
    stmt->sql_list = sql;
    stmt->state = STMT_STATE_PREPARED;
    stmt->param_count = 0;
    stmt->param_types = NULL;
    stmt->is_unnamed = is_unnamed;
    stmt->create_time = current_time;
    stmt->next = pgs->prepared_statements;
    pgs->prepared_statements = stmt;
}

void CPostgreParser::add_portal(postgre_stream_t *pgs, const char *name, prepared_statement_t *stmt)
{
    if (!pgs || !name || !stmt) return;
    
    bool is_unnamed = (strlen(name) == 0);
    uint64_t current_time = time(NULL) * 1000ULL;
    
    // 如果是unnamed portal，先清理现有的unnamed portal
    if (is_unnamed) {
        portal_t *portal = pgs->portals;
        portal_t *prev = NULL;
        
        while (portal) {
            if (portal->is_unnamed) {
                // 移除现有的unnamed portal
                if (prev) {
                    prev->next = portal->next;
                } else {
                    pgs->portals = portal->next;
                }
                
                cleanup_single_portal(portal);
                break;
            }
            prev = portal;
            portal = portal->next;
        }
    } else {
        // 如果已存在同名portal，更新它
        portal_t *existing = find_portal(pgs, name);
        if (existing) {
            existing->stmt = stmt;
            existing->state = PORTAL_STATE_BOUND;
            existing->create_time = current_time;
            return;
        }
    }
    
    // 创建新的portal
    portal_t *portal = new portal_t();
    if (!portal) return;
    
    // 初始化所有字段
    memset(portal, 0, sizeof(portal_t));
    
    portal->name = strdup(name);
    portal->stmt = stmt;
    portal->state = PORTAL_STATE_BOUND;
    portal->param_formats = NULL;
    portal->param_values = NULL;
    portal->param_count = 0;
    portal->result_formats = NULL;
    portal->result_format_count = 0;
    portal->max_rows = 0;
    portal->current_row = 0;
    portal->is_unnamed = is_unnamed;
    portal->create_time = current_time;
    portal->last_execute_time = 0;
    portal->next = pgs->portals;
    pgs->portals = portal;
}

// 客户端请求消息解析函数
int CPostgreParser::parse_query_msg(postgre_stream_t *pgs, const char *data, int len, struct half_stream *hlf)
{
    int offset = 0;
    char *last_parsed_stmt_name = NULL;  // 记录最近解析的statement名称

    while (offset < len)
    {
        // 检查剩余数据是否足够构成一个消息
        if (len - offset < POSTGRE_NORMAL_MIN_LEN)
        {
            return PARSER_STATUS_CONTINUE;
        }

        uint32_t msg_len = GET_MSG_LEN(data + offset + 1);

        // 基本合理性检查
        if (msg_len < POSTGRE_MSG_HEADER_LEN || msg_len > 0x10000000) { // 256MB上限
            GWLOG_WARN(m_comm, "[PostgreSQL][PacketLoss] Invalid message length: %u at offset %d\n", msg_len, offset);

            // 检查是否有已解析但未添加到请求链表的prepared statement
            // 这种情况通常发生在Parse消息完整解析后，Bind消息丢包的场景
            check_and_add_specific_prepared_statement(pgs, last_parsed_stmt_name);

            // 清理记录的statement名称
            if (last_parsed_stmt_name) {
                free(last_parsed_stmt_name);
                last_parsed_stmt_name = NULL;
            }

            return PARSER_STATUS_FINISH;
        }

        char msg_type = data[offset];
        const char *msg_data = data + offset + 5;
        
        switch (msg_type)
        {
            case POSTGRE_MSG_QUERY:
            {
                // Simple Query
                size_t query_len = msg_len - 4;

                postgre_half_stream_t *phs = pgs->p_postgre_client;
                if(!phs){
                    return PARSER_STATUS_CONTINUE;
                }

                // 丢包检测
                int lost_bytes = 0;
                if (detect_packet_loss(hlf, msg_len, len - offset, &lost_bytes, offset)) {
                    GWLOG_WARN(m_comm, "[PostgreSQL][PacketLoss] Simple Query affected by packet loss, "
                               "processing with loss handling, lost_bytes=%d\n", lost_bytes);

                    // 修正expect_rsp_ack值（针对最后分段丢失）
                    if (hlf && hlf->complete != 0 && lost_bytes > 0) {
                        phs->data.expect_rsp_ack = hlf->first_seq + msg_len + 1;
                        GWLOG_INFO(m_comm, "[PostgreSQL][PacketLoss] Corrected expect_rsp_ack for tail loss: %u\n",
                                   phs->data.expect_rsp_ack);
                    }

                    // 直接添加不完整的SQL语句，TCP层已经处理了丢包标记
                    add_sql_statement(&phs->data, msg_data, query_len, true, lost_bytes);
                    if (pgs->copy_context) {
                        // 重置COPY状态以避免状态不一致
                        pgs->copy_context->state = COPY_STATE_NONE;
                    }
                    return PARSER_STATUS_FINISH;
                }

                // 处理多条SQL语句的情况
                const char *current_pos = msg_data;
                const char *end_pos = msg_data + query_len;
                bool in_string = false;
                bool in_escape = false;
                bool in_comment = false;
                bool in_line_comment = false;
                const char *stmt_start = current_pos;
                
                while (current_pos < end_pos) {
                    char c = *current_pos;
                    
                    // 处理字符串内的内容
                    if (in_string) {
                        if (in_escape) {
                            in_escape = false;
                        } else if (c == '\\') {
                            in_escape = true;
                        } else if (c == '\'') {
                            in_string = false;
                        }
                    }
                    // 处理行注释
                    else if (in_line_comment) {
                        if (c == '\n') {
                            in_line_comment = false;
                        }
                    }
                    // 处理块注释
                    else if (in_comment) {
                        if (c == '*' && current_pos + 1 < end_pos && *(current_pos + 1) == '/') {
                            in_comment = false;
                            current_pos++; // 跳过'/'
                        }
                    }
                    // 处理普通SQL文本
                    else {
                        if (c == '\'') {
                            in_string = true;
                        } else if (c == '-' && current_pos + 1 < end_pos && *(current_pos + 1) == '-') {
                            in_line_comment = true;
                            current_pos++; // 跳过第二个'-'
                        } else if (c == '/' && current_pos + 1 < end_pos && *(current_pos + 1) == '*') {
                            in_comment = true;
                            current_pos++; // 跳过'*'
                        } else if (c == ';') {
                            // 找到语句结束符，添加当前语句
                            size_t stmt_len = current_pos - stmt_start + 1; // 包含分号
                            
                            // 跳过空白语句
                            bool is_empty = true;
                            for (const char *p = stmt_start; p < stmt_start + stmt_len - 1; p++) {
                                if (!isspace(*p)) {
                                    is_empty = false;
                                    break;
                                }
                            }
                            
                            if (!is_empty) {
                                add_sql_statement(&phs->data, stmt_start, stmt_len);
                            }
                            
                            // 更新下一个语句的起始位置
                            stmt_start = current_pos + 1;
                        }
                    }
                    
                    current_pos++;
                }
                
                // 处理最后一个语句（如果没有分号结尾）
                if (stmt_start < end_pos) {
                    size_t stmt_len = end_pos - stmt_start;
                    
                    // 跳过空白语句
                    bool is_empty = true;
                    for (const char *p = stmt_start; p < end_pos; p++) {
                        if (!isspace(*p) && *p != '\0') {
                            is_empty = false;
                            break;
                        }
                    }
                    
                    if (!is_empty) {
                        add_sql_statement(&phs->data, stmt_start, stmt_len);
                    }
                }

                // 检查是否是COPY命令或流复制命令
                // 添加空指针检查，防止empty_query场景下的段错误
                if (!is_copy_active(pgs) && phs->data.sql_list != NULL &&
                    phs->data.sql_list->sql.s != NULL && phs->data.sql_list->sql.len >= 4)
                {
                    const char *sql = phs->data.sql_list->sql.s;
                    size_t sql_len = phs->data.sql_list->sql.len;

                    if (strncasecmp(sql, "COPY", 4) == 0) {
                        // 确保COPY上下文存在
                        if (ensure_copy_context(pgs)) {
                            // 分析COPY命令类型和HEADER选项
                            if (strcasestr(sql, "FROM")) {
                                // COPY FROM - 客户端向服务器传输
                                // 分析HEADER选项
                                bool has_header = false;
                                const char *with_pos = strcasestr(sql, "WITH");
                                if (with_pos) {
                                    const char *header_pos = strcasestr(with_pos, "HEADER");
                                    if (header_pos) {
                                        // 验证HEADER是独立关键字
                                        const char *end_pos = header_pos + 6;
                                        if (end_pos >= sql + sql_len ||
                                            isspace(*end_pos) || *end_pos == ',' || *end_pos == ')' || *end_pos == ';') {
                                            has_header = true;
                                        }
                                    }
                                }

                                // 检查CSV HEADER简化语法
                                if (!has_header) {
                                    const char *csv_pos = strcasestr(sql, "CSV");
                                    if (csv_pos) {
                                        const char *header_pos = strcasestr(csv_pos, "HEADER");
                                        if (header_pos) {
                                            has_header = true;
                                        }
                                    }
                                }

                                pgs->copy_context->has_header = has_header;
                                pgs->copy_context->header_processed = false;

                                GWLOG_DEBUG(m_comm, "[PostgreSQL] COPY FROM detected, has_header=%s\n",
                                           has_header ? "true" : "false");

                            } else if (strcasestr(sql, "TO")) {
                                // COPY TO - 服务器向客户端传输
                                pgs->copy_context->has_header = false; // COPY TO通常不涉及客户端头部行
                                pgs->copy_context->header_processed = false;

                                GWLOG_DEBUG(m_comm, "[PostgreSQL] COPY TO detected\n");
                            }
                        }
                    } else if (is_replication_command(data, len)) {
                        if (ensure_copy_context(pgs)) {
                            pgs->copy_context->is_replication_mode = true;
                        }
                    }
                }
                break;
            }
            case POSTGRE_MSG_PARSE:
            {
                // Extended Query - Parse

                postgre_half_stream_t *phs = pgs->p_postgre_client;
                if(!phs){
                    return PARSER_STATUS_CONTINUE;
                }

                // 使用统一的丢包检测函数
                int lost_bytes = 0;
                if (detect_packet_loss(hlf, msg_len, len - offset, &lost_bytes, offset)) {
                    GWLOG_WARN(m_comm, "[PostgreSQL][PacketLoss] Parse message affected by packet loss, "
                               "processing with loss handling, lost_bytes=%d\n", lost_bytes);
                    
                    // 最后一段，丢弃处理
                    if(hlf->first_lost_offset == 0){
                        return PARSER_STATUS_FINISH;
                    }
                    
                    // 简化的内联丢包处理逻辑
                    // 1. 尝试解析statement名称（如果数据可用）
                    const char *stmt_name = msg_data;
                    size_t available_data_len = msg_len - 4;
                    size_t stmt_name_len = strnlen(stmt_name, available_data_len);

                    // 2. 计算可用的SQL内容
                    const char *sql_start = NULL;
                    size_t available_sql_len = 0;
                    if (stmt_name_len < available_data_len) {
                        sql_start = msg_data + stmt_name_len + 1;
                        available_sql_len = hlf->first_lost_offset - stmt_name_len - 5 + hlf->missing_info_len - 1; //偏移数据位置为第一次丢包处
                    }

                    // 3. 直接添加不完整的SQL语句
                    if (sql_start && available_sql_len > 0) {
                        add_sql_statement(&phs->data, sql_start, available_sql_len, true, lost_bytes);
                        GWLOG_INFO(m_comm, "[PostgreSQL][PacketLoss] Added incomplete Parse SQL, "
                                   "stmt_name='%.*s', sql_len=%zu, lost_bytes=%d\n",
                                   (int)stmt_name_len, stmt_name, available_sql_len, lost_bytes);
                    } else {
                        GWLOG_WARN(m_comm, "[PostgreSQL][PacketLoss] No SQL content available in incomplete Parse message\n");
                    }

                    return PARSER_STATUS_FINISH;
                }

                const char *stmt_name = msg_data;
                size_t stmt_name_len = strlen(stmt_name);
                const char *query = msg_data + stmt_name_len + 1;
                size_t query_len = msg_len - 4 - stmt_name_len - 1;

                // 创建SQL语句并添加到prepared statements
                sql_statement_t *sql = new sql_statement_t();
                if (sql)
                {
                    char *sql_text = (char *)malloc(query_len + 1);
                    if (sql_text)
                    {
                        memcpy(sql_text, query, query_len);
                        sql_text[query_len] = '\0';
                        sql->sql.s = sql_text;
                        sql->sql.len = query_len;
                        sql->next = NULL;
                        sql->is_incomplete = false;  // 正常Parse消息为完整
                        sql->lost_bytes = 0;         // 无丢失字节
                        
                        // 添加到prepared statements
                        add_prepared_statement(pgs, stmt_name, sql);

                        // 记录最近解析的statement名称，用于后续的孤立检测
                        if (last_parsed_stmt_name) {
                            free(last_parsed_stmt_name);
                        }
                        last_parsed_stmt_name = strdup(stmt_name);
                    }
                    else
                    {
                        delete sql;
                    }
                }
                break;
            }
            case POSTGRE_MSG_BIND:
            {
                // Extended Query - Bind
                // 解析Bind消息格式：
                // Portal名称(C字符串) + Statement名称(C字符串) + 参数格式代码数量(int16) + 
                // 参数格式代码数组(int16[]) + 参数值数量(int16) + 参数值数组 + 结果格式代码数量(int16) + 结果格式代码数组(int16[])
                
                int parse_offset = 0;
                
                // 解析portal名称
                const char *portal_name = msg_data + parse_offset;
                size_t portal_name_len = strlen(portal_name);
                parse_offset += portal_name_len + 1;
                
                // 检查剩余长度
                if (parse_offset >= msg_len - 4) {
                    GWLOG_WARN(m_comm, "[PostgreSQL] Invalid Bind message: insufficient data for statement name\n");
                    break;
                }
                
                // 解析statement名称
                const char *stmt_name = msg_data + parse_offset;
                size_t stmt_name_len = strlen(stmt_name);
                parse_offset += stmt_name_len + 1;
                
                // 检查剩余长度
                if (parse_offset + 2 > msg_len - 4) {
                    GWLOG_WARN(m_comm, "[PostgreSQL] Invalid Bind message: insufficient data for parameter format count\n");
                    break;
                }
                
                // 解析参数格式代码数量
                uint16_t param_format_count = ntohs(*((uint16_t*)(msg_data + parse_offset)));
                parse_offset += 2;
                
                // 跳过参数格式代码数组
                if (parse_offset + param_format_count * 2 > msg_len - 4) {
                    GWLOG_WARN(m_comm, "[PostgreSQL] Invalid Bind message: insufficient data for parameter formats\n");
                    break;
                }
                parse_offset += param_format_count * 2;
                
                // 检查剩余长度
                if (parse_offset + 2 > msg_len - 4) {
                    GWLOG_WARN(m_comm, "[PostgreSQL] Invalid Bind message: insufficient data for parameter count\n");
                    break;
                }
                
                // 解析参数值数量
                uint16_t param_count = ntohs(*((uint16_t*)(msg_data + parse_offset)));
                parse_offset += 2;
                
                // 查找prepared statement
                prepared_statement_t *stmt = find_prepared_statement(pgs, stmt_name);
                if (!stmt) {
                    GWLOG_WARN(m_comm, "[PostgreSQL] Prepared statement '%s' not found for Bind\n", stmt_name);
                    break;
                }
                
                // 解析参数值
                char **param_values = NULL;
                if (param_count > 0) {
                    param_values = (char**)calloc(param_count, sizeof(char*));
                    if (!param_values) {
                        GWLOG_ERROR(m_comm, "[PostgreSQL] Failed to allocate memory for parameter values\n");
                        break;
                    }
                    
                    for (uint16_t i = 0; i < param_count; i++) {
                        // 检查剩余长度
                        if (parse_offset + 4 > msg_len - 4) {
                            GWLOG_WARN(m_comm, "[PostgreSQL] Invalid Bind message: insufficient data for parameter %d length\n", i);
                            // 清理已分配的参数值
                            for (uint16_t j = 0; j < i; j++) {
                                if (param_values[j]) {
                                    free(param_values[j]);
                                }
                            }
                            free(param_values);
                            param_values = NULL;
                            break;
                        }
                        
                        // 解析参数长度
                        int32_t param_len = ntohl(*((int32_t*)(msg_data + parse_offset)));
                        parse_offset += 4;
                        
                        if (param_len == -1) {
                            // NULL参数
                            param_values[i] = NULL;
                        } else if (param_len >= 0) {
                            // 检查剩余长度
                            if (parse_offset + param_len > msg_len - 4) {
                                GWLOG_WARN(m_comm, "[PostgreSQL] Invalid Bind message: insufficient data for parameter %d value\n", i);
                                // 清理已分配的参数值
                                for (uint16_t j = 0; j < i; j++) {
                                    if (param_values[j]) {
                                        free(param_values[j]);
                                    }
                                }
                                free(param_values);
                                param_values = NULL;
                                break;
                            }
                            
                            // 分配内存并复制参数值
                            param_values[i] = (char*)malloc(param_len + 1);
                            if (param_values[i]) {
                                memcpy(param_values[i], msg_data + parse_offset, param_len);
                                param_values[i][param_len] = '\0'; // 确保字符串结束
                            } else {
                                GWLOG_ERROR(m_comm, "[PostgreSQL] Failed to allocate memory for parameter %d\n", i);
                                // 清理已分配的参数值
                                for (uint16_t j = 0; j < i; j++) {
                                    if (param_values[j]) {
                                        free(param_values[j]);
                                    }
                                }
                                free(param_values);
                                param_values = NULL;
                                break;
                            }
                            parse_offset += param_len;
                        } else {
                            GWLOG_WARN(m_comm, "[PostgreSQL] Invalid parameter length: %d\n", param_len);
                            // 清理已分配的参数值
                            for (uint16_t j = 0; j < i; j++) {
                                if (param_values[j]) {
                                    free(param_values[j]);
                                }
                            }
                            free(param_values);
                            param_values = NULL;
                            break;
                        }
                    }
                }
                
                // 创建portal并保存参数
                portal_t *portal = find_portal(pgs, portal_name);
                if (portal) {
                    // 更新现有portal
                    portal->stmt = stmt;
                    portal->state = PORTAL_STATE_BOUND;
                    
                    // 清理旧的参数值
                    if (portal->param_values) {
                        for (uint16_t i = 0; i < portal->param_count; i++) {
                            if (portal->param_values[i]) {
                                free(portal->param_values[i]);
                            }
                        }
                        free(portal->param_values);
                    }
                    
                    // 保存新的参数
                    portal->param_values = param_values;
                    portal->param_count = param_count;
                } else {
                    // 创建新portal
                    add_portal(pgs, portal_name, stmt);
                    portal = find_portal(pgs, portal_name);
                    if (portal) {
                        portal->param_values = param_values;
                        portal->param_count = param_count;
                    }
                }
                
                // 实例化SQL语句并添加到当前请求
                postgre_half_stream_t *phs = pgs->p_postgre_client;
                if (phs && stmt->sql_list && stmt->sql_list->sql.s) {
                    char *instantiated_sql = NULL;
                    size_t instantiated_len = 0;
                    
                    // 如果有参数，实例化SQL语句
                    if (param_count > 0 && param_values && stmt->sql_list && stmt->sql_list->sql.s) {
                        instantiated_sql = instantiate_sql_with_parameters(
                            stmt->sql_list->sql.s, stmt->sql_list->sql.len,
                            param_values, param_count, &instantiated_len);
                    }
                    
                    // 如果实例化成功，使用实例化的SQL；否则使用原始SQL
                    const char *sql_to_add = instantiated_sql ? instantiated_sql : (stmt->sql_list ? stmt->sql_list->sql.s : "");
                    size_t sql_len_to_add = instantiated_sql ? instantiated_len : (stmt->sql_list ? stmt->sql_list->sql.len : 0);
                    
                    // 检查是否已经添加过相同的SQL语句
                    bool sql_exists = false;
                    sql_statement_t *existing_sql = phs->data.sql_list;
                    while (existing_sql) {
                        if (existing_sql->sql.len == sql_len_to_add &&
                            memcmp(existing_sql->sql.s, sql_to_add, existing_sql->sql.len) == 0) {
                            sql_exists = true;
                            break;
                        }
                        existing_sql = existing_sql->next;
                    }
                    
                    // 只有当SQL语句不存在时才添加
                    if (!sql_exists) {
                        add_sql_statement(&phs->data, sql_to_add, sql_len_to_add);
                        
                        // 检查是否是COPY命令或START_REPLICATION命令
                        // 状态管理使用 pgs->copy_context
                        if (!is_copy_active(pgs) && (strncmp(sql_to_add, "COPY", 4) == 0 || strncasecmp(sql_to_add, "START_REPLICATION", 17) == 0)) {
                            // 不再调用init_copy_request，状态将在收到COPY响应时设置
                            GWLOG_DEBUG(m_comm, "[PostgreSQL] Detected COPY/START_REPLICATION command in portal, waiting for server response\n");
                        }
                    }
                    
                    // 清理实例化的SQL内存
                    if (instantiated_sql) {
                        free(instantiated_sql);
                    }
                }
                
                // 如果portal创建失败，清理参数值内存
                if (!portal && param_values) {
                    for (uint16_t i = 0; i < param_count; i++) {
                        if (param_values[i]) {
                            free(param_values[i]);
                        }
                    }
                    free(param_values);
                }
                
                break;
            }
            case POSTGRE_MSG_EXECUTE:
            {
                // Execute消息处理
                // 格式: portal名称 + 最大返回行数
                const char *portal_name = msg_data;
                size_t portal_name_len = strlen(portal_name);
                
                // 获取最大行数限制 (int32)
                int32_t max_rows = 0;
                if (msg_len >= (int)(portal_name_len + 1 + 4)) {
                    max_rows = ntohl(*((int32_t*)(msg_data + portal_name_len + 1)));
                }
                
                // 查找对应的portal
                portal_t *portal = find_portal(pgs, portal_name);
                
                // 验证portal状态
                if (!portal) {
                    GWLOG_WARN(m_comm, "[PostgreSQL] Portal '%s' not found for EXECUTE\n", portal_name);
                    break;
                }
                
                if (portal->state != PORTAL_STATE_BOUND && portal->state != PORTAL_STATE_SUSPENDED) {
                    GWLOG_WARN(m_comm, "[PostgreSQL] Portal '%s' in invalid state %d for EXECUTE\n", 
                              portal_name, portal->state);
                    break;
                }
                
                // 更新portal状态
                portal->state = PORTAL_STATE_EXECUTING;
                portal->max_rows = max_rows;
                portal->last_execute_time = time(NULL) * 1000ULL;
                
                // 防御性检查：确保portal->stmt有效
                bool is_valid_stmt = (portal != NULL && portal->stmt != NULL);
                bool is_valid_sql = (is_valid_stmt && portal->stmt->sql_list != NULL);
                bool is_valid_sql_text = (is_valid_sql && portal->stmt->sql_list->sql.s != NULL && portal->stmt->sql_list->sql.len > 0);
                
                if (is_valid_sql_text)
                {
                    postgre_half_stream_t *phs = pgs->p_postgre_client;
                    if (phs)
                    {
                        char *instantiated_sql = NULL;
                        size_t instantiated_len = 0;
                        
                        // 如果Portal有参数，实例化SQL语句
                        if (portal->param_count > 0 && portal->param_values && is_valid_sql_text) {
                            instantiated_sql = instantiate_sql_with_parameters(
                                portal->stmt->sql_list->sql.s, portal->stmt->sql_list->sql.len,
                                portal->param_values, portal->param_count, &instantiated_len);
                        }
                        
                        // 如果实例化成功，使用实例化的SQL；否则使用原始SQL
                        const char *sql_to_add = instantiated_sql ? instantiated_sql : (is_valid_sql_text ? portal->stmt->sql_list->sql.s : "");
                        size_t sql_len_to_add = instantiated_sql ? instantiated_len : (is_valid_sql_text ? portal->stmt->sql_list->sql.len : 0);
                        
                        // 检查是否已经添加过相同的SQL语句
                        bool sql_exists = false;
                        sql_statement_t *existing_sql = phs->data.sql_list;
                        while (existing_sql)
                        {
                            if (existing_sql->sql.len == sql_len_to_add &&
                                memcmp(existing_sql->sql.s, sql_to_add, existing_sql->sql.len) == 0)
                            {
                                sql_exists = true;
                                break;
                            }
                            existing_sql = existing_sql->next;
                        }
                        
                        // 只有当SQL语句不存在时才添加
                        if (!sql_exists)
                        {
                            add_sql_statement(&phs->data, sql_to_add, sql_len_to_add);
                        }
                        
                        // 清理实例化的SQL内存
                        if (instantiated_sql) {
                            free(instantiated_sql);
                        }
                        
                        // 对于游标操作，复制列定义
                        if (portal->col_def) {
                            // 先计算portal实际的列数
                            int actual_col_count = 0;
                            column_def_t *temp_col = portal->col_def;
                            while (temp_col) {
                                actual_col_count++;
                                temp_col = temp_col->next;
                            }
                            
                            if (actual_col_count > 0) {
                                result_set_t *current_rs = NULL;
                                add_result_set(&phs->data, NULL, actual_col_count, &current_rs);

                                // 从portal复制列定义到结果集中
                                // 克隆列定义链表
                                column_def_t *portal_col = portal->col_def;
                                column_def_t *last_col = NULL;
                                
                                while (portal_col) {
                                    column_def_t *new_col = new column_def_t();
                                    if (new_col && portal_col->name.s) {
                                        char *name_copy = strdup(portal_col->name.s);
                                        if (name_copy) {
                                            new_col->name.s = name_copy;
                                            new_col->name.len = portal_col->name.len;
                                            new_col->table_oid = portal_col->table_oid;
                                            new_col->column_id = portal_col->column_id;
                                            new_col->type_oid = portal_col->type_oid;
                                            new_col->type_len = portal_col->type_len;
                                            new_col->type_mod = portal_col->type_mod;
                                            new_col->format_code = portal_col->format_code;
                                            new_col->next = NULL;
                                            
                                            // 使用尾插法
                                            if (last_col == NULL) {
                                                current_rs->col_def = new_col;  // 第一个节点
                                            } else {
                                                last_col->next = new_col;      // 之前的节点指向新节点
                                            }
                                            last_col = new_col;               // 更新尾节点
                                        } else {
                                            delete new_col;
                                            break;
                                        }
                                    } else if (new_col) {
                                        delete new_col;
                                    }
                                    portal_col = portal_col->next;
                                }
                            }
                        }
                    }
                }
                break;
            }
            case POSTGRE_MSG_DESCRIBE:
            case POSTGRE_MSG_FLUSH:
            case POSTGRE_MSG_SYNC:
                break;
            case POSTGRE_MSG_CLOSE:
            {
                char object_type = msg_data[0]; // 'S' 表示 Statement, 'P' 表示 Portal
                const char *object_name = msg_data + 1;

                if (object_type == 'S')
                {
                    // 关闭prepared statement
                    prepared_statement_t *stmt = find_prepared_statement(pgs, object_name);
                    if (stmt)
                    {
                        // 先标记为关闭状态
                        stmt->state = STMT_STATE_CLOSED;
                        
                        prepared_statement_t *prev = NULL;
                        prepared_statement_t *curr = pgs->prepared_statements;
                        
                        // 在链表中查找prepared statement
                        while (curr && curr != stmt)
                        {
                            prev = curr;
                            curr = curr->next;
                        }
                        
                        if (curr)
                        {
                            // 从链表中移除
                            if (prev)
                            {
                                prev->next = curr->next;
                            }
                            else
                            {
                                pgs->prepared_statements = curr->next;
                            }
                            
                            // 在释放statement前，清理所有portal中对它的引用
                            clear_portal_references_to_statement(pgs, stmt);
                            // 使用统一的清理函数
                            cleanup_single_prepared_statement(stmt);
                        }
                    }
                }
                else if (object_type == 'P')
                {
                    // 关闭portal
                    portal_t *portal = find_portal(pgs, object_name);
                    if (portal)
                    {
                        // 先标记为关闭状态
                        portal->state = PORTAL_STATE_CLOSED;
                        
                        portal_t *prev = NULL;
                        portal_t *curr = pgs->portals;
                        
                        // 在链表中查找portal
                        while (curr && curr != portal)
                        {
                            prev = curr;
                            curr = curr->next;
                        }
                        
                        if (curr)
                        {
                            // 从链表中移除
                            if (prev)
                            {
                                prev->next = curr->next;
                            }
                            else
                            {
                                pgs->portals = curr->next;
                            }
                            
                            // 使用统一的清理函数
                            cleanup_single_portal(portal);
                        }
                    }
                }
                break;
            }
            case POSTGRE_MSG_FUNCTION_CALL:
            {
                // Function Call消息处理
                uint32_t func_oid = GET_MSG_LEN(msg_data);
                msg_data += 4;
                //uint16_t num_params = ntohs(*((uint16_t*)msg_data));
                msg_data += 2;

                postgre_half_stream_t *phs = pgs->p_postgre_client;
                if (phs)
                {
                    // 创建SQL语句表示函数调用
                    char func_call_sql[256];
                    snprintf(func_call_sql, sizeof(func_call_sql), "SELECT OID=%u()", func_oid);
                    add_sql_statement(&phs->data, func_call_sql, strlen(func_call_sql));
                }
                break;
            }
            case POSTGRE_MSG_COPY_DATA:
            {
                // COPY数据消息处理（请求侧 - 客户端到服务器）
                if (pgs->copy_context && pgs->copy_context->state == COPY_STATE_COPY_IN) {
                    process_copy_data_message(pgs, data, offset, msg_len, true);
                }
                break;
            }
            case POSTGRE_MSG_COPY_DONE:
            {
                // COPY数据传输完成
                if (is_copy_active(pgs)) {
                    // 标记为完成状态，等待COMMAND_COMPLETE
                    pgs->copy_context->state = COPY_STATE_COMPLETING;
                }
                break;
            }
            case POSTGRE_MSG_COPY_FAIL:
            {
                // 如果当前有活跃的COPY操作，处理失败情况
                if (is_copy_active(pgs))
                {
                    const char *error_msg = msg_data;
                    if (error_msg && strlen(error_msg) > 0) {
                        // 设置错误信息并更新状态
                        set_copy_error(pgs, error_msg);
                    } else {
                        // 没有错误信息的情况
                        set_copy_error(pgs, "Unknown COPY failure");
                    }
                }
                break;
            }
            default:
                return PARSER_STATUS_DROP_DATA;
        }

        offset += msg_len + 1;
    }

    // 清理记录的statement名称
    if (last_parsed_stmt_name) {
        free(last_parsed_stmt_name);
    }

    return PARSER_STATUS_FINISH;
}

// 服务端响应消息解析函数
int CPostgreParser::parse_response_msg(postgre_stream_t *pgs, const char *data, int len, const struct conn *pcon, CSession *p_session, struct half_stream *hlf)
{
    int offset = 0;

    while (offset < len)
    {
        // 检查剩余数据是否足够构成一个消息
        if (len - offset < POSTGRE_NORMAL_MIN_LEN)
        {
            return PARSER_STATUS_CONTINUE;
        }

        uint32_t msg_len = GET_MSG_LEN(data + offset + 1);

        // 基本合理性检查
        if (msg_len < POSTGRE_MSG_HEADER_LEN || msg_len > 0x10000000) { // 256MB上限
            GWLOG_WARN(m_comm, "[PostgreSQL][PacketLoss] Invalid message length: %u at offset %d\n", msg_len, offset);
            return PARSER_STATUS_FINISH;
        }

        char msg_type = data[offset];
        switch (msg_type)
        {
            case POSTGRE_MSG_ROW_DESCRIPTION:
            {
                // 使用统一的丢包检测函数
                int lost_bytes = 0;
                if (detect_packet_loss(hlf, msg_len, len - offset, &lost_bytes, offset)) {
                    GWLOG_WARN(m_comm, "[PostgreSQL][PacketLoss] Row Description affected by packet loss, "
                               "processing with loss handling, lost_bytes=%d\n", lost_bytes);

                    // 对于中间段丢失，计算可用的消息数据长度
                    int available_msg_len = len - offset - 5;
                    if (hlf && hlf->complete == 0 && hlf->first_lost_offset > 0) {
                        // 中间段丢失：限制处理范围到丢失位置
                        available_msg_len = hlf->first_lost_offset - offset - 5;
                    }

                    if (available_msg_len > 0) {
                        return process_incomplete_row_description(pgs, data + offset + 5, available_msg_len, hlf);
                    } else {
                        GWLOG_WARN(m_comm, "[PostgreSQL][PacketLoss] No available data for incomplete Row Description\n");
                        return PARSER_STATUS_FINISH;
                    }
                }

                // 解析字段描述
                uint16_t num_fields = ntohs(*((uint16_t*)(data + offset + 5)));
                pgs->pg_stat.num_fields = num_fields;

                // 创建一个列定义的链表头
                column_def_t *col_head = NULL;
                column_def_t *col_tail = NULL;

                const char *field_data = data + offset + 7;
                int remaining_data = msg_len - 2; // 减去num_fields的2字节

                for (int i = 0; i < num_fields; i++)
                {
                    column_def_t *col = NULL;
                    int consumed_bytes = 0;

                    // 使用复用函数解析单个列定义
                    if (try_parse_single_column(field_data, remaining_data, &col, &consumed_bytes)) {
                        // 解析成功，添加到链表
                        if (col_tail == NULL) {
                            col_head = col;
                            col_tail = col;
                        } else {
                            col_tail->next = col;
                            col_tail = col;
                        }

                        // 更新指针和剩余数据
                        field_data += consumed_bytes;
                        remaining_data -= consumed_bytes;
                    } else {
                        // 解析失败，清理已分配的列定义
                        GWLOG_ERROR(m_comm, "[PostgreSQL][RowDescription] Failed to parse column %d\n", i);
                        while (col_head) {
                            column_def_t *temp = col_head;
                            col_head = col_head->next;
                            if (temp->name.s) {
                                free(temp->name.s);
                            }
                            delete temp;
                        }
                        return PARSER_STATUS_DROP_DATA;
                    }
                }

                // 保存列定义到服务端半流
                postgre_half_stream_t *phs = pgs->p_postgre_server;
                if (phs)
                {
                    result_set_t *current_rs = NULL;
                    add_result_set(&phs->data, col_head, num_fields, &current_rs);
                    
                    // 对于游标操作，缓存列定义
                    portal_t *portal = pgs->portals;
                    while (portal && strcmp(portal->name,"") != 0) {
                        // 如果portal尚未关联列定义，则复制并关联
                        if (portal->col_def == NULL) {
                            // 克隆列定义链表用于portal
                            column_def_t *portal_col_head = NULL;
                            column_def_t *portal_col_tail = NULL;
                            column_def_t *curr = col_head;
                            bool clone_success = true;
                            
                            while (curr && clone_success) {
                                column_def_t *new_col = new column_def_t();
                                if (!new_col) {
                                    // 内存分配失败
                                    clone_success = false;
                                    // 清理已分配的列定义
                                    while (portal_col_head) {
                                        column_def_t *temp = portal_col_head;
                                        portal_col_head = portal_col_head->next;
                                        if (temp->name.s) {
                                            free(temp->name.s);
                                        }
                                        delete temp;
                                    }
                                    break;
                                }
                                
                                // 初始化新列定义
                                memset(new_col, 0, sizeof(column_def_t));
                                
                                // 复制列定义
                                if (curr->name.s) {
                                    new_col->name.s = strdup(curr->name.s);
                                    if (!new_col->name.s) {
                                        // 内存分配失败
                                        clone_success = false;
                                        delete new_col;
                                        // 清理已分配的列定义
                                        while (portal_col_head) {
                                            column_def_t *temp = portal_col_head;
                                            portal_col_head = portal_col_head->next;
                                            if (temp->name.s) {
                                                free(temp->name.s);
                                            }
                                            delete temp;
                                        }
                                        break;
                                    }
                                    new_col->name.len = curr->name.len;
                                } else {
                                    new_col->name.s = NULL;
                                    new_col->name.len = 0;
                                }
                                new_col->table_oid = curr->table_oid;
                                new_col->column_id = curr->column_id;
                                new_col->type_oid = curr->type_oid;
                                new_col->type_len = curr->type_len;
                                new_col->type_mod = curr->type_mod;
                                new_col->format_code = curr->format_code;
                                new_col->next = NULL;
                                
                                // 添加到列表
                                if (portal_col_tail == NULL) {
                                    portal_col_head = new_col;
                                    portal_col_tail = new_col;
                                } else {
                                    portal_col_tail->next = new_col;
                                    portal_col_tail = new_col;
                                }
                                
                                curr = curr->next;
                            }
                            
                            if (clone_success) {
                                // 关联列定义到portal
                                portal->col_def = portal_col_head;
                                break;
                            }
                        }
                        portal = portal->next;
                    }
                }
                break;
            }
            case POSTGRE_MSG_DATA_ROW:
            {
                // 使用统一的丢包检测函数
                int lost_bytes = 0;
                if (detect_packet_loss(hlf, msg_len, len - offset, &lost_bytes, offset)) {
                    GWLOG_WARN(m_comm, "[PostgreSQL][PacketLoss] Data Row affected by packet loss, "
                               "processing with loss handling, lost_bytes=%d\n", lost_bytes);

                    // 对于中间段丢失，计算可用的消息数据长度
                    int available_msg_len = len - offset - 5;
                    if (hlf && hlf->complete == 0 && hlf->first_lost_offset > 0) {
                        // 中间段丢失：限制处理范围到丢失位置
                        available_msg_len = hlf->first_lost_offset - offset - 5;
                    }

                    if (available_msg_len > 0) {
                        return process_incomplete_data_row(pgs, data + offset + 5, available_msg_len, hlf);
                    } else {
                        GWLOG_WARN(m_comm, "[PostgreSQL][PacketLoss] No available data for incomplete Data Row\n");
                        return PARSER_STATUS_FINISH;
                    }
                }

                // 解析数据行
                uint16_t num_fields = ntohs(*((uint16_t*)(data + offset + 5)));

                // 获取当前结果集并验证字段数量
                postgre_half_stream_t *phs = pgs->p_postgre_server;
                if (!phs) {
                    return PARSER_STATUS_DROP_DATA;
                }

                // 获取当前结果集
                result_set_t *current_rs = NULL;
                if (phs->data.rs_list) {
                    current_rs = phs->data.rs_list;
                    while (current_rs->next) {
                        current_rs = current_rs->next;
                    }
                }

                // 使用辅助函数验证字段数量
                if (!validate_field_count(pgs, current_rs, num_fields, "DataRow")) {
                    return PARSER_STATUS_DROP_DATA;
                }

                // 检查是否启用扩展二进制数据处理，以及是否存在具有二进制格式的列
                if (phs && m_use_binary_extended && current_rs)
                {
                    // 检查是否存在格式码为二进制的列
                    bool has_binary_format = false;
                    
                    // 检查列定义中是否有二进制格式
                    column_def_t *col = current_rs->col_def;
                    while (col && !has_binary_format) {
                        if (col->format_code == PG_FORMAT_BINARY) {
                            has_binary_format = true;
                        }
                        col = col->next;
                    }
                    
                    if (has_binary_format && current_rs) {
                        // 使用二进制解析处理
                        postgre_row_data_extended_t *ext_row = parse_data_row_extended(
                            data, len, offset, current_rs->col_def, current_rs->col_cnt, m_binary_handlers);
                        
                        if (ext_row) {
                            // 创建标准行数据结构（兼容现有处理逻辑）
                            postgre_row_data_t *row = new postgre_row_data_t();
                            if (!row) {
                                GWLOG_ERROR(m_comm, "[PostgreSQL][MemoryAlloc] Failed to allocate row structure\n");
                                cleanup_row_data_extended(ext_row);
                                return PARSER_STATUS_DROP_DATA;
                            }
                            
                            row->row = new b_string_t*[num_fields];
                            if (!row->row) {
                                GWLOG_ERROR(m_comm, "[PostgreSQL][MemoryAlloc] Failed to allocate row array\n");
                                delete row;
                                cleanup_row_data_extended(ext_row);
                                return PARSER_STATUS_DROP_DATA;
                            }
                            
                            // 初始化所有指针为NULL并设置字段数量
                            memset(row->row, 0, sizeof(b_string_t*) * num_fields);
                            row->field_count = num_fields;
                            
                            // 转换扩展字段值到标准格式
                            for (int i = 0; i < num_fields; i++) {
                                if (ext_row->values[i] && !ext_row->values[i]->is_null) {
                                    b_string_t *field = new b_string_t();
                                    if (!field) {
                                        GWLOG_ERROR(m_comm, "[PostgreSQL][MemoryAlloc] Failed to convert field\n");
                                        cleanup_row_data(row, i);
                                        cleanup_row_data_extended(ext_row);
                                        return PARSER_STATUS_DROP_DATA;
                                    }
                                    
                                    // 复制文本表示
                                    field->s = strdup(ext_row->values[i]->text_value.s);
                                    if (!field->s) {
                                        GWLOG_ERROR(m_comm, "[PostgreSQL][MemoryAlloc] Failed to copy field text\n");
                                        delete field;
                                        cleanup_row_data(row, i);
                                        cleanup_row_data_extended(ext_row);
                                        return PARSER_STATUS_DROP_DATA;
                                    }
                                    
                                    field->len = ext_row->values[i]->text_value.len;
                                    row->row[i] = field;
                                } else {
                                    row->row[i] = NULL; // NULL值
                                }
                            }
                            
                            if (current_rs) {
                                postgre_row_data_t **new_rows = new postgre_row_data_t*[1];
                                if (!new_rows) {
                                    GWLOG_ERROR(m_comm, "[PostgreSQL][MemoryAlloc] Failed to allocate rows array\n");
                                    cleanup_row_data(row, num_fields);
                                    cleanup_row_data_extended(ext_row);
                                    return PARSER_STATUS_DROP_DATA;
                                }
                                new_rows[0] = row;
                                update_result_set(current_rs, new_rows, 1);
                            }
                            else {
                                // 没有结果集，直接释放行数据
                                cleanup_row_data(row, num_fields);
                            }
                            
                            // 清理扩展行数据
                            cleanup_row_data_extended(ext_row);
                            break;
                        } else {
                            GWLOG_ERROR(m_comm, "[PostgreSQL][BinaryData] Failed to parse extended row, falling back to standard parsing\n");
                            // 失败时回退到标准解析
                        }
                    }
                    // 如果没有二进制格式或解析失败，继续使用标准解析方式
                }
                
                // 标准文本格式处理方式
                postgre_row_data_t *row = new postgre_row_data_t();
                if (!row)
                {
                    GWLOG_ERROR(m_comm, "[PostgreSQL][MemoryAlloc] Failed to allocate row structure\n");
                    return PARSER_STATUS_DROP_DATA;
                }

                row->row = new b_string_t*[num_fields];
                if (!row->row)
                {
                    GWLOG_ERROR(m_comm, "[PostgreSQL][MemoryAlloc] Failed to allocate row array for %d fields\n", num_fields);
                    delete row;
                    return PARSER_STATUS_DROP_DATA;
                }
                
                // 初始化所有指针为NULL并设置字段数量
                memset(row->row, 0, sizeof(b_string_t*) * num_fields);
                row->field_count = num_fields;

                const char *field_data = data + offset + 7;
                for (int i = 0; i < num_fields; i++)
                {
                    int32_t field_len = ntohl(*((int32_t*)field_data));
                    field_data += 4;

                    if (field_len == -1)
                    {
                        // NULL值，保持row->row[i]为NULL
                        row->row[i] = NULL;
                    }
                    else if (field_len >= 0)
                    {
                        b_string_t *field = new b_string_t();
                        if (!field)
                        {
                            GWLOG_ERROR(m_comm, "[PostgreSQL][MemoryAlloc] Failed to allocate b_string_t for field[%d]\n", i);
                            // 使用辅助函数清理资源
                            cleanup_row_data(row, num_fields);
                            return PARSER_STATUS_DROP_DATA;
                        }

                        char *field_copy = (char *)malloc(field_len + 1);
                        if (!field_copy) {
                            GWLOG_ERROR(m_comm, "[PostgreSQL][MemoryAlloc] Failed to allocate string buffer for field[%d], length %d\n", i, field_len);
                            delete field;
                            // 使用辅助函数清理资源
                            cleanup_row_data(row, num_fields);
                            return PARSER_STATUS_DROP_DATA;
                        }

                        memcpy(field_copy, field_data, field_len);
                        field_copy[field_len] = '\0';
                        field->s = field_copy;
                        field->len = field_len;
                        row->row[i] = field;
                        field_data += field_len;
                    }
                }

                // 检查是否有有效的结果集和列定义
                if(!current_rs)
                {
                    // 没有结果集，检查是否应该丢弃数据
                    // 如果没有列定义且不是预期的数据行，应该丢弃

                    // 检查是否有portal的列定义可以使用
                    bool has_portal_columns = false;
                    portal_t *portal = pgs->portals;
                    while (portal) {
                        if (portal->state == PORTAL_STATE_EXECUTING && portal->col_def) {
                            has_portal_columns = true;
                            break;
                        }
                        portal = portal->next;
                    }

                    if (!has_portal_columns) {
                        // 没有任何列定义信息，但有字段数据
                        // 这可能是Row Description丢包的情况，创建临时结果集存放行数据
                        // 后续会在handle_access_event中动态创建列定义
                        if (num_fields > 0) {
                            GWLOG_WARN(m_comm, "[PostgreSQL][PacketLoss] No column definitions for DataRow with %d fields, "
                                       "creating temporary result set for dynamic column creation\n", num_fields);
                            // 创建临时结果集，设置正确的列数以便后续Data Row能够通过验证
                            add_result_set(&phs->data, NULL, num_fields, &current_rs);
                            // 更新pg_stat.num_fields以确保后续Data Row验证一致
                            pgs->pg_stat.num_fields = num_fields;
                        } else {
                            // 没有字段数据，丢弃
                            cleanup_row_data(row, num_fields);
                            return PARSER_STATUS_DROP_DATA;
                        }
                    } else {
                        // 有portal列定义，创建新结果集存放行数据
                        add_result_set(&phs->data, NULL, 0, &current_rs);
                    }
                }

                // 创建单行数组并使用update_result_set添加
                postgre_row_data_t **new_rows = new postgre_row_data_t*[1];
                if (!new_rows)
                {
                    GWLOG_ERROR(m_comm, "[PostgreSQL][MemoryAlloc] Failed to allocate rows array\n");
                    cleanup_row_data(row, num_fields);
                    return PARSER_STATUS_DROP_DATA;
                }
                new_rows[0] = row;
                update_result_set(current_rs, new_rows, 1);

                // 添加调试日志，输出结果集状态信息
                GWLOG_INFO(m_comm, "[PostgreSQL][DataRow] Successfully added row to result set: "
                           "col_cnt=%d, row_cnt=%d, col_def=%p\n",
                           current_rs->col_cnt, current_rs->row_cnt, current_rs->col_def);

                break;
            }
            case POSTGRE_MSG_PARSE_COMPLETE:
            case POSTGRE_MSG_BIND_COMPLETE:
            case POSTGRE_MSG_CLOSE_COMPLETE:
            case POSTGRE_MSG_PORTAL_SUSPENDED:
            {
                // Portal被挂起，更新所有正在执行的portal状态
                portal_t *portal = pgs->portals;
                while (portal) {
                    if (portal->state == PORTAL_STATE_EXECUTING) {
                        portal->state = PORTAL_STATE_SUSPENDED;
                    }
                    portal = portal->next;
                }
                break;
            }
            case POSTGRE_MSG_NO_DATA:
            {
                // 没有数据返回，标记portal为exhausted
                portal_t *portal = pgs->portals;
                while (portal) {
                    if (portal->state == PORTAL_STATE_EXECUTING) {
                        portal->state = PORTAL_STATE_EXHAUSTED;
                    }
                    portal = portal->next;
                }
                break;
            }
            case POSTGRE_MSG_EMPTY_QUERY_RESPONSE:
            case POSTGRE_MSG_PARAMETER_DESCRIPTION:
            case POSTGRE_MSG_NOTIFICATION_RESPONSE:
                // Extended query协议的响应消息，只需验证其存在性
                break;

            case POSTGRE_MSG_COMMAND_COMPLETE:
            {
                // ✨ 检查是否是COPY命令完成，重置COPY状态
                if (is_copy_active(pgs)) {
                    pgs->copy_context->state = COPY_STATE_NONE; // 重置状态，允许后续匹配
                }

                // 获取命令完成消息的TAG字段
                const char *tag = data + offset + 5;
                size_t tag_len = strlen(tag);

                postgre_half_stream_t *phs = pgs->p_postgre_server;
                if (!phs)
                {
                    return PARSER_STATUS_DROP_DATA;
                }

                // 更新正在执行的portal状态
                portal_t *portal = pgs->portals;
                while (portal) {
                    if (portal->state == PORTAL_STATE_EXECUTING) {
                        // 根据命令类型决定portal状态
                        if (strncmp(tag, "SELECT", 6) == 0) {
                            // SELECT命令可能还有更多数据，保持执行状态或标记为exhausted
                            if (portal->max_rows > 0) {
                                // 有行数限制，可能还有更多数据
                                portal->state = PORTAL_STATE_SUSPENDED;
                            } else {
                                // 无行数限制，查询完成
                                portal->state = PORTAL_STATE_EXHAUSTED;
                            }
                        } else {
                            // 其他命令（INSERT、UPDATE、DELETE等）完成后portal即exhausted
                            portal->state = PORTAL_STATE_EXHAUSTED;
                        }
                    }
                    portal = portal->next;
                }

                // 检查是否已经有只有一列且列名为message的结果集，如果有则跳过
                bool has_message_rs = false;
                result_set_t *existing_rs = phs->data.rs_list;
                while (existing_rs) {
                    if (existing_rs->col_cnt == 1 && existing_rs->col_def && 
                        existing_rs->col_def->name.s && 
                        strcmp(existing_rs->col_def->name.s, "message") == 0) {
                        has_message_rs = true;
                        break;
                    }
                    existing_rs = existing_rs->next;
                }
                if (has_message_rs) {
                    break;
                }

                if (strncmp(tag, "SELECT", 6) != 0) 
                {
                    // 创建单列结果集
                    // ... existing code ...
                    postgre_half_stream_t *phs = pgs->p_postgre_server;
                    if (!phs) 
                    {
                        return PARSER_STATUS_DROP_DATA;
                    }

                    // 创建列定义
                    column_def_t *col = new column_def_t();
                    if (!col) 
                    {
                        return PARSER_STATUS_DROP_DATA;
                    }
                    memset(col, 0, sizeof(column_def_t));
                    char *nameCopy = strdup("message");
                    if (!nameCopy) 
                    {
                        delete col;
                        return PARSER_STATUS_DROP_DATA;
                    }
                    col->name.s = nameCopy;
                    col->name.len = strlen(nameCopy);
                    col->next = NULL;

                    result_set_t *currentRs = NULL;
                    add_result_set(&phs->data, col, 1, &currentRs);

                    // 创建结果行
                    postgre_row_data_t *row = new postgre_row_data_t();
                    if (!row) 
                    {
                        // 内存分配失败，清理所有资源
                        return PARSER_STATUS_DROP_DATA;
                    }
                    row->row = new b_string_t*[1];
                    if (!row->row) 
                    {
                        delete row;
                        return PARSER_STATUS_DROP_DATA;
                    }
                    row->field_count = 1; // 设置字段数量
                    row->row[0] = new b_string_t();
                    if (!row->row[0]) 
                    {
                        delete[] row->row;
                        delete row;
                        return PARSER_STATUS_DROP_DATA;
                    }

                    // 先将tag和" SUCCESS"拼接到一起
                    size_t successSuffixLen = strlen(" SUCCESS");
                    size_t newTagLen = tag_len + successSuffixLen;
                    char *tagWithSuccess = (char *)malloc(newTagLen + 1);
                    if (tagWithSuccess) 
                    {
                        memcpy(tagWithSuccess, tag, tag_len);
                        memcpy(tagWithSuccess + tag_len, " SUCCESS", successSuffixLen);
                        tagWithSuccess[newTagLen] = '\0';
                        row->row[0]->s = tagWithSuccess;
                        row->row[0]->len = newTagLen;
                    } 
                    else 
                    {
                        // 内存分配失败，回退为原tag
                        row->row[0]->s = strdup(tag);
                        row->row[0]->len = tag_len;
                    }

                    postgre_row_data_t **newRows = new postgre_row_data_t*[1];
                    if (!newRows) 
                    {
                        // 内存分配失败，清理所有资源
                        delete row->row[0];
                        delete[] row->row;
                        delete row;
                        return PARSER_STATUS_DROP_DATA;
                    }
                    newRows[0] = row;
                    update_result_set(currentRs, newRows, 1);
                }

                break;
            }
            case POSTGRE_MSG_ERROR_RESPONSE:
            {
                postgre_half_stream_t *phs = pgs->p_postgre_server;
                phs->data.close_time = get_time_ts_ms(p_session, m_conf_pcap_timestamp);
                if (phs)
                {
                    // 使用公共函数解析错误消息字段
                    char *detailed_err_msg = NULL;
                    int err_code = 0;
                    parse_error_response_fields(data + offset + 5, msg_len - 4, &detailed_err_msg, &err_code);

                    // 设置错误信息到流状态
                    if (detailed_err_msg) {
                        // 释放旧的错误消息(如果有)
                        if (phs->data.err_msg) {
                            free(phs->data.err_msg);
                        }
                        phs->data.err_msg = detailed_err_msg;
                        phs->data.err_code = err_code;
                    }

                    // 如果有err_msg，创建一个只有一列（message）的结果集
                    if (phs->data.err_msg && strlen(phs->data.err_msg) > 0) {
                        // 检查是否已经有message列的结果集
                        bool has_message_rs = false;
                        result_set_t *existing_rs = phs->data.rs_list;
                        while (existing_rs) {
                            if (existing_rs->col_cnt == 1 && existing_rs->col_def && 
                                existing_rs->col_def->name.s && 
                                strcmp(existing_rs->col_def->name.s, "message") == 0) {
                                has_message_rs = true;
                                break;
                            }
                            existing_rs = existing_rs->next;
                        }
                        
                        if (!has_message_rs) {
                            // 创建列定义
                            column_def_t *col = new column_def_t();
                            if (!col){
                                // 内存分配失败
                                return PARSER_STATUS_DROP_DATA;
                            }
                            memset(col, 0, sizeof(column_def_t));
                            char *name_copy = strdup("message");
                            if (!name_copy){
                                delete col;
                                return PARSER_STATUS_DROP_DATA;
                            }
                            col->name.s = name_copy;
                            col->name.len = strlen(name_copy);
                            col->next = NULL;
                            result_set_t *currentRs = NULL;
                            add_result_set(&phs->data, col, 1, &currentRs);

                            // 创建结果行
                            postgre_row_data_t *row = new postgre_row_data_t();
                            if (!row){
                                // 内存分配失败
                                return PARSER_STATUS_DROP_DATA;
                            }
                            row->row = new b_string_t*[1];
                            if (!row->row){
                                delete row;
                                return PARSER_STATUS_DROP_DATA;
                            }
                            row->field_count = 1; // 设置字段数量
                            row->row[0] = new b_string_t();
                            if (!row->row[0]){
                                delete[] row->row;
                                delete row;
                                return PARSER_STATUS_DROP_DATA;
                            }
                            row->row[0]->s = strdup(phs->data.err_msg);
                            row->row[0]->len = strlen(phs->data.err_msg);

                            postgre_row_data_t **newRows = new postgre_row_data_t*[1];
                            if (!newRows){
                                delete row->row[0];
                                delete[] row->row;
                                delete row;
                                return PARSER_STATUS_DROP_DATA;
                            }
                            newRows[0] = row;
                            update_result_set(currentRs, newRows, 1);
                        }
                    }
                }
                break;
            }
            case POSTGRE_MSG_READY_FOR_QUERY:
            {
                postgre_half_stream_t *phs = pgs->p_postgre_server;
                phs->data.close_time = get_time_ts_ms(p_session, m_conf_pcap_timestamp);
                char trans_status = data[offset + 5];
                char old_trans_status = pgs->pg_stat.transaction_status;

                // 处理事务状态变化
                handle_transaction_state_change(pgs, old_trans_status, trans_status);
                
                pgs->pg_stat.transaction_status = trans_status;
                break;
            }
            case POSTGRE_MSG_FUNCTION_CALL_RESPONSE:
            {
                // 函数调用响应处理
                int32_t result_len = ntohl(*((int32_t*)(data + offset + 5)));
                if (result_len < 0)
                {
                    break;
                }

                postgre_half_stream_t *phs = pgs->p_postgre_server;
                if (!phs)
                {
                    break;
                }

                // 创建单列结果集
                column_def_t *col = new column_def_t();
                if (!col)
                {
                    break;
                }

                memset(col, 0, sizeof(column_def_t)); // 初始化为0
                char *name_copy = strdup("function_result");
                if (!name_copy)
                {
                    // 内存分配失败，清理所有资源
                    delete col;
                    break;
                }
                col->name.s = name_copy;
                col->name.len = strlen(name_copy);
                col->next = NULL;

                // 创建结果集
                result_set_t *current_rs = NULL;
                add_result_set(&phs->data, col, 1, &current_rs);

                // 创建结果行
                postgre_row_data_t *row = new postgre_row_data_t();
                if (!row)
                {
                    break;
                }

                row->row = new b_string_t*[1];
                if (!row->row)
                {
                    // 内存分配失败，清理所有资源
                    delete row;
                    break;
                }
                memset(row->row, 0, sizeof(b_string_t*)); // 初始化为0
                row->field_count = 1; // 设置字段数量

                b_string_t *field = new b_string_t();
                if (!field)
                {
                    // 内存分配失败，清理所有资源
                    delete[] row->row;
                    delete row;
                    break;
                }
                memset(field, 0, sizeof(b_string_t)); // 初始化为0

                char *value_copy = (char *)malloc(result_len + 1);
                if (!value_copy)
                {
                    // 内存分配失败，清理所有资源
                    delete field;
                    delete[] row->row;
                    delete row;
                    break;
                }

                memcpy(value_copy, data + offset + 9, result_len);
                value_copy[result_len] = '\0';
                field->s = value_copy;
                field->len = result_len;
                row->row[0] = field;

                // 释放旧的行数据(如果有)
                if (current_rs->rows)
                {
                    for (int i = 0; i < current_rs->row_cnt; i++)
                    {
                        if (current_rs->rows[i])
                        {
                            // 使用行数据自身的字段数量，如果没有则使用结果集的列数量
                            int field_count = (current_rs->rows[i]->field_count > 0) ? 
                                            current_rs->rows[i]->field_count : current_rs->col_cnt;
                            cleanup_row_data(current_rs->rows[i], field_count);
                        }
                    }
                    delete[] current_rs->rows;
                    current_rs->rows = NULL;
                    current_rs->row_cnt = 0;
                    current_rs->row_capacity = 0;
                }

                // 创建行数组并添加到结果集
                postgre_row_data_t **new_rows = new postgre_row_data_t*[1];
                if (!new_rows)
                {
                    // 内存分配失败，清理所有资源
                    free(value_copy);
                    delete field;
                    delete[] row->row;
                    delete row;
                    break;
                }
                new_rows[0] = row;
                update_result_set(current_rs, new_rows, 1);
                break;
            }
            case POSTGRE_MSG_COPY_DATA:
            {
                // 检查是否为流复制模式
                if (is_replication_active(pgs)) {
                    // 流复制模式：base64编码存储
                    process_replication_copy_data(pgs, data + offset, msg_len);
                } else {
                    // COPY数据消息处理（响应侧 - 服务器到客户端）
                    if (is_copy_active(pgs)) {
                        process_copy_data_message(pgs, data, offset, msg_len, false);
                    }
                }
                break;
            }
            case POSTGRE_MSG_COPY_DONE:
            {
                // 响应侧COPY DONE处理（虽然通常由客户端发送，但为了完整性处理）
                if (is_copy_active(pgs)){
                    // 标记为完成状态，等待COMMAND_COMPLETE
                    pgs->copy_context->state = COPY_STATE_COMPLETING;
                }
                break;
            }
            case POSTGRE_MSG_COPY_FAIL:
            {
                // 响应侧COPY FAIL处理（虽然通常由客户端发送，但为了完整性处理）
                if (is_copy_active(pgs))
                {
                    const char *error_msg = data + offset + 5;
                    if (error_msg && strlen(error_msg) > 0) {
                        // 设置错误信息并更新状态
                        set_copy_error(pgs, error_msg);
                    } else {
                        // 没有错误信息的情况
                        set_copy_error(pgs, "Unknown COPY failure from server");
                    }
                }
                break;
            }
            case POSTGRE_MSG_COPY_IN_RESPONSE:
            {
                // COPY IN响应消息处理
                if (msg_len < 3) {
                    // 消息长度不足，跳过
                    break;
                }

                uint8_t format = data[offset + 5];
                uint16_t num_columns = ntohs(*((uint16_t*)(data + offset + 6)));

                // ✨ 设置COPY IN状态
                if (ensure_copy_context(pgs)) {
                    pgs->copy_context->state = COPY_STATE_COPY_IN;
                }

                // 直接存储COPY参数到响应端节点
                postgre_half_stream_t *phs = pgs->p_postgre_server;
                if (phs) {
                    phs->data.copy_format = format;
                    phs->data.copy_columns = num_columns;
                }
                break;
            }
            case POSTGRE_MSG_COPY_OUT_RESPONSE:
            {
                // COPY OUT响应消息处理
                if (msg_len < 3) {
                    // 消息长度不足，跳过
                    break;
                }

                uint8_t format = data[offset + 5];
                uint16_t num_columns = ntohs(*((uint16_t*)(data + offset + 6)));

                // ✨ 设置COPY OUT状态
                if (ensure_copy_context(pgs)) {
                    pgs->copy_context->state = COPY_STATE_COPY_OUT;
                }

                // 直接存储COPY参数到响应端节点
                postgre_half_stream_t *phs = pgs->p_postgre_server;
                if (phs) {
                    phs->data.copy_format = format;
                    phs->data.copy_columns = num_columns;
                }
                break;
            }
            case POSTGRE_MSG_COPY_BOTH_RESPONSE:
            {
                // COPY BOTH响应消息处理
                if (ensure_copy_context(pgs)) {
                    pgs->copy_context->state = COPY_STATE_COPY_BOTH;
                    pgs->copy_context->is_replication_mode = true;
                }
                break;
            }
            // 添加 NoticeResponse 消息处理
            case POSTGRE_MSG_NOTICE_RESPONSE:
            {
                postgre_half_stream_t *phs = pgs->p_postgre_server;
                if (phs)
                {
                    // NoticeResponse消息格式与ErrorResponse相同，但表示警告而非错误
                    // 以零结尾的字段序列，每个字段由一个字节类型标识符和字符串值组成
                    const char *notice_data = data + offset + 5;
                    char notice_msg[1024] = {0};
                    // 遍历所有字段
                    while (*notice_data != '\0')
                    {
                        char field_type = *notice_data++;
                        const char *field_value = notice_data;
                        size_t field_len = strlen(field_value);
                        notice_data += field_len + 1;
                        if (field_type == 'M')  // Notice Message
                        {
                            snprintf(notice_msg, sizeof(notice_msg), "%s", field_value);
                        }
                    }
                    // 如果有Notice Message，创建一个只有一列（message）的结果集
                    if (notice_msg[0] != '\0') {
                        // 创建列定义
                        column_def_t *col = new column_def_t();
                        if (!col){
                            // 内存分配失败
                            return PARSER_STATUS_DROP_DATA;
                        }
                        memset(col, 0, sizeof(column_def_t));
                        char *name_copy = strdup("message");
                        if (!name_copy){
                            delete col;
                            return PARSER_STATUS_DROP_DATA;
                        }
                        col->name.s = name_copy;
                        col->name.len = strlen(name_copy);
                        col->next = NULL;
                        result_set_t *currentRs = NULL;
                        add_result_set(&phs->data, col, 1, &currentRs);
                        // 创建结果行
                        postgre_row_data_t *row = new postgre_row_data_t();
                        if (!row){
                            return PARSER_STATUS_DROP_DATA;
                        }
                        row->row = new b_string_t*[1];
                        if (!row->row){
                            delete row;
                            return PARSER_STATUS_DROP_DATA;
                        }
                        row->field_count = 1; // 设置字段数量
                        row->row[0] = new b_string_t();
                        if (!row->row[0]){
                            delete[] row->row;
                            delete row;
                            return PARSER_STATUS_DROP_DATA;
                        }
                        row->row[0]->s = strdup(notice_msg);
                        row->row[0]->len = strlen(notice_msg);
                        postgre_row_data_t **newRows = new postgre_row_data_t*[1];
                        if (!newRows){
                            delete row->row[0];
                            delete[] row->row;
                            delete row;
                            return PARSER_STATUS_DROP_DATA;
                        }
                        newRows[0] = row;
                        update_result_set(currentRs, newRows, 1);
                    }
                }
                break;
            }
            case POSTGRE_MSG_NEGOTIATE_PROTOCOL_VERSION: 
                // 验证存在即可，不处理
                break;
            default:
                return PARSER_STATUS_DROP_DATA;
        }

        offset += msg_len + 1;
    }

    return PARSER_STATUS_FINISH;
}

// 解析CancelRequest消息 - 这是一个特殊的消息，直接发送到服务器，不经过正常会话流程
int CPostgreParser::parse_cancel_request_msg(postgre_stream_t *pgs, const char *data, int len, const struct conn *pcon)
{
    // CancelRequest消息格式:
    // uint32 - 总长度 (16)
    // uint32 - 请求代码 (80877102)
    // uint32 - 进程ID
    // uint32 - 密钥
    
    // 检查数据指针有效性
    if (data == NULL)
    {
        GWLOG_ERROR(m_comm, "[PostgreSQL] Invalid data pointer for CancelRequest message\n");
        return PARSER_STATUS_DROP_DATA;
    }
    
    // 检查消息长度
    if (len < 16)
    {
        GWLOG_ERROR(m_comm, "[PostgreSQL] CancelRequest message too short: %d bytes\n", len);
        return PARSER_STATUS_CONTINUE;
    }
    
    // 获取消息长度
    uint32_t msg_len = GET_MSG_LEN(data);
    if (msg_len != 16)
    {
        GWLOG_WARN(m_comm, "[PostgreSQL] Invalid CancelRequest message length: %u, expected 16\n", msg_len);
        return PARSER_STATUS_DROP_DATA;
    }
    
    // 检查请求代码是否为80877102 (0x04D2162E)
    uint32_t request_code = GET_MSG_LEN(data + 4);
    if (request_code != 80877102)
    {
        GWLOG_WARN(m_comm, "[PostgreSQL] Invalid CancelRequest code: %u, expected 80877102\n", request_code);
        return PARSER_STATUS_DROP_DATA;
    }
    
    // 获取进程ID和密钥
    uint32_t cancel_pid = GET_MSG_LEN(data + 8);
    uint32_t cancel_key = GET_MSG_LEN(data + 12);
    
    // 检查PID和Key的有效性 (简单检查，实际实现可能需要更复杂的验证逻辑)
    if (cancel_pid == 0)
    {
        GWLOG_WARN(m_comm, "[PostgreSQL] Invalid CancelRequest PID: 0\n");
        return PARSER_STATUS_DROP_DATA;
    }
    
    // 检查PostgreSQL流结构是否有效
    if (pgs == NULL)
    {
        GWLOG_ERROR(m_comm, "[PostgreSQL] Invalid PostgreSQL stream for CancelRequest\n");
        return PARSER_STATUS_DROP_DATA;
    }
    
    // 创建请求节点记录取消操作
    postgre_half_stream_t *p_new_stream = new postgre_half_stream_t();
    if (!p_new_stream)
    {
        GWLOG_ERROR(m_comm, "[Postgre][Parse] Failed to allocate new stream node for cancel request\n");
        return PARSER_STATUS_DROP_DATA;
    }
    p_new_stream->next = NULL;
    p_new_stream->prev = NULL;  // 确保初始化为NULL
    memset(&p_new_stream->data, 0, sizeof(postgre_parsed_data_t));
    
    // 添加SQL语句记录取消操作
    char cancel_sql[128];
    int sql_len = snprintf(cancel_sql, sizeof(cancel_sql), "CANCEL REQUEST (PID=%u, KEY=%u)", cancel_pid, cancel_key);
    if (sql_len < 0 || sql_len >= (int)sizeof(cancel_sql))
    {
        GWLOG_WARN(m_comm, "[PostgreSQL] Failed to format cancel SQL statement\n");
        delete p_new_stream;
        return PARSER_STATUS_DROP_DATA;
    }
    
    add_sql_statement(&p_new_stream->data, cancel_sql, sql_len);
    
    // 插入新节点到链表
    insert_into_parser_header(pgs, PGSQL_REQUEST, p_new_stream);
    
    return PARSER_STATUS_FINISH;
}

struct half_stream* CPostgreParser::get_half_stream(int dir, tcp_stream* a_tcp) {
    half_stream* p_hlf = NULL;
    switch(dir) {
        case STREAM_REQ:
            p_hlf = &a_tcp->client;
            break;
        case STREAM_RSP:
            p_hlf = &a_tcp->server;
            break;
        default:
            ASSERT(false);
            break;
    }
    return p_hlf;
}

int CPostgreParser::parse(CSessionMgt *psm, const app_stream *a_app, const struct conn *pcon, CSession *p_session)
{
    if (!m_conf_parser_enable)
    {
        return PARSER_STATUS_DROP_DATA;
    }

    enum pgsql_parser_type dir = PGSQL_BOTH;

    StreamData* p_tcp_data = p_session->get_stream_data_from_type(SESSION_PROTO_TYPE_TCP);
    if (p_tcp_data == NULL) {
        return -1;
    }

    tcp_stream* a_tcp = p_tcp_data->a_tcp;
    if (a_tcp == NULL) {
        return -1;
    }

    if(a_tcp->reverse == 0)
    {
        dir = static_cast<pgsql_parser_type>(a_app->dir);
    }
    else if(a_tcp->reverse == 1)
    {
        dir = static_cast<pgsql_parser_type>((!a_app->dir)&0x01);
    }

    StreamData *psd = NULL;
    const char *data = NULL;
    int data_len = 0;
    int offset_out = 0;

    // 获取数据内容
    data = p_session->get_data(this, a_app->dir, &data_len, &offset_out);
    if (data == NULL || data_len <= 0) {
        return false;
    }

    struct half_stream* hlf = get_half_stream(a_app->dir, a_tcp);
    if (hlf == NULL) {
        return 0;
    }

    // ===== 添加TCP丢包信息调试日志 =====
    GWLOG_INFO(m_comm, "[PostgreSQL][PacketLoss] TCP丢包状态调试信息:");
    GWLOG_INFO(m_comm, "[PostgreSQL][PacketLoss] - 方向: %s", (dir == PGSQL_REQUEST) ? "客户端->服务端" : "服务端->客户端");
    GWLOG_INFO(m_comm, "[PostgreSQL][PacketLoss] - 数据长度: %d bytes", data_len);

    // 输出half_stream中的丢包相关字段
    if (hlf) {
        GWLOG_INFO(m_comm, "[PostgreSQL][PacketLoss] - half_stream丢包信息:\n");
        GWLOG_INFO(m_comm, "[PostgreSQL][PacketLoss]   * complete: %d (流是否完整)\n", hlf->complete);
        GWLOG_INFO(m_comm, "[PostgreSQL][PacketLoss]   * lost_len: %d (累计丢失字节数)\n", hlf->lost_len);
        GWLOG_INFO(m_comm, "[PostgreSQL][PacketLoss]   * first_lost_offset: %d (第一次丢包位置)\n", hlf->first_lost_offset);
        GWLOG_INFO(m_comm, "[PostgreSQL][PacketLoss]   * missing_info_len: %d (丢包信息长度)\n", hlf->missing_info_len);
        GWLOG_INFO(m_comm, "[PostgreSQL][PacketLoss]   * data_len: %d (真实数据长度)\n", hlf->data_len);
        GWLOG_INFO(m_comm, "[PostgreSQL][PacketLoss]   * stream_cache_size: %d (流缓存大小)\n", hlf->stream_cache_size);
        GWLOG_INFO(m_comm, "[PostgreSQL][PacketLoss]   * seq: %u, ack: %u\n", hlf->seq, hlf->ack);
        GWLOG_INFO(m_comm, "[PostgreSQL][PacketLoss]   * first_seq: %u, stream_ack: %u\n", hlf->first_seq, hlf->stream_ack);
    }

    // 输出tcp_stream中的相关信息
    if (a_tcp) {
        GWLOG_INFO(m_comm, "[PostgreSQL][PacketLoss] - tcp_stream信息:\n");
        GWLOG_INFO(m_comm, "[PostgreSQL][PacketLoss]   * reverse: %d (连接方向)\n", a_tcp->reverse);
        GWLOG_INFO(m_comm, "[PostgreSQL][PacketLoss]   * direction_confirmed: %d (方向确认)\n", a_tcp->direction_confirmed);
        GWLOG_INFO(m_comm, "[PostgreSQL][PacketLoss]   * header_complete: %d (头部完整)\n", a_tcp->header_complete);
        GWLOG_INFO(m_comm, "[PostgreSQL][PacketLoss]   * drop_data_reason: %d (丢弃数据原因)\n", a_tcp->drop_data_reason);
        GWLOG_INFO(m_comm, "[PostgreSQL][PacketLoss]   * total: %d, read: %d\n", a_tcp->total, a_tcp->read);

        // 输出客户端和服务端的half_stream状态
        GWLOG_INFO(m_comm, "[PostgreSQL][PacketLoss]   * 客户端流状态: complete=%d, lost_len=%d\n",
                   a_tcp->client.complete, a_tcp->client.lost_len);
        GWLOG_INFO(m_comm, "[PostgreSQL][PacketLoss]   * 服务端流状态: complete=%d, lost_len=%d\n",
                   a_tcp->server.complete, a_tcp->server.lost_len);
    }
    // ===== 调试日志结束 =====


    psd = get_stream_data_from_session(p_session, a_app->dir);
    if (psd == NULL) {
        GWLOG_ERROR(m_comm, "[Postgre][Parse] No postgre stream data available\n");
        return 0;
    }

    // 判断消息方向
    bool is_client = (dir == PGSQL_REQUEST);

    // 获取PostgreSQL流
    postgre_stream_t *pgs = psd->p_postgre_stream;

    // 处理单字节的SSL/GSSENC响应
    if (!is_client && data_len == 1 && (data[0] == 'S' || data[0] == 'N' || data[0] == 'E')) {
        return PARSER_STATUS_FINISH;
    }

    // 基于消息特征的直接解析 - 核心改进点
    int ret = parse_by_message_signature(pgs, data, data_len, is_client, pcon, p_session, hlf);

    // 匹配+统计更新
    if (ret == PARSER_STATUS_FINISH) {
        bool match_success = postgre_parser_merge(pgs, pcon, dir);
        if (match_success) {
            // 匹配成功时更新session统计
            update_session_stats(data_len);
        }
        // 更新匹配统计
        update_match_stats(match_success, dir);
    }
    // 更新解析统计，传入方向信息
    update_parse_stats(ret, data_len, dir);

    return ret;
}

int CPostgreParser::parse_clear(CSessionMgt *psm, const app_stream *a_app, const struct conn *pcon, CSession *p_session)
{
    return PARSER_STATUS_FINISH;
}

int CPostgreParser::parse_on_close(CSessionMgt *psm, const app_stream *a_app, const struct conn *pcon, CSession *p_session)
{
    StreamData *psd = NULL;
    const char *data = NULL;
    int data_len = 0;
    int offset_out = 0;

    // 获取数据内容
    data = p_session->get_data(this, a_app->dir, &data_len, &offset_out);
    if (data == NULL || data_len <= 0) {
        GWLOG_ERROR(m_comm, "[Postgre][Parse] No data available, len=%d\n", data_len);
        return false;
    }

    psd = get_stream_data_from_session(p_session, a_app->dir);
    if (psd == NULL) {
        GWLOG_ERROR(m_comm, "[Postgre][Parse] No postgre stream data available\n");
        return 0;
    }

    // 获取PostgreSQL流
    postgre_stream_t *pgs = psd->p_postgre_stream;
    
    // 如果有未完成的COPY操作，尝试处理它
    if (is_copy_active(pgs))
    {
        // 在连接关闭时，强制完成任何未完成的COPY操作
        // 设置COPY状态为完成，触发最终匹配
        pgs->copy_context->state = COPY_STATE_NONE;

        // 尝试进行最终匹配，处理可能存在的未匹配数据
        bool match_success = postgre_parser_merge(pgs, pcon, a_app->dir);

        // 在关闭时的匹配统计
        update_match_stats(match_success, a_app->dir);

        if (match_success) {
            // 估算session统计，使用默认大小
            update_session_stats(1024);
        }

        GWLOG_DEBUG(m_comm, "[PostgreSQL] Forced COPY completion on connection close\n");
    }
        
        // 清理匹配索引
        if (pgs->match_index) {
            destroy_match_index(pgs->match_index);
            pgs->match_index = NULL;
        }
    
    // 清理prepared statements和portals
    cleanup_prepared_statements(pgs);
    cleanup_portals(pgs);
    return PARSER_STATUS_FINISH;
}

int CPostgreParser::parse_on_reset(CSessionMgt *psm, const app_stream *a_app, const struct conn *pcon, CSession *p_session)
{
    return PARSER_STATUS_FINISH;
}

StreamData *CPostgreParser::get_stream_data_from_session(CSession *p_session, int dir)
{
    StreamData *p_stream_data = NULL;

    if ((p_stream_data = p_session->get_stream_data_from_type(m_postgre_type)) == NULL)
    {
        // 创建 StreamData
        p_stream_data = new StreamData();
        if (!p_session->set_parser_by_type(m_postgre_type, this, p_stream_data))
        {
            delete p_stream_data;
            return NULL;
        }

        p_stream_data->p_postgre_stream = new postgre_stream_t();
        memset(p_stream_data->p_postgre_stream, 0, sizeof(postgre_stream_t));
        // 明确初始化匹配索引为NULL
        p_stream_data->p_postgre_stream->match_index = NULL;
    }

    p_session->update_time(); // 更新当前会话时间

    return p_stream_data;
}

void CPostgreParser::insert_into_parser_header(postgre_stream_t *p_stream, int dir, postgre_half_stream_t *p_phs)
{
    if (!p_stream || !p_phs)
    {
        return;
    }

    if (PGSQL_REQUEST == dir)
    {
        // 客户端请求
        p_phs->next = p_stream->p_postgre_client;
        p_phs->prev = NULL; // 新节点插入到头部，prev为NULL
        
        if (p_stream->p_postgre_client)
            p_stream->p_postgre_client->prev = p_phs; // 原头节点的prev指向新节点
            
        if (!p_stream->p_postgre_client_last)
            p_stream->p_postgre_client_last = p_phs;
            
        p_stream->p_postgre_client = p_phs;
    }
    else
    {
        // 服务端响应
        p_phs->next = p_stream->p_postgre_server;
        p_phs->prev = NULL; // 新节点插入到头部，prev为NULL
        
        if (p_stream->p_postgre_server)
            p_stream->p_postgre_server->prev = p_phs; // 原头节点的prev指向新节点
            
        if (!p_stream->p_postgre_server_last)
            p_stream->p_postgre_server_last = p_phs;
            
        p_stream->p_postgre_server = p_phs;
    }
    
    // 自动添加到匹配索引（如果存在）
    if (p_stream->match_index) {
        add_to_match_index(p_stream->match_index, p_phs->data.tcp_seq, p_phs->data.tcp_ack, 
                          p_phs, (dir == PGSQL_REQUEST));
    }
}


bool CPostgreParser::postgre_parser_merge(postgre_stream_t *p_stream, const struct conn *pcon, int dir)
{
    if (!p_stream)
    {
        return false;
    }

    // ✨ 关键改进：检查COPY状态，延迟匹配直到COPY完成
    if (is_copy_active(p_stream)) {
        return false; // 延迟匹配，直到COPY完成
    }

    postgre_half_stream_t *p_req = NULL;
    postgre_half_stream_t *p_resp = NULL;

    // 根据数据来源方向选择合适的节点进行匹配
    if (PGSQL_REQUEST == dir)
    {
        // 请求端：查找匹配的请求和响应
        p_req = p_stream->p_postgre_client; // 当前请求

        if (!p_req){
            return false;
        }

        // 根据TCP seq/ack进行匹配
        // 遍历所有响应节点，寻找seq值等于请求ack值的响应
        postgre_half_stream_t *p_resp_iter = p_stream->p_postgre_server_last; // 从最早的响应开始查找
        
        while (p_resp_iter) {
            // 检查序列号匹配：支持正常匹配和丢包场景下的匹配
            bool normal_match = (p_resp_iter->data.tcp_seq == p_req->data.tcp_ack);
            bool packet_loss_match = (p_req->data.expect_rsp_ack != 0 &&
                                     p_req->data.expect_rsp_ack == p_resp_iter->data.tcp_ack);

            if (normal_match || packet_loss_match) {
                p_resp = p_resp_iter;
                if (packet_loss_match) {
                    GWLOG_WARN(m_comm, "[PostgreSQL][PacketLoss] Matched request-response using expect_rsp_ack: "
                               "req_expect_ack=%u, resp_ack=%u\n",
                               p_req->data.expect_rsp_ack, p_resp_iter->data.tcp_ack);
                }
                break;
            }
            // 继续查找前一个响应（较新的响应）
            p_resp_iter = p_resp_iter->prev;
        }
        
        // 如果没有找到匹配的响应，则保持为NULL
    }
    else
    {
        // 响应端：查找匹配的请求和响应
        p_resp = p_stream->p_postgre_server; // 当前响应

        if (!p_resp){
            return false;
        }
        
        // 根据TCP seq/ack进行匹配
        // 遍历所有请求节点，寻找ack值等于响应seq值的请求
        postgre_half_stream_t *p_req_iter = p_stream->p_postgre_client_last; // 从最早的请求开始查找
        
        while (p_req_iter) {
            // 检查序列号匹配：支持正常匹配和丢包场景下的匹配
            bool normal_match = (p_req_iter->data.tcp_ack == p_resp->data.tcp_seq);
            bool packet_loss_match = (p_req_iter->data.expect_rsp_ack != 0 &&
                                     p_req_iter->data.expect_rsp_ack == p_resp->data.tcp_ack);

            if (normal_match || packet_loss_match) {
                p_req = p_req_iter;
                if (packet_loss_match) {
                    GWLOG_WARN(m_comm, "[PostgreSQL][PacketLoss] Matched request-response using expect_rsp_ack: "
                               "req_expect_ack=%u, resp_ack=%u\n",
                               p_req_iter->data.expect_rsp_ack, p_resp->data.tcp_ack);
                }
                break;
            }
            // 继续查找前一个请求（较新的请求）
            p_req_iter = p_req_iter->prev;
        }
        
        // 如果没有找到匹配的请求，则保持为NULL
    }

    // 检查是否有可匹配的请求和响应
    if (!p_req || !p_resp)
    {
        return false;
    }


    if (!p_req->data.sql_list || p_req->data.sql_count == 0)
    {
        free_postgre_matched_data(p_stream, p_req, p_resp);
        return false;
    }
    // 处理Execute命令对应的portal列信息同步
    if (p_req->data.rs_list)
    {
        result_set_t *req_rs = p_req->data.rs_list;
        
        // 创建或获取响应结果集
        result_set_t *p_resp_rs = NULL;
        if (p_resp->data.rs_list)
        {
            // 使用现有结果集
            p_resp_rs = p_resp->data.rs_list;
            while (p_resp_rs->next)
            {
                p_resp_rs = p_resp_rs->next;
            }
        }
        else
        {
            // 创建新的结果集
            add_result_set(&p_resp->data, NULL, 0, &p_resp_rs);
        }
        
        // 确保响应结果集有正确的列计数
        if (req_rs->col_cnt > 0)
        {
            p_resp_rs->col_cnt = req_rs->col_cnt;
        }
        
        // 如果请求结果集有列定义，而响应没有，则转移所有权
        if (req_rs->col_def && !p_resp_rs->col_def)
        {
            // 直接转移列定义的所有权
            p_resp_rs->col_def = req_rs->col_def;
            req_rs->col_def = NULL;  // 防止重复释放
        }
    }
    // 遍历SQL链表和结果集链表进行匹配
    while (p_req->data.sql_count > 0 && p_resp->data.rs_list){
        // 创建临时结构体
        postgre_parsed_data_t temp_data = {0};
        
        // 转移SQL语句所有权
        sql_statement_t *current_sql = p_req->data.sql_list;
        p_req->data.sql_list = current_sql->next;
        current_sql->next = NULL;
        temp_data.sql_list = current_sql;
        temp_data.sql_count = 1;
        p_req->data.sql_count--;
        // 转移结果集所有权
        result_set_t *current_rs = p_resp->data.rs_list;
        p_resp->data.rs_list = current_rs->next;
        current_rs->next = NULL;
        temp_data.rs_list = current_rs;
        if(p_resp->data.rs_count > 0){
            temp_data.rs_count = 1;
            p_resp->data.rs_count--;
        }else{
            temp_data.rs_count = 0;
        }
        // 设置时间戳等信息
        if (p_resp->data.err_msg) {
            temp_data.err_msg = strdup(p_resp->data.err_msg);
        }
        temp_data.err_code = p_resp->data.err_code;
        temp_data.pcap_ts = p_req->data.pcap_ts;
        temp_data.start_time = p_resp->data.start_time;
        temp_data.close_time = p_resp->data.close_time;
        // 上传访问事件
        handle_access_event(p_stream, pcon, &temp_data);
        // 释放临时结构体（会释放已转移的内存）
        del_postgre_merged_data(&temp_data);
    }

    free_postgre_matched_data(p_stream, p_req, p_resp);

    return true;
}

void CPostgreParser::del_postgre_merged_data(postgre_parsed_data_t *p_data)
{
    if (!p_data)
    {
        return;
    }

    // 释放SQL语句链表（使用防御性检查）
    if (p_data->sql_list != NULL) {
        // 在释放前验证链表完整性
        if (!validate_sql_list_integrity(p_data->sql_list, "del_postgre_merged_data")) {
            GWLOG_ERROR(m_comm, "[PostgreSQL][MemorySafety] SQL list integrity validation failed before cleanup\n");
            // 即使验证失败，仍然尝试释放，但更谨慎
        }
        free_sql_list(p_data->sql_list);
        p_data->sql_list = NULL;  // 明确置空，防止重复释放
    }
    p_data->sql_count = 0;

    // 释放结果集链表
    result_set_t *rs = p_data->rs_list;
    while (rs)
    {
        result_set_t *next_rs = rs->next;
        
        // 释放列定义
        column_def_t *col = rs->col_def;
        while (col)
        {
            column_def_t *next_col = col->next;
            if (col->name.s)
            {
                free((void*)col->name.s);
            }
            delete col;
            col = next_col;
        }
        rs->col_def = NULL;
        
        // 释放行数据
        if (rs->rows)
        {
            // 确保row_cnt和col_cnt的值合理，防止内存越界访问
            if (rs->row_cnt > 0)
            {
                for (int i = 0; i < rs->row_cnt; i++)
                {
                    if (rs->rows[i])
                    {
                        // 使用辅助函数清理行数据，传入正确的列数量
                        int field_count = (rs->col_cnt > 0) ? rs->col_cnt : 1;
                        cleanup_row_data(rs->rows[i], field_count);
                        rs->rows[i] = NULL;
                    }
                }
            }
            // 释放行数组
            delete[] rs->rows;
            rs->rows = NULL;
        }
        
        // 释放结果集结构
        delete rs;
        rs = next_rs;
    }
    p_data->rs_list = NULL;
    p_data->rs_count = 0;

    // 释放错误信息
    if (p_data->err_msg)
    {
        free(p_data->err_msg);
        p_data->err_msg = NULL;
    }

    // 重置其他相关数据
    p_data->err_code = 0;
    
    // 重置COPY相关数据
    p_data->copy_len = 0;
    p_data->copy_format = 0;
    p_data->copy_columns = 0;
    p_data->copy_done = 0;
    p_data->copy_state = COPY_STATE_NONE;
    if (p_data->copy_error_msg) {
        free(p_data->copy_error_msg);
        p_data->copy_error_msg = NULL;
    }
}

void CPostgreParser::cleanup_prepared_statements(postgre_stream_t *pgs)
{
    if (!pgs) return;
    
    prepared_statement_t *stmt = pgs->prepared_statements;
    
    while (stmt) {
        prepared_statement_t *next = stmt->next;
        
        // 释放名称
        if (stmt->name) {
            free(stmt->name);
            stmt->name = NULL;
        }
        
        // 释放SQL语句链表
        if (stmt->sql_list) {
            free_sql_list(stmt->sql_list);
            stmt->sql_list = NULL;
        }
        
        // 清理指针防止悬空引用
        stmt->next = NULL;
        delete stmt;
        stmt = next;
    }
    
    pgs->prepared_statements = NULL;
}

void CPostgreParser::cleanup_portals(postgre_stream_t *pgs)
{
    if (!pgs) return;
    
    portal_t *portal = pgs->portals;
    while (portal) {
        portal_t *next = portal->next;
        
        cleanup_single_portal(portal);
        portal = next;
    }
    
    pgs->portals = NULL;
}

// 事务状态变化处理函数
void CPostgreParser::handle_transaction_state_change(postgre_stream_t *pgs, char old_status, char new_status)
{
    if (!pgs) return;
    
    switch (new_status) {
        case POSTGRE_TRANS_IDLE:
            // 事务正常结束，清理unnamed对象和exhausted portals
            if (old_status != POSTGRE_TRANS_IDLE) {
                cleanup_unnamed_objects(pgs);
                cleanup_exhausted_portals(pgs);
            }
            break;
            
        case POSTGRE_TRANS_ERROR:
            // 事务错误状态，标记所有portals需要验证
            mark_portals_for_validation(pgs);
            break;
            
        case POSTGRE_TRANS_IN_TRANSACTION:
            // 事务开始，重置对象状态
            if (old_status == POSTGRE_TRANS_ERROR) {
                cleanup_invalid_objects(pgs);
            }
            break;
    }
}

// 清理unnamed对象
void CPostgreParser::cleanup_unnamed_objects(postgre_stream_t *pgs)
{
    if (!pgs) return;
    
    // 清理unnamed prepared statement
    prepared_statement_t *stmt = pgs->prepared_statements;
    prepared_statement_t *prev_stmt = NULL;
    
    while (stmt) {
        prepared_statement_t *next_stmt = stmt->next;
        
        if (stmt->is_unnamed) {
            // 移除unnamed statement
            if (prev_stmt) {
                prev_stmt->next = next_stmt;
            } else {
                pgs->prepared_statements = next_stmt;
            }
            
            // 在释放statement前，清理所有portal中对它的引用
            clear_portal_references_to_statement(pgs, stmt);
            cleanup_single_prepared_statement(stmt);
        } else {
            prev_stmt = stmt;
        }
        
        stmt = next_stmt;
    }
    
    // 清理unnamed portal
    portal_t *portal = pgs->portals;
    portal_t *prev_portal = NULL;
    
    while (portal) {
        portal_t *next_portal = portal->next;
        
        if (portal->is_unnamed) {
            // 移除unnamed portal
            if (prev_portal) {
                prev_portal->next = next_portal;
            } else {
                pgs->portals = next_portal;
            }
            
            cleanup_single_portal(portal);
        } else {
            prev_portal = portal;
        }
        
        portal = next_portal;
    }
}

// 清理exhausted portals
void CPostgreParser::cleanup_exhausted_portals(postgre_stream_t *pgs)
{
    if (!pgs) return;
    
    portal_t *portal = pgs->portals;
    portal_t *prev = NULL;
    
    while (portal) {
        portal_t *next = portal->next;
        
        if (portal->state == PORTAL_STATE_EXHAUSTED || portal->state == PORTAL_STATE_CLOSED) {
            // 移除exhausted或closed portal
            if (prev) {
                prev->next = next;
            } else {
                pgs->portals = next;
            }
            
            cleanup_single_portal(portal);
        } else {
            prev = portal;
        }
        
        portal = next;
    }
}

// 标记portals需要验证
void CPostgreParser::mark_portals_for_validation(postgre_stream_t *pgs)
{
    if (!pgs) return;
    
    portal_t *portal = pgs->portals;
    while (portal) {
        // 在错误状态下，将执行中的portal标记为需要验证
        if (portal->state == PORTAL_STATE_EXECUTING) {
            portal->state = PORTAL_STATE_SUSPENDED;
        }
        portal = portal->next;
    }
}

// 清理无效对象
void CPostgreParser::cleanup_invalid_objects(postgre_stream_t *pgs)
{
    if (!pgs) return;
    
    // 清理状态异常的portals
    portal_t *portal = pgs->portals;
    portal_t *prev = NULL;
    
    while (portal) {
        portal_t *next = portal->next;
        
        // 清理状态为SUSPENDED且长时间未使用的portal
        uint64_t current_time = time(NULL) * 1000ULL;
        if (portal->state == PORTAL_STATE_SUSPENDED && 
            (current_time - portal->last_execute_time) > 300000) { // 5分钟超时
            
            if (prev) {
                prev->next = next;
            } else {
                pgs->portals = next;
            }
            
            cleanup_single_portal(portal);
        } else {
            prev = portal;
        }
        
        portal = next;
    }
}

// 清理单个prepared statement
void CPostgreParser::cleanup_single_prepared_statement(prepared_statement_t *stmt)
{
    if (!stmt) return;
    
    // 释放名称
    if (stmt->name) {
        free(stmt->name);
        stmt->name = NULL;
    }
    
    // 释放SQL语句链表
    if (stmt->sql_list) {
        free_sql_list(stmt->sql_list);
        stmt->sql_list = NULL;
    }
    
    // 释放参数类型数组
    if (stmt->param_types) {
        free(stmt->param_types);
        stmt->param_types = NULL;
    }
    
    // 清理其他字段
    stmt->state = STMT_STATE_CLOSED;
    stmt->param_count = 0;
    stmt->is_unnamed = false;
    stmt->create_time = 0;
    stmt->next = NULL;
    
    delete stmt;
}

// 清理单个portal
void CPostgreParser::cleanup_single_portal(portal_t *portal)
{
    if (!portal) return;
    
    // 释放名称
    if (portal->name) {
        free(portal->name);
        portal->name = NULL;
    }
    
    // 释放列定义链表
    column_def_t *col = portal->col_def;
    while (col) {
        column_def_t *next_col = col->next;
        
        if (col->name.s) {
            free((void*)col->name.s);
            col->name.s = NULL;
        }
        
        delete col;
        col = next_col;
    }
    portal->col_def = NULL;
    
    // 释放参数格式代码数组
    if (portal->param_formats) {
        free(portal->param_formats); 
        portal->param_formats = NULL;
    }
    
    // 释放参数值数组
    if (portal->param_values) {
        for (int i = 0; i < portal->param_count; i++) {
            if (portal->param_values[i]) {
                free(portal->param_values[i]);
                portal->param_values[i] = NULL;
            }
        }
        free(portal->param_values);
        portal->param_values = NULL;
    }
    
    // 释放结果格式代码数组
    if (portal->result_formats) {
        free(portal->result_formats);
        portal->result_formats = NULL;
    }
    
    // 清理其他字段
    portal->state = PORTAL_STATE_CLOSED;
    portal->stmt = NULL; // 不要释放，因为它指向prepared_statements中的元素
    portal->param_count = 0;
    portal->result_format_count = 0;
    portal->max_rows = 0;
    portal->current_row = 0;
    portal->is_unnamed = false;
    portal->create_time = 0;
    portal->last_execute_time = 0;
    portal->next = NULL;
    
    delete portal;
}

// 添加新函数用于释放匹配的请求和响应节点
void CPostgreParser::free_postgre_matched_data(postgre_stream_t *p_stream, postgre_half_stream_t *p_req, postgre_half_stream_t *p_resp)
{
    if (!p_stream)
    {
        return;
    }

    // 释放请求节点（增强安全性）
    if (p_req)
    {
        // 防御性检查：确保节点指针有效
        if ((uintptr_t)p_req < 0x1000) {
            GWLOG_ERROR(m_comm, "[PostgreSQL][FreeMatched] Invalid request node pointer: %p\n", p_req);
            return;
        }
        
        // 防御性检查：确保节点确实在客户端链表中
        bool found_in_client_list = false;
        postgre_half_stream_t *check_node = p_stream->p_postgre_client;
        while (check_node) {
            if (check_node == p_req) {
                found_in_client_list = true;
                break;
            }
            check_node = check_node->next;
        }
        
        if (!found_in_client_list) {
            GWLOG_WARN(m_comm, "[PostgreSQL][FreeMatched] Request node %p not found in client list, possible double free avoided\n", p_req);
            return;
        }
        
        // 从匹配索引中移除请求节点
        if (p_stream->match_index) {
            remove_from_match_index(p_stream->match_index, p_req->data.tcp_seq, p_req->data.tcp_ack, true);
        }
        
        if (p_req->prev)
            p_req->prev->next = p_req->next;
        else
            p_stream->p_postgre_client = p_req->next; // 头节点

        if (p_req->next)
            p_req->next->prev = p_req->prev;
        else
            p_stream->p_postgre_client_last = p_req->prev; // 尾节点

        // 清理指针防止悬空引用
        p_req->prev = NULL;
        p_req->next = NULL;
        
        // 释放节点数据
        del_postgre_merged_data(&p_req->data);
        delete p_req;
    }

    // 释放响应节点（增强安全性）
    if (p_resp)
    {   
        // 防御性检查：确保节点指针有效
        if ((uintptr_t)p_resp < 0x1000) {
            GWLOG_ERROR(m_comm, "[PostgreSQL][FreeMatched] Invalid response node pointer: %p\n", p_resp);
            return;
        }
        
        // 防御性检查：确保节点确实在服务端链表中
        bool found_in_server_list = false;
        postgre_half_stream_t *check_node = p_stream->p_postgre_server;
        while (check_node) {
            if (check_node == p_resp) {
                found_in_server_list = true;
                break;
            }
            check_node = check_node->next;
        }
        
        if (!found_in_server_list) {
            GWLOG_WARN(m_comm, "[PostgreSQL][FreeMatched] Response node %p not found in server list, possible double free avoided\n", p_resp);
            return;
        }
        
        // 从匹配索引中移除响应节点
        if (p_stream->match_index) {
            remove_from_match_index(p_stream->match_index, p_resp->data.tcp_seq, p_resp->data.tcp_ack, false);
        }
        
        if (p_resp->prev)
            p_resp->prev->next = p_resp->next;
        else
            p_stream->p_postgre_server = p_resp->next; // 头节点

        if (p_resp->next)
            p_resp->next->prev = p_resp->prev;
        else
            p_stream->p_postgre_server_last = p_resp->prev; // 尾节点
        
        // 清理指针防止悬空引用
        p_resp->prev = NULL;
        p_resp->next = NULL;
        
        // 释放节点
        del_postgre_merged_data(&p_resp->data);
        delete p_resp;
    }
}

void CPostgreParser::add_result_set(postgre_parsed_data_t *p_data, column_def_t *col_def, int col_cnt, result_set_t** p_current_rs)
{
    if (!p_data)
    {
        // 清理传入的列定义，防止内存泄漏
        cleanup_column_definitions(col_def);
        return;
    }

    // 参数验证：确保列数与列定义链表长度一致
    if (col_cnt > 0 && col_def) {
        int actual_col_cnt = 0;
        column_def_t *temp_col = col_def;
        while (temp_col) {
            actual_col_cnt++;
            temp_col = temp_col->next;
        }
        if (actual_col_cnt != col_cnt) {
            GWLOG_ERROR(m_comm, "[PostgreSQL] Column count mismatch: expected %d, actual %d\n", 
                       col_cnt, actual_col_cnt);
            cleanup_column_definitions(col_def);
            return;
        }
    }

    // 检查是否已存在相同的结果集，避免重复创建
    if (p_data->rs_list && col_cnt == 1 && col_def && col_def->name.s) {
        result_set_t *existing_rs = p_data->rs_list;
        while (existing_rs) {
            if (existing_rs->col_cnt == 1 && existing_rs->col_def && 
                existing_rs->col_def->name.s && 
                strcmp(existing_rs->col_def->name.s, col_def->name.s) == 0) {
                *p_current_rs = existing_rs;
                // 清理传入的重复列定义
                cleanup_column_definitions(col_def);
                return;
            }
            existing_rs = existing_rs->next;
        }
    }

    // 创建新的数据结果集
    result_set_t *current_rs = new result_set_t();
    if (!current_rs)
    {
        GWLOG_ERROR(m_comm, "[PostgreSQL] Failed to allocate result set\n");
        // 清理已分配的列定义
        cleanup_column_definitions(col_def);
        return;
    }
    
    // 初始化结果集
    current_rs->rows = NULL;
    current_rs->row_cnt = 0;
    current_rs->row_capacity = 0;
    current_rs->col_def = col_def;
    current_rs->col_cnt = col_cnt;
    current_rs->next = NULL;
    
    // 添加到结果集链表末尾
    if (!p_data->rs_list)
    {
        p_data->rs_list = current_rs;
    }
    else
    {
        result_set_t *last_rs = p_data->rs_list;
        while (last_rs->next)
        {
            last_rs = last_rs->next;
        }
        last_rs->next = current_rs;
    }

    *p_current_rs = current_rs;
    p_data->rs_count++;
}

void CPostgreParser::update_result_set(result_set_t *target_rs, postgre_row_data_t **rows, int row_cnt)
{
    if (!target_rs || !rows || row_cnt <= 0)
    {
        // 参数验证失败，清理行数据防止内存泄漏
        if (rows) {
            for (int i = 0; i < row_cnt; i++) {
                if (rows[i]) {
                    // 使用行数据自身的字段数量，如果没有则使用结果集的列数量
                    int field_count = (rows[i]->field_count > 0) ? rows[i]->field_count : 
                                     (target_rs ? target_rs->col_cnt : 0);
                    cleanup_row_data(rows[i], field_count);
                }
            }
            delete[] rows;
        }
        return;
    }

    int total_rows = target_rs->row_cnt + row_cnt;
    
    // 检查是否需要扩容
    if (total_rows > target_rs->row_capacity)
    {
        int new_capacity = target_rs->row_capacity;
        if (new_capacity == 0) {
            // 初始容量设为8或row_cnt的较大值
            new_capacity = (row_cnt > 8) ? row_cnt : 8;
        } else {
            // 扩容策略：倍增直到能容纳新行
            while (new_capacity < total_rows) {
                new_capacity *= 2;
                if (new_capacity <= 0) { // 防止整数溢出
                    new_capacity = total_rows;
                    break;
                }
            }
        }
        
        // 分配新的行数组
        postgre_row_data_t **new_rows_array = new postgre_row_data_t*[new_capacity];
        if (!new_rows_array)
        {
            GWLOG_ERROR(m_comm, "[PostgreSQL] Failed to allocate memory for expanded row array\n");
            // 释放传入的行数组及数据
            for (int i = 0; i < row_cnt; i++) {
                if (rows[i]) {
                    // 使用行数据自身的字段数量，如果没有则使用结果集的列数量
                    int field_count = (rows[i]->field_count > 0) ? rows[i]->field_count : target_rs->col_cnt;
                    cleanup_row_data(rows[i], field_count);
                }
            }
            delete[] rows;
            return;
        }
        
        // 复制现有行数据
        if (target_rs->rows && target_rs->row_cnt > 0)
        {
            memcpy(new_rows_array, target_rs->rows, sizeof(postgre_row_data_t*) * target_rs->row_cnt);
            delete[] target_rs->rows;
        }
        
        // 复制新行数据
        memcpy(new_rows_array + target_rs->row_cnt, rows, sizeof(postgre_row_data_t*) * row_cnt);
        
        // 更新行数组
        target_rs->rows = new_rows_array;
        target_rs->row_cnt = total_rows;
        target_rs->row_capacity = new_capacity;
        
        // 释放传入的行数组（但不释放其中的行数据）
        delete[] rows;
    }
    else
    {
        // 有足够空间，直接复制新行
        memcpy(target_rs->rows + target_rs->row_cnt, rows, sizeof(postgre_row_data_t*) * row_cnt);
        target_rs->row_cnt += row_cnt;
        
        // 释放传入的行数组（但不释放其中的行数据）
        delete[] rows;
    }
}

//启动类消息识别函数
bool CPostgreParser::is_startup_like_message(const char *data, int len)
{
    if (len < 8) return false;
    
    uint32_t msg_len = GET_MSG_LEN(data);
    uint32_t code = GET_MSG_LEN(data + 4);
    
    // SSL请求
    if (msg_len == POSTGRE_SSL_REQUEST_LEN && code == POSTGRE_SSL_REQUEST_CODE) {
        return true;
    }
    
    // GSSENC请求
    if (msg_len == POSTGRE_SSL_REQUEST_LEN && code == POSTGRE_GSSENC_REQUEST_CODE) {
        return true;
    }
    
    // 取消请求
    if (msg_len == 16 && code == 80877102) {
        return true;
    }
    
    // 启动消息
    if (code == POSTGRE_PROTOCOL_VERSION_3) {
        return true;
    }
    
    return false;
}

// 基于消息特征的核心解析函数
int CPostgreParser::parse_by_message_signature(postgre_stream_t *pgs, const char *data, int len,
                                              bool is_client, const struct conn *pcon, CSession *p_session, half_stream* hlf)
{
    if (is_client) {
        return parse_client_message_by_signature(pgs, data, len, pcon, p_session, hlf);
    } else {
        return parse_server_message_by_signature(pgs, data, len, pcon, p_session, hlf);
    }
}

// 客户端消息特征解析
int CPostgreParser::parse_client_message_by_signature(postgre_stream_t *pgs, const char *data, int len, 
                                                    const struct conn *pcon, CSession *p_session, half_stream* hlf)
{
    // 检查是否为启动类消息（无消息类型字节）
    if (len >= 8 && is_startup_like_message(data, len)) {
        // 记录启动时间
        if (pgs->pg_stat.startup_time == 0) {
            pgs->pg_stat.startup_time = get_time_ts_ms(p_session, m_conf_pcap_timestamp);
        }
        
        // 检查取消请求的特征
        uint32_t msg_len = GET_MSG_LEN(data);
        uint32_t request_code = GET_MSG_LEN(data + 4);
        
        if (msg_len == 16 && request_code == 80877102) {
            return parse_cancel_request_msg(pgs, data, len, pcon);
        }
        
        return parse_startup_msg(pgs, data, len, pcon);
    }
    
    // 检查是否为标准客户端消息（有消息类型字节）
    if (len >= 5) {
        char msg_type = data[0];
        
        switch (msg_type) {
            case POSTGRE_MSG_PASSWORD:        // 'p' - Password Response
            {
                // 记录启动时间
                if (pgs->pg_stat.startup_time == 0) {
                    pgs->pg_stat.startup_time = get_time_ts_ms(p_session, m_conf_pcap_timestamp);
                }
                return parse_client_auth_msg(pgs, data, len);
            }
            default:
            {
                // ✨ 关键改进：在创建节点前检查COPY状态
                if (is_copy_active(pgs)) {
                    // 在COPY模式下，直接处理消息，不创建新节点
                    return parse_query_msg(pgs, data, len, hlf);
                }

                // 非COPY模式：创建新的解析节点用于业务消息
                postgre_half_stream_t *p_new_stream = new postgre_half_stream_t();
                if (!p_new_stream) {
                    GWLOG_ERROR(m_comm, "[Postgre][Parse] Failed to allocate new stream node\n");
                    return PARSER_STATUS_DROP_DATA;
                }
                p_new_stream->next = NULL;
                p_new_stream->prev = NULL;  // 确保初始化为NULL
                memset(&p_new_stream->data, 0, sizeof(postgre_parsed_data_t));
                p_new_stream->data.tcp_seq = hlf->first_seq;
                p_new_stream->data.tcp_ack = hlf->stream_ack;
                // 设置期待的响应ACK值，用于丢包场景下的匹配
                p_new_stream->data.expect_rsp_ack = hlf->first_seq + hlf->data_len + hlf->lost_len;

                // 设置时间戳
                if (p_new_stream->data.pcap_ts == 0) {
                    p_new_stream->data.pcap_ts = get_time_ts_ms(p_session, m_conf_pcap_timestamp);
                }

                // 插入新节点到链表
                insert_into_parser_header(pgs, PGSQL_REQUEST, p_new_stream);

                // 统计新创建的请求节点
                GWLOG_DEBUG(m_comm, "[PostgreSQL][Stats] Created new request stream node\n");

                return parse_query_msg(pgs, data, len, hlf);
            }
        }
    }
    
    return PARSER_STATUS_CONTINUE;
}

// 服务端消息特征解析
int CPostgreParser::parse_server_message_by_signature(postgre_stream_t *pgs, const char *data, int len, 
                                                     const struct conn *pcon, CSession *p_session, half_stream* hlf)
{
    if (len >= 5) {
        char msg_type = data[0];
        
        switch (msg_type) {
            case POSTGRE_MSG_AUTHENTICATION:        // 'R' - Authentication
            {
                // 记录启动响应时间
                if (pgs->pg_stat.startup_start_time == 0) {
                    pgs->pg_stat.startup_start_time = get_time_ts_ms(p_session, m_conf_pcap_timestamp);
                }
                return parse_auth_msg(pgs, data, len, pcon, p_session);
            }
            case POSTGRE_MSG_ERROR_RESPONSE:        // 'E' - Error Response
            {
                // 检查是否处于认证阶段
                if (pgs->pg_stat.transaction_status == 0 && pgs->pg_stat.startup_close_time == 0) {
                    // 检查是否有客户端发送过SQL查询语句
                    bool has_sql_activity = false;
                    postgre_half_stream_t *client_stream = pgs->p_postgre_client;
                    while (client_stream) {
                        if (client_stream->data.sql_list && client_stream->data.sql_count > 0) {
                            has_sql_activity = true;
                            break;
                        }
                        client_stream = client_stream->next;
                    }

                    // 如果没有SQL活动，才认为是认证阶段的错误
                    if (!has_sql_activity) {
                        return parse_auth_msg(pgs, data, len, pcon, p_session);
                    }
                }
            }

            default:
            {
                // ✨ 关键改进：在创建节点前检查COPY状态
                if (is_copy_active(pgs)) {
                    // 在COPY模式下，直接处理消息，不创建新节点
                    return parse_response_msg(pgs, data, len, pcon, p_session, hlf);
                }

                // 非COPY模式：创建新的解析节点用于响应消息
                postgre_half_stream_t *p_new_stream = new postgre_half_stream_t();
                if (!p_new_stream) {
                    GWLOG_ERROR(m_comm, "[Postgre][Parse] Failed to allocate new stream node\n");
                    return PARSER_STATUS_DROP_DATA;
                }
                p_new_stream->next = NULL;
                p_new_stream->prev = NULL;  // 确保初始化为NULL
                memset(&p_new_stream->data, 0, sizeof(postgre_parsed_data_t));
                p_new_stream->data.tcp_seq = hlf->first_seq;
                p_new_stream->data.tcp_ack = hlf->stream_ack;

                // 设置时间戳
                if (p_new_stream->data.start_time == 0) {
                    p_new_stream->data.start_time = get_time_ts_ms(p_session, m_conf_pcap_timestamp);
                }

                // 插入新节点到链表
                insert_into_parser_header(pgs, PGSQL_RESPONSE, p_new_stream);

                return parse_response_msg(pgs, data, len, pcon, p_session, hlf);
            }
        }
    }
    
    return PARSER_STATUS_CONTINUE;
}

// 统计信息更新辅助函数
void CPostgreParser::update_parse_stats(int success, int data_len, int dir)
{
    // 更新总体解析统计
    __sync_add_and_fetch(&m_stats_postgre.p.cnt_p, 1);
    __sync_add_and_fetch(&m_stats_postgre.p.cnt_p_bytes, data_len);
    
    if (success > 0) {
        __sync_add_and_fetch(&m_stats_postgre.p.cnt_p_succ, 1);
    } else if (success < 0) {
        __sync_add_and_fetch(&m_stats_postgre.p.cnt_p_fail, 1);
    }
    
    // 根据方向更新请求/响应侧统计
    if (dir == PGSQL_REQUEST) {
        // 客户端请求侧统计
        __sync_add_and_fetch(&m_stats_postgre.req.cnt_p, 1);
        __sync_add_and_fetch(&m_stats_postgre.req.cnt_p_bytes, data_len);
        
        if (success > 0) {
            __sync_add_and_fetch(&m_stats_postgre.req.cnt_p_succ, 1);
        } else if (success < 0) {
            __sync_add_and_fetch(&m_stats_postgre.req.cnt_p_fail, 1);
        }
    } else if (dir == PGSQL_RESPONSE) {
        // 服务端响应侧统计
        __sync_add_and_fetch(&m_stats_postgre.rsp.cnt_p, 1);
        __sync_add_and_fetch(&m_stats_postgre.rsp.cnt_p_bytes, data_len);
        
        if (success > 0) {
            __sync_add_and_fetch(&m_stats_postgre.rsp.cnt_p_succ, 1);
        } else if (success < 0) {
            __sync_add_and_fetch(&m_stats_postgre.rsp.cnt_p_fail, 1);
        }
    }
}

// 更新匹配统计
void CPostgreParser::update_match_stats(bool success, int dir)
{
    // 更新总体匹配统计
    __sync_add_and_fetch(&m_stats_postgre.m.cnt_m, 1);
    if (success) {
        __sync_add_and_fetch(&m_stats_postgre.m.cnt_m_succ, 1);
    } else {
        __sync_add_and_fetch(&m_stats_postgre.m.cnt_m_fail, 1);
    }
    
    // 根据方向更新具体的匹配统计
    if (dir == PGSQL_REQUEST) {
        // 请求匹配响应的统计
        __sync_add_and_fetch(&m_stats_postgre.req_match_rsp.cnt_m, 1);
        if (success) {
            __sync_add_and_fetch(&m_stats_postgre.req_match_rsp.cnt_m_succ, 1);
        } else {
            __sync_add_and_fetch(&m_stats_postgre.req_match_rsp.cnt_m_fail, 1);
        }
    } else if (dir == PGSQL_RESPONSE) {
        // 响应匹配请求的统计
        __sync_add_and_fetch(&m_stats_postgre.rsp_match_req.cnt_m, 1);
        if (success) {
            __sync_add_and_fetch(&m_stats_postgre.rsp_match_req.cnt_m_succ, 1);
        } else {
            __sync_add_and_fetch(&m_stats_postgre.rsp_match_req.cnt_m_fail, 1);
        }
    }
}

// 更新session统计
void CPostgreParser::update_session_stats(int data_len)
{
    __sync_add_and_fetch(&m_stats_postgre.cnt_session, 1);
    __sync_add_and_fetch(&m_stats_postgre.cnt_session_bytes, data_len);
}

//清理portal中对指定statement的引用
void CPostgreParser::clear_portal_references_to_statement(postgre_stream_t *pgs, prepared_statement_t *stmt)
{
    if (!pgs || !stmt) return;
    
    portal_t *portal = pgs->portals;
    while (portal) {
        if (portal->stmt == stmt) {
            portal->stmt = NULL;
        }
        portal = portal->next;
    }
}

// 获取期望的字段数量，优先使用结果集的列数，其次使用pg_stat.num_fields
int CPostgreParser::get_expected_field_count(postgre_stream_t *pgs, result_set_t *current_rs)
{
    if (current_rs && current_rs->col_cnt > 0) {
        return current_rs->col_cnt;
    }
    return pgs->pg_stat.num_fields;
}

// 验证字段数量是否匹配
bool CPostgreParser::validate_field_count(postgre_stream_t *pgs, result_set_t *current_rs, int actual_fields, const char *context)
{
    int expected_fields = get_expected_field_count(pgs, current_rs);

    // 特殊情况：如果没有结果集且没有portal列定义，但有实际字段数据
    // 这可能是Row Description丢包的情况，允许通过验证以便后续动态创建列定义
    if (!current_rs && expected_fields == 0) {
        if (actual_fields > 0) {
            GWLOG_WARN(m_comm, "[PostgreSQL][PacketLoss] %s: No column definitions but got %d fields, "
                       "allowing for dynamic column creation\n", context, actual_fields);
            return true;  // 允许通过验证，后续会动态创建列定义
        }
        return false;
    }

    if (actual_fields != expected_fields) {
        // 特殊情况：如果有实际字段但期望为0，可能是Row Description丢包
        // 在这种情况下允许通过验证，后续会动态创建列定义
        if (expected_fields == 0 && actual_fields > 0) {
            GWLOG_WARN(m_comm, "[PostgreSQL][PacketLoss] %s: Expected 0 fields but got %d, "
                       "likely Row Description packet loss, allowing for dynamic column creation\n",
                       context, actual_fields);
            return true;
        }

        const char *source = (current_rs && current_rs->col_cnt > 0) ? "result_set" : "pg_stat";
        GWLOG_ERROR(m_comm, "[PostgreSQL] %s field count mismatch: expected %d (from %s), got %d\n",
                   context, expected_fields, source, actual_fields);
        return false;
    }
    return true;
}

// ========== 匹配优化实现 ==========

// 匹配索引数据结构
struct PostgreMatchIndex {
    std::unordered_map<uint32_t, postgre_half_stream_t*> request_index;   // 请求索引：tcp_ack -> stream
    std::unordered_map<uint32_t, postgre_half_stream_t*> response_index;  // 响应索引：tcp_seq -> stream
    size_t max_size;                                                      // 最大索引大小
    
    PostgreMatchIndex() : max_size(10000) {}  // 默认最大10000个条目
};

// 创建匹配索引
void* CPostgreParser::create_match_index() {
    return new PostgreMatchIndex();
}

// 销毁匹配索引
void CPostgreParser::destroy_match_index(void *index) {
    if (index) {
        delete static_cast<PostgreMatchIndex*>(index);
    }
}

// 向匹配索引添加条目
void CPostgreParser::add_to_match_index(void *index, uint32_t tcp_seq, uint32_t tcp_ack, 
                                       postgre_half_stream_t *stream, bool is_request) {
    if (!index || !stream) return;
    
    PostgreMatchIndex *match_index = static_cast<PostgreMatchIndex*>(index);
    
    if (is_request) {
        // 请求端：使用tcp_ack作为键，这样响应端可以通过tcp_seq找到匹配的请求
        match_index->request_index[tcp_ack] = stream;
        
        // 防止索引过大占用内存
        if (match_index->request_index.size() > match_index->max_size) {
            GWLOG_DEBUG(m_comm, "[PostgreSQL][MatchIndex] Request index size exceeded %zu, cleaning up\n", 
                       match_index->max_size);
            cleanup_match_index(index);
        }
    } else {
        // 响应端：使用tcp_seq作为键，这样请求端可以通过tcp_ack找到匹配的响应
        match_index->response_index[tcp_seq] = stream;
        
        // 防止索引过大占用内存
        if (match_index->response_index.size() > match_index->max_size) {
            GWLOG_DEBUG(m_comm, "[PostgreSQL][MatchIndex] Response index size exceeded %zu, cleaning up\n", 
                       match_index->max_size);
            cleanup_match_index(index);
        }
    }
}

// 通过序列号查找匹配的流
postgre_half_stream_t* CPostgreParser::find_match_by_seq(void *index, uint32_t target_seq, bool search_requests) {
    if (!index) return nullptr;
    
    PostgreMatchIndex *match_index = static_cast<PostgreMatchIndex*>(index);
    
    if (search_requests) {
        auto it = match_index->request_index.find(target_seq);
        return (it != match_index->request_index.end()) ? it->second : nullptr;
    } else {
        auto it = match_index->response_index.find(target_seq);
        return (it != match_index->response_index.end()) ? it->second : nullptr;
    }
}

// 从匹配索引移除条目
void CPostgreParser::remove_from_match_index(void *index, uint32_t tcp_seq, uint32_t tcp_ack, bool is_request) {
    if (!index) return;
    
    PostgreMatchIndex *match_index = static_cast<PostgreMatchIndex*>(index);
    
    if (is_request) {
        match_index->request_index.erase(tcp_ack);
    } else {
        match_index->response_index.erase(tcp_seq);
    }
}

// 清理匹配索引（保留最近的一半条目）
void CPostgreParser::cleanup_match_index(void *index) {
    if (!index) return;
    
    PostgreMatchIndex *match_index = static_cast<PostgreMatchIndex*>(index);
    
    // 简单的清理策略：清除所有条目
    // 在实际应用中，可以实现更复杂的LRU策略
    if (match_index->request_index.size() > match_index->max_size / 2) {
        size_t old_size = match_index->request_index.size();
        match_index->request_index.clear();
        GWLOG_DEBUG(m_comm, "[PostgreSQL][MatchIndex] Cleaned up request index: %zu -> 0\n", old_size);
    }
    
    if (match_index->response_index.size() > match_index->max_size / 2) {
        size_t old_size = match_index->response_index.size();
        match_index->response_index.clear();
        GWLOG_DEBUG(m_comm, "[PostgreSQL][MatchIndex] Cleaned up response index: %zu -> 0\n", old_size);
    }
}

// 优化后的匹配函数 - 使用哈希索引实现O(1)查找
bool CPostgreParser::postgre_parser_merge_optimized(postgre_stream_t *p_stream, const struct conn *pcon, int dir) {
    if (!p_stream) {
        return false;
    }

    // 确保匹配索引存在
    if (!p_stream->match_index) {
        p_stream->match_index = create_match_index();
        if (!p_stream->match_index) {
            GWLOG_ERROR(m_comm, "[PostgreSQL][MatchIndex] Failed to create match index\n");
            // 回退到原始匹配算法
            return postgre_parser_merge(p_stream, pcon, dir);
        }
    }

    postgre_half_stream_t *p_req = NULL;
    postgre_half_stream_t *p_resp = NULL;

    // 根据数据来源方向选择合适的节点进行匹配
    if (PGSQL_REQUEST == dir) {
        // 请求端：查找匹配的请求和响应
        p_req = p_stream->p_postgre_client;
        if (!p_req) {
            return false;
        }

        // 使用哈希索引快速查找匹配的响应 - O(1)复杂度
        p_resp = find_match_by_seq(p_stream->match_index, p_req->data.tcp_ack, false);
        
        // 如果没有找到匹配的响应，将请求添加到索引中等待匹配
        if (!p_resp) {
            add_to_match_index(p_stream->match_index, p_req->data.tcp_seq, p_req->data.tcp_ack, p_req, true);
            return false;
        }
    } else {
        // 响应端：查找匹配的请求和响应
        p_resp = p_stream->p_postgre_server;
        if (!p_resp) {
            return false;
        }
        
        // 使用哈希索引快速查找匹配的请求 - O(1)复杂度
        p_req = find_match_by_seq(p_stream->match_index, p_resp->data.tcp_seq, true);
        
        // 如果没有找到匹配的请求，将响应添加到索引中等待匹配
        if (!p_req) {
            add_to_match_index(p_stream->match_index, p_resp->data.tcp_seq, p_resp->data.tcp_ack, p_resp, false);
            return false;
        }
    }

    // 检查是否有可匹配的请求和响应
    if (!p_req || !p_resp) {
        return false;
    }

    // 从索引中移除已匹配的条目
    remove_from_match_index(p_stream->match_index, p_req->data.tcp_seq, p_req->data.tcp_ack, true);
    remove_from_match_index(p_stream->match_index, p_resp->data.tcp_seq, p_resp->data.tcp_ack, false);

    // 执行实际的匹配逻辑（复用原有的匹配处理逻辑）
    return execute_match_logic(p_stream, pcon, p_req, p_resp);
}

// 执行匹配逻辑的辅助函数 - 从原有的postgre_parser_merge函数中提取
bool CPostgreParser::execute_match_logic(postgre_stream_t *p_stream, const struct conn *pcon, 
                                        postgre_half_stream_t *p_req, postgre_half_stream_t *p_resp) {
    if (!p_req->data.sql_list || p_req->data.sql_count == 0) {
        free_postgre_matched_data(p_stream, p_req, p_resp);
        return false;
    }
    // 处理Execute命令对应的portal列信息同步
    if (p_req->data.rs_list) {
        result_set_t *req_rs = p_req->data.rs_list;
        
        // 创建或获取响应结果集
        result_set_t *p_resp_rs = NULL;
        if (p_resp->data.rs_list) {
            p_resp_rs = p_resp->data.rs_list;
            while (p_resp_rs->next) {
                p_resp_rs = p_resp_rs->next;
            }
        } else {
            add_result_set(&p_resp->data, NULL, 0, &p_resp_rs);
        }
        
        // 确保响应结果集有正确的列计数
        if (req_rs->col_cnt > 0) {
            p_resp_rs->col_cnt = req_rs->col_cnt;
        }
        
        // 如果请求结果集有列定义，而响应没有，则转移所有权
        if (req_rs->col_def && !p_resp_rs->col_def) {
            p_resp_rs->col_def = req_rs->col_def;
            req_rs->col_def = NULL;
        }
    }
    // 遍历SQL链表和结果集链表进行匹配
    while (p_req->data.sql_count > 0 && p_resp->data.rs_list) {
        // 创建临时结构体
        postgre_parsed_data_t temp_data = {0};
        
        // 转移SQL语句所有权
        sql_statement_t *current_sql = p_req->data.sql_list;
        p_req->data.sql_list = current_sql->next;
        current_sql->next = NULL;
        temp_data.sql_list = current_sql;
        temp_data.sql_count = 1;
        p_req->data.sql_count--;
        // 转移结果集所有权
        result_set_t *current_rs = p_resp->data.rs_list;
        p_resp->data.rs_list = current_rs->next;
        current_rs->next = NULL;
        temp_data.rs_list = current_rs;
        if (p_resp->data.rs_count > 0) {
            temp_data.rs_count = 1;
            p_resp->data.rs_count--;
        } else {
            temp_data.rs_count = 0;
        }
        // 设置时间戳等信息
        if (p_resp->data.err_msg) {
            temp_data.err_msg = strdup(p_resp->data.err_msg);
        }
        temp_data.err_code = p_resp->data.err_code;
        temp_data.pcap_ts = p_req->data.pcap_ts;
        temp_data.start_time = p_resp->data.start_time;
        temp_data.close_time = p_resp->data.close_time;
        // 上传访问事件
        handle_access_event(p_stream, pcon, &temp_data);
        // 释放临时结构体
        del_postgre_merged_data(&temp_data);
    }

    free_postgre_matched_data(p_stream, p_req, p_resp);
    return true;
}

// ==================== COPY DATA消息统一处理函数 ====================

/**
 * 统一处理COPY DATA消息
 * @param pgs PostgreSQL流对象
 * @param data 消息数据
 * @param offset 数据偏移量
 * @param msg_len 消息长度
 * @param is_client 方向标识（true表示客户端到服务器，false表示服务器到客户端）
 * @param is_header 是否为头部行的引用（用于跟踪COPY操作的头部状态）
 * @return 解析状态
 */
int CPostgreParser::process_copy_data_message(postgre_stream_t *pgs, const char *data, int offset,
                                            uint32_t msg_len, bool is_client)
{
    // 检查COPY上下文
    if (!pgs || !pgs->copy_context || pgs->copy_context->state == COPY_STATE_NONE) {
        GWLOG_WARN(m_comm, "[PostgreSQL] COPY DATA received without active COPY context\n");
        return PARSER_STATUS_DROP_DATA;
    }

    copy_context_t *ctx = pgs->copy_context;

    // 获取目标存储节点（统一存储到响应端）
    postgre_half_stream_t *target_node = pgs->p_postgre_server;
    if (!target_node) {
        return PARSER_STATUS_DROP_DATA;
    }

    // 统一的数据指针计算和存储位置（都存储到响应端）
    const char *copy_data = data + offset + 5;
    result_set_t **rs_list_ptr = &(target_node->data.rs_list);
    int *rs_count_ptr = &(target_node->data.rs_count);

    // 从响应端节点获取COPY参数
    uint8_t copy_format = target_node->data.copy_format;
    uint16_t copy_columns = target_node->data.copy_columns;

    size_t copy_data_len = msg_len - 4;

    // 判断当前数据是否为头部行
    bool is_current_header = false;
    if (ctx->has_header && !ctx->header_processed) {
        // 第一条COPY DATA且SQL命令包含HEADER选项
        is_current_header = true;
        ctx->header_processed = true;
        GWLOG_DEBUG(m_comm, "[PostgreSQL] Processing COPY header row\n");
    } else {
        GWLOG_DEBUG(m_comm, "[PostgreSQL] Processing COPY data row\n");
    }

    // 获取当前结果集或创建新的结果集
    result_set_t *current_rs = NULL;
    if (*rs_list_ptr) {
        current_rs = *rs_list_ptr;
        while (current_rs->next) {
            current_rs = current_rs->next;
        }
    }

    // 如果没有结果集或结果集没有列定义，创建列定义
    if ((!current_rs || !current_rs->col_def) && copy_columns > 0) {
        // 如果没有结果集，创建新的结果集
        if (!current_rs) {
            current_rs = new result_set_t();
            if (!current_rs) {
                GWLOG_ERROR(m_comm, "[PostgreSQL][COPY] Failed to allocate result set for COPY\n");
                return PARSER_STATUS_DROP_DATA;
            }

            // 初始化结果集
            current_rs->rows = NULL;
            current_rs->row_cnt = 0;
            current_rs->row_capacity = 0;
            current_rs->col_def = NULL;
            current_rs->col_cnt = 0;
            current_rs->next = NULL;

            // 添加到链表
            if (!*rs_list_ptr) {
                *rs_list_ptr = current_rs;
            } else {
                result_set_t *last_rs = *rs_list_ptr;
                while (last_rs->next) {
                    last_rs = last_rs->next;
                }
                last_rs->next = current_rs;
            }
            (*rs_count_ptr)++;
        }

        // 创建列定义（基于之前服务器发送的COPY响应消息）
        column_def_t *last_col = NULL;
        for (int i = 0; i < copy_columns; i++) {
            column_def_t *col = new column_def_t();
            if (col) {
                char col_name[32];
                snprintf(col_name, sizeof(col_name), "column_%d", i + 1);
                char *name_copy = strdup(col_name);
                if (name_copy) {
                    col->name.s = name_copy;
                    col->name.len = strlen(name_copy);
                    col->next = NULL;

                    // 设置格式代码，从COPY响应消息中获取
                    col->format_code = copy_format;

                    // 使用尾插法
                    if (last_col == NULL) {
                        current_rs->col_def = col;  // 第一个节点
                    } else {
                        last_col->next = col;       // 插入到尾部
                    }
                    last_col = col;
                    current_rs->col_cnt++;
                } else {
                    delete col;
                }
            }
        }
    }

    // 处理COPY数据内容
    bool binary_fail = false;

    // 处理二进制格式的COPY数据
    if (copy_format == PG_FORMAT_BINARY && m_use_binary_extended) {
        // 验证二进制格式的COPY数据
        if (copy_data_len >= 11) { // 最小二进制格式长度: 签名(11字节)
            const char *binary_data = copy_data;

            // 二进制格式通常不包含头部行，但保留检查逻辑
            bool local_is_header = is_current_header && binary_data[0] == 'P' && binary_data[1] == 'G';

            // 检查二进制格式签名
            if (binary_data[0] == 'P' && binary_data[1] == 'G' &&
                binary_data[2] == 'C' && binary_data[3] == 'O' &&
                binary_data[4] == 'P' && binary_data[5] == 'Y' &&
                binary_data[6] == '\n' && binary_data[7] == '\377' &&
                binary_data[8] == '\r' && binary_data[9] == '\n' &&
                binary_data[10] == '\0') {

                // 二进制格式的头部处理（通常不需要特殊处理）
                if (local_is_header) {
                    GWLOG_DEBUG(m_comm, "[PostgreSQL] Binary format header detected\n");
                }

                // 跳过签名部分
                binary_data += 11;
                size_t binary_data_len = copy_data_len - 11;

                // 处理元数据和实际数据
                uint32_t field_count = copy_columns;
                size_t pos = 0;

                // 创建扩展行数据
                postgre_row_data_extended_t *ext_row = (postgre_row_data_extended_t*)calloc(1, sizeof(postgre_row_data_extended_t));
                if (!ext_row) {
                    GWLOG_ERROR(m_comm, "[Postgre][COPY] Failed to allocate extended row data\n");
                    return PARSER_STATUS_DROP_DATA;
                }

                ext_row->field_count = field_count;
                ext_row->values = (postgre_field_value_t**)calloc(field_count, sizeof(postgre_field_value_t*));
                if (!ext_row->values) {
                    GWLOG_ERROR(m_comm, "[Postgre][COPY] Failed to allocate field values array\n");
                    free(ext_row);
                    return PARSER_STATUS_DROP_DATA;
                }

                // 读取字段数量
                uint16_t header_fields = 0;
                if (pos + 2 <= binary_data_len) {
                    memcpy(&header_fields, binary_data + pos, 2);
                    header_fields = ntohs(header_fields);
                    pos += 2;

                    if (header_fields != field_count) {
                        GWLOG_ERROR(m_comm, "[Postgre][COPY] Field count mismatch: expected %d, got %d\n",
                                   field_count, header_fields);
                        cleanup_row_data_extended(ext_row);
                        return PARSER_STATUS_DROP_DATA;
                    }
                } else {
                    GWLOG_ERROR(m_comm, "[Postgre][COPY] Binary data too short for field count\n");
                    cleanup_row_data_extended(ext_row);
                    return PARSER_STATUS_DROP_DATA;
                }

                // 处理每个字段
                column_def_t *col = current_rs->col_def;
                for (uint32_t i = 0; i < field_count && col; i++, col = col->next) {
                    if (pos + 4 > binary_data_len) {
                        GWLOG_ERROR(m_comm, "[Postgre][COPY] Binary data too short for field length\n");
                        cleanup_row_data_extended(ext_row);
                        return PARSER_STATUS_DROP_DATA;
                    }

                    // 读取字段长度
                    int32_t field_len = 0;
                    memcpy(&field_len, binary_data + pos, 4);
                    field_len = ntohl(field_len);
                    pos += 4;

                    // 为每个字段创建字段值结构
                    postgre_field_value_t *field = (postgre_field_value_t*)calloc(1, sizeof(postgre_field_value_t));
                    if (!field) {
                        cleanup_row_data_extended(ext_row);
                        return PARSER_STATUS_DROP_DATA;
                    }

                    if (field_len == -1) {
                        // NULL值
                        field->is_null = true;
                        field->text_value.s = strdup("NULL");
                        field->text_value.len = 4;
                        ext_row->values[i] = field;
                    } else if (field_len >= 0) {
                        if (pos + field_len > binary_data_len) {
                            GWLOG_ERROR(m_comm, "[Postgre][COPY] Binary data too short for field value\n");
                            free(field);
                            cleanup_row_data_extended(ext_row);
                            return PARSER_STATUS_DROP_DATA;
                        }

                        // 统一的二进制字段处理逻辑
                        postgre_field_value_t *decoded_field = decode_binary_field(
                            binary_data + pos, field_len, col->type_oid,
                            PG_FORMAT_BINARY, col->type_len, m_binary_handlers);

                        if (decoded_field) {
                            // 复制解码后的字段值到新字段（统一使用安全的复制方式）
                            field->is_null = decoded_field->is_null;
                            field->text_value.s = strdup(decoded_field->text_value.s);
                            field->text_value.len = decoded_field->text_value.len;
                            if (decoded_field->binary_len > 0) {
                                field->binary_value = malloc(decoded_field->binary_len);
                                if (field->binary_value) {
                                    memcpy(field->binary_value, decoded_field->binary_value, decoded_field->binary_len);
                                    field->binary_len = decoded_field->binary_len;
                                }
                            }
                            field->type_oid = decoded_field->type_oid;
                            field->format_code = decoded_field->format_code;

                            // 清理临时字段
                            cleanup_field_value(decoded_field);
                        } else {
                            // 如果解码失败，使用原始二进制数据
                            field->is_null = false;
                            field->binary_value = malloc(field_len);
                            if (field->binary_value) {
                                memcpy(field->binary_value, binary_data + pos, field_len);
                                field->binary_len = field_len;
                            }
                            field->text_value.s = strdup("[BINARY]");
                            field->text_value.len = 8;
                        }

                        ext_row->values[i] = field;

                        pos += field_len;
                    }
                }

                // 转换为标准行数据格式
                postgre_row_data_t *row = new postgre_row_data_t();
                if (!row) {
                    GWLOG_ERROR(m_comm, "[Postgre][COPY] Failed to allocate row structure\n");
                    cleanup_row_data_extended(ext_row);
                    return PARSER_STATUS_DROP_DATA;
                }

                row->row = new b_string_t*[field_count];
                if (!row->row) {
                    GWLOG_ERROR(m_comm, "[Postgre][COPY] Failed to allocate row array\n");
                    delete row;
                    cleanup_row_data_extended(ext_row);
                    return PARSER_STATUS_DROP_DATA;
                }

                // 初始化所有指针为NULL并设置字段数量
                memset(row->row, 0, sizeof(b_string_t*) * field_count);
                row->field_count = field_count;

                // 转换扩展字段值到标准格式
                for (uint32_t i = 0; i < field_count; i++) {
                    if (ext_row->values[i] && !ext_row->values[i]->is_null) {
                        b_string_t *field = new b_string_t();
                        if (!field) {
                            GWLOG_ERROR(m_comm, "[Postgre][COPY] Failed to convert field\n");
                            cleanup_row_data(row, i);
                            cleanup_row_data_extended(ext_row);
                            return PARSER_STATUS_DROP_DATA;
                        }

                        // 复制文本表示
                        field->s = strdup(ext_row->values[i]->text_value.s);
                        if (!field->s) {
                            GWLOG_ERROR(m_comm, "[Postgre][COPY] Failed to copy field text\n");
                            delete field;
                            cleanup_row_data(row, i);
                            cleanup_row_data_extended(ext_row);
                            return PARSER_STATUS_DROP_DATA;
                        }

                        field->len = ext_row->values[i]->text_value.len;
                        row->row[i] = field;
                    } else {
                        row->row[i] = NULL; // NULL值
                    }
                }

                if (current_rs) {
                    // 创建单行数组
                    postgre_row_data_t **new_rows = new postgre_row_data_t*[1];
                    if (!new_rows) {
                        GWLOG_ERROR(m_comm, "[Postgre][COPY] Failed to allocate rows array\n");
                        cleanup_row_data(row, field_count);
                        cleanup_row_data_extended(ext_row);
                        return PARSER_STATUS_DROP_DATA;
                    }
                    new_rows[0] = row;
                    update_result_set(current_rs, new_rows, 1);
                } else {
                    // 没有结果集，直接释放行数据
                    cleanup_row_data(row, field_count);
                }

                // 清理扩展行数据
                cleanup_row_data_extended(ext_row);
                return PARSER_STATUS_FINISH;  // 成功处理二进制数据
            }
        }
        // 如果不是有效的二进制格式或解析失败，继续使用文本格式解析
        binary_fail = true;
        GWLOG_DEBUG(m_comm, "[Postgre][COPY] Invalid binary format, falling back to text format\n");
    }

    // 解析COPY数据（文本格式）
    // 文本格式处理 (如果二进制格式处理失败或者本来就是文本格式)
    if (copy_format == PG_FORMAT_TEXT || binary_fail) {
        // 验证copy_columns参数
        if (copy_columns == 0) {
            GWLOG_WARN(m_comm, "[PostgreSQL][COPY] copy_columns is 0, cannot process CSV data\n");
            return PARSER_STATUS_DROP_DATA;
        }

        GWLOG_DEBUG(m_comm, "[PostgreSQL][COPY] Processing text format data, columns=%d, is_header=%s\n",
                   copy_columns, is_current_header ? "true" : "false");

        // 查找行分隔符（通常是换行符）
        const char *line_start = copy_data;
        const char *line_end = NULL;

        while ((line_end = (const char *)memchr(line_start, '\n', copy_data_len - (line_start - copy_data))) != NULL) {
            // 提取行数据，去除回车符
            size_t line_len = line_end - line_start;
            if (line_len > 0 && line_start[line_len - 1] == '\r') {
                line_len--; // 去除Windows风格的\r\n中的\r
            }

            if (line_len > 0) {

                // 为行数据创建结果集行
                postgre_row_data_t *row = new postgre_row_data_t();
                if (row) {
                    row->row = new b_string_t*[copy_columns];
                    if (row->row) {
                        memset(row->row, 0, sizeof(b_string_t*) * copy_columns);
                        row->field_count = copy_columns;

                        // 解析CSV行中的列数据
                        const char *field_start = line_start;
                        const char *field_end = NULL;
                        int field_idx = 0;
                        const char *line_limit = line_start + line_len;

                        // CSV格式解析：优先使用逗号作为分隔符
                        while (field_idx < copy_columns && field_start < line_limit) {
                            // 查找下一个逗号分隔符
                            field_end = (const char *)memchr(field_start, ',', line_limit - field_start);

                            // 如果没有找到逗号，说明是最后一个字段
                            if (field_end == NULL) {
                                field_end = line_limit;
                            }

                            size_t field_len = field_end - field_start;

                            // 创建字段
                            b_string_t *field = new b_string_t();
                            if (field) {
                                char *field_data = (char *)malloc(field_len + 1);
                                if (field_data) {
                                    memcpy(field_data, field_start, field_len);
                                    field_data[field_len] = '\0';
                                    field->s = field_data;
                                    field->len = field_len;
                                    row->row[field_idx] = field;


                                } else {
                                    delete field;
                                    GWLOG_ERROR(m_comm, "[PostgreSQL][COPY] Failed to allocate field data\n");
                                }
                            }

                            // 移动到下一个字段
                            field_start = (field_end < line_limit) ? field_end + 1 : line_limit;
                            field_idx++;
                        }

                        // 如果解析的字段数少于预期，填充空字段
                        while (field_idx < copy_columns) {
                            b_string_t *field = new b_string_t();
                            if (field) {
                                field->s = strdup("");
                                field->len = 0;
                                row->row[field_idx] = field;
                            }
                            field_idx++;
                        }

                        // 如果是头部行，更新列名但不存储为数据行
                        if (is_current_header) {

                            column_def_t *col = current_rs->col_def;
                            for (int i = 0; i < copy_columns && col && row->row[i]; i++) {
                                if (row->row[i]->s && row->row[i]->len > 0) {
                                    // 释放原有列名
                                    if (col->name.s) {
                                        free((void*)col->name.s);
                                    }
                                    // 设置新列名
                                    col->name.s = strdup(row->row[i]->s);
                                    col->name.len = row->row[i]->len;
                                }
                                col = col->next;
                            }

                            // 释放头部行数据（不存储到结果集）
                            for (int i = 0; i < copy_columns; i++) {
                                if (row->row[i]) {
                                    if (row->row[i]->s) {
                                        free((void*)row->row[i]->s);
                                    }
                                    delete row->row[i];
                                }
                            }
                            delete[] row->row;
                            delete row;
                        } else {
                            // 数据行：存储到结果集
                            if (current_rs) {

                                // 创建单行数组
                                postgre_row_data_t **new_rows = new postgre_row_data_t*[1];
                                if (!new_rows) {
                                    GWLOG_ERROR(m_comm, "[PostgreSQL][COPY] Failed to allocate rows array\n");
                                    // 清理行数据
                                    for (int i = 0; i < copy_columns; i++) {
                                        if (row->row[i]) {
                                            if (row->row[i]->s) {
                                                free((void*)row->row[i]->s);
                                            }
                                            delete row->row[i];
                                        }
                                    }
                                    delete[] row->row;
                                    delete row;
                                    continue;
                                }
                                new_rows[0] = row;
                                update_result_set(current_rs, new_rows, 1);
                            } else {
                                // 没有结果集，直接释放行数据
                                for (int i = 0; i < copy_columns; i++) {
                                    if (row->row[i]) {
                                        if (row->row[i]->s) {
                                            free((void*)row->row[i]->s);
                                        }
                                        delete row->row[i];
                                    }
                                }
                                delete[] row->row;
                                delete row;
                            }
                        }
                    } else {
                        GWLOG_ERROR(m_comm, "[PostgreSQL][COPY] Failed to allocate row array\n");
                        delete row;
                    }
                } else {
                    GWLOG_ERROR(m_comm, "[PostgreSQL][COPY] Failed to allocate row structure\n");
                }
            }

            // 移动到下一行
            line_start = line_end + 1;
            if (line_start >= copy_data + copy_data_len) {
                break;
            }
        }

        // 处理最后一行（如果没有换行符结尾）
        if (line_start < copy_data + copy_data_len) {
            size_t line_len = copy_data + copy_data_len - line_start;
            if (line_len > 0) {
                GWLOG_DEBUG(m_comm, "[PostgreSQL][COPY] Processing final line without newline: %.*s\n",
                           (int)line_len, line_start);

                // 去除可能的回车符
                if (line_len > 0 && line_start[line_len - 1] == '\r') {
                    line_len--;
                }

                if (line_len > 0) {
                    // 为最后一行创建行数据（复用上面的逻辑）
                    postgre_row_data_t *row = new postgre_row_data_t();
                    if (row) {
                        row->row = new b_string_t*[copy_columns];
                        if (row->row) {
                            memset(row->row, 0, sizeof(b_string_t*) * copy_columns);
                            row->field_count = copy_columns;

                            // 简化的CSV解析（最后一行通常不是头部行）
                            const char *field_start = line_start;
                            const char *field_end = NULL;
                            int field_idx = 0;
                            const char *line_limit = line_start + line_len;

                            while (field_idx < copy_columns && field_start < line_limit) {
                                field_end = (const char *)memchr(field_start, ',', line_limit - field_start);
                                if (field_end == NULL) {
                                    field_end = line_limit;
                                }

                                size_t field_len = field_end - field_start;
                                b_string_t *field = new b_string_t();
                                if (field) {
                                    char *field_data = (char *)malloc(field_len + 1);
                                    if (field_data) {
                                        memcpy(field_data, field_start, field_len);
                                        field_data[field_len] = '\0';
                                        field->s = field_data;
                                        field->len = field_len;
                                        row->row[field_idx] = field;
                                    } else {
                                        delete field;
                                    }
                                }

                                field_start = (field_end < line_limit) ? field_end + 1 : line_limit;
                                field_idx++;
                            }

                            // 存储最后一行数据
                            if (current_rs) {
                                postgre_row_data_t **new_rows = new postgre_row_data_t*[1];
                                if (new_rows) {
                                    new_rows[0] = row;
                                    update_result_set(current_rs, new_rows, 1);
                                    GWLOG_DEBUG(m_comm, "[PostgreSQL][COPY] Final line stored\n");
                                } else {
                                    // 清理行数据
                                    for (int i = 0; i < copy_columns; i++) {
                                        if (row->row[i] && row->row[i]->s) {
                                            free((void*)row->row[i]->s);
                                            delete row->row[i];
                                        }
                                    }
                                    delete[] row->row;
                                    delete row;
                                }
                            } else {
                                // 清理行数据
                                for (int i = 0; i < copy_columns; i++) {
                                    if (row->row[i] && row->row[i]->s) {
                                        free((void*)row->row[i]->s);
                                        delete row->row[i];
                                    }
                                }
                                delete[] row->row;
                                delete row;
                            }
                        } else {
                            delete row;
                        }
                    }
                }
            }
        }
    }

    return PARSER_STATUS_FINISH;
}

// ==================== COPY上下文管理函数实现 ====================

/**
 * 确保COPY上下文存在（按需创建）
 */
bool CPostgreParser::ensure_copy_context(postgre_stream_t *pgs)
{
    if (!pgs) {
        return false;
    }

    if (!pgs->copy_context) {
        pgs->copy_context = new copy_context_t();
        if (!pgs->copy_context) {
            GWLOG_ERROR(m_comm, "[PostgreSQL] Failed to allocate COPY context\n");
            return false;
        }
        // 初始化为默认值
        memset(pgs->copy_context, 0, sizeof(copy_context_t));
        pgs->copy_context->state = COPY_STATE_NONE;
        pgs->copy_context->has_header = false;
        pgs->copy_context->header_processed = false;
        pgs->copy_context->is_replication_mode = false;

        GWLOG_DEBUG(m_comm, "[PostgreSQL] COPY context created\n");
    }

    return true;
}

/**
 * 设置COPY错误信息
 */
void CPostgreParser::set_copy_error(postgre_stream_t *pgs, const char *error_msg)
{
    if (!pgs || !error_msg) {
        return;
    }

    // 存储到响应端节点
    postgre_half_stream_t *phs = pgs->p_postgre_server;
    if (phs) {
        if (phs->data.err_msg) {
            free(phs->data.err_msg);
        }
        phs->data.err_msg = strdup(error_msg);

        // 设置COPY状态为失败
        if (ensure_copy_context(pgs)) {
            pgs->copy_context->state = COPY_STATE_FAILED;
        }
    }
}

/**
 * 清理COPY上下文
 */
void CPostgreParser::cleanup_copy_context(postgre_stream_t *pgs)
{
    if (pgs && pgs->copy_context) {
        delete pgs->copy_context;
        pgs->copy_context = NULL;
        GWLOG_DEBUG(m_comm, "[PostgreSQL] COPY context cleaned up\n");
    }
}

// ==================== 丢包处理相关函数实现 ====================
/**
 * 处理不完整的Data Row消息（TCP分段丢包场景）
 * @param pgs PostgreSQL流状态
 * @param msg_data 消息数据（已跳过消息头5字节）
 * @param available_msg_len 可用的消息数据长度
 * @param hlf TCP半流信息
 * @return 解析状态
 */
int CPostgreParser::process_incomplete_data_row(postgre_stream_t *pgs, const char *msg_data,
                                               int available_msg_len,
                                               struct half_stream *hlf)
{
    if (!pgs || !msg_data || !hlf) {
        GWLOG_ERROR(m_comm, "[PostgreSQL][PacketLoss][DataRow] Invalid parameters\n");
        return PARSER_STATUS_DROP_DATA;
    }

    GWLOG_WARN(m_comm, "[PostgreSQL][PacketLoss][DataRow] Processing incomplete Data Row: "
               "available_msg_len=%d, lost_bytes=%d, first_lost_offset=%d\n",
               available_msg_len, hlf->lost_len, hlf->first_lost_offset);

    // 检查是否有足够数据解析字段数量（需要2字节）
    if (available_msg_len < 2) {
        GWLOG_WARN(m_comm, "[PostgreSQL][PacketLoss][DataRow] Insufficient data for field count\n");
        return create_packet_loss_placeholder_data_row(pgs, hlf);
    }

    // 解析字段数量（msg_data已经跳过了消息头5字节）
    uint16_t num_fields = ntohs(*((uint16_t*)msg_data));

    GWLOG_INFO(m_comm, "[PostgreSQL][PacketLoss][DataRow] Attempting to parse %d fields with %d bytes available\n",
               num_fields, available_msg_len);

    // 获取当前结果集
    postgre_half_stream_t *phs = pgs->p_postgre_server;
    if (!phs) {
        GWLOG_ERROR(m_comm, "[PostgreSQL][PacketLoss][DataRow] No server stream available\n");
        return PARSER_STATUS_DROP_DATA;
    }

    result_set_t *current_rs = NULL;
    if (phs->data.rs_list) {
        current_rs = phs->data.rs_list;
        while (current_rs->next) {
            current_rs = current_rs->next;
        }
    }

    // 验证字段数量
    if (!validate_field_count(pgs, current_rs, num_fields, "DataRow[PacketLoss]")) {
        return create_packet_loss_placeholder_data_row(pgs, hlf);
    }

    // 如果没有结果集但有字段数据，创建临时结果集用于动态列定义创建
    if (!current_rs && num_fields > 0) {
        GWLOG_WARN(m_comm, "[PostgreSQL][PacketLoss][DataRow] No result set for %d fields, "
                   "creating temporary result set for dynamic column creation\n", num_fields);
        // 创建临时结果集，设置正确的列数以便后续Data Row能够通过验证
        add_result_set(&phs->data, NULL, num_fields, &current_rs);
        // 更新pg_stat.num_fields以确保后续Data Row验证一致
        pgs->pg_stat.num_fields = num_fields;
    }

    // 创建行数据结构
    postgre_row_data_t *row = new postgre_row_data_t();
    if (!row) {
        GWLOG_ERROR(m_comm, "[PostgreSQL][PacketLoss][DataRow] Failed to allocate row structure\n");
        return PARSER_STATUS_DROP_DATA;
    }

    row->row = new b_string_t*[num_fields];
    if (!row->row) {
        GWLOG_ERROR(m_comm, "[PostgreSQL][PacketLoss][DataRow] Failed to allocate row array\n");
        delete row;
        return PARSER_STATUS_DROP_DATA;
    }

    // 初始化所有指针为NULL
    memset(row->row, 0, sizeof(b_string_t*) * num_fields);
    row->field_count = num_fields;

    // 逐字段安全解析（从字段数量之后开始，跳过2字节的字段数量）
    const char *field_data = msg_data + 2;
    int parsed_fields = 0;
    bool encountered_packet_loss = false;
    int remaining_data = available_msg_len - 2; // 减去字段数量占用的2字节

    for (int i = 0; i < num_fields; i++) {
        // 检查字段长度信息是否有足够的剩余数据
        if (remaining_data < 4) {
            GWLOG_WARN(m_comm, "[PostgreSQL][PacketLoss][DataRow] Field %d length info insufficient data\n", i);
            encountered_packet_loss = true;
            break;
        }

        int32_t field_len = ntohl(*((int32_t*)field_data));
        field_data += 4;
        remaining_data -= 4;

        if (field_len == -1) {
            // NULL值
            row->row[i] = NULL;
            parsed_fields++;
        } else if (field_len >= 0) {
            // 检查字段数据是否有足够的剩余数据
            if (field_len > remaining_data) {
                // 字段数据不完整，尝试部分解析
                if (remaining_data > 0) {
                    GWLOG_WARN(m_comm, "[PostgreSQL][PacketLoss][DataRow] Field %d partially available: "
                               "expected=%d, available=%d\n", i, field_len, remaining_data);

                    // 创建包含部分数据和丢包标记的字段
                    add_packet_loss_marker_to_field(&row->row[i], field_data, remaining_data, hlf);
                    parsed_fields++;
                } else {
                    GWLOG_WARN(m_comm, "[PostgreSQL][PacketLoss][DataRow] Field %d completely missing\n", i);
                }

                encountered_packet_loss = true;
                break;
            }

            // 正常解析字段
            b_string_t *field = new b_string_t();
            if (!field) {
                GWLOG_ERROR(m_comm, "[PostgreSQL][PacketLoss][DataRow] Failed to allocate field %d\n", i);
                cleanup_row_data(row, parsed_fields);
                return PARSER_STATUS_DROP_DATA;
            }

            char *field_copy = (char *)malloc(field_len + 1);
            if (!field_copy) {
                GWLOG_ERROR(m_comm, "[PostgreSQL][PacketLoss][DataRow] Failed to allocate field data %d\n", i);
                delete field;
                cleanup_row_data(row, parsed_fields);
                return PARSER_STATUS_DROP_DATA;
            }

            memcpy(field_copy, field_data, field_len);
            field_copy[field_len] = '\0';
            field->s = field_copy;
            field->len = field_len;
            row->row[i] = field;

            field_data += field_len;
            remaining_data -= field_len;
            parsed_fields++;
        } else {
            GWLOG_WARN(m_comm, "[PostgreSQL][PacketLoss][DataRow] Invalid field length %d for field %d\n",
                       field_len, i);
            encountered_packet_loss = true;
            break;
        }
    }

    GWLOG_INFO(m_comm, "[PostgreSQL][PacketLoss][DataRow] Successfully parsed %d/%d fields\n",
               parsed_fields, num_fields);

    // 添加到结果集
    if (!current_rs) {
        // 没有结果集，创建新的
        add_result_set(&phs->data, NULL, 0, &current_rs);
    }

    if (current_rs) {
        postgre_row_data_t **new_rows = new postgre_row_data_t*[1];
        if (!new_rows) {
            GWLOG_ERROR(m_comm, "[PostgreSQL][PacketLoss][DataRow] Failed to allocate rows array\n");
            cleanup_row_data(row, num_fields);
            return PARSER_STATUS_DROP_DATA;
        }
        new_rows[0] = row;
        update_result_set(current_rs, new_rows, 1);
    } else {
        cleanup_row_data(row, num_fields);
        return PARSER_STATUS_DROP_DATA;
    }

    // 处理完成后立即跳出解析循环，避免访问丢包区域
    return PARSER_STATUS_FINISH;
}

/**
 * 为字段添加丢包标记
 * @param field 字段指针的指针
 * @param original_data 原始字段数据
 * @param available_len 可用数据长度
 * @param hlf TCP半流信息
 */
void CPostgreParser::add_packet_loss_marker_to_field(b_string_t **field, const char *original_data,
                                                    size_t available_len, struct half_stream *hlf)
{
    if (!field || !hlf) return;

    *field = new b_string_t();
    if (!*field) return;

    // 计算需要的缓冲区大小：原始数据 + 丢包标记
    size_t marker_len = 64; // 为丢包标记预留空间
    size_t total_len = available_len + marker_len;

    char *field_with_marker = (char *)malloc(total_len);
    if (!field_with_marker) {
        delete *field;
        *field = NULL;
        return;
    }

    // 复制可用的原始数据
    if (available_len > 0 && original_data) {
        memcpy(field_with_marker, original_data, available_len);
    }

    // 添加丢包标记
    int marker_written = snprintf(field_with_marker + available_len, marker_len,
                                 "[%d bytes missing]", hlf->lost_len);
    if (marker_written < 0 || marker_written >= (int)marker_len) {
        marker_written = 0;
    }

    size_t final_len = available_len + marker_written;
    field_with_marker[final_len] = '\0';

    (*field)->s = field_with_marker;
    (*field)->len = final_len;
}

/**
 * 创建Data Row丢包占位符
 * @param pgs PostgreSQL流状态
 * @param hlf TCP半流信息
 * @return 解析状态
 */
int CPostgreParser::create_packet_loss_placeholder_data_row(postgre_stream_t *pgs,
                                                           struct half_stream *hlf)
{
    if (!pgs || !hlf) {
        return PARSER_STATUS_DROP_DATA;
    }

    GWLOG_WARN(m_comm, "[PostgreSQL][PacketLoss][DataRow] Creating placeholder for completely lost Data Row\n");

    postgre_half_stream_t *phs = pgs->p_postgre_server;
    if (!phs) {
        return PARSER_STATUS_DROP_DATA;
    }

    // 创建单列结果集用于显示丢包信息
    column_def_t *col = new column_def_t();
    if (!col) {
        return PARSER_STATUS_DROP_DATA;
    }

    memset(col, 0, sizeof(column_def_t));
    char *name_copy = strdup("packet_loss_info");
    if (!name_copy) {
        delete col;
        return PARSER_STATUS_DROP_DATA;
    }

    col->name.s = name_copy;
    col->name.len = strlen(name_copy);
    col->next = NULL;

    result_set_t *current_rs = NULL;
    add_result_set(&phs->data, col, 1, &current_rs);

    // 创建包含丢包信息的行
    postgre_row_data_t *row = new postgre_row_data_t();
    if (!row) {
        return PARSER_STATUS_DROP_DATA;
    }

    row->row = new b_string_t*[1];
    if (!row->row) {
        delete row;
        return PARSER_STATUS_DROP_DATA;
    }

    row->field_count = 1;
    row->row[0] = new b_string_t();
    if (!row->row[0]) {
        delete[] row->row;
        delete row;
        return PARSER_STATUS_DROP_DATA;
    }

    // 创建丢包信息字符串
    char loss_info[256];
    snprintf(loss_info, sizeof(loss_info),
             "[DATA_ROW_INCOMPLETE: %d bytes missing at offset %d]",
             hlf->lost_len, hlf->first_lost_offset);

    row->row[0]->s = strdup(loss_info);
    row->row[0]->len = strlen(loss_info);

    if (current_rs) {
        postgre_row_data_t **new_rows = new postgre_row_data_t*[1];
        if (!new_rows) {
            cleanup_row_data(row, 1);
            return PARSER_STATUS_DROP_DATA;
        }
        new_rows[0] = row;
        update_result_set(current_rs, new_rows, 1);
    } else {
        cleanup_row_data(row, 1);
        return PARSER_STATUS_DROP_DATA;
    }

    return PARSER_STATUS_FINISH;
}

/**
 * 处理不完整的Row Description消息（TCP分段丢包场景）
 * @param pgs PostgreSQL流状态
 * @param msg_data 消息数据（已跳过消息头5字节）
 * @param available_msg_len 可用的消息数据长度
 * @param hlf TCP半流信息
 * @return 解析状态
 */
int CPostgreParser::process_incomplete_row_description(postgre_stream_t *pgs,
                                                      const char *msg_data,
                                                      int available_msg_len,
                                                      struct half_stream *hlf)
{
    if (!pgs || !msg_data || !hlf) {
        GWLOG_ERROR(m_comm, "[PostgreSQL][PacketLoss][RowDescription] Invalid parameters\n");
        return PARSER_STATUS_DROP_DATA;
    }

    // field count字段必然存在（前提条件保证）
    if (available_msg_len < 2) {
        GWLOG_ERROR(m_comm, "[PostgreSQL][PacketLoss][RowDescription] Impossible: field count missing\n");
        return PARSER_STATUS_DROP_DATA;
    }

    // 解析字段数量（必然可用）
    uint16_t num_fields = ntohs(*((uint16_t*)msg_data));

    GWLOG_WARN(m_comm, "[PostgreSQL][PacketLoss][RowDescription] Processing incomplete Row Description: "
               "num_fields=%d, available_msg_len=%d, lost_bytes=%d\n",
               num_fields, available_msg_len, hlf->lost_len);

    // 尝试解析尽可能多的完整列定义
    const char *field_data = msg_data + 2;
    int remaining_data = available_msg_len - 2;
    int parsed_columns = 0;

    column_def_t *col_head = NULL;
    column_def_t *col_tail = NULL;

    // 逐列安全解析
    for (int i = 0; i < num_fields && remaining_data > 0; i++) {
        column_def_t *col = NULL;
        int consumed_bytes = 0;

        // 尝试解析单个列定义
        if (try_parse_single_column(field_data, remaining_data, &col, &consumed_bytes)) {
            // 解析成功，添加到链表
            if (col_tail) {
                col_tail->next = col;
                col_tail = col;
            } else {
                col_head = col_tail = col;
            }

            field_data += consumed_bytes;
            remaining_data -= consumed_bytes;
            parsed_columns++;
        } else {
            // 解析失败，说明遇到了丢包边界
            GWLOG_WARN(m_comm, "[PostgreSQL][PacketLoss][RowDescription] Column %d parsing failed, "
                       "data truncated due to packet loss\n", i);
            break;
        }
    }

    // 创建结果集（仅使用成功解析的列）
    postgre_half_stream_t *phs = pgs->p_postgre_server;
    if (phs && parsed_columns > 0) {
        result_set_t *current_rs = NULL;
        add_result_set(&phs->data, col_head, parsed_columns, &current_rs);

        GWLOG_INFO(m_comm, "[PostgreSQL][PacketLoss][RowDescription] Created result set with %d parsed columns "
                   "(original expected: %d columns)\n", parsed_columns, num_fields);
    } else {
        // 清理列定义，防止内存泄漏
        cleanup_column_definitions(col_head);

        if (parsed_columns == 0) {
            GWLOG_WARN(m_comm, "[PostgreSQL][PacketLoss][RowDescription] No columns could be parsed, "
                       "dropping incomplete Row Description\n");
        }
        return PARSER_STATUS_DROP_DATA;
    }

    return PARSER_STATUS_FINISH;
}

/**
 * 尝试解析单个列定义
 * @param data 数据指针
 * @param available_len 可用数据长度
 * @param col 输出的列定义指针
 * @param consumed_bytes 消耗的字节数
 * @return 是否解析成功
 */
bool CPostgreParser::try_parse_single_column(const char *data, int available_len,
                                            column_def_t **col, int *consumed_bytes)
{
    *col = NULL;
    *consumed_bytes = 0;

    if (available_len < 1) return false; // 至少需要1字节用于列名

    // 1. 解析列名（null结尾字符串）
    const char *name_end = (const char *)memchr(data, '\0', available_len);
    if (!name_end) {
        // 列名不完整
        return false;
    }

    size_t name_len = name_end - data;
    int name_total_len = name_len + 1; // 包括null终止符

    // 2. 检查固定字段是否完整（18字节：table_oid(4) + column_id(2) + type_oid(4) + type_len(2) + type_mod(4) + format_code(2)）
    if (available_len < name_total_len + 18) {
        // 固定字段不完整
        return false;
    }

    // 3. 创建列定义
    *col = new column_def_t();
    if (!*col) return false;

    memset(*col, 0, sizeof(column_def_t));

    // 4. 复制列名
    (*col)->name.s = (char *)malloc(name_len + 1);
    if (!(*col)->name.s) {
        delete *col;
        *col = NULL;
        return false;
    }

    memcpy((*col)->name.s, data, name_len);
    (*col)->name.s[name_len] = '\0';
    (*col)->name.len = name_len;

    // 5. 解析固定字段
    const char *fixed_data = data + name_total_len;
    (*col)->table_oid = ntohl(*((uint32_t*)fixed_data));
    (*col)->column_id = ntohs(*((uint16_t*)(fixed_data + 4)));
    (*col)->type_oid = ntohl(*((uint32_t*)(fixed_data + 6)));
    (*col)->type_len = ntohs(*((uint16_t*)(fixed_data + 10)));
    (*col)->type_mod = ntohl(*((uint32_t*)(fixed_data + 12)));
    (*col)->format_code = ntohs(*((uint16_t*)(fixed_data + 16)));
    (*col)->next = NULL;

    *consumed_bytes = name_total_len + 18;
    return true;
}

/**
 * 统一的丢包检测函数
 * 检测消息是否受到丢包影响，支持TCP标记丢包和最后分段丢失的双重检测
 * @param hlf TCP半流信息
 * @param msg_len 消息长度（不包含消息类型字节）
 * @param available_len 可用数据长度（包含消息类型字节）
 * @param lost_bytes 输出参数：丢失的字节数
 * @param current_offset 当前消息在数据流中的偏移位置
 * @return true表示有丢包
 */
bool CPostgreParser::detect_packet_loss(struct half_stream *hlf, uint32_t msg_len,
                                       int available_len, int *lost_bytes, int current_offset)
{
    if (!lost_bytes) {
        return false;
    }

    // 快速路径：如果没有TCP层丢包标记且数据完整，直接返回
    if (!hlf || (hlf->complete != 0 && msg_len + 1 <= (uint32_t)available_len)) {
        *lost_bytes = 0;
        return false;
    }

    // 当前消息的完整长度（包含消息类型字节）
    uint32_t current_msg_total_len = msg_len + 1;

    // 检查TCP层标记的丢包（中间段丢失）
    bool has_tcp_loss = (hlf && hlf->complete == 0);

    // 如果有TCP层丢包，需要判断丢包是否影响当前消息
    if (has_tcp_loss && hlf->first_lost_offset > 0) {
        // 计算当前消息的结束位置
        int current_msg_end_offset = current_offset + current_msg_total_len;

        // 如果第一次丢包位置在当前消息范围内，则当前消息受影响
        if (hlf->first_lost_offset < current_msg_end_offset) {
            *lost_bytes = hlf->lost_len;
            return true;
        }

        // 如果第一次丢包位置在当前消息之后，当前消息完整
        // 但需要检查当前消息是否有足够的数据
        if (current_msg_total_len > (uint32_t)available_len) {
            // 当前消息数据不足，可能是尾部丢失
            *lost_bytes = current_msg_total_len - available_len;
            return true;
        }

        // 当前消息完整，丢包在后续消息中
        *lost_bytes = 0;
        return false;
    }

    // 检查最后分段丢失（消息长度超过可用数据）
    bool has_tail_loss = (current_msg_total_len > (uint32_t)available_len);

    if (has_tcp_loss && hlf->first_lost_offset == 0) {
        // TCP层标记有丢包但没有first_lost_offset，通常是最后分段丢失
        *lost_bytes = hlf->lost_len;
        return true;
    } else if (has_tail_loss) {
        *lost_bytes = current_msg_total_len - available_len;
        return true;
    }

    *lost_bytes = 0;
    return false;
}

/**
 * 检查并添加指定的prepared statement到请求链表
 * 当Parse消息完整解析但后续Bind消息丢包时，prepared statement已创建但未添加到请求链表
 * 此函数根据statement名称精确匹配并添加对应的SQL语句到请求链表中
 * @param pgs PostgreSQL流状态
 * @param stmt_name statement名称，NULL或空字符串表示unnamed statement
 */
void CPostgreParser::check_and_add_specific_prepared_statement(postgre_stream_t *pgs, const char *stmt_name)
{
    if (!pgs) return;

    postgre_half_stream_t *phs = pgs->p_postgre_client;
    if (!phs) return;

    // 检查请求链表是否为空，如果不为空则无需处理
    if (phs->data.sql_count > 0) return;

    prepared_statement_t *stmt = NULL;

    if (!stmt_name || strlen(stmt_name) == 0) {
        // 查找unnamed statement
        stmt = pgs->prepared_statements;
        while (stmt) {
            if (stmt->is_unnamed) break;
            stmt = stmt->next;
        }
        GWLOG_INFO(m_comm, "[PostgreSQL][SpecificStmt] Looking for unnamed statement\n");
    } else {
        // 查找指定名称的statement
        stmt = find_prepared_statement(pgs, stmt_name);
        GWLOG_INFO(m_comm, "[PostgreSQL][SpecificStmt] Looking for named statement: '%s'\n", stmt_name);
    }

    // 直接添加匹配的statement，无需时间窗口验证
    if (stmt && stmt->sql_list && stmt->sql_list->sql.s && stmt->sql_list->sql.len > 0 &&
        stmt->state == STMT_STATE_PREPARED) {

        add_sql_statement(&phs->data, stmt->sql_list->sql.s, stmt->sql_list->sql.len,
                        stmt->sql_list->is_incomplete, stmt->sql_list->lost_bytes);

        GWLOG_INFO(m_comm, "[PostgreSQL][SpecificStmt] Successfully added prepared statement to request list: "
                   "name='%s', sql_len=%zu, is_incomplete=%d\n",
                   stmt->name ? stmt->name : "(unnamed)",
                   stmt->sql_list->sql.len, stmt->sql_list->is_incomplete);
    } else {
        GWLOG_WARN(m_comm, "[PostgreSQL][SpecificStmt] No matching prepared statement found or invalid state: "
                   "stmt=%p, stmt_name='%s'\n", stmt, stmt_name ? stmt_name : "(unnamed)");
    }
}

