syntax="proto3";
package com.quanzhi.audit_core.common.model;
option java_outer_classname = "ProtobufRawPostgreEvent$$ByJProtobuf";

// PostgreSQL协议事件主消息
message ProtobufRawPostgreEvent {
    PostgreRequest req = 1;
    PostgreResponse rsp = 2;
    Meta meta = 3;
    Net net = 4;
    UniqueId uniqueId = 5;
    Source source = 6;
    Mac mac = 7;
}

// 元数据信息
message Meta {
    uint64 tm = 1;                  // 时间戳(毫秒级)
    string type = 2;                // 数据库类型，当前解析器为PostgreSQL
    string app_name = 3;            // 应用名称
    string server_version = 4;      // 服务器版本
}

// 唯一标识
message UniqueId {
    string eventId = 1;
}

// PostgreSQL请求信息
message PostgreRequest {
    string db_user = 1;             // 数据库用户
    string db_name = 2;             // 数据库名
    string db_password = 3;         // 数据库密码
    string cmd_type = 4;            // 操作类型
    string sql = 5;                 // 原始SQL文本
    int32 errCode = 6;              // 错误码
}

// PostgreSQL响应信息
message PostgreResponse {
    int32 status = 1;               // 状态码
    uint64 start_time = 2;          // 开始时间(毫秒级)
    uint64 close_time = 3;          // 结束时间(毫秒级)
    int32 row_count = 4;            // 影响行数
    ResultSet result_set = 5;       // 结果集
    int32 errCode = 6;              // 错误码
}

// 结果集信息
message ResultSet {
    repeated string field_names = 1;    // 字段名数组
    repeated ResultRow rows = 2;        // 行数据数组
    int32 field_count = 3;              // 字段数量
}

// 结果行数据
message ResultRow {
    repeated string field_values = 1;   // 字段值数组
}

// 网络信息
message Net {
    string srcIp = 1;               // 源IP
    int32 srcPort = 2;              // 源端口
    string dstIp = 3;               // 目标IP
    int32 dstPort = 4;              // 目标端口
    string flowSource = 5;          // 流量源
}

// MAC地址信息
message Mac {
    string mac = 1;                 // MAC地址
}

// 数据源信息
message Source {
    SourceTypeEnum sourceType = 1;  // 数据源类型
    string taskId = 2;              // 任务ID
    string app = 3;                 // 应用名称
}

// 数据源类型枚举
enum SourceTypeEnum {
    app_har = 0;
    flow = 1;
}
