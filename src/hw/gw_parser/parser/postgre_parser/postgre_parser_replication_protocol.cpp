/**
 * PostgreSQL流复制协议解析器实现（简化版）
 * 统一处理所有流复制协议，使用base64编码存储COPY DATA消息
 */

#include "postgre_parser.h"
#include "gw_common.h"
#include "gw_logger.h"
#include "session_mgt.h"
#include "session.h"
#include "utils.h"

#include <string.h>
#include <stdlib.h>
#include <arpa/inet.h>
#include <ctype.h>
#include <time.h>

// 流复制数据列定义
#define REPLICATION_COLUMN_NAME "content"
#define REPLICATION_COLUMN_TYPE PG_TYPE_TEXT
#define REPLICATION_COLUMN_FORMAT PG_FORMAT_TEXT

// Base64编码后长度计算宏（原始长度的4/3倍，向上取整，加上padding）
#define BASE64_ENCODED_LEN(len) (((len) + 2) / 3 * 4 + 1)

// 流复制命令检测
bool CPostgreParser::is_replication_command(const char *data, int len) {
    if (len < 5) return false;

    // 检查是否为Query消息
    if (data[0] != POSTGRE_MSG_QUERY) return false;

    uint32_t msg_len = GET_MSG_LEN(data + 1);
    if (msg_len > (uint32_t)(len - 1)) return false;

    const char *query = data + 5;
    size_t query_len = msg_len - 4;

    // 检查常见的流复制命令
    const char *replication_commands[] = {
        "IDENTIFY_SYSTEM",
        "CREATE_REPLICATION_SLOT",
        "START_REPLICATION",
        "BASE_BACKUP",
        "TIMELINE_HISTORY"
    };

    for (size_t i = 0; i < sizeof(replication_commands) / sizeof(replication_commands[0]); i++) {
        size_t cmd_len = strlen(replication_commands[i]);
        if (query_len >= cmd_len &&
            strncasecmp(query, replication_commands[i], cmd_len) == 0) {
            return true;
        }
    }

    return false;
}

/**
 * 创建流复制数据结果集（仅创建结构，不包含行数据）
 * @return 创建的结果集对象，失败返回NULL
 */
result_set_t* CPostgreParser::create_replication_result_set() {
    result_set_t *rs = new result_set_t();
    if (!rs) {
        return NULL;
    }

    memset(rs, 0, sizeof(result_set_t));

    // 创建单列定义：content
    column_def_t *col = new column_def_t();
    if (!col) {
        delete rs;
        return NULL;
    }

    memset(col, 0, sizeof(column_def_t));
    col->name.s = strdup(REPLICATION_COLUMN_NAME);
    col->name.len = strlen(REPLICATION_COLUMN_NAME);
    col->type_oid = REPLICATION_COLUMN_TYPE;
    col->format_code = REPLICATION_COLUMN_FORMAT;
    col->next = NULL;

    rs->col_def = col;
    rs->col_cnt = 1;
    rs->rows = NULL;
    rs->row_cnt = 0;
    rs->row_capacity = 0;

    return rs;
}

/**
 * 向流复制结果集添加新行
 * @param rs 结果集对象
 * @param base64_content base64编码的内容
 * @return 成功返回true，失败返回false
 */
bool CPostgreParser::add_row_to_replication_result_set(result_set_t *rs, const char *base64_content) {
    if (!rs || !base64_content) {
        return false;
    }

    // 创建新行数据
    postgre_row_data_t *new_row = new postgre_row_data_t();
    if (!new_row) {
        return false;
    }

    memset(new_row, 0, sizeof(postgre_row_data_t));
    new_row->row = new b_string_t*[1];
    new_row->field_count = 1;

    new_row->row[0] = new b_string_t();
    new_row->row[0]->s = strdup(base64_content);
    new_row->row[0]->len = strlen(base64_content);

    // 扩展行数组
    if (rs->row_cnt >= rs->row_capacity) {
        // 增加容量（初始为4，之后每次翻倍）
        int new_capacity = rs->row_capacity == 0 ? 4 : rs->row_capacity * 2;
        postgre_row_data_t **new_rows = new postgre_row_data_t*[new_capacity];
        if (!new_rows) {
            // 清理新创建的行
            free(new_row->row[0]->s);
            delete new_row->row[0];
            delete[] new_row->row;
            delete new_row;
            return false;
        }

        // 复制现有行指针
        for (int i = 0; i < rs->row_cnt; i++) {
            new_rows[i] = rs->rows[i];
        }

        // 释放旧数组，更新指针
        delete[] rs->rows;
        rs->rows = new_rows;
        rs->row_capacity = new_capacity;
    }

    // 添加新行
    rs->rows[rs->row_cnt] = new_row;
    rs->row_cnt++;

    return true;
}

/**
 * 查找或创建流复制结果集
 * @param server_stream 服务端流节点
 * @return 流复制结果集，失败返回NULL
 */
result_set_t* CPostgreParser::find_or_create_replication_result_set(postgre_half_stream_t *server_stream) {
    if (!server_stream) {
        return NULL;
    }

    // 查找现有的流复制结果集（检查第一个结果集是否为流复制结果集）
    if (server_stream->data.rs_list &&
        server_stream->data.rs_list->col_def &&
        server_stream->data.rs_list->col_def->name.s &&
        strcmp(server_stream->data.rs_list->col_def->name.s, REPLICATION_COLUMN_NAME) == 0) {
        return server_stream->data.rs_list;
    }

    // 创建新的流复制结果集
    result_set_t *rs = create_replication_result_set();
    if (!rs) {
        return NULL;
    }

    // 将新结果集添加到链表头部
    rs->next = server_stream->data.rs_list;
    server_stream->data.rs_list = rs;
    server_stream->data.rs_count++;

    return rs;
}

/**
 * 处理流复制模式下的COPY DATA消息
 * @param pgs PostgreSQL流对象
 * @param data COPY DATA消息内容
 * @param len 消息长度
 */
void CPostgreParser::process_replication_copy_data(postgre_stream_t *pgs, const char *data, int len) {
    if (!pgs || !data || len <= 0) {
        return;
    }

    // 计算base64编码后的长度并分配缓冲区
    size_t encoded_len = BASE64_ENCODED_LEN(len);
    unsigned char *base64_buffer = new unsigned char[encoded_len];
    if (!base64_buffer) {
        GWLOG_ERROR(m_comm, "[PostgreSQL][Replication] Failed to allocate base64 buffer\n");
        return;
    }

    // 使用现有的base64_encode函数进行编码
    base64_encode(base64_buffer, (const unsigned char*)data, len);

    // 获取或创建响应端流节点
    postgre_half_stream_t *server_stream = pgs->p_postgre_server;
    if (!server_stream) {
        // 创建新的服务端流节点
        server_stream = new postgre_half_stream_t();
        if (!server_stream) {
            delete[] base64_buffer;
            GWLOG_ERROR(m_comm, "[PostgreSQL][Replication] Failed to create server stream\n");
            return;
        }

        memset(server_stream, 0, sizeof(postgre_half_stream_t));
        server_stream->data.start_time = get_time_ts_ms(NULL, m_conf_pcap_timestamp);
        server_stream->data.pcap_ts = server_stream->data.start_time;

        // 插入到服务端链表
        insert_into_parser_header(pgs, PGSQL_RESPONSE, server_stream);
    }

    // 查找或创建流复制结果集
    result_set_t *rs = find_or_create_replication_result_set(server_stream);
    if (!rs) {
        delete[] base64_buffer;
        GWLOG_ERROR(m_comm, "[PostgreSQL][Replication] Failed to find or create result set\n");
        return;
    }

    // 向结果集添加新行
    if (!add_row_to_replication_result_set(rs, (const char*)base64_buffer)) {
        delete[] base64_buffer;
        GWLOG_ERROR(m_comm, "[PostgreSQL][Replication] Failed to add row to result set\n");
        return;
    }

    delete[] base64_buffer;

    GWLOG_DEBUG(m_comm, "[PostgreSQL][Replication] Processed COPY DATA: %d bytes -> base64 encoded (total rows: %d)\n",
                len, rs->row_cnt);
}
