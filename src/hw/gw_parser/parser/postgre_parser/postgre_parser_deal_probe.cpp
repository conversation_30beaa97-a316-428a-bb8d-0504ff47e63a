#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <arpa/inet.h>

#include "postgre_parser.h"
#include "gw_common.h"
#include "gw_logger.h"
#include "session_mgt.h"
#include "session.h"
#include "utils.h"

bool CPostgreParser::is_valid_startup_msg(const char *data, int len)
{
    if (len < POSTGRE_STARTUP_MIN_LEN)
    {
        return false;
    }

    // 检查消息长度
    uint32_t msg_len = GET_MSG_LEN(data);
    if (msg_len < POSTGRE_STARTUP_MIN_LEN || msg_len > (uint32_t)len)
    {
        return false;
    }

    // 检查是否是SSLRequest消息
    if (msg_len == POSTGRE_SSL_REQUEST_LEN && GET_MSG_LEN(data + 4) == POSTGRE_SSL_REQUEST_CODE)
    {
        return true;
    }

    // 检查是否是GSSENCRequest消息
    if (msg_len == POSTGRE_SSL_REQUEST_LEN && GET_MSG_LEN(data + 4) == POSTGRE_GSSENC_REQUEST_CODE)
    {
        return true;
    }

    // 检查协议版本号
    uint32_t protocol_version = GET_MSG_LEN(data + 4);
    if (protocol_version != POSTGRE_PROTOCOL_VERSION_3)
    {
        return false;
    }

    // 检查参数格式
    const char *param_start = data + 8;
    const char *param_end = data + msg_len;
    while (param_start < param_end)
    {
        // 参数必须以null结尾
        const char *param_name = param_start;
        size_t param_name_len = strnlen(param_name, param_end - param_name);
        if (param_name_len == 0 || param_name_len == (size_t)(param_end - param_name))
        {
            break;
        }
        param_start += param_name_len + 1;

        // 参数值必须以null结尾
        const char *param_value = param_start;
        size_t param_value_len = strnlen(param_value, param_end - param_value);
        if (param_value_len == (size_t)(param_end - param_value))
        {
            return false;
        }
        param_start += param_value_len + 1;
    }

    return true;
}

bool CPostgreParser::is_valid_normal_msg(const char *data, int len, int dir)
{
    if (len < POSTGRE_NORMAL_MIN_LEN)
    {
        return false;
    }

    // 检查消息长度
    uint32_t msg_len = GET_MSG_LEN(data + 1);
    if (msg_len < POSTGRE_MSG_HEADER_LEN || msg_len + 1 > (uint32_t)len)
    {
        return false;
    }

    // 检查消息类型
    if (dir == PGSQL_REQUEST)
        return client_msg(data);
    else if (dir == PGSQL_RESPONSE)
        return server_msg(data);
    
    return false;
}

bool CPostgreParser::probe(CSessionMgt *psm, const app_stream *a_app, const struct conn *pcon, CSession *p_session)
{
    if (!m_conf_parser_enable)
    {
        return false;
    }

    StreamData * psd = NULL;
    postgre_stream_t *p_ms = NULL;
    enum pgsql_parser_type dir = PGSQL_BOTH;
    const char *data = NULL;
    int data_len = 0;
    int offset_out = 0;
    tcp_stream *a_tcp = NULL;

    // 获取TCP数据
    StreamData *tcp_data = p_session->get_stream_data_from_type(SESSION_PROTO_TYPE_TCP);
    if (tcp_data == NULL) {
        return false;
    }

    a_tcp = tcp_data->a_tcp;
    ASSERT(a_tcp != NULL);
    
    if ((a_app->dir ^ a_tcp->reverse) == 0)
    {
        dir = PGSQL_REQUEST;
    }
    else
    {
        dir = PGSQL_RESPONSE;
    }

    psd = get_stream_data_from_session(p_session, a_app->dir);
    if (NULL == psd || NULL == (p_ms = psd->p_postgre_stream))
    {
        return false;
    }

    if (p_ms->is_postgre == 1)
    {
        return true;
    }
    else if (p_ms->is_postgre == 2)
    {
        return false;
    }

    // 获取数据内容
    data = p_session->get_data(this, a_app->dir, &data_len, &offset_out);

    if (data == NULL || data_len <= 0) {
        return false;
    }

    // 增强型消息方向检测
    bool is_client_msg = false;
    bool is_server_msg = false;
    
    // 使用严格的PostgreSQL消息验证
    if (is_valid_pgsql_msg_strict(data, data_len, dir)) {
        // 检查是否是单字节的SSL响应
        if (data_len == 1 && (data[0] == 'S' || data[0] == 'N' || data[0] == 'E')) {
            // 这是服务器对SSLRequest或GSSENCRequest的单字节响应
            is_server_msg = true;
        }
        // 检查是否是启动消息、SSLRequest或GSSENCRequest(只能是客户端)
        else if (is_valid_startup_msg(data, data_len)) {
            is_client_msg = true;
        }
        // 检查第一个字节的消息类型
        else if (data_len >= 1) {
            // 客户端特有的消息类型
            if (client_msg(data)) {
                if (is_valid_normal_msg(data, data_len, PGSQL_REQUEST)) {
                    is_client_msg = true;
                }
            }
            
            // 服务器特有的消息类型
            else if (server_msg(data)) {
                if (is_valid_normal_msg(data, data_len, PGSQL_RESPONSE)) {
                    is_server_msg = true;
                }
            }
        }
    }
    
    // 根据消息特征判断方向是否需要校正
    if (dir == PGSQL_REQUEST && is_server_msg && !is_client_msg) {
        // 当前认为是请求方向，但数据特征是服务器消息
        a_tcp->reverse = !a_tcp->reverse;
        dir = PGSQL_RESPONSE;
    }
    else if (dir == PGSQL_RESPONSE && is_client_msg && !is_server_msg) {
        // 当前认为是响应方向，但数据特征是客户端消息
        a_tcp->reverse = !a_tcp->reverse;
        dir = PGSQL_REQUEST;
    }
    
    // 最终确定是否识别为PostgreSQL
    if ((dir == PGSQL_REQUEST && is_client_msg) || 
        (dir == PGSQL_RESPONSE && is_server_msg)) {
        p_ms->is_postgre = 1;
    }
    else if (p_session->m_probe_cnt++ <= m_conf_postgre_probe_cnt) {
        return false;  // 暂时不确定，等待更多数据
    }
    else {
        p_ms->is_postgre = 2;  // 确认不是PostgreSQL
    }

    return p_ms->is_postgre == 1 ? true : false;
}

bool CPostgreParser::probe_on_close(CSessionMgt *psm, const app_stream *pas, const struct conn *pcon, CSession *ps)
{
    return false;
}

bool CPostgreParser::probe_on_reset(CSessionMgt *psm, const app_stream *pas, const struct conn *pcon, CSession *ps)
{
    return false;
}

// 严格探测函数实现
bool CPostgreParser::is_valid_pgsql_msg_strict(const char *data, int len, int dir)
{
    // 第一层：基本格式检查
    if (dir == PGSQL_REQUEST) {
        // 检查是否是启动消息
        if (len >= POSTGRE_STARTUP_MIN_LEN && data[0] == 0) {
            if (!is_valid_startup_msg(data, len)) {
                return false;
            }
        } else if (len >= POSTGRE_NORMAL_MIN_LEN) {
            if (!is_valid_normal_msg(data, len, dir)) {
                return false;
            }
        } else {
            return false;
        }
    } else if (dir == PGSQL_RESPONSE) {
        // 检查单字节SSL/GSSENC响应或正常消息
        if (len == 1) {
            if (!(data[0] == 'S' || data[0] == 'N' || data[0] == 'E')) {
                return false;
            }
        } else if (len >= POSTGRE_NORMAL_MIN_LEN) {
            if (!is_valid_normal_msg(data, len, dir)) {
                return false;
            }
        } else {
            return false;
        }
    }
    
    // 第二层：检查是否与其他协议冲突
    if (conflicts_with_other_protocols(data, len)) {
        return false;
    }
    
    // 第三层：PostgreSQL协议指纹检查
    if (!check_pgsql_protocol_fingerprint(data, len, dir)) {
        return false;
    }
    
    // 第四层：消息序列一致性验证
    if (!validate_message_sequence_consistency(data, len, dir)) {
        return false;
    }
    
    // 第五层：PostgreSQL特有模式检查
    if (!check_postgresql_specific_patterns(data, len)) {
        return false;
    }
    
    return true;
}

bool CPostgreParser::validate_pgsql_startup_parameters(const char *data, int len)
{
    if (len < POSTGRE_STARTUP_MIN_LEN) {
        return false;
    }
    
    uint32_t msg_len = GET_MSG_LEN(data);
    if (msg_len < POSTGRE_STARTUP_MIN_LEN || msg_len > (uint32_t)len) {
        return false;
    }
    
    // 检查是否包含PostgreSQL特有的参数名称
    return validate_startup_parameter_names(data, len);
}

bool CPostgreParser::check_pgsql_protocol_fingerprint(const char *data, int len, int dir)
{
    if (dir == PGSQL_REQUEST) {
        // 检查启动消息的协议指纹
        if (len >= POSTGRE_STARTUP_MIN_LEN && data[0] == 0) {
            uint32_t msg_len = GET_MSG_LEN(data);
            uint32_t code = GET_MSG_LEN(data + 4);
            
            // 检查SSL/GSSENC请求的特殊代码
            if (msg_len == POSTGRE_SSL_REQUEST_LEN) {
                return (code == POSTGRE_SSL_REQUEST_CODE || code == POSTGRE_GSSENC_REQUEST_CODE);
            }
            
            // 检查标准协议版本
            if (code == POSTGRE_PROTOCOL_VERSION_3) {
                return validate_pgsql_startup_parameters(data, len);
            }
            
            return false;
        }
        
        // 检查普通客户端消息
        if (len >= POSTGRE_NORMAL_MIN_LEN) {
            uint32_t msg_len = GET_MSG_LEN(data + 1);
            
            // 验证消息长度的合理性
            if (msg_len < POSTGRE_MSG_HEADER_LEN || msg_len + 1 > (uint32_t)len) {
                return false;
            }
            
            // 检查消息类型是否为已知的客户端消息类型
            return client_msg(data);
        }
    } else if (dir == PGSQL_RESPONSE) {
        // 检查单字节响应
        if (len == 1) {
            return (data[0] == 'S' || data[0] == 'N' || data[0] == 'E');
        }
        
        // 检查普通服务器消息
        if (len >= POSTGRE_NORMAL_MIN_LEN) {
            uint32_t msg_len = GET_MSG_LEN(data + 1);
            
            // 验证消息长度的合理性
            if (msg_len < POSTGRE_MSG_HEADER_LEN || msg_len + 1 > (uint32_t)len) {
                return false;
            }
            
            // 检查消息类型是否为已知的服务器消息类型
            return server_msg(data);
        }
    }
    
    return false;
}

bool CPostgreParser::conflicts_with_other_protocols(const char *data, int len)
{
    if (len < 4) {
        return false;
    }
    
    // 检查MongoDB协议冲突
    if (len >= 16) {
        // 检查MongoDB消息头格式
        uint32_t msg_len = GET_MSG_LEN(data);
        int32_t op_code = *(int32_t*)(data + 12);
        
        // MongoDB操作码范围检查
        if (msg_len >= 16 && msg_len <= (uint32_t)len &&
            (op_code == 2013 || op_code == 2004 || op_code == 1 ||
             op_code == 2001 || op_code == 2002 || op_code == 2006 ||
             op_code == 2012)) {
            return true; // 可能是MongoDB协议
        }
    }
    
    // 检查HTTP协议冲突
    if (len >= 4) {
        if (strncmp(data, "GET ", 4) == 0 || 
            strncmp(data, "POST", 4) == 0 || 
            strncmp(data, "PUT ", 4) == 0 || 
            strncmp(data, "HEAD", 4) == 0 || 
            strncmp(data, "HTTP", 4) == 0) {
            return true; // 是HTTP协议
        }
    }
    
    // 检查MySQL协议冲突
    if (len >= 5) {
        // MySQL握手包的特征：包长度 + 包序号 + 协议版本
        uint32_t mysql_len = (uint32_t)(data[0] | (data[1] << 8) | (data[2] << 16));
        if (mysql_len > 0 && mysql_len < 0xFFFFFF && data[3] == 0 && 
            (data[4] == 10 || data[4] == 9)) { // MySQL协议版本
            return true; // 可能是MySQL协议
        }
    }
    
    return false;
}

bool CPostgreParser::validate_message_sequence_consistency(const char *data, int len, int dir)
{
    // 基本的消息序列验证
    if (dir == PGSQL_REQUEST && len >= POSTGRE_STARTUP_MIN_LEN && data[0] == 0) {
        // 启动消息应该是连接的第一个消息
        uint32_t msg_len = GET_MSG_LEN(data);
        uint32_t code = GET_MSG_LEN(data + 4);
        
        // 验证启动消息的合理性
        if (code == POSTGRE_PROTOCOL_VERSION_3) {
            // 检查消息长度是否合理（通常在100-1000字节之间）
            return (msg_len >= POSTGRE_STARTUP_MIN_LEN && msg_len <= 1024);
        } else if (code == POSTGRE_SSL_REQUEST_CODE || code == POSTGRE_GSSENC_REQUEST_CODE) {
            // SSL/GSSENC请求必须是固定长度
            return (msg_len == POSTGRE_SSL_REQUEST_LEN);
        }
    }
    
    return true;
}

bool CPostgreParser::check_postgresql_specific_patterns(const char *data, int len)
{
    if (len >= POSTGRE_STARTUP_MIN_LEN && data[0] == 0) {
        uint32_t code = GET_MSG_LEN(data + 4);
        
        // 检查启动消息的特殊模式
        if (code == POSTGRE_PROTOCOL_VERSION_3) {
            return validate_startup_parameter_names(data, len);
        }
    }
    
    // 对于普通消息，检查是否包含PostgreSQL特有的模式
    if (len >= POSTGRE_NORMAL_MIN_LEN) {
        char msg_type = data[0];
        
        // 检查认证相关消息的特殊模式
        if (msg_type == POSTGRE_MSG_AUTHENTICATION && len >= 9) {
            uint32_t auth_type = GET_MSG_LEN(data + 5);
            // 检查认证类型是否为PostgreSQL已知类型
            return (auth_type == POSTGRE_AUTH_OK || 
                    auth_type == POSTGRE_AUTH_CLEARTEXT_PASSWORD ||
                    auth_type == POSTGRE_AUTH_MD5_PASSWORD ||
                    auth_type == POSTGRE_AUTH_SASL ||
                    auth_type == POSTGRE_AUTH_SASL_CONTINUE ||
                    auth_type == POSTGRE_AUTH_SASL_FINAL);
        }
    }
    
    return true;
}

bool CPostgreParser::validate_startup_parameter_names(const char *data, int len)
{
    if (len < POSTGRE_STARTUP_MIN_LEN) {
        return false;
    }
    
    uint32_t msg_len = GET_MSG_LEN(data);
    if (msg_len > (uint32_t)len) {
        return false;
    }
    
    // 检查参数名称
    const char *param_start = data + 8;
    const char *param_end = data + msg_len;
    bool found_pgsql_param = false;
    
    // PostgreSQL常见的启动参数名称
    const char* pgsql_params[] = {
        "user", "database", "application_name", "client_encoding",
        "DateStyle", "TimeZone", "extra_float_digits", "search_path"
    };
    const int param_count = sizeof(pgsql_params) / sizeof(pgsql_params[0]);
    
    while (param_start < param_end) {
        // 获取参数名
        const char *param_name = param_start;
        size_t param_name_len = strnlen(param_name, param_end - param_name);
        if (param_name_len == 0 || param_name_len == (size_t)(param_end - param_name)) {
            break;
        }
        
        // 检查是否为PostgreSQL特有参数
        for (int i = 0; i < param_count; i++) {
            if (strncmp(param_name, pgsql_params[i], param_name_len) == 0 && 
                strlen(pgsql_params[i]) == param_name_len) {
                found_pgsql_param = true;
                break;
            }
        }
        
        param_start += param_name_len + 1;
        
        // 跳过参数值
        const char *param_value = param_start;
        size_t param_value_len = strnlen(param_value, param_end - param_value);
        if (param_value_len == (size_t)(param_end - param_value)) {
            return false;
        }
        param_start += param_value_len + 1;
    }
    
    return found_pgsql_param;
} 