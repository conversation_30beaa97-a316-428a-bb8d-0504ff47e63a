// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ProtobufRawPostgreEvent.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_ProtobufRawPostgreEvent_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_ProtobufRawPostgreEvent_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3020000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3020001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_ProtobufRawPostgreEvent_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_ProtobufRawPostgreEvent_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_ProtobufRawPostgreEvent_2eproto;
namespace com {
namespace quanzhi {
namespace audit_core {
namespace common {
namespace model {
class Mac;
struct MacDefaultTypeInternal;
extern MacDefaultTypeInternal _Mac_default_instance_;
class Meta;
struct MetaDefaultTypeInternal;
extern MetaDefaultTypeInternal _Meta_default_instance_;
class Net;
struct NetDefaultTypeInternal;
extern NetDefaultTypeInternal _Net_default_instance_;
class PostgreRequest;
struct PostgreRequestDefaultTypeInternal;
extern PostgreRequestDefaultTypeInternal _PostgreRequest_default_instance_;
class PostgreResponse;
struct PostgreResponseDefaultTypeInternal;
extern PostgreResponseDefaultTypeInternal _PostgreResponse_default_instance_;
class ProtobufRawPostgreEvent;
struct ProtobufRawPostgreEventDefaultTypeInternal;
extern ProtobufRawPostgreEventDefaultTypeInternal _ProtobufRawPostgreEvent_default_instance_;
class ResultRow;
struct ResultRowDefaultTypeInternal;
extern ResultRowDefaultTypeInternal _ResultRow_default_instance_;
class ResultSet;
struct ResultSetDefaultTypeInternal;
extern ResultSetDefaultTypeInternal _ResultSet_default_instance_;
class Source;
struct SourceDefaultTypeInternal;
extern SourceDefaultTypeInternal _Source_default_instance_;
class UniqueId;
struct UniqueIdDefaultTypeInternal;
extern UniqueIdDefaultTypeInternal _UniqueId_default_instance_;
}  // namespace model
}  // namespace common
}  // namespace audit_core
}  // namespace quanzhi
}  // namespace com
PROTOBUF_NAMESPACE_OPEN
template<> ::com::quanzhi::audit_core::common::model::Mac* Arena::CreateMaybeMessage<::com::quanzhi::audit_core::common::model::Mac>(Arena*);
template<> ::com::quanzhi::audit_core::common::model::Meta* Arena::CreateMaybeMessage<::com::quanzhi::audit_core::common::model::Meta>(Arena*);
template<> ::com::quanzhi::audit_core::common::model::Net* Arena::CreateMaybeMessage<::com::quanzhi::audit_core::common::model::Net>(Arena*);
template<> ::com::quanzhi::audit_core::common::model::PostgreRequest* Arena::CreateMaybeMessage<::com::quanzhi::audit_core::common::model::PostgreRequest>(Arena*);
template<> ::com::quanzhi::audit_core::common::model::PostgreResponse* Arena::CreateMaybeMessage<::com::quanzhi::audit_core::common::model::PostgreResponse>(Arena*);
template<> ::com::quanzhi::audit_core::common::model::ProtobufRawPostgreEvent* Arena::CreateMaybeMessage<::com::quanzhi::audit_core::common::model::ProtobufRawPostgreEvent>(Arena*);
template<> ::com::quanzhi::audit_core::common::model::ResultRow* Arena::CreateMaybeMessage<::com::quanzhi::audit_core::common::model::ResultRow>(Arena*);
template<> ::com::quanzhi::audit_core::common::model::ResultSet* Arena::CreateMaybeMessage<::com::quanzhi::audit_core::common::model::ResultSet>(Arena*);
template<> ::com::quanzhi::audit_core::common::model::Source* Arena::CreateMaybeMessage<::com::quanzhi::audit_core::common::model::Source>(Arena*);
template<> ::com::quanzhi::audit_core::common::model::UniqueId* Arena::CreateMaybeMessage<::com::quanzhi::audit_core::common::model::UniqueId>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace com {
namespace quanzhi {
namespace audit_core {
namespace common {
namespace model {

enum SourceTypeEnum : int {
  app_har = 0,
  flow = 1,
  SourceTypeEnum_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  SourceTypeEnum_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool SourceTypeEnum_IsValid(int value);
constexpr SourceTypeEnum SourceTypeEnum_MIN = app_har;
constexpr SourceTypeEnum SourceTypeEnum_MAX = flow;
constexpr int SourceTypeEnum_ARRAYSIZE = SourceTypeEnum_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* SourceTypeEnum_descriptor();
template<typename T>
inline const std::string& SourceTypeEnum_Name(T enum_t_value) {
  static_assert(::std::is_same<T, SourceTypeEnum>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function SourceTypeEnum_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    SourceTypeEnum_descriptor(), enum_t_value);
}
inline bool SourceTypeEnum_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, SourceTypeEnum* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<SourceTypeEnum>(
    SourceTypeEnum_descriptor(), name, value);
}
// ===================================================================

class ProtobufRawPostgreEvent final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:com.quanzhi.audit_core.common.model.ProtobufRawPostgreEvent) */ {
 public:
  inline ProtobufRawPostgreEvent() : ProtobufRawPostgreEvent(nullptr) {}
  ~ProtobufRawPostgreEvent() override;
  explicit PROTOBUF_CONSTEXPR ProtobufRawPostgreEvent(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ProtobufRawPostgreEvent(const ProtobufRawPostgreEvent& from);
  ProtobufRawPostgreEvent(ProtobufRawPostgreEvent&& from) noexcept
    : ProtobufRawPostgreEvent() {
    *this = ::std::move(from);
  }

  inline ProtobufRawPostgreEvent& operator=(const ProtobufRawPostgreEvent& from) {
    CopyFrom(from);
    return *this;
  }
  inline ProtobufRawPostgreEvent& operator=(ProtobufRawPostgreEvent&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ProtobufRawPostgreEvent& default_instance() {
    return *internal_default_instance();
  }
  static inline const ProtobufRawPostgreEvent* internal_default_instance() {
    return reinterpret_cast<const ProtobufRawPostgreEvent*>(
               &_ProtobufRawPostgreEvent_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(ProtobufRawPostgreEvent& a, ProtobufRawPostgreEvent& b) {
    a.Swap(&b);
  }
  inline void Swap(ProtobufRawPostgreEvent* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ProtobufRawPostgreEvent* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ProtobufRawPostgreEvent* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ProtobufRawPostgreEvent>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ProtobufRawPostgreEvent& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ProtobufRawPostgreEvent& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ProtobufRawPostgreEvent* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "com.quanzhi.audit_core.common.model.ProtobufRawPostgreEvent";
  }
  protected:
  explicit ProtobufRawPostgreEvent(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kReqFieldNumber = 1,
    kRspFieldNumber = 2,
    kMetaFieldNumber = 3,
    kNetFieldNumber = 4,
    kUniqueIdFieldNumber = 5,
    kSourceFieldNumber = 6,
    kMacFieldNumber = 7,
  };
  // .com.quanzhi.audit_core.common.model.PostgreRequest req = 1;
  bool has_req() const;
  private:
  bool _internal_has_req() const;
  public:
  void clear_req();
  const ::com::quanzhi::audit_core::common::model::PostgreRequest& req() const;
  PROTOBUF_NODISCARD ::com::quanzhi::audit_core::common::model::PostgreRequest* release_req();
  ::com::quanzhi::audit_core::common::model::PostgreRequest* mutable_req();
  void set_allocated_req(::com::quanzhi::audit_core::common::model::PostgreRequest* req);
  private:
  const ::com::quanzhi::audit_core::common::model::PostgreRequest& _internal_req() const;
  ::com::quanzhi::audit_core::common::model::PostgreRequest* _internal_mutable_req();
  public:
  void unsafe_arena_set_allocated_req(
      ::com::quanzhi::audit_core::common::model::PostgreRequest* req);
  ::com::quanzhi::audit_core::common::model::PostgreRequest* unsafe_arena_release_req();

  // .com.quanzhi.audit_core.common.model.PostgreResponse rsp = 2;
  bool has_rsp() const;
  private:
  bool _internal_has_rsp() const;
  public:
  void clear_rsp();
  const ::com::quanzhi::audit_core::common::model::PostgreResponse& rsp() const;
  PROTOBUF_NODISCARD ::com::quanzhi::audit_core::common::model::PostgreResponse* release_rsp();
  ::com::quanzhi::audit_core::common::model::PostgreResponse* mutable_rsp();
  void set_allocated_rsp(::com::quanzhi::audit_core::common::model::PostgreResponse* rsp);
  private:
  const ::com::quanzhi::audit_core::common::model::PostgreResponse& _internal_rsp() const;
  ::com::quanzhi::audit_core::common::model::PostgreResponse* _internal_mutable_rsp();
  public:
  void unsafe_arena_set_allocated_rsp(
      ::com::quanzhi::audit_core::common::model::PostgreResponse* rsp);
  ::com::quanzhi::audit_core::common::model::PostgreResponse* unsafe_arena_release_rsp();

  // .com.quanzhi.audit_core.common.model.Meta meta = 3;
  bool has_meta() const;
  private:
  bool _internal_has_meta() const;
  public:
  void clear_meta();
  const ::com::quanzhi::audit_core::common::model::Meta& meta() const;
  PROTOBUF_NODISCARD ::com::quanzhi::audit_core::common::model::Meta* release_meta();
  ::com::quanzhi::audit_core::common::model::Meta* mutable_meta();
  void set_allocated_meta(::com::quanzhi::audit_core::common::model::Meta* meta);
  private:
  const ::com::quanzhi::audit_core::common::model::Meta& _internal_meta() const;
  ::com::quanzhi::audit_core::common::model::Meta* _internal_mutable_meta();
  public:
  void unsafe_arena_set_allocated_meta(
      ::com::quanzhi::audit_core::common::model::Meta* meta);
  ::com::quanzhi::audit_core::common::model::Meta* unsafe_arena_release_meta();

  // .com.quanzhi.audit_core.common.model.Net net = 4;
  bool has_net() const;
  private:
  bool _internal_has_net() const;
  public:
  void clear_net();
  const ::com::quanzhi::audit_core::common::model::Net& net() const;
  PROTOBUF_NODISCARD ::com::quanzhi::audit_core::common::model::Net* release_net();
  ::com::quanzhi::audit_core::common::model::Net* mutable_net();
  void set_allocated_net(::com::quanzhi::audit_core::common::model::Net* net);
  private:
  const ::com::quanzhi::audit_core::common::model::Net& _internal_net() const;
  ::com::quanzhi::audit_core::common::model::Net* _internal_mutable_net();
  public:
  void unsafe_arena_set_allocated_net(
      ::com::quanzhi::audit_core::common::model::Net* net);
  ::com::quanzhi::audit_core::common::model::Net* unsafe_arena_release_net();

  // .com.quanzhi.audit_core.common.model.UniqueId uniqueId = 5;
  bool has_uniqueid() const;
  private:
  bool _internal_has_uniqueid() const;
  public:
  void clear_uniqueid();
  const ::com::quanzhi::audit_core::common::model::UniqueId& uniqueid() const;
  PROTOBUF_NODISCARD ::com::quanzhi::audit_core::common::model::UniqueId* release_uniqueid();
  ::com::quanzhi::audit_core::common::model::UniqueId* mutable_uniqueid();
  void set_allocated_uniqueid(::com::quanzhi::audit_core::common::model::UniqueId* uniqueid);
  private:
  const ::com::quanzhi::audit_core::common::model::UniqueId& _internal_uniqueid() const;
  ::com::quanzhi::audit_core::common::model::UniqueId* _internal_mutable_uniqueid();
  public:
  void unsafe_arena_set_allocated_uniqueid(
      ::com::quanzhi::audit_core::common::model::UniqueId* uniqueid);
  ::com::quanzhi::audit_core::common::model::UniqueId* unsafe_arena_release_uniqueid();

  // .com.quanzhi.audit_core.common.model.Source source = 6;
  bool has_source() const;
  private:
  bool _internal_has_source() const;
  public:
  void clear_source();
  const ::com::quanzhi::audit_core::common::model::Source& source() const;
  PROTOBUF_NODISCARD ::com::quanzhi::audit_core::common::model::Source* release_source();
  ::com::quanzhi::audit_core::common::model::Source* mutable_source();
  void set_allocated_source(::com::quanzhi::audit_core::common::model::Source* source);
  private:
  const ::com::quanzhi::audit_core::common::model::Source& _internal_source() const;
  ::com::quanzhi::audit_core::common::model::Source* _internal_mutable_source();
  public:
  void unsafe_arena_set_allocated_source(
      ::com::quanzhi::audit_core::common::model::Source* source);
  ::com::quanzhi::audit_core::common::model::Source* unsafe_arena_release_source();

  // .com.quanzhi.audit_core.common.model.Mac mac = 7;
  bool has_mac() const;
  private:
  bool _internal_has_mac() const;
  public:
  void clear_mac();
  const ::com::quanzhi::audit_core::common::model::Mac& mac() const;
  PROTOBUF_NODISCARD ::com::quanzhi::audit_core::common::model::Mac* release_mac();
  ::com::quanzhi::audit_core::common::model::Mac* mutable_mac();
  void set_allocated_mac(::com::quanzhi::audit_core::common::model::Mac* mac);
  private:
  const ::com::quanzhi::audit_core::common::model::Mac& _internal_mac() const;
  ::com::quanzhi::audit_core::common::model::Mac* _internal_mutable_mac();
  public:
  void unsafe_arena_set_allocated_mac(
      ::com::quanzhi::audit_core::common::model::Mac* mac);
  ::com::quanzhi::audit_core::common::model::Mac* unsafe_arena_release_mac();

  // @@protoc_insertion_point(class_scope:com.quanzhi.audit_core.common.model.ProtobufRawPostgreEvent)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::com::quanzhi::audit_core::common::model::PostgreRequest* req_;
  ::com::quanzhi::audit_core::common::model::PostgreResponse* rsp_;
  ::com::quanzhi::audit_core::common::model::Meta* meta_;
  ::com::quanzhi::audit_core::common::model::Net* net_;
  ::com::quanzhi::audit_core::common::model::UniqueId* uniqueid_;
  ::com::quanzhi::audit_core::common::model::Source* source_;
  ::com::quanzhi::audit_core::common::model::Mac* mac_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_ProtobufRawPostgreEvent_2eproto;
};
// -------------------------------------------------------------------

class Meta final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:com.quanzhi.audit_core.common.model.Meta) */ {
 public:
  inline Meta() : Meta(nullptr) {}
  ~Meta() override;
  explicit PROTOBUF_CONSTEXPR Meta(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Meta(const Meta& from);
  Meta(Meta&& from) noexcept
    : Meta() {
    *this = ::std::move(from);
  }

  inline Meta& operator=(const Meta& from) {
    CopyFrom(from);
    return *this;
  }
  inline Meta& operator=(Meta&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Meta& default_instance() {
    return *internal_default_instance();
  }
  static inline const Meta* internal_default_instance() {
    return reinterpret_cast<const Meta*>(
               &_Meta_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(Meta& a, Meta& b) {
    a.Swap(&b);
  }
  inline void Swap(Meta* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Meta* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Meta* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Meta>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Meta& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const Meta& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Meta* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "com.quanzhi.audit_core.common.model.Meta";
  }
  protected:
  explicit Meta(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTypeFieldNumber = 2,
    kAppNameFieldNumber = 3,
    kServerVersionFieldNumber = 4,
    kTmFieldNumber = 1,
  };
  // string type = 2;
  void clear_type();
  const std::string& type() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_type(ArgT0&& arg0, ArgT... args);
  std::string* mutable_type();
  PROTOBUF_NODISCARD std::string* release_type();
  void set_allocated_type(std::string* type);
  private:
  const std::string& _internal_type() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_type(const std::string& value);
  std::string* _internal_mutable_type();
  public:

  // string app_name = 3;
  void clear_app_name();
  const std::string& app_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_app_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_app_name();
  PROTOBUF_NODISCARD std::string* release_app_name();
  void set_allocated_app_name(std::string* app_name);
  private:
  const std::string& _internal_app_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_app_name(const std::string& value);
  std::string* _internal_mutable_app_name();
  public:

  // string server_version = 4;
  void clear_server_version();
  const std::string& server_version() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_server_version(ArgT0&& arg0, ArgT... args);
  std::string* mutable_server_version();
  PROTOBUF_NODISCARD std::string* release_server_version();
  void set_allocated_server_version(std::string* server_version);
  private:
  const std::string& _internal_server_version() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_server_version(const std::string& value);
  std::string* _internal_mutable_server_version();
  public:

  // uint64 tm = 1;
  void clear_tm();
  uint64_t tm() const;
  void set_tm(uint64_t value);
  private:
  uint64_t _internal_tm() const;
  void _internal_set_tm(uint64_t value);
  public:

  // @@protoc_insertion_point(class_scope:com.quanzhi.audit_core.common.model.Meta)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr type_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr app_name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr server_version_;
  uint64_t tm_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_ProtobufRawPostgreEvent_2eproto;
};
// -------------------------------------------------------------------

class UniqueId final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:com.quanzhi.audit_core.common.model.UniqueId) */ {
 public:
  inline UniqueId() : UniqueId(nullptr) {}
  ~UniqueId() override;
  explicit PROTOBUF_CONSTEXPR UniqueId(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  UniqueId(const UniqueId& from);
  UniqueId(UniqueId&& from) noexcept
    : UniqueId() {
    *this = ::std::move(from);
  }

  inline UniqueId& operator=(const UniqueId& from) {
    CopyFrom(from);
    return *this;
  }
  inline UniqueId& operator=(UniqueId&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const UniqueId& default_instance() {
    return *internal_default_instance();
  }
  static inline const UniqueId* internal_default_instance() {
    return reinterpret_cast<const UniqueId*>(
               &_UniqueId_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(UniqueId& a, UniqueId& b) {
    a.Swap(&b);
  }
  inline void Swap(UniqueId* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(UniqueId* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  UniqueId* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<UniqueId>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const UniqueId& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const UniqueId& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(UniqueId* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "com.quanzhi.audit_core.common.model.UniqueId";
  }
  protected:
  explicit UniqueId(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kEventIdFieldNumber = 1,
  };
  // string eventId = 1;
  void clear_eventid();
  const std::string& eventid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_eventid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_eventid();
  PROTOBUF_NODISCARD std::string* release_eventid();
  void set_allocated_eventid(std::string* eventid);
  private:
  const std::string& _internal_eventid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_eventid(const std::string& value);
  std::string* _internal_mutable_eventid();
  public:

  // @@protoc_insertion_point(class_scope:com.quanzhi.audit_core.common.model.UniqueId)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr eventid_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_ProtobufRawPostgreEvent_2eproto;
};
// -------------------------------------------------------------------

class PostgreRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:com.quanzhi.audit_core.common.model.PostgreRequest) */ {
 public:
  inline PostgreRequest() : PostgreRequest(nullptr) {}
  ~PostgreRequest() override;
  explicit PROTOBUF_CONSTEXPR PostgreRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PostgreRequest(const PostgreRequest& from);
  PostgreRequest(PostgreRequest&& from) noexcept
    : PostgreRequest() {
    *this = ::std::move(from);
  }

  inline PostgreRequest& operator=(const PostgreRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline PostgreRequest& operator=(PostgreRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PostgreRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const PostgreRequest* internal_default_instance() {
    return reinterpret_cast<const PostgreRequest*>(
               &_PostgreRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(PostgreRequest& a, PostgreRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(PostgreRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PostgreRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  PostgreRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<PostgreRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const PostgreRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const PostgreRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PostgreRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "com.quanzhi.audit_core.common.model.PostgreRequest";
  }
  protected:
  explicit PostgreRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDbUserFieldNumber = 1,
    kDbNameFieldNumber = 2,
    kDbPasswordFieldNumber = 3,
    kCmdTypeFieldNumber = 4,
    kSqlFieldNumber = 5,
    kErrCodeFieldNumber = 6,
  };
  // string db_user = 1;
  void clear_db_user();
  const std::string& db_user() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_db_user(ArgT0&& arg0, ArgT... args);
  std::string* mutable_db_user();
  PROTOBUF_NODISCARD std::string* release_db_user();
  void set_allocated_db_user(std::string* db_user);
  private:
  const std::string& _internal_db_user() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_db_user(const std::string& value);
  std::string* _internal_mutable_db_user();
  public:

  // string db_name = 2;
  void clear_db_name();
  const std::string& db_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_db_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_db_name();
  PROTOBUF_NODISCARD std::string* release_db_name();
  void set_allocated_db_name(std::string* db_name);
  private:
  const std::string& _internal_db_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_db_name(const std::string& value);
  std::string* _internal_mutable_db_name();
  public:

  // string db_password = 3;
  void clear_db_password();
  const std::string& db_password() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_db_password(ArgT0&& arg0, ArgT... args);
  std::string* mutable_db_password();
  PROTOBUF_NODISCARD std::string* release_db_password();
  void set_allocated_db_password(std::string* db_password);
  private:
  const std::string& _internal_db_password() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_db_password(const std::string& value);
  std::string* _internal_mutable_db_password();
  public:

  // string cmd_type = 4;
  void clear_cmd_type();
  const std::string& cmd_type() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_cmd_type(ArgT0&& arg0, ArgT... args);
  std::string* mutable_cmd_type();
  PROTOBUF_NODISCARD std::string* release_cmd_type();
  void set_allocated_cmd_type(std::string* cmd_type);
  private:
  const std::string& _internal_cmd_type() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_cmd_type(const std::string& value);
  std::string* _internal_mutable_cmd_type();
  public:

  // string sql = 5;
  void clear_sql();
  const std::string& sql() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_sql(ArgT0&& arg0, ArgT... args);
  std::string* mutable_sql();
  PROTOBUF_NODISCARD std::string* release_sql();
  void set_allocated_sql(std::string* sql);
  private:
  const std::string& _internal_sql() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_sql(const std::string& value);
  std::string* _internal_mutable_sql();
  public:

  // int32 errCode = 6;
  void clear_errcode();
  int32_t errcode() const;
  void set_errcode(int32_t value);
  private:
  int32_t _internal_errcode() const;
  void _internal_set_errcode(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:com.quanzhi.audit_core.common.model.PostgreRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr db_user_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr db_name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr db_password_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr cmd_type_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr sql_;
  int32_t errcode_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_ProtobufRawPostgreEvent_2eproto;
};
// -------------------------------------------------------------------

class PostgreResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:com.quanzhi.audit_core.common.model.PostgreResponse) */ {
 public:
  inline PostgreResponse() : PostgreResponse(nullptr) {}
  ~PostgreResponse() override;
  explicit PROTOBUF_CONSTEXPR PostgreResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PostgreResponse(const PostgreResponse& from);
  PostgreResponse(PostgreResponse&& from) noexcept
    : PostgreResponse() {
    *this = ::std::move(from);
  }

  inline PostgreResponse& operator=(const PostgreResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline PostgreResponse& operator=(PostgreResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PostgreResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const PostgreResponse* internal_default_instance() {
    return reinterpret_cast<const PostgreResponse*>(
               &_PostgreResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(PostgreResponse& a, PostgreResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(PostgreResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PostgreResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  PostgreResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<PostgreResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const PostgreResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const PostgreResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PostgreResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "com.quanzhi.audit_core.common.model.PostgreResponse";
  }
  protected:
  explicit PostgreResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kResultSetFieldNumber = 5,
    kStartTimeFieldNumber = 2,
    kStatusFieldNumber = 1,
    kRowCountFieldNumber = 4,
    kCloseTimeFieldNumber = 3,
    kErrCodeFieldNumber = 6,
  };
  // .com.quanzhi.audit_core.common.model.ResultSet result_set = 5;
  bool has_result_set() const;
  private:
  bool _internal_has_result_set() const;
  public:
  void clear_result_set();
  const ::com::quanzhi::audit_core::common::model::ResultSet& result_set() const;
  PROTOBUF_NODISCARD ::com::quanzhi::audit_core::common::model::ResultSet* release_result_set();
  ::com::quanzhi::audit_core::common::model::ResultSet* mutable_result_set();
  void set_allocated_result_set(::com::quanzhi::audit_core::common::model::ResultSet* result_set);
  private:
  const ::com::quanzhi::audit_core::common::model::ResultSet& _internal_result_set() const;
  ::com::quanzhi::audit_core::common::model::ResultSet* _internal_mutable_result_set();
  public:
  void unsafe_arena_set_allocated_result_set(
      ::com::quanzhi::audit_core::common::model::ResultSet* result_set);
  ::com::quanzhi::audit_core::common::model::ResultSet* unsafe_arena_release_result_set();

  // uint64 start_time = 2;
  void clear_start_time();
  uint64_t start_time() const;
  void set_start_time(uint64_t value);
  private:
  uint64_t _internal_start_time() const;
  void _internal_set_start_time(uint64_t value);
  public:

  // int32 status = 1;
  void clear_status();
  int32_t status() const;
  void set_status(int32_t value);
  private:
  int32_t _internal_status() const;
  void _internal_set_status(int32_t value);
  public:

  // int32 row_count = 4;
  void clear_row_count();
  int32_t row_count() const;
  void set_row_count(int32_t value);
  private:
  int32_t _internal_row_count() const;
  void _internal_set_row_count(int32_t value);
  public:

  // uint64 close_time = 3;
  void clear_close_time();
  uint64_t close_time() const;
  void set_close_time(uint64_t value);
  private:
  uint64_t _internal_close_time() const;
  void _internal_set_close_time(uint64_t value);
  public:

  // int32 errCode = 6;
  void clear_errcode();
  int32_t errcode() const;
  void set_errcode(int32_t value);
  private:
  int32_t _internal_errcode() const;
  void _internal_set_errcode(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:com.quanzhi.audit_core.common.model.PostgreResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::com::quanzhi::audit_core::common::model::ResultSet* result_set_;
  uint64_t start_time_;
  int32_t status_;
  int32_t row_count_;
  uint64_t close_time_;
  int32_t errcode_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_ProtobufRawPostgreEvent_2eproto;
};
// -------------------------------------------------------------------

class ResultSet final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:com.quanzhi.audit_core.common.model.ResultSet) */ {
 public:
  inline ResultSet() : ResultSet(nullptr) {}
  ~ResultSet() override;
  explicit PROTOBUF_CONSTEXPR ResultSet(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ResultSet(const ResultSet& from);
  ResultSet(ResultSet&& from) noexcept
    : ResultSet() {
    *this = ::std::move(from);
  }

  inline ResultSet& operator=(const ResultSet& from) {
    CopyFrom(from);
    return *this;
  }
  inline ResultSet& operator=(ResultSet&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ResultSet& default_instance() {
    return *internal_default_instance();
  }
  static inline const ResultSet* internal_default_instance() {
    return reinterpret_cast<const ResultSet*>(
               &_ResultSet_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(ResultSet& a, ResultSet& b) {
    a.Swap(&b);
  }
  inline void Swap(ResultSet* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ResultSet* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ResultSet* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ResultSet>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ResultSet& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ResultSet& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ResultSet* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "com.quanzhi.audit_core.common.model.ResultSet";
  }
  protected:
  explicit ResultSet(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFieldNamesFieldNumber = 1,
    kRowsFieldNumber = 2,
    kFieldCountFieldNumber = 3,
  };
  // repeated string field_names = 1;
  int field_names_size() const;
  private:
  int _internal_field_names_size() const;
  public:
  void clear_field_names();
  const std::string& field_names(int index) const;
  std::string* mutable_field_names(int index);
  void set_field_names(int index, const std::string& value);
  void set_field_names(int index, std::string&& value);
  void set_field_names(int index, const char* value);
  void set_field_names(int index, const char* value, size_t size);
  std::string* add_field_names();
  void add_field_names(const std::string& value);
  void add_field_names(std::string&& value);
  void add_field_names(const char* value);
  void add_field_names(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& field_names() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_field_names();
  private:
  const std::string& _internal_field_names(int index) const;
  std::string* _internal_add_field_names();
  public:

  // repeated .com.quanzhi.audit_core.common.model.ResultRow rows = 2;
  int rows_size() const;
  private:
  int _internal_rows_size() const;
  public:
  void clear_rows();
  ::com::quanzhi::audit_core::common::model::ResultRow* mutable_rows(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::com::quanzhi::audit_core::common::model::ResultRow >*
      mutable_rows();
  private:
  const ::com::quanzhi::audit_core::common::model::ResultRow& _internal_rows(int index) const;
  ::com::quanzhi::audit_core::common::model::ResultRow* _internal_add_rows();
  public:
  const ::com::quanzhi::audit_core::common::model::ResultRow& rows(int index) const;
  ::com::quanzhi::audit_core::common::model::ResultRow* add_rows();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::com::quanzhi::audit_core::common::model::ResultRow >&
      rows() const;

  // int32 field_count = 3;
  void clear_field_count();
  int32_t field_count() const;
  void set_field_count(int32_t value);
  private:
  int32_t _internal_field_count() const;
  void _internal_set_field_count(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:com.quanzhi.audit_core.common.model.ResultSet)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> field_names_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::com::quanzhi::audit_core::common::model::ResultRow > rows_;
  int32_t field_count_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_ProtobufRawPostgreEvent_2eproto;
};
// -------------------------------------------------------------------

class ResultRow final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:com.quanzhi.audit_core.common.model.ResultRow) */ {
 public:
  inline ResultRow() : ResultRow(nullptr) {}
  ~ResultRow() override;
  explicit PROTOBUF_CONSTEXPR ResultRow(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ResultRow(const ResultRow& from);
  ResultRow(ResultRow&& from) noexcept
    : ResultRow() {
    *this = ::std::move(from);
  }

  inline ResultRow& operator=(const ResultRow& from) {
    CopyFrom(from);
    return *this;
  }
  inline ResultRow& operator=(ResultRow&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ResultRow& default_instance() {
    return *internal_default_instance();
  }
  static inline const ResultRow* internal_default_instance() {
    return reinterpret_cast<const ResultRow*>(
               &_ResultRow_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(ResultRow& a, ResultRow& b) {
    a.Swap(&b);
  }
  inline void Swap(ResultRow* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ResultRow* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ResultRow* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ResultRow>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ResultRow& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ResultRow& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ResultRow* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "com.quanzhi.audit_core.common.model.ResultRow";
  }
  protected:
  explicit ResultRow(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFieldValuesFieldNumber = 1,
  };
  // repeated string field_values = 1;
  int field_values_size() const;
  private:
  int _internal_field_values_size() const;
  public:
  void clear_field_values();
  const std::string& field_values(int index) const;
  std::string* mutable_field_values(int index);
  void set_field_values(int index, const std::string& value);
  void set_field_values(int index, std::string&& value);
  void set_field_values(int index, const char* value);
  void set_field_values(int index, const char* value, size_t size);
  std::string* add_field_values();
  void add_field_values(const std::string& value);
  void add_field_values(std::string&& value);
  void add_field_values(const char* value);
  void add_field_values(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& field_values() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_field_values();
  private:
  const std::string& _internal_field_values(int index) const;
  std::string* _internal_add_field_values();
  public:

  // @@protoc_insertion_point(class_scope:com.quanzhi.audit_core.common.model.ResultRow)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> field_values_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_ProtobufRawPostgreEvent_2eproto;
};
// -------------------------------------------------------------------

class Net final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:com.quanzhi.audit_core.common.model.Net) */ {
 public:
  inline Net() : Net(nullptr) {}
  ~Net() override;
  explicit PROTOBUF_CONSTEXPR Net(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Net(const Net& from);
  Net(Net&& from) noexcept
    : Net() {
    *this = ::std::move(from);
  }

  inline Net& operator=(const Net& from) {
    CopyFrom(from);
    return *this;
  }
  inline Net& operator=(Net&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Net& default_instance() {
    return *internal_default_instance();
  }
  static inline const Net* internal_default_instance() {
    return reinterpret_cast<const Net*>(
               &_Net_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(Net& a, Net& b) {
    a.Swap(&b);
  }
  inline void Swap(Net* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Net* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Net* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Net>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Net& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const Net& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Net* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "com.quanzhi.audit_core.common.model.Net";
  }
  protected:
  explicit Net(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSrcIpFieldNumber = 1,
    kDstIpFieldNumber = 3,
    kFlowSourceFieldNumber = 5,
    kSrcPortFieldNumber = 2,
    kDstPortFieldNumber = 4,
  };
  // string srcIp = 1;
  void clear_srcip();
  const std::string& srcip() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_srcip(ArgT0&& arg0, ArgT... args);
  std::string* mutable_srcip();
  PROTOBUF_NODISCARD std::string* release_srcip();
  void set_allocated_srcip(std::string* srcip);
  private:
  const std::string& _internal_srcip() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_srcip(const std::string& value);
  std::string* _internal_mutable_srcip();
  public:

  // string dstIp = 3;
  void clear_dstip();
  const std::string& dstip() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_dstip(ArgT0&& arg0, ArgT... args);
  std::string* mutable_dstip();
  PROTOBUF_NODISCARD std::string* release_dstip();
  void set_allocated_dstip(std::string* dstip);
  private:
  const std::string& _internal_dstip() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_dstip(const std::string& value);
  std::string* _internal_mutable_dstip();
  public:

  // string flowSource = 5;
  void clear_flowsource();
  const std::string& flowsource() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_flowsource(ArgT0&& arg0, ArgT... args);
  std::string* mutable_flowsource();
  PROTOBUF_NODISCARD std::string* release_flowsource();
  void set_allocated_flowsource(std::string* flowsource);
  private:
  const std::string& _internal_flowsource() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_flowsource(const std::string& value);
  std::string* _internal_mutable_flowsource();
  public:

  // int32 srcPort = 2;
  void clear_srcport();
  int32_t srcport() const;
  void set_srcport(int32_t value);
  private:
  int32_t _internal_srcport() const;
  void _internal_set_srcport(int32_t value);
  public:

  // int32 dstPort = 4;
  void clear_dstport();
  int32_t dstport() const;
  void set_dstport(int32_t value);
  private:
  int32_t _internal_dstport() const;
  void _internal_set_dstport(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:com.quanzhi.audit_core.common.model.Net)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr srcip_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr dstip_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr flowsource_;
  int32_t srcport_;
  int32_t dstport_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_ProtobufRawPostgreEvent_2eproto;
};
// -------------------------------------------------------------------

class Mac final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:com.quanzhi.audit_core.common.model.Mac) */ {
 public:
  inline Mac() : Mac(nullptr) {}
  ~Mac() override;
  explicit PROTOBUF_CONSTEXPR Mac(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Mac(const Mac& from);
  Mac(Mac&& from) noexcept
    : Mac() {
    *this = ::std::move(from);
  }

  inline Mac& operator=(const Mac& from) {
    CopyFrom(from);
    return *this;
  }
  inline Mac& operator=(Mac&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Mac& default_instance() {
    return *internal_default_instance();
  }
  static inline const Mac* internal_default_instance() {
    return reinterpret_cast<const Mac*>(
               &_Mac_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(Mac& a, Mac& b) {
    a.Swap(&b);
  }
  inline void Swap(Mac* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Mac* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Mac* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Mac>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Mac& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const Mac& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Mac* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "com.quanzhi.audit_core.common.model.Mac";
  }
  protected:
  explicit Mac(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMacFieldNumber = 1,
  };
  // string mac = 1;
  void clear_mac();
  const std::string& mac() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_mac(ArgT0&& arg0, ArgT... args);
  std::string* mutable_mac();
  PROTOBUF_NODISCARD std::string* release_mac();
  void set_allocated_mac(std::string* mac);
  private:
  const std::string& _internal_mac() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_mac(const std::string& value);
  std::string* _internal_mutable_mac();
  public:

  // @@protoc_insertion_point(class_scope:com.quanzhi.audit_core.common.model.Mac)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr mac_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_ProtobufRawPostgreEvent_2eproto;
};
// -------------------------------------------------------------------

class Source final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:com.quanzhi.audit_core.common.model.Source) */ {
 public:
  inline Source() : Source(nullptr) {}
  ~Source() override;
  explicit PROTOBUF_CONSTEXPR Source(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Source(const Source& from);
  Source(Source&& from) noexcept
    : Source() {
    *this = ::std::move(from);
  }

  inline Source& operator=(const Source& from) {
    CopyFrom(from);
    return *this;
  }
  inline Source& operator=(Source&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Source& default_instance() {
    return *internal_default_instance();
  }
  static inline const Source* internal_default_instance() {
    return reinterpret_cast<const Source*>(
               &_Source_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(Source& a, Source& b) {
    a.Swap(&b);
  }
  inline void Swap(Source* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Source* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Source* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Source>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Source& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const Source& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Source* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "com.quanzhi.audit_core.common.model.Source";
  }
  protected:
  explicit Source(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTaskIdFieldNumber = 2,
    kAppFieldNumber = 3,
    kSourceTypeFieldNumber = 1,
  };
  // string taskId = 2;
  void clear_taskid();
  const std::string& taskid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_taskid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_taskid();
  PROTOBUF_NODISCARD std::string* release_taskid();
  void set_allocated_taskid(std::string* taskid);
  private:
  const std::string& _internal_taskid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_taskid(const std::string& value);
  std::string* _internal_mutable_taskid();
  public:

  // string app = 3;
  void clear_app();
  const std::string& app() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_app(ArgT0&& arg0, ArgT... args);
  std::string* mutable_app();
  PROTOBUF_NODISCARD std::string* release_app();
  void set_allocated_app(std::string* app);
  private:
  const std::string& _internal_app() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_app(const std::string& value);
  std::string* _internal_mutable_app();
  public:

  // .com.quanzhi.audit_core.common.model.SourceTypeEnum sourceType = 1;
  void clear_sourcetype();
  ::com::quanzhi::audit_core::common::model::SourceTypeEnum sourcetype() const;
  void set_sourcetype(::com::quanzhi::audit_core::common::model::SourceTypeEnum value);
  private:
  ::com::quanzhi::audit_core::common::model::SourceTypeEnum _internal_sourcetype() const;
  void _internal_set_sourcetype(::com::quanzhi::audit_core::common::model::SourceTypeEnum value);
  public:

  // @@protoc_insertion_point(class_scope:com.quanzhi.audit_core.common.model.Source)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr taskid_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr app_;
  int sourcetype_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_ProtobufRawPostgreEvent_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// ProtobufRawPostgreEvent

// .com.quanzhi.audit_core.common.model.PostgreRequest req = 1;
inline bool ProtobufRawPostgreEvent::_internal_has_req() const {
  return this != internal_default_instance() && req_ != nullptr;
}
inline bool ProtobufRawPostgreEvent::has_req() const {
  return _internal_has_req();
}
inline void ProtobufRawPostgreEvent::clear_req() {
  if (GetArenaForAllocation() == nullptr && req_ != nullptr) {
    delete req_;
  }
  req_ = nullptr;
}
inline const ::com::quanzhi::audit_core::common::model::PostgreRequest& ProtobufRawPostgreEvent::_internal_req() const {
  const ::com::quanzhi::audit_core::common::model::PostgreRequest* p = req_;
  return p != nullptr ? *p : reinterpret_cast<const ::com::quanzhi::audit_core::common::model::PostgreRequest&>(
      ::com::quanzhi::audit_core::common::model::_PostgreRequest_default_instance_);
}
inline const ::com::quanzhi::audit_core::common::model::PostgreRequest& ProtobufRawPostgreEvent::req() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.ProtobufRawPostgreEvent.req)
  return _internal_req();
}
inline void ProtobufRawPostgreEvent::unsafe_arena_set_allocated_req(
    ::com::quanzhi::audit_core::common::model::PostgreRequest* req) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(req_);
  }
  req_ = req;
  if (req) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:com.quanzhi.audit_core.common.model.ProtobufRawPostgreEvent.req)
}
inline ::com::quanzhi::audit_core::common::model::PostgreRequest* ProtobufRawPostgreEvent::release_req() {
  
  ::com::quanzhi::audit_core::common::model::PostgreRequest* temp = req_;
  req_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::com::quanzhi::audit_core::common::model::PostgreRequest* ProtobufRawPostgreEvent::unsafe_arena_release_req() {
  // @@protoc_insertion_point(field_release:com.quanzhi.audit_core.common.model.ProtobufRawPostgreEvent.req)
  
  ::com::quanzhi::audit_core::common::model::PostgreRequest* temp = req_;
  req_ = nullptr;
  return temp;
}
inline ::com::quanzhi::audit_core::common::model::PostgreRequest* ProtobufRawPostgreEvent::_internal_mutable_req() {
  
  if (req_ == nullptr) {
    auto* p = CreateMaybeMessage<::com::quanzhi::audit_core::common::model::PostgreRequest>(GetArenaForAllocation());
    req_ = p;
  }
  return req_;
}
inline ::com::quanzhi::audit_core::common::model::PostgreRequest* ProtobufRawPostgreEvent::mutable_req() {
  ::com::quanzhi::audit_core::common::model::PostgreRequest* _msg = _internal_mutable_req();
  // @@protoc_insertion_point(field_mutable:com.quanzhi.audit_core.common.model.ProtobufRawPostgreEvent.req)
  return _msg;
}
inline void ProtobufRawPostgreEvent::set_allocated_req(::com::quanzhi::audit_core::common::model::PostgreRequest* req) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete req_;
  }
  if (req) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(req);
    if (message_arena != submessage_arena) {
      req = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, req, submessage_arena);
    }
    
  } else {
    
  }
  req_ = req;
  // @@protoc_insertion_point(field_set_allocated:com.quanzhi.audit_core.common.model.ProtobufRawPostgreEvent.req)
}

// .com.quanzhi.audit_core.common.model.PostgreResponse rsp = 2;
inline bool ProtobufRawPostgreEvent::_internal_has_rsp() const {
  return this != internal_default_instance() && rsp_ != nullptr;
}
inline bool ProtobufRawPostgreEvent::has_rsp() const {
  return _internal_has_rsp();
}
inline void ProtobufRawPostgreEvent::clear_rsp() {
  if (GetArenaForAllocation() == nullptr && rsp_ != nullptr) {
    delete rsp_;
  }
  rsp_ = nullptr;
}
inline const ::com::quanzhi::audit_core::common::model::PostgreResponse& ProtobufRawPostgreEvent::_internal_rsp() const {
  const ::com::quanzhi::audit_core::common::model::PostgreResponse* p = rsp_;
  return p != nullptr ? *p : reinterpret_cast<const ::com::quanzhi::audit_core::common::model::PostgreResponse&>(
      ::com::quanzhi::audit_core::common::model::_PostgreResponse_default_instance_);
}
inline const ::com::quanzhi::audit_core::common::model::PostgreResponse& ProtobufRawPostgreEvent::rsp() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.ProtobufRawPostgreEvent.rsp)
  return _internal_rsp();
}
inline void ProtobufRawPostgreEvent::unsafe_arena_set_allocated_rsp(
    ::com::quanzhi::audit_core::common::model::PostgreResponse* rsp) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(rsp_);
  }
  rsp_ = rsp;
  if (rsp) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:com.quanzhi.audit_core.common.model.ProtobufRawPostgreEvent.rsp)
}
inline ::com::quanzhi::audit_core::common::model::PostgreResponse* ProtobufRawPostgreEvent::release_rsp() {
  
  ::com::quanzhi::audit_core::common::model::PostgreResponse* temp = rsp_;
  rsp_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::com::quanzhi::audit_core::common::model::PostgreResponse* ProtobufRawPostgreEvent::unsafe_arena_release_rsp() {
  // @@protoc_insertion_point(field_release:com.quanzhi.audit_core.common.model.ProtobufRawPostgreEvent.rsp)
  
  ::com::quanzhi::audit_core::common::model::PostgreResponse* temp = rsp_;
  rsp_ = nullptr;
  return temp;
}
inline ::com::quanzhi::audit_core::common::model::PostgreResponse* ProtobufRawPostgreEvent::_internal_mutable_rsp() {
  
  if (rsp_ == nullptr) {
    auto* p = CreateMaybeMessage<::com::quanzhi::audit_core::common::model::PostgreResponse>(GetArenaForAllocation());
    rsp_ = p;
  }
  return rsp_;
}
inline ::com::quanzhi::audit_core::common::model::PostgreResponse* ProtobufRawPostgreEvent::mutable_rsp() {
  ::com::quanzhi::audit_core::common::model::PostgreResponse* _msg = _internal_mutable_rsp();
  // @@protoc_insertion_point(field_mutable:com.quanzhi.audit_core.common.model.ProtobufRawPostgreEvent.rsp)
  return _msg;
}
inline void ProtobufRawPostgreEvent::set_allocated_rsp(::com::quanzhi::audit_core::common::model::PostgreResponse* rsp) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete rsp_;
  }
  if (rsp) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(rsp);
    if (message_arena != submessage_arena) {
      rsp = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, rsp, submessage_arena);
    }
    
  } else {
    
  }
  rsp_ = rsp;
  // @@protoc_insertion_point(field_set_allocated:com.quanzhi.audit_core.common.model.ProtobufRawPostgreEvent.rsp)
}

// .com.quanzhi.audit_core.common.model.Meta meta = 3;
inline bool ProtobufRawPostgreEvent::_internal_has_meta() const {
  return this != internal_default_instance() && meta_ != nullptr;
}
inline bool ProtobufRawPostgreEvent::has_meta() const {
  return _internal_has_meta();
}
inline void ProtobufRawPostgreEvent::clear_meta() {
  if (GetArenaForAllocation() == nullptr && meta_ != nullptr) {
    delete meta_;
  }
  meta_ = nullptr;
}
inline const ::com::quanzhi::audit_core::common::model::Meta& ProtobufRawPostgreEvent::_internal_meta() const {
  const ::com::quanzhi::audit_core::common::model::Meta* p = meta_;
  return p != nullptr ? *p : reinterpret_cast<const ::com::quanzhi::audit_core::common::model::Meta&>(
      ::com::quanzhi::audit_core::common::model::_Meta_default_instance_);
}
inline const ::com::quanzhi::audit_core::common::model::Meta& ProtobufRawPostgreEvent::meta() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.ProtobufRawPostgreEvent.meta)
  return _internal_meta();
}
inline void ProtobufRawPostgreEvent::unsafe_arena_set_allocated_meta(
    ::com::quanzhi::audit_core::common::model::Meta* meta) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(meta_);
  }
  meta_ = meta;
  if (meta) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:com.quanzhi.audit_core.common.model.ProtobufRawPostgreEvent.meta)
}
inline ::com::quanzhi::audit_core::common::model::Meta* ProtobufRawPostgreEvent::release_meta() {
  
  ::com::quanzhi::audit_core::common::model::Meta* temp = meta_;
  meta_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::com::quanzhi::audit_core::common::model::Meta* ProtobufRawPostgreEvent::unsafe_arena_release_meta() {
  // @@protoc_insertion_point(field_release:com.quanzhi.audit_core.common.model.ProtobufRawPostgreEvent.meta)
  
  ::com::quanzhi::audit_core::common::model::Meta* temp = meta_;
  meta_ = nullptr;
  return temp;
}
inline ::com::quanzhi::audit_core::common::model::Meta* ProtobufRawPostgreEvent::_internal_mutable_meta() {
  
  if (meta_ == nullptr) {
    auto* p = CreateMaybeMessage<::com::quanzhi::audit_core::common::model::Meta>(GetArenaForAllocation());
    meta_ = p;
  }
  return meta_;
}
inline ::com::quanzhi::audit_core::common::model::Meta* ProtobufRawPostgreEvent::mutable_meta() {
  ::com::quanzhi::audit_core::common::model::Meta* _msg = _internal_mutable_meta();
  // @@protoc_insertion_point(field_mutable:com.quanzhi.audit_core.common.model.ProtobufRawPostgreEvent.meta)
  return _msg;
}
inline void ProtobufRawPostgreEvent::set_allocated_meta(::com::quanzhi::audit_core::common::model::Meta* meta) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete meta_;
  }
  if (meta) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(meta);
    if (message_arena != submessage_arena) {
      meta = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, meta, submessage_arena);
    }
    
  } else {
    
  }
  meta_ = meta;
  // @@protoc_insertion_point(field_set_allocated:com.quanzhi.audit_core.common.model.ProtobufRawPostgreEvent.meta)
}

// .com.quanzhi.audit_core.common.model.Net net = 4;
inline bool ProtobufRawPostgreEvent::_internal_has_net() const {
  return this != internal_default_instance() && net_ != nullptr;
}
inline bool ProtobufRawPostgreEvent::has_net() const {
  return _internal_has_net();
}
inline void ProtobufRawPostgreEvent::clear_net() {
  if (GetArenaForAllocation() == nullptr && net_ != nullptr) {
    delete net_;
  }
  net_ = nullptr;
}
inline const ::com::quanzhi::audit_core::common::model::Net& ProtobufRawPostgreEvent::_internal_net() const {
  const ::com::quanzhi::audit_core::common::model::Net* p = net_;
  return p != nullptr ? *p : reinterpret_cast<const ::com::quanzhi::audit_core::common::model::Net&>(
      ::com::quanzhi::audit_core::common::model::_Net_default_instance_);
}
inline const ::com::quanzhi::audit_core::common::model::Net& ProtobufRawPostgreEvent::net() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.ProtobufRawPostgreEvent.net)
  return _internal_net();
}
inline void ProtobufRawPostgreEvent::unsafe_arena_set_allocated_net(
    ::com::quanzhi::audit_core::common::model::Net* net) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(net_);
  }
  net_ = net;
  if (net) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:com.quanzhi.audit_core.common.model.ProtobufRawPostgreEvent.net)
}
inline ::com::quanzhi::audit_core::common::model::Net* ProtobufRawPostgreEvent::release_net() {
  
  ::com::quanzhi::audit_core::common::model::Net* temp = net_;
  net_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::com::quanzhi::audit_core::common::model::Net* ProtobufRawPostgreEvent::unsafe_arena_release_net() {
  // @@protoc_insertion_point(field_release:com.quanzhi.audit_core.common.model.ProtobufRawPostgreEvent.net)
  
  ::com::quanzhi::audit_core::common::model::Net* temp = net_;
  net_ = nullptr;
  return temp;
}
inline ::com::quanzhi::audit_core::common::model::Net* ProtobufRawPostgreEvent::_internal_mutable_net() {
  
  if (net_ == nullptr) {
    auto* p = CreateMaybeMessage<::com::quanzhi::audit_core::common::model::Net>(GetArenaForAllocation());
    net_ = p;
  }
  return net_;
}
inline ::com::quanzhi::audit_core::common::model::Net* ProtobufRawPostgreEvent::mutable_net() {
  ::com::quanzhi::audit_core::common::model::Net* _msg = _internal_mutable_net();
  // @@protoc_insertion_point(field_mutable:com.quanzhi.audit_core.common.model.ProtobufRawPostgreEvent.net)
  return _msg;
}
inline void ProtobufRawPostgreEvent::set_allocated_net(::com::quanzhi::audit_core::common::model::Net* net) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete net_;
  }
  if (net) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(net);
    if (message_arena != submessage_arena) {
      net = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, net, submessage_arena);
    }
    
  } else {
    
  }
  net_ = net;
  // @@protoc_insertion_point(field_set_allocated:com.quanzhi.audit_core.common.model.ProtobufRawPostgreEvent.net)
}

// .com.quanzhi.audit_core.common.model.UniqueId uniqueId = 5;
inline bool ProtobufRawPostgreEvent::_internal_has_uniqueid() const {
  return this != internal_default_instance() && uniqueid_ != nullptr;
}
inline bool ProtobufRawPostgreEvent::has_uniqueid() const {
  return _internal_has_uniqueid();
}
inline void ProtobufRawPostgreEvent::clear_uniqueid() {
  if (GetArenaForAllocation() == nullptr && uniqueid_ != nullptr) {
    delete uniqueid_;
  }
  uniqueid_ = nullptr;
}
inline const ::com::quanzhi::audit_core::common::model::UniqueId& ProtobufRawPostgreEvent::_internal_uniqueid() const {
  const ::com::quanzhi::audit_core::common::model::UniqueId* p = uniqueid_;
  return p != nullptr ? *p : reinterpret_cast<const ::com::quanzhi::audit_core::common::model::UniqueId&>(
      ::com::quanzhi::audit_core::common::model::_UniqueId_default_instance_);
}
inline const ::com::quanzhi::audit_core::common::model::UniqueId& ProtobufRawPostgreEvent::uniqueid() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.ProtobufRawPostgreEvent.uniqueId)
  return _internal_uniqueid();
}
inline void ProtobufRawPostgreEvent::unsafe_arena_set_allocated_uniqueid(
    ::com::quanzhi::audit_core::common::model::UniqueId* uniqueid) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(uniqueid_);
  }
  uniqueid_ = uniqueid;
  if (uniqueid) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:com.quanzhi.audit_core.common.model.ProtobufRawPostgreEvent.uniqueId)
}
inline ::com::quanzhi::audit_core::common::model::UniqueId* ProtobufRawPostgreEvent::release_uniqueid() {
  
  ::com::quanzhi::audit_core::common::model::UniqueId* temp = uniqueid_;
  uniqueid_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::com::quanzhi::audit_core::common::model::UniqueId* ProtobufRawPostgreEvent::unsafe_arena_release_uniqueid() {
  // @@protoc_insertion_point(field_release:com.quanzhi.audit_core.common.model.ProtobufRawPostgreEvent.uniqueId)
  
  ::com::quanzhi::audit_core::common::model::UniqueId* temp = uniqueid_;
  uniqueid_ = nullptr;
  return temp;
}
inline ::com::quanzhi::audit_core::common::model::UniqueId* ProtobufRawPostgreEvent::_internal_mutable_uniqueid() {
  
  if (uniqueid_ == nullptr) {
    auto* p = CreateMaybeMessage<::com::quanzhi::audit_core::common::model::UniqueId>(GetArenaForAllocation());
    uniqueid_ = p;
  }
  return uniqueid_;
}
inline ::com::quanzhi::audit_core::common::model::UniqueId* ProtobufRawPostgreEvent::mutable_uniqueid() {
  ::com::quanzhi::audit_core::common::model::UniqueId* _msg = _internal_mutable_uniqueid();
  // @@protoc_insertion_point(field_mutable:com.quanzhi.audit_core.common.model.ProtobufRawPostgreEvent.uniqueId)
  return _msg;
}
inline void ProtobufRawPostgreEvent::set_allocated_uniqueid(::com::quanzhi::audit_core::common::model::UniqueId* uniqueid) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete uniqueid_;
  }
  if (uniqueid) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(uniqueid);
    if (message_arena != submessage_arena) {
      uniqueid = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, uniqueid, submessage_arena);
    }
    
  } else {
    
  }
  uniqueid_ = uniqueid;
  // @@protoc_insertion_point(field_set_allocated:com.quanzhi.audit_core.common.model.ProtobufRawPostgreEvent.uniqueId)
}

// .com.quanzhi.audit_core.common.model.Source source = 6;
inline bool ProtobufRawPostgreEvent::_internal_has_source() const {
  return this != internal_default_instance() && source_ != nullptr;
}
inline bool ProtobufRawPostgreEvent::has_source() const {
  return _internal_has_source();
}
inline void ProtobufRawPostgreEvent::clear_source() {
  if (GetArenaForAllocation() == nullptr && source_ != nullptr) {
    delete source_;
  }
  source_ = nullptr;
}
inline const ::com::quanzhi::audit_core::common::model::Source& ProtobufRawPostgreEvent::_internal_source() const {
  const ::com::quanzhi::audit_core::common::model::Source* p = source_;
  return p != nullptr ? *p : reinterpret_cast<const ::com::quanzhi::audit_core::common::model::Source&>(
      ::com::quanzhi::audit_core::common::model::_Source_default_instance_);
}
inline const ::com::quanzhi::audit_core::common::model::Source& ProtobufRawPostgreEvent::source() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.ProtobufRawPostgreEvent.source)
  return _internal_source();
}
inline void ProtobufRawPostgreEvent::unsafe_arena_set_allocated_source(
    ::com::quanzhi::audit_core::common::model::Source* source) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(source_);
  }
  source_ = source;
  if (source) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:com.quanzhi.audit_core.common.model.ProtobufRawPostgreEvent.source)
}
inline ::com::quanzhi::audit_core::common::model::Source* ProtobufRawPostgreEvent::release_source() {
  
  ::com::quanzhi::audit_core::common::model::Source* temp = source_;
  source_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::com::quanzhi::audit_core::common::model::Source* ProtobufRawPostgreEvent::unsafe_arena_release_source() {
  // @@protoc_insertion_point(field_release:com.quanzhi.audit_core.common.model.ProtobufRawPostgreEvent.source)
  
  ::com::quanzhi::audit_core::common::model::Source* temp = source_;
  source_ = nullptr;
  return temp;
}
inline ::com::quanzhi::audit_core::common::model::Source* ProtobufRawPostgreEvent::_internal_mutable_source() {
  
  if (source_ == nullptr) {
    auto* p = CreateMaybeMessage<::com::quanzhi::audit_core::common::model::Source>(GetArenaForAllocation());
    source_ = p;
  }
  return source_;
}
inline ::com::quanzhi::audit_core::common::model::Source* ProtobufRawPostgreEvent::mutable_source() {
  ::com::quanzhi::audit_core::common::model::Source* _msg = _internal_mutable_source();
  // @@protoc_insertion_point(field_mutable:com.quanzhi.audit_core.common.model.ProtobufRawPostgreEvent.source)
  return _msg;
}
inline void ProtobufRawPostgreEvent::set_allocated_source(::com::quanzhi::audit_core::common::model::Source* source) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete source_;
  }
  if (source) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(source);
    if (message_arena != submessage_arena) {
      source = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, source, submessage_arena);
    }
    
  } else {
    
  }
  source_ = source;
  // @@protoc_insertion_point(field_set_allocated:com.quanzhi.audit_core.common.model.ProtobufRawPostgreEvent.source)
}

// .com.quanzhi.audit_core.common.model.Mac mac = 7;
inline bool ProtobufRawPostgreEvent::_internal_has_mac() const {
  return this != internal_default_instance() && mac_ != nullptr;
}
inline bool ProtobufRawPostgreEvent::has_mac() const {
  return _internal_has_mac();
}
inline void ProtobufRawPostgreEvent::clear_mac() {
  if (GetArenaForAllocation() == nullptr && mac_ != nullptr) {
    delete mac_;
  }
  mac_ = nullptr;
}
inline const ::com::quanzhi::audit_core::common::model::Mac& ProtobufRawPostgreEvent::_internal_mac() const {
  const ::com::quanzhi::audit_core::common::model::Mac* p = mac_;
  return p != nullptr ? *p : reinterpret_cast<const ::com::quanzhi::audit_core::common::model::Mac&>(
      ::com::quanzhi::audit_core::common::model::_Mac_default_instance_);
}
inline const ::com::quanzhi::audit_core::common::model::Mac& ProtobufRawPostgreEvent::mac() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.ProtobufRawPostgreEvent.mac)
  return _internal_mac();
}
inline void ProtobufRawPostgreEvent::unsafe_arena_set_allocated_mac(
    ::com::quanzhi::audit_core::common::model::Mac* mac) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(mac_);
  }
  mac_ = mac;
  if (mac) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:com.quanzhi.audit_core.common.model.ProtobufRawPostgreEvent.mac)
}
inline ::com::quanzhi::audit_core::common::model::Mac* ProtobufRawPostgreEvent::release_mac() {
  
  ::com::quanzhi::audit_core::common::model::Mac* temp = mac_;
  mac_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::com::quanzhi::audit_core::common::model::Mac* ProtobufRawPostgreEvent::unsafe_arena_release_mac() {
  // @@protoc_insertion_point(field_release:com.quanzhi.audit_core.common.model.ProtobufRawPostgreEvent.mac)
  
  ::com::quanzhi::audit_core::common::model::Mac* temp = mac_;
  mac_ = nullptr;
  return temp;
}
inline ::com::quanzhi::audit_core::common::model::Mac* ProtobufRawPostgreEvent::_internal_mutable_mac() {
  
  if (mac_ == nullptr) {
    auto* p = CreateMaybeMessage<::com::quanzhi::audit_core::common::model::Mac>(GetArenaForAllocation());
    mac_ = p;
  }
  return mac_;
}
inline ::com::quanzhi::audit_core::common::model::Mac* ProtobufRawPostgreEvent::mutable_mac() {
  ::com::quanzhi::audit_core::common::model::Mac* _msg = _internal_mutable_mac();
  // @@protoc_insertion_point(field_mutable:com.quanzhi.audit_core.common.model.ProtobufRawPostgreEvent.mac)
  return _msg;
}
inline void ProtobufRawPostgreEvent::set_allocated_mac(::com::quanzhi::audit_core::common::model::Mac* mac) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete mac_;
  }
  if (mac) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(mac);
    if (message_arena != submessage_arena) {
      mac = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, mac, submessage_arena);
    }
    
  } else {
    
  }
  mac_ = mac;
  // @@protoc_insertion_point(field_set_allocated:com.quanzhi.audit_core.common.model.ProtobufRawPostgreEvent.mac)
}

// -------------------------------------------------------------------

// Meta

// uint64 tm = 1;
inline void Meta::clear_tm() {
  tm_ = uint64_t{0u};
}
inline uint64_t Meta::_internal_tm() const {
  return tm_;
}
inline uint64_t Meta::tm() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.Meta.tm)
  return _internal_tm();
}
inline void Meta::_internal_set_tm(uint64_t value) {
  
  tm_ = value;
}
inline void Meta::set_tm(uint64_t value) {
  _internal_set_tm(value);
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.Meta.tm)
}

// string type = 2;
inline void Meta::clear_type() {
  type_.ClearToEmpty();
}
inline const std::string& Meta::type() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.Meta.type)
  return _internal_type();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Meta::set_type(ArgT0&& arg0, ArgT... args) {
 
 type_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.Meta.type)
}
inline std::string* Meta::mutable_type() {
  std::string* _s = _internal_mutable_type();
  // @@protoc_insertion_point(field_mutable:com.quanzhi.audit_core.common.model.Meta.type)
  return _s;
}
inline const std::string& Meta::_internal_type() const {
  return type_.Get();
}
inline void Meta::_internal_set_type(const std::string& value) {
  
  type_.Set(value, GetArenaForAllocation());
}
inline std::string* Meta::_internal_mutable_type() {
  
  return type_.Mutable(GetArenaForAllocation());
}
inline std::string* Meta::release_type() {
  // @@protoc_insertion_point(field_release:com.quanzhi.audit_core.common.model.Meta.type)
  return type_.Release();
}
inline void Meta::set_allocated_type(std::string* type) {
  if (type != nullptr) {
    
  } else {
    
  }
  type_.SetAllocated(type, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (type_.IsDefault()) {
    type_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:com.quanzhi.audit_core.common.model.Meta.type)
}

// string app_name = 3;
inline void Meta::clear_app_name() {
  app_name_.ClearToEmpty();
}
inline const std::string& Meta::app_name() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.Meta.app_name)
  return _internal_app_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Meta::set_app_name(ArgT0&& arg0, ArgT... args) {
 
 app_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.Meta.app_name)
}
inline std::string* Meta::mutable_app_name() {
  std::string* _s = _internal_mutable_app_name();
  // @@protoc_insertion_point(field_mutable:com.quanzhi.audit_core.common.model.Meta.app_name)
  return _s;
}
inline const std::string& Meta::_internal_app_name() const {
  return app_name_.Get();
}
inline void Meta::_internal_set_app_name(const std::string& value) {
  
  app_name_.Set(value, GetArenaForAllocation());
}
inline std::string* Meta::_internal_mutable_app_name() {
  
  return app_name_.Mutable(GetArenaForAllocation());
}
inline std::string* Meta::release_app_name() {
  // @@protoc_insertion_point(field_release:com.quanzhi.audit_core.common.model.Meta.app_name)
  return app_name_.Release();
}
inline void Meta::set_allocated_app_name(std::string* app_name) {
  if (app_name != nullptr) {
    
  } else {
    
  }
  app_name_.SetAllocated(app_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (app_name_.IsDefault()) {
    app_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:com.quanzhi.audit_core.common.model.Meta.app_name)
}

// string server_version = 4;
inline void Meta::clear_server_version() {
  server_version_.ClearToEmpty();
}
inline const std::string& Meta::server_version() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.Meta.server_version)
  return _internal_server_version();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Meta::set_server_version(ArgT0&& arg0, ArgT... args) {
 
 server_version_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.Meta.server_version)
}
inline std::string* Meta::mutable_server_version() {
  std::string* _s = _internal_mutable_server_version();
  // @@protoc_insertion_point(field_mutable:com.quanzhi.audit_core.common.model.Meta.server_version)
  return _s;
}
inline const std::string& Meta::_internal_server_version() const {
  return server_version_.Get();
}
inline void Meta::_internal_set_server_version(const std::string& value) {
  
  server_version_.Set(value, GetArenaForAllocation());
}
inline std::string* Meta::_internal_mutable_server_version() {
  
  return server_version_.Mutable(GetArenaForAllocation());
}
inline std::string* Meta::release_server_version() {
  // @@protoc_insertion_point(field_release:com.quanzhi.audit_core.common.model.Meta.server_version)
  return server_version_.Release();
}
inline void Meta::set_allocated_server_version(std::string* server_version) {
  if (server_version != nullptr) {
    
  } else {
    
  }
  server_version_.SetAllocated(server_version, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (server_version_.IsDefault()) {
    server_version_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:com.quanzhi.audit_core.common.model.Meta.server_version)
}

// -------------------------------------------------------------------

// UniqueId

// string eventId = 1;
inline void UniqueId::clear_eventid() {
  eventid_.ClearToEmpty();
}
inline const std::string& UniqueId::eventid() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.UniqueId.eventId)
  return _internal_eventid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void UniqueId::set_eventid(ArgT0&& arg0, ArgT... args) {
 
 eventid_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.UniqueId.eventId)
}
inline std::string* UniqueId::mutable_eventid() {
  std::string* _s = _internal_mutable_eventid();
  // @@protoc_insertion_point(field_mutable:com.quanzhi.audit_core.common.model.UniqueId.eventId)
  return _s;
}
inline const std::string& UniqueId::_internal_eventid() const {
  return eventid_.Get();
}
inline void UniqueId::_internal_set_eventid(const std::string& value) {
  
  eventid_.Set(value, GetArenaForAllocation());
}
inline std::string* UniqueId::_internal_mutable_eventid() {
  
  return eventid_.Mutable(GetArenaForAllocation());
}
inline std::string* UniqueId::release_eventid() {
  // @@protoc_insertion_point(field_release:com.quanzhi.audit_core.common.model.UniqueId.eventId)
  return eventid_.Release();
}
inline void UniqueId::set_allocated_eventid(std::string* eventid) {
  if (eventid != nullptr) {
    
  } else {
    
  }
  eventid_.SetAllocated(eventid, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (eventid_.IsDefault()) {
    eventid_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:com.quanzhi.audit_core.common.model.UniqueId.eventId)
}

// -------------------------------------------------------------------

// PostgreRequest

// string db_user = 1;
inline void PostgreRequest::clear_db_user() {
  db_user_.ClearToEmpty();
}
inline const std::string& PostgreRequest::db_user() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.PostgreRequest.db_user)
  return _internal_db_user();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void PostgreRequest::set_db_user(ArgT0&& arg0, ArgT... args) {
 
 db_user_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.PostgreRequest.db_user)
}
inline std::string* PostgreRequest::mutable_db_user() {
  std::string* _s = _internal_mutable_db_user();
  // @@protoc_insertion_point(field_mutable:com.quanzhi.audit_core.common.model.PostgreRequest.db_user)
  return _s;
}
inline const std::string& PostgreRequest::_internal_db_user() const {
  return db_user_.Get();
}
inline void PostgreRequest::_internal_set_db_user(const std::string& value) {
  
  db_user_.Set(value, GetArenaForAllocation());
}
inline std::string* PostgreRequest::_internal_mutable_db_user() {
  
  return db_user_.Mutable(GetArenaForAllocation());
}
inline std::string* PostgreRequest::release_db_user() {
  // @@protoc_insertion_point(field_release:com.quanzhi.audit_core.common.model.PostgreRequest.db_user)
  return db_user_.Release();
}
inline void PostgreRequest::set_allocated_db_user(std::string* db_user) {
  if (db_user != nullptr) {
    
  } else {
    
  }
  db_user_.SetAllocated(db_user, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (db_user_.IsDefault()) {
    db_user_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:com.quanzhi.audit_core.common.model.PostgreRequest.db_user)
}

// string db_name = 2;
inline void PostgreRequest::clear_db_name() {
  db_name_.ClearToEmpty();
}
inline const std::string& PostgreRequest::db_name() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.PostgreRequest.db_name)
  return _internal_db_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void PostgreRequest::set_db_name(ArgT0&& arg0, ArgT... args) {
 
 db_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.PostgreRequest.db_name)
}
inline std::string* PostgreRequest::mutable_db_name() {
  std::string* _s = _internal_mutable_db_name();
  // @@protoc_insertion_point(field_mutable:com.quanzhi.audit_core.common.model.PostgreRequest.db_name)
  return _s;
}
inline const std::string& PostgreRequest::_internal_db_name() const {
  return db_name_.Get();
}
inline void PostgreRequest::_internal_set_db_name(const std::string& value) {
  
  db_name_.Set(value, GetArenaForAllocation());
}
inline std::string* PostgreRequest::_internal_mutable_db_name() {
  
  return db_name_.Mutable(GetArenaForAllocation());
}
inline std::string* PostgreRequest::release_db_name() {
  // @@protoc_insertion_point(field_release:com.quanzhi.audit_core.common.model.PostgreRequest.db_name)
  return db_name_.Release();
}
inline void PostgreRequest::set_allocated_db_name(std::string* db_name) {
  if (db_name != nullptr) {
    
  } else {
    
  }
  db_name_.SetAllocated(db_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (db_name_.IsDefault()) {
    db_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:com.quanzhi.audit_core.common.model.PostgreRequest.db_name)
}

// string db_password = 3;
inline void PostgreRequest::clear_db_password() {
  db_password_.ClearToEmpty();
}
inline const std::string& PostgreRequest::db_password() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.PostgreRequest.db_password)
  return _internal_db_password();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void PostgreRequest::set_db_password(ArgT0&& arg0, ArgT... args) {
 
 db_password_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.PostgreRequest.db_password)
}
inline std::string* PostgreRequest::mutable_db_password() {
  std::string* _s = _internal_mutable_db_password();
  // @@protoc_insertion_point(field_mutable:com.quanzhi.audit_core.common.model.PostgreRequest.db_password)
  return _s;
}
inline const std::string& PostgreRequest::_internal_db_password() const {
  return db_password_.Get();
}
inline void PostgreRequest::_internal_set_db_password(const std::string& value) {
  
  db_password_.Set(value, GetArenaForAllocation());
}
inline std::string* PostgreRequest::_internal_mutable_db_password() {
  
  return db_password_.Mutable(GetArenaForAllocation());
}
inline std::string* PostgreRequest::release_db_password() {
  // @@protoc_insertion_point(field_release:com.quanzhi.audit_core.common.model.PostgreRequest.db_password)
  return db_password_.Release();
}
inline void PostgreRequest::set_allocated_db_password(std::string* db_password) {
  if (db_password != nullptr) {
    
  } else {
    
  }
  db_password_.SetAllocated(db_password, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (db_password_.IsDefault()) {
    db_password_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:com.quanzhi.audit_core.common.model.PostgreRequest.db_password)
}

// string cmd_type = 4;
inline void PostgreRequest::clear_cmd_type() {
  cmd_type_.ClearToEmpty();
}
inline const std::string& PostgreRequest::cmd_type() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.PostgreRequest.cmd_type)
  return _internal_cmd_type();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void PostgreRequest::set_cmd_type(ArgT0&& arg0, ArgT... args) {
 
 cmd_type_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.PostgreRequest.cmd_type)
}
inline std::string* PostgreRequest::mutable_cmd_type() {
  std::string* _s = _internal_mutable_cmd_type();
  // @@protoc_insertion_point(field_mutable:com.quanzhi.audit_core.common.model.PostgreRequest.cmd_type)
  return _s;
}
inline const std::string& PostgreRequest::_internal_cmd_type() const {
  return cmd_type_.Get();
}
inline void PostgreRequest::_internal_set_cmd_type(const std::string& value) {
  
  cmd_type_.Set(value, GetArenaForAllocation());
}
inline std::string* PostgreRequest::_internal_mutable_cmd_type() {
  
  return cmd_type_.Mutable(GetArenaForAllocation());
}
inline std::string* PostgreRequest::release_cmd_type() {
  // @@protoc_insertion_point(field_release:com.quanzhi.audit_core.common.model.PostgreRequest.cmd_type)
  return cmd_type_.Release();
}
inline void PostgreRequest::set_allocated_cmd_type(std::string* cmd_type) {
  if (cmd_type != nullptr) {
    
  } else {
    
  }
  cmd_type_.SetAllocated(cmd_type, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (cmd_type_.IsDefault()) {
    cmd_type_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:com.quanzhi.audit_core.common.model.PostgreRequest.cmd_type)
}

// string sql = 5;
inline void PostgreRequest::clear_sql() {
  sql_.ClearToEmpty();
}
inline const std::string& PostgreRequest::sql() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.PostgreRequest.sql)
  return _internal_sql();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void PostgreRequest::set_sql(ArgT0&& arg0, ArgT... args) {
 
 sql_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.PostgreRequest.sql)
}
inline std::string* PostgreRequest::mutable_sql() {
  std::string* _s = _internal_mutable_sql();
  // @@protoc_insertion_point(field_mutable:com.quanzhi.audit_core.common.model.PostgreRequest.sql)
  return _s;
}
inline const std::string& PostgreRequest::_internal_sql() const {
  return sql_.Get();
}
inline void PostgreRequest::_internal_set_sql(const std::string& value) {
  
  sql_.Set(value, GetArenaForAllocation());
}
inline std::string* PostgreRequest::_internal_mutable_sql() {
  
  return sql_.Mutable(GetArenaForAllocation());
}
inline std::string* PostgreRequest::release_sql() {
  // @@protoc_insertion_point(field_release:com.quanzhi.audit_core.common.model.PostgreRequest.sql)
  return sql_.Release();
}
inline void PostgreRequest::set_allocated_sql(std::string* sql) {
  if (sql != nullptr) {
    
  } else {
    
  }
  sql_.SetAllocated(sql, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (sql_.IsDefault()) {
    sql_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:com.quanzhi.audit_core.common.model.PostgreRequest.sql)
}

// int32 errCode = 6;
inline void PostgreRequest::clear_errcode() {
  errcode_ = 0;
}
inline int32_t PostgreRequest::_internal_errcode() const {
  return errcode_;
}
inline int32_t PostgreRequest::errcode() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.PostgreRequest.errCode)
  return _internal_errcode();
}
inline void PostgreRequest::_internal_set_errcode(int32_t value) {
  
  errcode_ = value;
}
inline void PostgreRequest::set_errcode(int32_t value) {
  _internal_set_errcode(value);
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.PostgreRequest.errCode)
}

// -------------------------------------------------------------------

// PostgreResponse

// int32 status = 1;
inline void PostgreResponse::clear_status() {
  status_ = 0;
}
inline int32_t PostgreResponse::_internal_status() const {
  return status_;
}
inline int32_t PostgreResponse::status() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.PostgreResponse.status)
  return _internal_status();
}
inline void PostgreResponse::_internal_set_status(int32_t value) {
  
  status_ = value;
}
inline void PostgreResponse::set_status(int32_t value) {
  _internal_set_status(value);
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.PostgreResponse.status)
}

// uint64 start_time = 2;
inline void PostgreResponse::clear_start_time() {
  start_time_ = uint64_t{0u};
}
inline uint64_t PostgreResponse::_internal_start_time() const {
  return start_time_;
}
inline uint64_t PostgreResponse::start_time() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.PostgreResponse.start_time)
  return _internal_start_time();
}
inline void PostgreResponse::_internal_set_start_time(uint64_t value) {
  
  start_time_ = value;
}
inline void PostgreResponse::set_start_time(uint64_t value) {
  _internal_set_start_time(value);
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.PostgreResponse.start_time)
}

// uint64 close_time = 3;
inline void PostgreResponse::clear_close_time() {
  close_time_ = uint64_t{0u};
}
inline uint64_t PostgreResponse::_internal_close_time() const {
  return close_time_;
}
inline uint64_t PostgreResponse::close_time() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.PostgreResponse.close_time)
  return _internal_close_time();
}
inline void PostgreResponse::_internal_set_close_time(uint64_t value) {
  
  close_time_ = value;
}
inline void PostgreResponse::set_close_time(uint64_t value) {
  _internal_set_close_time(value);
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.PostgreResponse.close_time)
}

// int32 row_count = 4;
inline void PostgreResponse::clear_row_count() {
  row_count_ = 0;
}
inline int32_t PostgreResponse::_internal_row_count() const {
  return row_count_;
}
inline int32_t PostgreResponse::row_count() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.PostgreResponse.row_count)
  return _internal_row_count();
}
inline void PostgreResponse::_internal_set_row_count(int32_t value) {
  
  row_count_ = value;
}
inline void PostgreResponse::set_row_count(int32_t value) {
  _internal_set_row_count(value);
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.PostgreResponse.row_count)
}

// .com.quanzhi.audit_core.common.model.ResultSet result_set = 5;
inline bool PostgreResponse::_internal_has_result_set() const {
  return this != internal_default_instance() && result_set_ != nullptr;
}
inline bool PostgreResponse::has_result_set() const {
  return _internal_has_result_set();
}
inline void PostgreResponse::clear_result_set() {
  if (GetArenaForAllocation() == nullptr && result_set_ != nullptr) {
    delete result_set_;
  }
  result_set_ = nullptr;
}
inline const ::com::quanzhi::audit_core::common::model::ResultSet& PostgreResponse::_internal_result_set() const {
  const ::com::quanzhi::audit_core::common::model::ResultSet* p = result_set_;
  return p != nullptr ? *p : reinterpret_cast<const ::com::quanzhi::audit_core::common::model::ResultSet&>(
      ::com::quanzhi::audit_core::common::model::_ResultSet_default_instance_);
}
inline const ::com::quanzhi::audit_core::common::model::ResultSet& PostgreResponse::result_set() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.PostgreResponse.result_set)
  return _internal_result_set();
}
inline void PostgreResponse::unsafe_arena_set_allocated_result_set(
    ::com::quanzhi::audit_core::common::model::ResultSet* result_set) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(result_set_);
  }
  result_set_ = result_set;
  if (result_set) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:com.quanzhi.audit_core.common.model.PostgreResponse.result_set)
}
inline ::com::quanzhi::audit_core::common::model::ResultSet* PostgreResponse::release_result_set() {
  
  ::com::quanzhi::audit_core::common::model::ResultSet* temp = result_set_;
  result_set_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::com::quanzhi::audit_core::common::model::ResultSet* PostgreResponse::unsafe_arena_release_result_set() {
  // @@protoc_insertion_point(field_release:com.quanzhi.audit_core.common.model.PostgreResponse.result_set)
  
  ::com::quanzhi::audit_core::common::model::ResultSet* temp = result_set_;
  result_set_ = nullptr;
  return temp;
}
inline ::com::quanzhi::audit_core::common::model::ResultSet* PostgreResponse::_internal_mutable_result_set() {
  
  if (result_set_ == nullptr) {
    auto* p = CreateMaybeMessage<::com::quanzhi::audit_core::common::model::ResultSet>(GetArenaForAllocation());
    result_set_ = p;
  }
  return result_set_;
}
inline ::com::quanzhi::audit_core::common::model::ResultSet* PostgreResponse::mutable_result_set() {
  ::com::quanzhi::audit_core::common::model::ResultSet* _msg = _internal_mutable_result_set();
  // @@protoc_insertion_point(field_mutable:com.quanzhi.audit_core.common.model.PostgreResponse.result_set)
  return _msg;
}
inline void PostgreResponse::set_allocated_result_set(::com::quanzhi::audit_core::common::model::ResultSet* result_set) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete result_set_;
  }
  if (result_set) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(result_set);
    if (message_arena != submessage_arena) {
      result_set = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, result_set, submessage_arena);
    }
    
  } else {
    
  }
  result_set_ = result_set;
  // @@protoc_insertion_point(field_set_allocated:com.quanzhi.audit_core.common.model.PostgreResponse.result_set)
}

// int32 errCode = 6;
inline void PostgreResponse::clear_errcode() {
  errcode_ = 0;
}
inline int32_t PostgreResponse::_internal_errcode() const {
  return errcode_;
}
inline int32_t PostgreResponse::errcode() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.PostgreResponse.errCode)
  return _internal_errcode();
}
inline void PostgreResponse::_internal_set_errcode(int32_t value) {
  
  errcode_ = value;
}
inline void PostgreResponse::set_errcode(int32_t value) {
  _internal_set_errcode(value);
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.PostgreResponse.errCode)
}

// -------------------------------------------------------------------

// ResultSet

// repeated string field_names = 1;
inline int ResultSet::_internal_field_names_size() const {
  return field_names_.size();
}
inline int ResultSet::field_names_size() const {
  return _internal_field_names_size();
}
inline void ResultSet::clear_field_names() {
  field_names_.Clear();
}
inline std::string* ResultSet::add_field_names() {
  std::string* _s = _internal_add_field_names();
  // @@protoc_insertion_point(field_add_mutable:com.quanzhi.audit_core.common.model.ResultSet.field_names)
  return _s;
}
inline const std::string& ResultSet::_internal_field_names(int index) const {
  return field_names_.Get(index);
}
inline const std::string& ResultSet::field_names(int index) const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.ResultSet.field_names)
  return _internal_field_names(index);
}
inline std::string* ResultSet::mutable_field_names(int index) {
  // @@protoc_insertion_point(field_mutable:com.quanzhi.audit_core.common.model.ResultSet.field_names)
  return field_names_.Mutable(index);
}
inline void ResultSet::set_field_names(int index, const std::string& value) {
  field_names_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.ResultSet.field_names)
}
inline void ResultSet::set_field_names(int index, std::string&& value) {
  field_names_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.ResultSet.field_names)
}
inline void ResultSet::set_field_names(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  field_names_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:com.quanzhi.audit_core.common.model.ResultSet.field_names)
}
inline void ResultSet::set_field_names(int index, const char* value, size_t size) {
  field_names_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:com.quanzhi.audit_core.common.model.ResultSet.field_names)
}
inline std::string* ResultSet::_internal_add_field_names() {
  return field_names_.Add();
}
inline void ResultSet::add_field_names(const std::string& value) {
  field_names_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:com.quanzhi.audit_core.common.model.ResultSet.field_names)
}
inline void ResultSet::add_field_names(std::string&& value) {
  field_names_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:com.quanzhi.audit_core.common.model.ResultSet.field_names)
}
inline void ResultSet::add_field_names(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  field_names_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:com.quanzhi.audit_core.common.model.ResultSet.field_names)
}
inline void ResultSet::add_field_names(const char* value, size_t size) {
  field_names_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:com.quanzhi.audit_core.common.model.ResultSet.field_names)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
ResultSet::field_names() const {
  // @@protoc_insertion_point(field_list:com.quanzhi.audit_core.common.model.ResultSet.field_names)
  return field_names_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
ResultSet::mutable_field_names() {
  // @@protoc_insertion_point(field_mutable_list:com.quanzhi.audit_core.common.model.ResultSet.field_names)
  return &field_names_;
}

// repeated .com.quanzhi.audit_core.common.model.ResultRow rows = 2;
inline int ResultSet::_internal_rows_size() const {
  return rows_.size();
}
inline int ResultSet::rows_size() const {
  return _internal_rows_size();
}
inline void ResultSet::clear_rows() {
  rows_.Clear();
}
inline ::com::quanzhi::audit_core::common::model::ResultRow* ResultSet::mutable_rows(int index) {
  // @@protoc_insertion_point(field_mutable:com.quanzhi.audit_core.common.model.ResultSet.rows)
  return rows_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::com::quanzhi::audit_core::common::model::ResultRow >*
ResultSet::mutable_rows() {
  // @@protoc_insertion_point(field_mutable_list:com.quanzhi.audit_core.common.model.ResultSet.rows)
  return &rows_;
}
inline const ::com::quanzhi::audit_core::common::model::ResultRow& ResultSet::_internal_rows(int index) const {
  return rows_.Get(index);
}
inline const ::com::quanzhi::audit_core::common::model::ResultRow& ResultSet::rows(int index) const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.ResultSet.rows)
  return _internal_rows(index);
}
inline ::com::quanzhi::audit_core::common::model::ResultRow* ResultSet::_internal_add_rows() {
  return rows_.Add();
}
inline ::com::quanzhi::audit_core::common::model::ResultRow* ResultSet::add_rows() {
  ::com::quanzhi::audit_core::common::model::ResultRow* _add = _internal_add_rows();
  // @@protoc_insertion_point(field_add:com.quanzhi.audit_core.common.model.ResultSet.rows)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::com::quanzhi::audit_core::common::model::ResultRow >&
ResultSet::rows() const {
  // @@protoc_insertion_point(field_list:com.quanzhi.audit_core.common.model.ResultSet.rows)
  return rows_;
}

// int32 field_count = 3;
inline void ResultSet::clear_field_count() {
  field_count_ = 0;
}
inline int32_t ResultSet::_internal_field_count() const {
  return field_count_;
}
inline int32_t ResultSet::field_count() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.ResultSet.field_count)
  return _internal_field_count();
}
inline void ResultSet::_internal_set_field_count(int32_t value) {
  
  field_count_ = value;
}
inline void ResultSet::set_field_count(int32_t value) {
  _internal_set_field_count(value);
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.ResultSet.field_count)
}

// -------------------------------------------------------------------

// ResultRow

// repeated string field_values = 1;
inline int ResultRow::_internal_field_values_size() const {
  return field_values_.size();
}
inline int ResultRow::field_values_size() const {
  return _internal_field_values_size();
}
inline void ResultRow::clear_field_values() {
  field_values_.Clear();
}
inline std::string* ResultRow::add_field_values() {
  std::string* _s = _internal_add_field_values();
  // @@protoc_insertion_point(field_add_mutable:com.quanzhi.audit_core.common.model.ResultRow.field_values)
  return _s;
}
inline const std::string& ResultRow::_internal_field_values(int index) const {
  return field_values_.Get(index);
}
inline const std::string& ResultRow::field_values(int index) const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.ResultRow.field_values)
  return _internal_field_values(index);
}
inline std::string* ResultRow::mutable_field_values(int index) {
  // @@protoc_insertion_point(field_mutable:com.quanzhi.audit_core.common.model.ResultRow.field_values)
  return field_values_.Mutable(index);
}
inline void ResultRow::set_field_values(int index, const std::string& value) {
  field_values_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.ResultRow.field_values)
}
inline void ResultRow::set_field_values(int index, std::string&& value) {
  field_values_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.ResultRow.field_values)
}
inline void ResultRow::set_field_values(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  field_values_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:com.quanzhi.audit_core.common.model.ResultRow.field_values)
}
inline void ResultRow::set_field_values(int index, const char* value, size_t size) {
  field_values_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:com.quanzhi.audit_core.common.model.ResultRow.field_values)
}
inline std::string* ResultRow::_internal_add_field_values() {
  return field_values_.Add();
}
inline void ResultRow::add_field_values(const std::string& value) {
  field_values_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:com.quanzhi.audit_core.common.model.ResultRow.field_values)
}
inline void ResultRow::add_field_values(std::string&& value) {
  field_values_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:com.quanzhi.audit_core.common.model.ResultRow.field_values)
}
inline void ResultRow::add_field_values(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  field_values_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:com.quanzhi.audit_core.common.model.ResultRow.field_values)
}
inline void ResultRow::add_field_values(const char* value, size_t size) {
  field_values_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:com.quanzhi.audit_core.common.model.ResultRow.field_values)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
ResultRow::field_values() const {
  // @@protoc_insertion_point(field_list:com.quanzhi.audit_core.common.model.ResultRow.field_values)
  return field_values_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
ResultRow::mutable_field_values() {
  // @@protoc_insertion_point(field_mutable_list:com.quanzhi.audit_core.common.model.ResultRow.field_values)
  return &field_values_;
}

// -------------------------------------------------------------------

// Net

// string srcIp = 1;
inline void Net::clear_srcip() {
  srcip_.ClearToEmpty();
}
inline const std::string& Net::srcip() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.Net.srcIp)
  return _internal_srcip();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Net::set_srcip(ArgT0&& arg0, ArgT... args) {
 
 srcip_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.Net.srcIp)
}
inline std::string* Net::mutable_srcip() {
  std::string* _s = _internal_mutable_srcip();
  // @@protoc_insertion_point(field_mutable:com.quanzhi.audit_core.common.model.Net.srcIp)
  return _s;
}
inline const std::string& Net::_internal_srcip() const {
  return srcip_.Get();
}
inline void Net::_internal_set_srcip(const std::string& value) {
  
  srcip_.Set(value, GetArenaForAllocation());
}
inline std::string* Net::_internal_mutable_srcip() {
  
  return srcip_.Mutable(GetArenaForAllocation());
}
inline std::string* Net::release_srcip() {
  // @@protoc_insertion_point(field_release:com.quanzhi.audit_core.common.model.Net.srcIp)
  return srcip_.Release();
}
inline void Net::set_allocated_srcip(std::string* srcip) {
  if (srcip != nullptr) {
    
  } else {
    
  }
  srcip_.SetAllocated(srcip, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (srcip_.IsDefault()) {
    srcip_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:com.quanzhi.audit_core.common.model.Net.srcIp)
}

// int32 srcPort = 2;
inline void Net::clear_srcport() {
  srcport_ = 0;
}
inline int32_t Net::_internal_srcport() const {
  return srcport_;
}
inline int32_t Net::srcport() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.Net.srcPort)
  return _internal_srcport();
}
inline void Net::_internal_set_srcport(int32_t value) {
  
  srcport_ = value;
}
inline void Net::set_srcport(int32_t value) {
  _internal_set_srcport(value);
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.Net.srcPort)
}

// string dstIp = 3;
inline void Net::clear_dstip() {
  dstip_.ClearToEmpty();
}
inline const std::string& Net::dstip() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.Net.dstIp)
  return _internal_dstip();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Net::set_dstip(ArgT0&& arg0, ArgT... args) {
 
 dstip_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.Net.dstIp)
}
inline std::string* Net::mutable_dstip() {
  std::string* _s = _internal_mutable_dstip();
  // @@protoc_insertion_point(field_mutable:com.quanzhi.audit_core.common.model.Net.dstIp)
  return _s;
}
inline const std::string& Net::_internal_dstip() const {
  return dstip_.Get();
}
inline void Net::_internal_set_dstip(const std::string& value) {
  
  dstip_.Set(value, GetArenaForAllocation());
}
inline std::string* Net::_internal_mutable_dstip() {
  
  return dstip_.Mutable(GetArenaForAllocation());
}
inline std::string* Net::release_dstip() {
  // @@protoc_insertion_point(field_release:com.quanzhi.audit_core.common.model.Net.dstIp)
  return dstip_.Release();
}
inline void Net::set_allocated_dstip(std::string* dstip) {
  if (dstip != nullptr) {
    
  } else {
    
  }
  dstip_.SetAllocated(dstip, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (dstip_.IsDefault()) {
    dstip_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:com.quanzhi.audit_core.common.model.Net.dstIp)
}

// int32 dstPort = 4;
inline void Net::clear_dstport() {
  dstport_ = 0;
}
inline int32_t Net::_internal_dstport() const {
  return dstport_;
}
inline int32_t Net::dstport() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.Net.dstPort)
  return _internal_dstport();
}
inline void Net::_internal_set_dstport(int32_t value) {
  
  dstport_ = value;
}
inline void Net::set_dstport(int32_t value) {
  _internal_set_dstport(value);
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.Net.dstPort)
}

// string flowSource = 5;
inline void Net::clear_flowsource() {
  flowsource_.ClearToEmpty();
}
inline const std::string& Net::flowsource() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.Net.flowSource)
  return _internal_flowsource();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Net::set_flowsource(ArgT0&& arg0, ArgT... args) {
 
 flowsource_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.Net.flowSource)
}
inline std::string* Net::mutable_flowsource() {
  std::string* _s = _internal_mutable_flowsource();
  // @@protoc_insertion_point(field_mutable:com.quanzhi.audit_core.common.model.Net.flowSource)
  return _s;
}
inline const std::string& Net::_internal_flowsource() const {
  return flowsource_.Get();
}
inline void Net::_internal_set_flowsource(const std::string& value) {
  
  flowsource_.Set(value, GetArenaForAllocation());
}
inline std::string* Net::_internal_mutable_flowsource() {
  
  return flowsource_.Mutable(GetArenaForAllocation());
}
inline std::string* Net::release_flowsource() {
  // @@protoc_insertion_point(field_release:com.quanzhi.audit_core.common.model.Net.flowSource)
  return flowsource_.Release();
}
inline void Net::set_allocated_flowsource(std::string* flowsource) {
  if (flowsource != nullptr) {
    
  } else {
    
  }
  flowsource_.SetAllocated(flowsource, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (flowsource_.IsDefault()) {
    flowsource_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:com.quanzhi.audit_core.common.model.Net.flowSource)
}

// -------------------------------------------------------------------

// Mac

// string mac = 1;
inline void Mac::clear_mac() {
  mac_.ClearToEmpty();
}
inline const std::string& Mac::mac() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.Mac.mac)
  return _internal_mac();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Mac::set_mac(ArgT0&& arg0, ArgT... args) {
 
 mac_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.Mac.mac)
}
inline std::string* Mac::mutable_mac() {
  std::string* _s = _internal_mutable_mac();
  // @@protoc_insertion_point(field_mutable:com.quanzhi.audit_core.common.model.Mac.mac)
  return _s;
}
inline const std::string& Mac::_internal_mac() const {
  return mac_.Get();
}
inline void Mac::_internal_set_mac(const std::string& value) {
  
  mac_.Set(value, GetArenaForAllocation());
}
inline std::string* Mac::_internal_mutable_mac() {
  
  return mac_.Mutable(GetArenaForAllocation());
}
inline std::string* Mac::release_mac() {
  // @@protoc_insertion_point(field_release:com.quanzhi.audit_core.common.model.Mac.mac)
  return mac_.Release();
}
inline void Mac::set_allocated_mac(std::string* mac) {
  if (mac != nullptr) {
    
  } else {
    
  }
  mac_.SetAllocated(mac, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (mac_.IsDefault()) {
    mac_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:com.quanzhi.audit_core.common.model.Mac.mac)
}

// -------------------------------------------------------------------

// Source

// .com.quanzhi.audit_core.common.model.SourceTypeEnum sourceType = 1;
inline void Source::clear_sourcetype() {
  sourcetype_ = 0;
}
inline ::com::quanzhi::audit_core::common::model::SourceTypeEnum Source::_internal_sourcetype() const {
  return static_cast< ::com::quanzhi::audit_core::common::model::SourceTypeEnum >(sourcetype_);
}
inline ::com::quanzhi::audit_core::common::model::SourceTypeEnum Source::sourcetype() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.Source.sourceType)
  return _internal_sourcetype();
}
inline void Source::_internal_set_sourcetype(::com::quanzhi::audit_core::common::model::SourceTypeEnum value) {
  
  sourcetype_ = value;
}
inline void Source::set_sourcetype(::com::quanzhi::audit_core::common::model::SourceTypeEnum value) {
  _internal_set_sourcetype(value);
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.Source.sourceType)
}

// string taskId = 2;
inline void Source::clear_taskid() {
  taskid_.ClearToEmpty();
}
inline const std::string& Source::taskid() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.Source.taskId)
  return _internal_taskid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Source::set_taskid(ArgT0&& arg0, ArgT... args) {
 
 taskid_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.Source.taskId)
}
inline std::string* Source::mutable_taskid() {
  std::string* _s = _internal_mutable_taskid();
  // @@protoc_insertion_point(field_mutable:com.quanzhi.audit_core.common.model.Source.taskId)
  return _s;
}
inline const std::string& Source::_internal_taskid() const {
  return taskid_.Get();
}
inline void Source::_internal_set_taskid(const std::string& value) {
  
  taskid_.Set(value, GetArenaForAllocation());
}
inline std::string* Source::_internal_mutable_taskid() {
  
  return taskid_.Mutable(GetArenaForAllocation());
}
inline std::string* Source::release_taskid() {
  // @@protoc_insertion_point(field_release:com.quanzhi.audit_core.common.model.Source.taskId)
  return taskid_.Release();
}
inline void Source::set_allocated_taskid(std::string* taskid) {
  if (taskid != nullptr) {
    
  } else {
    
  }
  taskid_.SetAllocated(taskid, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (taskid_.IsDefault()) {
    taskid_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:com.quanzhi.audit_core.common.model.Source.taskId)
}

// string app = 3;
inline void Source::clear_app() {
  app_.ClearToEmpty();
}
inline const std::string& Source::app() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.Source.app)
  return _internal_app();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Source::set_app(ArgT0&& arg0, ArgT... args) {
 
 app_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.Source.app)
}
inline std::string* Source::mutable_app() {
  std::string* _s = _internal_mutable_app();
  // @@protoc_insertion_point(field_mutable:com.quanzhi.audit_core.common.model.Source.app)
  return _s;
}
inline const std::string& Source::_internal_app() const {
  return app_.Get();
}
inline void Source::_internal_set_app(const std::string& value) {
  
  app_.Set(value, GetArenaForAllocation());
}
inline std::string* Source::_internal_mutable_app() {
  
  return app_.Mutable(GetArenaForAllocation());
}
inline std::string* Source::release_app() {
  // @@protoc_insertion_point(field_release:com.quanzhi.audit_core.common.model.Source.app)
  return app_.Release();
}
inline void Source::set_allocated_app(std::string* app) {
  if (app != nullptr) {
    
  } else {
    
  }
  app_.SetAllocated(app, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (app_.IsDefault()) {
    app_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:com.quanzhi.audit_core.common.model.Source.app)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace common
}  // namespace audit_core
}  // namespace quanzhi
}  // namespace com

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::com::quanzhi::audit_core::common::model::SourceTypeEnum> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::com::quanzhi::audit_core::common::model::SourceTypeEnum>() {
  return ::com::quanzhi::audit_core::common::model::SourceTypeEnum_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_ProtobufRawPostgreEvent_2eproto
