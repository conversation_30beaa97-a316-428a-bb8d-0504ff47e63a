#include <stdio.h>
#include <string.h>
#include <stdlib.h>

#include "postgre_parser.h"
#include "gw_common.h"
#include "gw_logger.h"
#include "gw_config.h"
#include "session_mgt.h"
#include "session.h"
#include "gw_stats.h"
#include "display_stats_define.h"

#include "worker_queue.h"
#include "utils.h"
#include "minio_upload.h"
#include "nacos_listen_conf.h"

#define POSTGRE_PARSER_TOTAL "postgreSQL"
#define POSTGRE_PARSER_REQ_FAILED "postgreSQL request failed"
#define POSTGRE_PARSER_RSP_FAILED "postgreSQL response failed"
#define POSTGRE_PARSER_BYTES "postgreSQL parser bytes"
#define POSTGRE_MATCH_SUC_BYTES "suc bytes"

CPostgreParser::CPostgreParser(void) : m_quit_signal(0)
                             , m_comm(NULL)
                             , m_name{0}
                             , m_u64_postgre_upload_ms(0)
                             , m_u32_postgre_upload_index(0)
                             , m_str_gw_ip("127.0.0.1")
                             , m_conf_upload_name("log")
                             , m_stats_postgre{0}
                             , m_postgre_type(14)
                             , m_upload(NULL)
                             , m_upload_file(false)
                             , m_upload_data_wq(NULL)
                             , m_upload_data_wk(NULL)
                             , m_conf_postgre_upload_queue_max_num(1000)
                             , m_conf_postgre_upload_queue_memory_max_size_bytes(100 * 1024 * 1024UL)
                             , m_conf_postgre_upload_thread_num(1)
                             , m_conf_parser_enable(1)
                             , m_conf_postgre_probe_cnt(3)
                             , m_binary_handlers(NULL)
                             , m_use_binary_extended(true)
                             , m_agent_client_ip_file_path("/opt/qzkj_agent_server/tmp/client_ip.txt")
                             , m_conf_pcap_timestamp(1)
                             , m_upload_results_num(100)
                             , m_upload_field_max_size(8192)  // 默认最大字段值为8KB
                             , m_upload_protobuf_enable(1)    // 默认使用protobuf格式
{
    snprintf(m_name, COUNTOF(m_name) - 1, "CPostgreParser-%" PRIu64 "", ((uint64_t)this) & 0xffff);
    
    // 初始化二进制数据类型处理器
    m_binary_handlers = init_binary_handlers();
}

CPostgreParser::~CPostgreParser(void)
{
    if (m_upload_data_wq) {
        delete m_upload_data_wq;
        m_upload_data_wq = NULL;
    }
    if (m_upload_data_wk) {
        // 安全删除抽象基类指针，避免虚析构函数警告
        CTaskWorker *worker = m_upload_data_wk;
        m_upload_data_wk = NULL;
        delete worker;
    }
    
    // 清理二进制数据类型处理器
    cleanup_binary_handlers(m_binary_handlers);
    m_binary_handlers = NULL;
}

void CPostgreParser::modify_stats(int enable) {
    GWLOG_INFO(m_comm, "CPostgreParser modify stats: %d, parser_enable: %d\n", enable, m_conf_parser_enable);
    if (enable > 1) 
        enable = 1;
    if (__sync_bool_compare_and_swap(&m_conf_parser_enable, enable^1, enable)) {
        GWLOG_DEBUG(m_comm, "CPostgreParser modify true\n");
        cache_clean();
    }
    GWLOG_DEBUG(m_comm, "CPostgreParser after modify stats: %d, parser_enable: %d\n", enable, m_conf_parser_enable);
}

void CPostgreParser::cache_clean() 
{
    if (m_upload_data_wq)
    {
        m_upload_data_wq->flush_queue();
    }
}

uint32_t CPostgreParser::parser_status() const
{
    if (m_upload_data_wq && m_upload_data_wq->queue_elements_num())
    {
        return 1;
    }
    return 0;
}

const char *CPostgreParser::get_data(const struct StreamData *psd, int dir, int *data_len, int *offset_out)
{
    *data_len = 0;
    *offset_out = 0;
    return NULL;
}

bool CPostgreParser::discard(struct StreamData *psd, int dir, int num)
{
    return false;
}

bool CPostgreParser::discard_and_update(struct StreamData *psd, int dir, int num)
{
    return false;
}

void CPostgreParser::del_session_stream(StreamData *psd)
{
    if (psd) {
        postgre_stream_t *p_stream = psd->p_postgre_stream;
        if (p_stream)
        {
            postgre_half_stream_t *p_phs;

            // 清理服务端响应节点链表
            p_phs = p_stream->p_postgre_server;
            while(p_phs)
            {
                postgre_half_stream_t *next = p_phs->next;
                del_postgre_merged_data(&p_phs->data);
                delete p_phs;
                p_phs = next;
            }
            p_stream->p_postgre_server = NULL;
            p_stream->p_postgre_server_last = NULL;

            // 清理客户端请求节点链表
            p_phs = p_stream->p_postgre_client;
            while(p_phs)
            {
                postgre_half_stream_t *next = p_phs->next;
                del_postgre_merged_data(&p_phs->data);
                delete p_phs;
                p_phs = next;
            }
            p_stream->p_postgre_client = NULL;
            p_stream->p_postgre_client_last = NULL;

            // 清理prepared statements
            cleanup_prepared_statements(p_stream);

            // 清理portals
            cleanup_portals(p_stream);

            // 清理认证上下文
            if (p_stream->auth_context) {
                delete p_stream->auth_context;
                p_stream->auth_context = NULL;
            }

            // 清理COPY上下文
            cleanup_copy_context(p_stream);

            // 清理连接状态信息
            if (p_stream->pg_stat.user.s)
            {
                free((void*)p_stream->pg_stat.user.s);
            }
            if (p_stream->pg_stat.db_name.s)
            {
                free((void*)p_stream->pg_stat.db_name.s);
            }
            if (p_stream->pg_stat.application_name.s)
            {
                free((void*)p_stream->pg_stat.application_name.s);
            }
            if (p_stream->pg_stat.server_version.s)
            {
                free((void*)p_stream->pg_stat.server_version.s);
            }
            if (p_stream->pg_stat.password.s)
            {
                free((void*)p_stream->pg_stat.password.s);
            }

            delete p_stream;
        }
        delete psd;
    }
}

void CPostgreParser::del_session_param(SessionMgtData *psmd)
{
}

void CPostgreParser::init()
{
    ASSERT(m_comm != NULL);
    m_quit_signal = 0;
    load_conf(NULL);

    m_comm->get_gw_stats()->set_stats_callback(POSTGRE_SHOW, print_postgre_stats_callback, this);
    m_comm->get_gw_stats()->set_qps(POSTGRE_SESSION_QPS, &m_stats_postgre.cnt_session, 90);

    // 设置主解析统计 - 使用解析总数作为主统计
    m_comm->get_gw_stats()->set_stats(POSTGRE_PARSER_TOTAL, NULL, &(m_stats_postgre.p.cnt_p), 60);
    m_comm->get_gw_stats()->set_stats(POSTGRE_PARSER_TOTAL, POSTGRE_PARSER_REQ_FAILED, &(m_stats_postgre.req.cnt_p_fail));
    m_comm->get_gw_stats()->set_stats(POSTGRE_PARSER_TOTAL, POSTGRE_PARSER_RSP_FAILED, &(m_stats_postgre.rsp.cnt_p_fail));

    // 设置字节统计 - 使用解析字节总数作为主统计，成功字节数作为分支
    m_comm->get_gw_stats()->set_byte_stats(POSTGRE_PARSER_BYTES, NULL, &(m_stats_postgre.p.cnt_p_bytes));
    m_comm->get_gw_stats()->set_byte_stats(POSTGRE_PARSER_BYTES, POSTGRE_MATCH_SUC_BYTES, &(m_stats_postgre.cnt_session_bytes));

    // 初始化读写锁
    if (pthread_rwlock_init(&m_traffic_source_rwlock, NULL) != 0) {
        GWLOG_ERROR(m_comm, "init traffic source rw lock failed!\n");
    }
}

void CPostgreParser::fini()
{
    if (m_upload_data_wq) {
        m_upload_data_wq->flush_queue();
    }
    pthread_rwlock_destroy(&m_traffic_source_rwlock);
}

void CPostgreParser::run()
{
}

const char *CPostgreParser::get_name(void) const
{
    return m_name;
}

const char *CPostgreParser::get_version(void) const
{
    return POSTGREPARSER_VER;
}   

void CPostgreParser::set_gw_common(CGwCommon *comm)
{
    ASSERT(comm != NULL);
    m_comm = comm;
}

bool CPostgreParser::load_conf(const char *)
{
    CGwConfig *pgwc = m_comm->get_gw_config();
    std::string str_upload_name = pgwc->read_conf_string("parser", "upload_mode");
    if (str_upload_name.size() > 0)
    {
        m_conf_upload_name = str_upload_name;
    }

    m_conf_pcap_timestamp = pgwc->read_conf_int("parser", "pcap_timestamp", m_conf_pcap_timestamp);

    std::string str_gw_ip = pgwc->read_conf_string("parser", "gw_ip");
    if (str_gw_ip.size() > 0)
    {
        m_str_gw_ip = str_gw_ip;
    }

    m_upload = m_comm->get_upload_from_parser(this, m_conf_upload_name.c_str());
    if (m_upload == NULL)
    {
        GWLOG_ERROR(m_comm, "upload null(%s)\n", m_conf_upload_name.c_str());
    }
    
    m_conf_postgre_upload_queue_max_num = pgwc->read_conf_int("parser", "postgre_upload_queue_num", m_conf_postgre_upload_queue_max_num);
    m_conf_postgre_upload_queue_memory_max_size_bytes = pgwc->read_conf_int("parser", "postgre_upload_queue_memory_size", 
                                                                             m_conf_postgre_upload_queue_memory_max_size_bytes / (1 << 20ULL)) * (1 << 20ULL);
    m_conf_postgre_upload_thread_num = pgwc->read_conf_int("parser", "postgre_upload_thread_num", m_conf_postgre_upload_thread_num);
    
    // 读取二进制数据处理相关配置
    m_use_binary_extended = pgwc->read_conf_int("parser", "postgre_use_binary_extended", 1) > 0;
    GWLOG_INFO(m_comm, "PostgreSQL binary data handling: %s\n", m_use_binary_extended ? "enabled" : "disabled");

    // 读取限制数据结果集的上传数量
    m_upload_results_num = pgwc->read_conf_int("parser", "postgre_upload_results_num", m_upload_results_num);
    GWLOG_INFO(m_comm, "PostgreSQL upload results numbers: %d\n", m_upload_results_num);

    // 读取限制单个字段值的最大字节数
    m_upload_field_max_size = pgwc->read_conf_int("parser", "postgre_upload_field_max_size", m_upload_field_max_size);
    GWLOG_INFO(m_comm, "PostgreSQL upload field max size: %d bytes\n", m_upload_field_max_size);

    // 读取protobuf上传格式配置
    m_upload_protobuf_enable = pgwc->read_conf_int("parser", "postgre_upload_protobuf_enable", 1);
    GWLOG_INFO(m_comm, "PostgreSQL protobuf upload: %s\n", m_upload_protobuf_enable ? "enabled" : "disabled");

    m_agent_client_ip_file_path = pgwc->read_conf_string("parser", "agent_client_ip_file_path");
    FILE* fp = fopen(m_agent_client_ip_file_path.c_str(), "r");
    if (NULL != fp)
    {
      char buf[32] = {0};
      char* sign = NULL;
      while (fgets(buf, sizeof(buf), fp) != NULL)
      {
        if ((sign = strchr(buf, '\n')) != NULL)
        {
          *sign = 0;
        }

        GWLOG_INFO(m_comm, "get agent ip=%s\n", buf);

        m_agent_client_ip.insert(buf);
      }

      fclose(fp);
    }
    
    new_wq_upload_msg();
    return true;
}

void CPostgreParser::set_quit_signal(void)
{
    m_quit_signal = 1;
}

void CPostgreParser::wait_for_stop(void)
{
    if (m_upload_data_wq) {
        m_upload_data_wq->flush_queue();
    }
}

void CPostgreParser::set_url_filter_rule(CFilterRule *rule)
{
}

void CPostgreParser::set_accout_filter_rule(CFilterRule *rule)
{
}

void CPostgreParser::set_upload_filter_rule(CFilterRule *client_rule, CFilterRule *server_rule)
{
}

void CPostgreParser::add_upstream(CParser *parser)
{
}

void CPostgreParser::reset_upstream(void)
{
}

void CPostgreParser::push_upstream_msg(char *s, size_t length)
{
}

bool CPostgreParser::is_parsed(const struct StreamData *) const
{
    return true;
}

struct StreamData *CPostgreParser::clone_stream_data(const struct StreamData *)
{
    return NULL;
}

uint64_t CPostgreParser::get_parser_http_cnt()
{
    return 0;
}

uint64_t CPostgreParser::get_succ_parser_http_cnt()
{
    return 0;
}

void* CPostgreParser::get_parser_status()
{
    return NULL;
}

void CPostgreParser::set_parser_type(int type)
{
    m_postgre_type = type;
}

void CPostgreParser::read_conf_urlbase_for_mon()
{
    std::unordered_set<std::string> traffic_source;
    FILE* fp = fopen(m_agent_client_ip_file_path.c_str(), "r");
    if (NULL != fp)
    {
        char buf[32] = {0};
        char* sign = NULL;
        while (fgets(buf, sizeof(buf), fp) != NULL)
        {
            if ((sign = strchr(buf, '\n')) != NULL)
            {
                *sign = 0;
            }

            GWLOG_INFO(m_comm, "get agent ip=%s\n", buf);

            traffic_source.insert(buf);
        }

        fclose(fp);
    }

    pthread_rwlock_wrlock(&m_traffic_source_rwlock);
    std::unordered_set<std::string>().swap(m_agent_client_ip);
    m_agent_client_ip = std::move(traffic_source);
    pthread_rwlock_unlock(&m_traffic_source_rwlock);
}

void CPostgreParser::read_conf_filetype_for_mon()
{
}

void CPostgreParser::get_log_buf(char *log_buf, size_t log_buf_len) const
{
    /*
    stats postgre                 total      success      failure
    postgre session:                  0
    postgre parser:                   0            0            0
    postgre request parser:           0            0            0
    postgre reponse parser:           0            0            0
    postgre match:                    0            0            0
    postgre request match:            0            0            0
    postgre reponse match:            0            0            0
    */

    printf("\n%-21s %12s %12s %12s\n", "stats postgre ", "total", "success", "failure");
    printf("postgre session:        %12" PRIu64 "\n",
           m_stats_postgre.cnt_session);

    printf("postgre parser:         %12" PRIu64 " %12" PRIu64 " %12" PRIu64 "\n",
           m_stats_postgre.p.cnt_p,
           m_stats_postgre.p.cnt_p_succ,
           m_stats_postgre.p.cnt_p_fail);
        if (unlikely(m_comm->get_verbose()))
    {
      printf("postgre request parser: %12" PRIu64 " %12" PRIu64 " %12" PRIu64 "\n",
             m_stats_postgre.req.cnt_p,
             m_stats_postgre.req.cnt_p_succ,
             m_stats_postgre.req.cnt_p_fail);
      printf("postgre reponse parser: %12" PRIu64 " %12" PRIu64 " %12" PRIu64 "\n",
             m_stats_postgre.rsp.cnt_p,
             m_stats_postgre.rsp.cnt_p_succ,
             m_stats_postgre.rsp.cnt_p_fail);
    }

    printf("postgre match:          %12" PRIu64 " %12" PRIu64 " %12" PRIu64 "\n",
           m_stats_postgre.m.cnt_m,
           m_stats_postgre.m.cnt_m_succ,
           m_stats_postgre.m.cnt_m_fail);
    if (unlikely(m_comm->get_verbose()))
    {
      printf("postgre request match:  %12" PRIu64 " %12" PRIu64 " %12" PRIu64 "\n",
             m_stats_postgre.req_match_rsp.cnt_m,
             m_stats_postgre.req_match_rsp.cnt_m_succ,
             m_stats_postgre.req_match_rsp.cnt_m_fail);
      printf("postgre reponse match:  %12" PRIu64 " %12" PRIu64 " %12" PRIu64 "\n",
             m_stats_postgre.rsp_match_req.cnt_m,
             m_stats_postgre.rsp_match_req.cnt_m_succ,
             m_stats_postgre.rsp_match_req.cnt_m_fail);
    }
}

void CPostgreParser::print_postgre_stats_callback(void *p)
{
    ASSERT(p != NULL);
    CPostgreParser *pThis = (CPostgreParser*)p;
    pThis->print_postgre_stats();
}

void CPostgreParser::print_postgre_stats(void) const
{
    char log_buf[LOG_BUF_LEN] = {0};
    get_log_buf(log_buf, LOG_BUF_LEN);
    printf("%s", log_buf);
}