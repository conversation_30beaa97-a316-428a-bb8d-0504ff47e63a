/**
 * Project gw-hw
 * <AUTHOR> Assistant
 * @version 1.0.0
 */

#ifndef __POSTGRE_PARSER_COMMON_H__
#define __POSTGRE_PARSER_COMMON_H__

#include <stdlib.h>
#include <inttypes.h>
#include <stdbool.h>

// 返回字段值的最大长度
#define MAX_COL_VAL_LEN 1024

#define POSTGRE_LOG_PRE   "[PostgreSQL]"

// PostgreSQL Protocol Version
#define POSTGRE_PROTOCOL_VERSION_3                0x00030000

// SSL请求码
#define POSTGRE_SSL_REQUEST_CODE                 0x04D2162F

// GSSAPI加密请求码
#define POSTGRE_GSSENC_REQUEST_CODE              0x04D22162

// PostgreSQL默认端口
#define POSTGRE_DEFAULT_PORT 5432

// PostgreSQL消息头长度(不包含消息类型字节)
#define POSTGRE_MSG_HEADER_LEN 4

// PostgreSQL startup消息最小长度
#define POSTGRE_STARTUP_MIN_LEN 8

// PostgreSQL normal消息最小长度
#define POSTGRE_NORMAL_MIN_LEN 5

// SSLRequest/GSSENCRequest消息长度(固定为8字节)
#define POSTGRE_SSL_REQUEST_LEN 8

// 获取消息长度(不包含消息类型字节)
#define GET_MSG_LEN(buf) (ntohl(*((uint32_t*)(buf))))

enum pgsql_parser_type { PGSQL_REQUEST, PGSQL_RESPONSE, PGSQL_BOTH };

// Backend Message Types (first byte)
#define POSTGRE_MSG_AUTHENTICATION                'R'
#define POSTGRE_MSG_ERROR_RESPONSE               'E'
#define POSTGRE_MSG_NOTICE_RESPONSE              'N'
#define POSTGRE_MSG_NOTIFICATION_RESPONSE        'A'
#define POSTGRE_MSG_COMMAND_COMPLETE            'C'
#define POSTGRE_MSG_PARAMETER_STATUS            'S'
#define POSTGRE_MSG_READY_FOR_QUERY            'Z'
#define POSTGRE_MSG_ROW_DESCRIPTION            'T'
#define POSTGRE_MSG_DATA_ROW                   'D'
#define POSTGRE_MSG_COPY_IN_RESPONSE           'G'
#define POSTGRE_MSG_COPY_OUT_RESPONSE          'H'
#define POSTGRE_MSG_COPY_BOTH_RESPONSE         'W'
#define POSTGRE_MSG_COPY_DATA                  'd'
#define POSTGRE_MSG_COPY_DONE                  'c'
#define POSTGRE_MSG_PARSE_COMPLETE             '1'
#define POSTGRE_MSG_BIND_COMPLETE              '2'
#define POSTGRE_MSG_CLOSE_COMPLETE             '3'
#define POSTGRE_MSG_PORTAL_SUSPENDED           's'
#define POSTGRE_MSG_NO_DATA                    'n'
#define POSTGRE_MSG_PARAMETER_DESCRIPTION      't'
#define POSTGRE_MSG_BACKEND_KEY_DATA           'K'
#define POSTGRE_MSG_EMPTY_QUERY_RESPONSE       'I'
#define POSTGRE_MSG_FUNCTION_CALL_RESPONSE      'V'
#define POSTGRE_MSG_NEGOTIATE_PROTOCOL_VERSION  'v' // 协议版本协商(PostgreSQL 10+)
#define POSTGRE_MSG_SSL_RESPONSE                'S' // SSL响应(单字节'S'表示允许SSL，'N'表示不允许)
#define POSTGRE_MSG_GSSENC_RESPONSE             'E' // GSSENC响应(单字节'E'表示允许GSSAPI加密)

// Frontend Message Types
#define POSTGRE_MSG_STARTUP                    '\0'
#define POSTGRE_MSG_QUERY                      'Q'
#define POSTGRE_MSG_TERMINATE                  'X'
#define POSTGRE_MSG_PARSE                      'P'
#define POSTGRE_MSG_BIND                       'B'
#define POSTGRE_MSG_DESCRIBE                   'D'
#define POSTGRE_MSG_EXECUTE                    'E'
#define POSTGRE_MSG_SYNC                       'S'
#define POSTGRE_MSG_CLOSE                      'C'
#define POSTGRE_MSG_FLUSH                      'H'
#define POSTGRE_MSG_PASSWORD                   'p'
#define POSTGRE_MSG_SASL_CONTINUE               'R'
#define POSTGRE_MSG_FUNCTION_CALL               'F'
#define POSTGRE_MSG_COPY_FAIL                  'f'
#define POSTGRE_MSG_CANCEL_REQUEST             0x1234  // CancelRequest消息类型，这不是一个字符，而是一个特殊的整数标识
#define POSTGRE_MSG_SSL_REQUEST                0x1234  // SSLRequest消息类型(使用专用请求码0x04D2162F识别)
#define POSTGRE_MSG_GSSENC_REQUEST             0x1234  // GSSENCRequest消息类型(使用专用请求码0x04D22162识别)

// 流复制协议消息类型 (物理流复制)
#define POSTGRE_MSG_XLOG_DATA                  'w'    // WAL数据消息 (服务器->客户端)
#define POSTGRE_MSG_PRIMARY_KEEPALIVE          'k'    // 主服务器心跳消息 (服务器->客户端)
#define POSTGRE_MSG_STANDBY_STATUS_UPDATE      'r'    // 备机状态更新消息 (客户端->服务器)
#define POSTGRE_MSG_HOT_STANDBY_FEEDBACK       'h'    // 热备反馈消息 (客户端->服务器)

// 逻辑流复制消息类型
#define LOGICAL_MSG_BEGIN                      'B'    // 事务开始
#define LOGICAL_MSG_COMMIT                     'C'    // 事务提交
#define LOGICAL_MSG_ORIGIN                     'O'    // 源标识
#define LOGICAL_MSG_RELATION                   'R'    // 关系定义
#define LOGICAL_MSG_TYPE                       'Y'    // 类型定义
#define LOGICAL_MSG_INSERT                     'I'    // 插入操作
#define LOGICAL_MSG_UPDATE                     'U'    // 更新操作
#define LOGICAL_MSG_DELETE                     'D'    // 删除操作
#define LOGICAL_MSG_TRUNCATE                   'T'    // 截断操作
#define LOGICAL_MSG_MESSAGE                    'M'    // 逻辑消息
#define LOGICAL_MSG_STREAM_START               'S'    // 流式事务开始
#define LOGICAL_MSG_STREAM_STOP                'E'    // 流式事务停止
#define LOGICAL_MSG_STREAM_COMMIT              'c'    // 流式事务提交
#define LOGICAL_MSG_STREAM_ABORT               'A'    // 流式事务中止

// Authentication Types
#define POSTGRE_AUTH_OK                        0
#define POSTGRE_AUTH_KERBEROS_V5               2
#define POSTGRE_AUTH_CLEARTEXT_PASSWORD        3
#define POSTGRE_AUTH_MD5_PASSWORD              5
#define POSTGRE_AUTH_SCM_CREDENTIAL            6
#define POSTGRE_AUTH_GSS                       7
#define POSTGRE_AUTH_GSS_CONTINUE              8
#define POSTGRE_AUTH_SSPI                      9
#define POSTGRE_AUTH_SASL                      10
#define POSTGRE_AUTH_SASL_CONTINUE             11
#define POSTGRE_AUTH_SASL_FINAL                12

// Transaction Status Indicators
#define POSTGRE_TRANS_IDLE                    'I'
#define POSTGRE_TRANS_IN_TRANSACTION          'T'
#define POSTGRE_TRANS_ERROR                   'E'

// PostgreSQL数据类型OID
#define PG_TYPE_BOOL         16    // boolean, 'true'/'false'
#define PG_TYPE_BYTEA        17    // variable-length binary string
#define PG_TYPE_CHAR         18    // single character
#define PG_TYPE_NAME         19    // name of field
#define PG_TYPE_INT8         20    // ~18 digit integer, 8-byte storage
#define PG_TYPE_INT2         21    // -32 thousand to 32 thousand, 2-byte storage
#define PG_TYPE_INT4         23    // -2 billion to 2 billion integer, 4-byte storage
#define PG_TYPE_TEXT         25    // variable-length string, no limit specified
#define PG_TYPE_OID          26    // object identifier(oid), maximum 4 billion
#define PG_TYPE_JSON         114   // JSON
#define PG_TYPE_XML          142   // XML
#define PG_TYPE_FLOAT4       700   // single-precision floating point number, 4-byte storage
#define PG_TYPE_FLOAT8       701   // double-precision floating point number, 8-byte storage
#define PG_TYPE_VARCHAR      1043  // variable-length with limit
#define PG_TYPE_DATE         1082  // date
#define PG_TYPE_TIME         1083  // time of day without time zone
#define PG_TYPE_TIMESTAMP    1114  // date and time without time zone
#define PG_TYPE_TIMESTAMPTZ  1184  // date and time with time zone
#define PG_TYPE_INTERVAL     1186  // time interval
#define PG_TYPE_NUMERIC      1700  // numeric(precision, decimal), arbitrary precision
#define PG_TYPE_UUID         2950  // UUID datatype
#define PG_TYPE_JSONB        3802  // JSON (binary format)

// 字段格式
#define PG_FORMAT_TEXT       0    // 文本格式
#define PG_FORMAT_BINARY     1    // 二进制格式

// 附加数据类型（根据需要扩展）
// 空间类型
#define PG_TYPE_POINT        600   // 点类型
#define PG_TYPE_LSEG         601   // 线段类型
#define PG_TYPE_PATH         602   // 路径类型
#define PG_TYPE_BOX          603   // 矩形类型
#define PG_TYPE_POLYGON      604   // 多边形类型

// 网络地址类型
#define PG_TYPE_INET         869   // IP地址（IPv4或IPv6）
#define PG_TYPE_CIDR         650   // 网络地址
#define PG_TYPE_MACADDR      829   // MAC地址
#define PG_TYPE_MACADDR8     774   // MAC地址（8字节格式）

// 数组类型（基础类型的数组版本）
#define PG_TYPE_BOOL_ARRAY   1000  // boolean数组
#define PG_TYPE_BYTEA_ARRAY  1001  // bytea数组
#define PG_TYPE_CHAR_ARRAY   1002  // char数组
#define PG_TYPE_NAME_ARRAY   1003  // name数组
#define PG_TYPE_INT8_ARRAY   1016  // int8数组
#define PG_TYPE_INT2_ARRAY   1005  // int2数组
#define PG_TYPE_INT4_ARRAY   1007  // int4数组
#define PG_TYPE_TEXT_ARRAY   1009  // text数组
#define PG_TYPE_OID_ARRAY    1028  // oid数组
#define PG_TYPE_FLOAT4_ARRAY 1021  // float4数组
#define PG_TYPE_FLOAT8_ARRAY 1022  // float8数组
#define PG_TYPE_VARCHAR_ARRAY 1015 // varchar数组
#define PG_TYPE_DATE_ARRAY   1182  // date数组
#define PG_TYPE_TIME_ARRAY   1183  // time数组
#define PG_TYPE_TIMESTAMP_ARRAY 1115 // timestamp数组
#define PG_TYPE_TIMESTAMPTZ_ARRAY 1185 // timestamptz数组
#define PG_TYPE_INTERVAL_ARRAY 1187 // interval数组
#define PG_TYPE_NUMERIC_ARRAY 1231 // numeric数组
#define PG_TYPE_UUID_ARRAY   2951  // uuid数组
#define PG_TYPE_INET_ARRAY   1041  // inet数组
#define PG_TYPE_CIDR_ARRAY   651   // cidr数组
#define PG_TYPE_MACADDR_ARRAY 1040 // macaddr数组

// 枚举类型（动态类型，OID由CREATE TYPE确定）
// 注意：枚举类型的OID是动态分配的，通常从16384开始
// 这里定义一个范围用于识别
#define PG_TYPE_ENUM_MIN     16384 // 用户定义类型的最小OID
#define PG_TYPE_ENUM_MAX     2147483647 // 用户定义类型的最大OID

// parser status
#define PARSER_STATUS_CONTINUE                0       // keep tcp stream offset
#define PARSER_STATUS_FINISH                  1       // set tcp stream offset
#define PARSER_STATUS_DROP_DATA              -1       // drop tcp stream

// COPY操作状态定义
#define COPY_STATE_NONE                       0       // 无COPY操作
#define COPY_STATE_COPY_IN_PENDING            1       // COPY IN等待中（检测到COPY FROM命令）
#define COPY_STATE_COPY_OUT_PENDING           2       // COPY OUT等待中（检测到COPY TO命令）
#define COPY_STATE_COPY_IN                    3       // COPY IN处理中
#define COPY_STATE_COPY_OUT                   4       // COPY OUT处理中
#define COPY_STATE_COPY_BOTH                  5       // 流复制COPY BOTH处理中
#define COPY_STATE_COMPLETING                 6       // COPY完成中，等待COMMAND_COMPLETE
#define COPY_STATE_FAILED                     7       // COPY操作失败（收到COPY_FAIL消息）

// COPY操作上下文结构体
typedef struct copy_context {
    int state;                          // COPY操作状态（使用COPY_STATE_*常量）
    bool has_header;                    // COPY命令是否包含HEADER选项
    bool header_processed;              // 是否已处理头部行
    bool is_replication_mode;           // 是否为流复制模式
} copy_context_t;

// 流复制状态检查宏
#define is_replication_active(pgs) \
    ((pgs) && (pgs)->copy_context && \
     (pgs)->copy_context->state == COPY_STATE_COPY_BOTH && \
     (pgs)->copy_context->is_replication_mode)

#define CONTR_DIR(x) (STREAM_REQ ^ STREAM_RSP ^ (x))

/*
// 流复制连接类型
enum pgsql_connection_type {
    PGSQL_CONN_NORMAL = 0,          // 普通SQL连接
    PGSQL_CONN_PHYSICAL_REP = 1,    // 物理流复制连接
    PGSQL_CONN_LOGICAL_REP = 2      // 逻辑流复制连接
};

// 流复制状态
enum replication_state {
    REP_STATE_NONE = 0,             // 无流复制状态
    REP_STATE_IDENTIFIED = 1,       // 已识别系统
    REP_STATE_SLOT_CREATED = 2,     // 复制槽已创建
    REP_STATE_STREAMING = 3,        // 流复制进行中
    REP_STATE_COMPLETED = 4,        // 流复制完成
    REP_STATE_ERROR = 5             // 错误状态
};

// 流复制错误类型
enum replication_error_type {
    REP_ERROR_NONE = 0,
    REP_ERROR_INVALID_MESSAGE = 1,      // 无效消息格式
    REP_ERROR_PROTOCOL_VIOLATION = 2,   // 协议违规
    REP_ERROR_MEMORY_ALLOCATION = 3,    // 内存分配失败
    REP_ERROR_STATE_TRANSITION = 4,     // 状态转换错误
    REP_ERROR_WAL_DECODE_FAILED = 5,    // WAL解码失败
    REP_ERROR_LOGICAL_DECODE_FAILED = 6, // 逻辑解码失败
    REP_ERROR_TIMEOUT = 7,              // 超时错误
    REP_ERROR_CONNECTION_LOST = 8       // 连接丢失
};

// 流复制连接信息
typedef struct replication_connection {
    pgsql_connection_type conn_type;    // 连接类型
    replication_state state;            // 流复制状态
    char *slot_name;                    // 复制槽名称
    char *publication_names;            // 发布名称列表
    uint64_t start_lsn;                 // 开始LSN
    uint64_t current_lsn;               // 当前LSN
    uint64_t flush_lsn;                 // 刷新LSN
    uint64_t apply_lsn;                 // 应用LSN
    uint32_t timeline_id;               // 时间线ID
    char *system_id;                    // 系统标识符
    bool is_logical;                    // 是否为逻辑复制
    bool binary_mode;                   // 是否为二进制模式
    int proto_version;                  // 协议版本
    uint64_t start_time;                // 开始时间
    uint64_t last_activity;             // 最后活动时间
    uint32_t error_count;               // 错误计数
    replication_error_type last_error;  // 最后错误类型
} replication_connection_t;

// WAL数据记录
typedef struct wal_data_record {
    uint64_t start_lsn;                 // WAL起始LSN
    uint64_t end_lsn;                   // WAL结束LSN
    uint64_t timestamp;                 // 时间戳
    size_t data_len;                    // 数据长度
    uint32_t record_count;              // 记录数量估计
    struct wal_data_record *next;       // 下一个记录
} wal_data_record_t;

// 逻辑复制事件
typedef struct logical_replication_event {
    char event_type;                    // 事件类型 (B/C/R/I/U/D/T/M等)
    uint64_t lsn;                       // LSN位置
    uint64_t timestamp;                 // 时间戳
    uint32_t xid;                       // 事务ID
    uint32_t relation_id;               // 关系ID
    char *relation_name;                // 关系名称
    char *schema_name;                  // 模式名称
    void *event_data;                   // 事件数据
    size_t data_len;                    // 数据长度
    struct logical_replication_event *next; // 下一个事件
} logical_replication_event_t;

// 逻辑复制关系定义
typedef struct logical_relation {
    uint32_t relation_id;               // 关系ID
    char *namespace_name;               // 命名空间名称
    char *relation_name;                // 关系名称
    uint8_t replica_identity;           // 复制标识
    uint16_t column_count;              // 列数量
    struct logical_column *columns;     // 列定义数组
    struct logical_relation *next;      // 下一个关系
} logical_relation_t;

// 逻辑复制列定义
typedef struct logical_column {
    uint8_t flags;                      // 标志位
    char *column_name;                  // 列名
    uint32_t type_oid;                  // 数据类型OID
    int32_t type_modifier;              // 类型修饰符
} logical_column_t;
*/

// 基本字符串结构
typedef struct b_string {
    char *s;
    size_t len;
} b_string_t;

// SQL语句结构
typedef struct sql_statement {
    b_string_t sql;              // SQL语句
    struct sql_statement *next;  // 下一条SQL语句
    bool is_incomplete;          // 新增：标记是否因丢包而不完整
    int lost_bytes;             // 新增：记录丢失的字节数
} sql_statement_t;

// 列定义结构
typedef struct column_def {
    b_string_t name;            // 列名
    uint32_t table_oid;         // 表OID
    uint16_t column_id;         // 列ID
    uint32_t type_oid;          // 类型OID
    int16_t type_len;           // 类型长度
    int32_t type_mod;           // 类型修饰符
    int16_t format_code;        // 格式代码
    struct column_def *next;    // 下一列
} column_def_t;

// 认证上下文
typedef struct postgre_auth_context {
    uint32_t current_auth_type;     // 当前认证类型
    bool is_sasl_flow;              // 是否为SASL认证流程
    int auth_round;                 // 认证轮数计数
} postgre_auth_context_t;

// 行数据结构
typedef struct postgre_row_data {
    b_string_t **row;               // 行数据
    int field_count;                // 字段数量（用于安全清理）
} postgre_row_data_t;

// 结果集结构，用于保存一个查询的所有行
typedef struct result_set {
    postgre_row_data_t **rows;       // 行数据数组
    int row_cnt;                     // 行数量
    int row_capacity;                // 结果集容量
    column_def_t *col_def;           // 列定义
    int col_cnt;                     // 列数量
    struct result_set *next;         // 下一个结果集
} result_set_t;

// Statement状态枚举
typedef enum {
    STMT_STATE_PREPARING,           // 正在准备中
    STMT_STATE_PREPARED,            // 已准备完成
    STMT_STATE_CLOSED               // 已关闭
} statement_state_t;

// Portal状态枚举
typedef enum {
    PORTAL_STATE_BINDING,           // 正在绑定中
    PORTAL_STATE_BOUND,             // 已绑定完成
    PORTAL_STATE_EXECUTING,         // 正在执行中
    PORTAL_STATE_SUSPENDED,         // 已挂起
    PORTAL_STATE_EXHAUSTED,         // 已耗尽
    PORTAL_STATE_CLOSED             // 已关闭
} portal_state_t;

// prepared statement结构
typedef struct _prepared_statement {
    char *name;                     // statement名称，空字符串表示unnamed statement
    sql_statement_t *sql_list;      // 关联的SQL语句
    statement_state_t state;        // statement状态
    uint16_t param_count;           // 参数数量
    uint32_t *param_types;          // 参数类型OID数组
    bool is_unnamed;                // 是否为unnamed statement
    uint64_t create_time;           // 创建时间
    struct _prepared_statement *next;
} prepared_statement_t;

// portal结构
typedef struct _portal {
    char *name;                     // portal名称，空字符串表示unnamed portal
    column_def_t *col_def;          // 关联的列定义
    prepared_statement_t *stmt;     // 关联的prepared statement
    portal_state_t state;           // portal状态
    uint16_t *param_formats;        // 参数格式代码数组
    char **param_values;            // 参数值数组
    uint16_t param_count;           // 参数数量
    uint16_t *result_formats;       // 结果格式代码数组
    uint16_t result_format_count;   // 结果格式数量
    int32_t max_rows;               // 最大返回行数(0表示无限制)
    uint32_t current_row;           // 当前行位置
    bool is_unnamed;                // 是否为unnamed portal
    uint64_t create_time;           // 创建时间
    uint64_t last_execute_time;     // 最后执行时间
    struct _portal *next;
} portal_t;

// 自定义类型处理函数类型定义
// 函数指针类型，用于从二进制数据生成文本表示
typedef char* (*pg_binary_to_text_fn)(const char* data, int len, uint32_t type_oid, int16_t type_len);

// 类型处理器结构体
typedef struct _pg_type_handler {
    uint32_t type_oid;                 // 类型OID
    pg_binary_to_text_fn handler_fn;   // 处理函数
    const char* type_name;             // 类型名称
    size_t fixed_size;                 // 固定大小（bytes），0表示变长类型
    struct _pg_type_handler *next;     // 链表的下一个节点
} pg_type_handler_t;

// 行值结构体 - 可以包含二进制和文本表示
typedef struct postgre_field_value {
    b_string_t text_value;           // 文本值
    void *binary_value;              // 二进制值的指针（视需要分配内存）
    size_t binary_len;               // 二进制值长度
    uint32_t type_oid;               // 类型OID
    int16_t format_code;             // 格式代码（0=文本，1=二进制）
    bool is_null;                    // 是否为NULL值
} postgre_field_value_t;

// 扩展行数据结构，支持二进制数据
typedef struct postgre_row_data_extended {
    postgre_field_value_t **values;  // 列值数组
    int field_count;                 // 字段数量
} postgre_row_data_extended_t;

// 解析数据结构
typedef struct postgre_parsed_data {
    sql_statement_t *sql_list;      // SQL语句链表
    int sql_count;                  // SQL语句数量
    result_set_t *rs_list;          // 结果集链表
    int rs_count;                   // 结果集数量
    char *err_msg;                  // 错误信息
    int err_code;                   // 错误码
    uint64_t pcap_ts;               // 请求时间
    uint64_t start_time;            // 访问开始时间
    uint64_t close_time;            // 访问结束时间
    // COPY相关字段
    size_t copy_len;                // COPY数据的总长度
    uint8_t copy_format;            // COPY操作的格式(0:文本, 1:二进制)
    uint16_t copy_columns;          // COPY操作的列数
    int copy_done;                  // COPY操作是否完成
    int copy_state;                 // COPY操作状态（使用COPY_STATE_*常量）
    char *copy_error_msg;           // COPY失败时的错误信息

    uint32_t tcp_seq;
    uint32_t tcp_ack;
    uint32_t expect_rsp_ack;  // 请求期待的响应ACK值，用于丢包场景下的匹配
} postgre_parsed_data_t;

// 半流结构
typedef struct postgre_half_stream {
    postgre_parsed_data_t data;         // 解析数据
    struct postgre_half_stream *next;   // 下一个半流
    struct postgre_half_stream *prev;   // 上一个半流
} postgre_half_stream_t;

// PostgreSQL状态结构
typedef struct postgre_stat {
    uint32_t protocol_version;   // 协议版本
    b_string_t user;             // 用户名
    b_string_t password;         // 密码
    b_string_t db_name;          // 数据库名
    b_string_t application_name; // 应用名称
    b_string_t server_version;   // 服务器版本
    uint32_t backend_pid;        // 后端进程ID
    uint32_t backend_key;        // 后端密钥
    char transaction_status;     // 事务状态
    uint16_t num_fields;         // 字段数量

    uint64_t startup_time;       // 启动阶段请求时间
    uint64_t startup_start_time; // 启动阶段响应开始时间
    uint64_t startup_close_time; // 启动阶段响应结束时间
} postgre_stat_t;

// PostgreSQL流结构
typedef struct postgre_stream {
    int is_postgre;                                     // 是否PostgreSQL流
    postgre_stat_t pg_stat;                             // PostgreSQL状态
    postgre_auth_context_t *auth_context;               // 认证上下文
    postgre_half_stream_t *p_postgre_client;            // 客户端半流
    postgre_half_stream_t *p_postgre_client_last;       // 客户端最后一个半流
    postgre_half_stream_t *p_postgre_server;            // 服务端半流
    postgre_half_stream_t *p_postgre_server_last;       // 服务端最后一个半流

    // 添加prepared statements和portals的跟踪
    prepared_statement_t *prepared_statements;          // prepared statements链表
    portal_t *portals;                                  // portals链表

    // COPY协议状态管理
    struct copy_context *copy_context;                  // COPY操作上下文

    // 添加匹配优化索引
    void *match_index;                                  // 匹配索引（使用void*避免C++依赖）
} postgre_stream_t;

#endif // __POSTGRE_PARSER_COMMON_H__