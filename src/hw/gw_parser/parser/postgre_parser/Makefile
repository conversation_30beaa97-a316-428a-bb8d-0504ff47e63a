ifeq ("$(BUILD_CC_TOOL)","clang++")
CC              = clang++ -fsanitize=address -fno-omit-frame-pointer -D_CC_CLANG_PP
else ifeq ("$(BUILD_CC_TOOL)","clang")
CC              = clang -fsanitize=address -fno-omit-frame-pointer -D_CC_CLANG
else ifeq ("$(BUILD_CC_TOOL)","g++")
CC              = g++ -D_CC_GNU_PP
else ifeq ("$(BUILD_CC_TOOL)", "aarch64-linux-gnu-gcc")
CC              = aarch64-linux-gnu-gcc
else
CC              = gcc
endif

MKFILE_PATH :=$(abspath $(lastword $(MAKEFILE_LIST)))
MKFILE_DIR :=$(patsubst %/, %, $(dir $(MKFILE_PATH)))
MKFILE_DIR_STRIP :=$(strip $(MKFILE_DIR))
ROOT_DIR :=$(MKFILE_DIR_STRIP)/../..

ifeq ("$(BUILD_SCHEME)", "UnitTest")
CFLAGS          =  -fvisibility=default
CFLAGS += -DNDEBUG
else ifeq ("$(BUILD_SCHEME)", "CiUnitTest")
CFLAGS          =  -fvisibility=default
CFLAGS += -DNDEBUG
else
CFLAGS          =  -fvisibility=hidden
endif

CFLAGS         += -g  -fPIC -I. -I../.././include -I../.././utils/queue/ -I../.././utils/cjson/ -I../.././utils/ -I../.././core/l4/ -I../.././core/

ifeq ("$(BUILD_ARCH)", "ARM")
CFLAGS         += -I/home/<USER>/3rd/libpcap-1.9.1/ -I/home/<USER>/3rd/protobuf/include/
endif

LDFLAGS         = -shared 

ifeq ("$(BUILD_ARCH)", "x86")
LDFLAGS        += -lstdc++ -lz -lpthread -lprotobuf

else ifeq ("$(BUILD_ARCH)", "ARM")
LDFLAGS        += -lstdc++ -L/home/<USER>/3rd/zlib/lib/ -lz -lpthread -L/home/<USER>/3rd/protobuf/lib/ -lprotobuf

CFLAGS         += -I/home/<USER>/3rd/iconv/include/
LDFLAGS        += -L/home/<USER>/3rd/iconv/lib/ -liconv
endif

include ../../flags.make

O_FILES = postgre_parser.o
O_FILES += postgre_parser_parser_msg.o
O_FILES += postgre_parser_deal_probe.o
O_FILES += postgre_parser_deal_parser.o
O_FILES += postgre_parser_binary_handlers.o
O_FILES += postgre_parser_replication_protocol.o
O_FILES += ProtobufRawPostgreEvent.pb.o

O_FILES += module_mgt_postgre_parser.o
O_FILES += cJSON.o cJSON_Utils.o utils.o cpp_utils.o postgre_parser_upload_task_worker.o

.PHONY: all clean

all: postgre_parser.so 

%.o:%.cpp
	$(CC) -c $(CPPFLAGS)  $(LIBS_CFLAGS) $<

%.o:%.c
	$(CC) -c $(CFLAGS)  $(LIBS_CFLAGS) $<

cJSON.o: ../.././utils/cjson/cJSON.c ../.././utils/cjson/cJSON.h
	$(CC) -c $(CFLAGS)  $(LIBS_CFLAGS) $<

cJSON_Utils.o: ../.././utils/cjson/cJSON_Utils.c ../.././utils/cjson/cJSON_Utils.h
	$(CC) -c $(CFLAGS)  $(LIBS_CFLAGS) $<

utils.o: ../../core/utils.c ../../include/utils.h
	$(CC) -c $(CFLAGS) $(LIBS_CFLAGS) $<

cpp_utils.o: ../../core/cpp_utils.cpp ../../include/cpp_utils.h
	$(CC) -c $(CPPFLAGS)  $(LIBS_CFLAGS) $<

postgre_parser.so: $(O_FILES) 
	$(CC) -o $@ $^ $(LDFLAGS) $(LIBS) $(LIB)

clean:
	rm -f *.o *~ postgre_parser.so 