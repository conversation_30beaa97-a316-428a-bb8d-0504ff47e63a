/**
 * Project gw-hw
 * <AUTHOR> Assistant
 * @version 1.0.0
 */

#ifndef __POSTGRE_PARSER_H__
#define __POSTGRE_PARSER_H__

#include <pthread.h>
#include <stdint.h>
#include <string>
#include <string.h>
#include <unordered_set>
#include "gw_i_parser.h"
#include "task_worker.h"
#include "gw_ver.h"
#include "simple_json.h"
#include "pp.h"
#include "postgre_parser_common.h"

#define POSTGREPARSER_VER GW_VER_STRING(GW_VER_MAJOR, GW_VER_MINOR, GW_VER_REVISION)

class CWorkerQueue;
class CTaskWorker;
class CTaskWorkerUploadMsg;

typedef struct pgsql_req_info{
    char db_user[64];             // 数据库用户
    char db_name[64];             // 数据库名
    char db_password[64];         // 数据库密码
    char cmd_type[32];            // 操作类型
    char sql_text[4096];          // 原始SQL文本
}pgsql_req_info_t;

typedef struct pgsql_rsp_info{
    int code;                       // 状态码
    uint64_t start_time;            // 开始时间(毫秒级)
    uint64_t close_time;            // 结束时间(毫秒级)
    int row_count;                  // 影响行数
    struct {
        char **field_names;      // 字段名数组
        char **field_values;     // 字段值数组
        int field_count;         // 字段数量
    } result_set;                // 结果集
}pgsql_rsp_info_t;

typedef struct pgsql_meta_info{
    uint64_t ts;                    // 时间戳(毫秒级)
    char type[32];                  // 数据库类型，当前解析器为PostgreSQL
    char app_name[32];              // 应用名称
    char server_version[64];        // 服务器版本
}pgsql_meta_info_t;

typedef struct pgsql_mac_info{
    char mac[18];
}pgsql_mac_info_t;

typedef struct pgsql_net_info{
    unsigned short src_port;
    unsigned short dst_port;
    char a_src_ip[64];
    char a_dst_ip[64];
    char flow_source[64];
}pgsql_net_info_t;

// 事件上传数据结构，涵盖了登录事件和访问事件
typedef struct upload_postgre_info : public TaskWorkerData{
    pgsql_req_info_t pgsql_req_info;
    pgsql_rsp_info_t pgsql_rsp_info;
    pgsql_meta_info_t pgsql_meta_info;
    pgsql_net_info_t pgsql_net_info;
    pgsql_mac_info_t pgsql_mac_info;
    char a_unique_id[128];
    size_t mem_size;            // 内存大小

    upload_postgre_info() {
        memset(this, 0, sizeof(upload_postgre_info));
        mem_size = sizeof(upload_postgre_info);  // 继承自 TaskWorkerData
    }
}upload_postgre_info_t;

typedef struct
{
  volatile uint64_t cnt_p; // 解析总数
  volatile uint64_t cnt_p_bytes; // 字节总数
  volatile uint64_t cnt_p_succ; // 解析成功数量
  volatile uint64_t cnt_p_fail; // 解析失败数量
} stats_postgre_parser_t;

typedef struct
{
  volatile uint64_t cnt_m;      // 解析总数
  volatile uint64_t cnt_m_succ; // 解析成功数量
  volatile uint64_t cnt_m_fail; // 解析失败数量
} stats_postgre_match_t;

typedef struct
{
  // session
  volatile uint64_t cnt_session;       // 数量
  volatile uint64_t cnt_session_bytes; // 字节总数量

  // parser
  volatile stats_postgre_parser_t p;
  // match
  volatile stats_postgre_match_t m;

  // request
  volatile stats_postgre_parser_t req;
  volatile stats_postgre_match_t req_match_rsp; // 匹配respone 成功数量

  // response
  volatile stats_postgre_parser_t rsp;
  volatile stats_postgre_match_t rsp_match_req; // 匹配request

} stats_postgre_t;

struct tcp_stream;
struct postgre_stream;
struct StreamData;
struct UploadMsg;
struct cJSON;
struct net_info;

class CGwCommon;
class CSession;
class CUpload;
class CMinioUpload;

class CPostgreParser : public CParser
{
public:
    CPostgreParser(void);
    virtual ~CPostgreParser(void);

public:
    virtual void cache_clean();
    virtual bool probe(CSessionMgt *, const app_stream *,const struct conn *, CSession *);
    virtual bool probe_on_close(CSessionMgt *, const app_stream *,const struct conn *, CSession *);
    virtual bool probe_on_reset(CSessionMgt *, const app_stream *,const struct conn *, CSession *);
    virtual int parse(CSessionMgt *, const app_stream *,const struct conn *, CSession *);
    virtual int parse_clear(CSessionMgt *, const app_stream *,const struct conn *, CSession *);
    virtual int parse_on_close(CSessionMgt *, const app_stream *,const struct conn *, CSession *);
    virtual int parse_on_reset(CSessionMgt *, const app_stream *,const struct conn *, CSession *);
    virtual const char *get_data(const struct StreamData *, int dir, int *data_len, int *offset_out);
    virtual bool discard(struct StreamData *, int dir, int num);
    virtual bool discard_and_update(struct StreamData *, int dir, int num);
    virtual void del_session_stream(StreamData *);
    virtual void del_session_param(SessionMgtData *);
    virtual void init();
    virtual void fini();
    virtual void run();
    virtual const char *get_name(void) const;
    virtual const char *get_version(void) const;
    virtual void set_gw_common(CGwCommon *comm);
    virtual bool load_conf(const char *);
    virtual void set_quit_signal(void);
    virtual void wait_for_stop(void);
    virtual void set_url_filter_rule(CFilterRule *rule);
    virtual void set_accout_filter_rule(CFilterRule *rule);
    virtual void set_upload_filter_rule(CFilterRule *client_rule, CFilterRule *server_rule);
    virtual void add_upstream(CParser *parser);
    virtual void reset_upstream(void);
    virtual void push_upstream_msg(char *s, size_t length);
    virtual bool is_parsed(const struct StreamData *) const;
    virtual struct StreamData *clone_stream_data(const struct StreamData *);
    virtual uint64_t get_parser_http_cnt();
    virtual uint64_t get_succ_parser_http_cnt();
    virtual void* get_parser_status();
    virtual void set_parser_type(int type);
    virtual void read_conf_urlbase_for_mon();
    virtual void read_conf_filetype_for_mon();
    virtual void get_log_buf(char *log_buf, size_t log_buf_len) const;
    virtual uint32_t parser_status() const;

protected:
    int m_quit_signal;
    CGwCommon *m_comm;
    char m_name[32];
    uint64_t m_u64_postgre_upload_ms;          // 上传时间戳(毫秒)
    uint32_t m_u32_postgre_upload_index;       // 上传索引
    std::string m_str_gw_ip;
    std::string m_conf_upload_name;
    stats_postgre_t m_stats_postgre;
    int m_postgre_type;
    CUpload *m_upload;
    bool m_upload_file;
    CWorkerQueue *m_upload_data_wq;
    CTaskWorker *m_upload_data_wk;
    int m_conf_postgre_upload_queue_max_num;
    int m_conf_postgre_upload_queue_memory_max_size_bytes;
    int m_conf_postgre_upload_thread_num;
    int m_conf_parser_enable;
    int m_conf_postgre_probe_cnt;
    // 二进制数据处理相关变量
    pg_type_handler_t *m_binary_handlers;
    bool m_use_binary_extended;             // 是否使用扩展的二进制数据处理
    std::unordered_set<std::string> m_agent_client_ip;
    std::string m_agent_client_ip_file_path;
    pthread_rwlock_t m_traffic_source_rwlock;
    int m_conf_pcap_timestamp;
    int m_upload_results_num;               // 结果集上传数量
    int m_upload_field_max_size;            // 单个字段值最大字节数
    int m_upload_protobuf_enable;           // protobuf格式上传开关

protected:
    friend class CTaskWorkerUploadMsg;
    inline CWorkerQueue *get_wq_upload_msg(void) const
    {
      return m_upload_data_wq;
    }
    CWorkerQueue *new_wq_upload_msg();
    int worker_routine_postgre_upload_data_inner(upload_postgre_info_t*);
    void free_postgre_upload_data(upload_postgre_info_t *p_upload_postgre_info);

    void modify_stats(int enable);
    static void print_postgre_stats_callback(void *p);
    void print_postgre_stats(void) const;

    // 解析统计信息更新辅助函数
    void update_parse_stats(int success, int data_len, int dir = PGSQL_BOTH);
    void update_match_stats(bool success, int dir);
    void update_session_stats(int data_len);

private:
    struct half_stream* get_half_stream(int dir, tcp_stream* a_tcp);
    StreamData *get_stream_data_from_session(CSession *p_session, int dir);
    static void free_upload_msg(const struct UploadMsg *pum);
    void upload_postgre_data(struct StreamData *p_stream_data, const struct conn *pcon, double ts, CSession *p_session);
    void handle_access_event(postgre_stream_t *pgs, const struct conn *pcon, const postgre_parsed_data_t *p_data = NULL);
    void handle_login_event(postgre_stream_t *pgs, const struct conn *pcon, const postgre_parsed_data_t *p_data = NULL);
    void postgre_cb_upload_msg(const char *s, size_t s_len);
    void send_postgre_data(upload_postgre_info_t *p_upload_postgre_info);
    void add_net_json(pgsql_net_info_t *p_net_info, const struct conn *pcon, bool reverse);
    char* upload_postgre_data_encode(upload_postgre_info_t& st_upload_postgre_info, size_t *p_s_len);
    void add_event_id(char *p_event_id);

private:
    bool client_msg(const char *data)
    {
        return data[0] == POSTGRE_MSG_QUERY ||
                data[0] == POSTGRE_MSG_PARSE ||
                data[0] == POSTGRE_MSG_BIND ||
                data[0] == POSTGRE_MSG_EXECUTE ||
                data[0] == POSTGRE_MSG_DESCRIBE ||
                data[0] == POSTGRE_MSG_SYNC ||
                data[0] == POSTGRE_MSG_CLOSE ||
                data[0] == POSTGRE_MSG_FLUSH ||
                data[0] == POSTGRE_MSG_TERMINATE ||
                data[0] == POSTGRE_MSG_PASSWORD ||
                data[0] == POSTGRE_MSG_COPY_FAIL ||
                data[0] == POSTGRE_MSG_FUNCTION_CALL ||
                data[0] == POSTGRE_MSG_COPY_DATA ||
                data[0] == POSTGRE_MSG_COPY_DONE;
    }
    bool server_msg(const char *data)
    {
        return data[0] == POSTGRE_MSG_AUTHENTICATION ||
                data[0] == POSTGRE_MSG_ERROR_RESPONSE ||
                data[0] == POSTGRE_MSG_NOTICE_RESPONSE ||
                data[0] == POSTGRE_MSG_NOTIFICATION_RESPONSE ||
                data[0] == POSTGRE_MSG_COMMAND_COMPLETE ||
                data[0] == POSTGRE_MSG_PARAMETER_STATUS ||
                data[0] == POSTGRE_MSG_READY_FOR_QUERY ||
                data[0] == POSTGRE_MSG_ROW_DESCRIPTION ||
                data[0] == POSTGRE_MSG_DATA_ROW ||
                data[0] == POSTGRE_MSG_COPY_IN_RESPONSE ||
                data[0] == POSTGRE_MSG_COPY_OUT_RESPONSE ||
                data[0] == POSTGRE_MSG_COPY_BOTH_RESPONSE ||
                data[0] == POSTGRE_MSG_COPY_DATA ||
                data[0] == POSTGRE_MSG_COPY_DONE ||
                data[0] == POSTGRE_MSG_PARSE_COMPLETE ||
                data[0] == POSTGRE_MSG_BIND_COMPLETE ||
                data[0] == POSTGRE_MSG_CLOSE_COMPLETE ||
                data[0] == POSTGRE_MSG_PORTAL_SUSPENDED ||
                data[0] == POSTGRE_MSG_NO_DATA ||
                data[0] == POSTGRE_MSG_PARAMETER_DESCRIPTION ||
                data[0] == POSTGRE_MSG_BACKEND_KEY_DATA ||
                data[0] == POSTGRE_MSG_EMPTY_QUERY_RESPONSE ||
                data[0] == POSTGRE_MSG_FUNCTION_CALL_RESPONSE ||
                data[0] == POSTGRE_MSG_NEGOTIATE_PROTOCOL_VERSION;
    }
    bool is_valid_startup_msg(const char *data, int len);
    bool is_valid_normal_msg(const char *data, int len, int dir);

    /*
    * 严格探测函数
    */
    bool is_valid_pgsql_msg_strict(const char *data, int len, int dir);
    bool validate_pgsql_startup_parameters(const char *data, int len);
    bool check_pgsql_protocol_fingerprint(const char *data, int len, int dir);
    bool conflicts_with_other_protocols(const char *data, int len);
    bool validate_message_sequence_consistency(const char *data, int len, int dir);
    bool check_postgresql_specific_patterns(const char *data, int len);
    bool validate_startup_parameter_names(const char *data, int len);

    /*
    * 消息特征匹配相关函数
    */
    int parse_by_message_signature(postgre_stream_t *pgs, const char *data, int len,
                                  bool is_client, const struct conn *pcon, CSession *p_session, half_stream* hlf);
    int parse_client_message_by_signature(postgre_stream_t *pgs, const char *data, int len,
                                        const struct conn *pcon, CSession *p_session, half_stream* hlf);
    int parse_server_message_by_signature(postgre_stream_t *pgs, const char *data, int len,
                                        const struct conn *pcon, CSession *p_session, half_stream* hlf);
    bool is_startup_like_message(const char *data, int len);

    int parse_startup_msg(postgre_stream_t *pgs, const char *data, int len, const struct conn *pcon);
    int parse_auth_msg(postgre_stream_t *pgs, const char *data, int len, const struct conn *pcon, CSession *p_session);
    int parse_client_auth_msg(postgre_stream_t *pgs, const char *data, int len);
    int parse_query_msg(postgre_stream_t *pgs, const char *data, int len, struct half_stream *hlf);
    int parse_response_msg(postgre_stream_t *pgs, const char *data, int len, const struct conn *pcon, CSession *p_session, struct half_stream *hlf);
    int parse_cancel_request_msg(postgre_stream_t *pgs, const char *data, int len, const struct conn *pcon);
    
    // 获取秒级时间戳
    uint64_t get_time_ts(CSession *p_session, int use_pcap_ts);

    // 获取毫秒级时间戳
    uint64_t get_time_ts_ms(CSession *p_session, int use_pcap_ts);

    // 插入新的解析节点到链表
    void insert_into_parser_header(postgre_stream_t *p_stream, int dir, postgre_half_stream_t *p_phs);
    
    // 尝试匹配请求和响应，并处理访问事件和清理
    bool postgre_parser_merge(postgre_stream_t *p_stream, const struct conn *pcon, int dir);
    
    // 释放已处理的节点数据
    void del_postgre_merged_data(postgre_parsed_data_t *p_data);
    
    // 释放匹配的请求和响应节点
    void free_postgre_matched_data(postgre_stream_t *p_stream, postgre_half_stream_t *p_req, postgre_half_stream_t *p_resp);

    // 统一的SQL语句添加函数，支持丢包信息
    void add_sql_statement(postgre_parsed_data_t *p_data, const char *sql, size_t sql_len,
                          bool is_incomplete = false, int lost_bytes = 0);

    // 释放SQL语句链表
    void free_sql_list(sql_statement_t *sql_list);

    // 添加成员函数声明
    void cleanup_prepared_statements(postgre_stream_t *pgs);
    void cleanup_portals(postgre_stream_t *pgs);
    void cleanup_row_data(postgre_row_data_t *row, int field_count);
    void cleanup_column_definitions(column_def_t *col_def);
    char* instantiate_sql_with_parameters(const char* parameterized_sql, size_t sql_len, 
                                         char** param_values, uint16_t param_count, 
                                         size_t* result_len);
    
    // 辅助函数：验证SQL链表完整性
    bool validate_sql_list_integrity(sql_statement_t *sql_list, const char *context);
    prepared_statement_t* find_prepared_statement(postgre_stream_t *pgs, const char *name);
    portal_t* find_portal(postgre_stream_t *pgs, const char *name);
    void add_prepared_statement(postgre_stream_t *pgs, const char *name, sql_statement_t *sql);
    void add_portal(postgre_stream_t *pgs, const char *name, prepared_statement_t *stmt);
    
    // 结果集管理函数
    void add_result_set(postgre_parsed_data_t *p_data, column_def_t *col_def, int col_cnt, result_set_t** p_current_rs);
    void update_result_set(result_set_t *target_rs, postgre_row_data_t **rows, int row_cnt);

    /*
    * COPY协议状态管理函数
    */
    void set_copy_error(postgre_stream_t *pgs, const char *error_msg);

    // 内联辅助函数
    inline bool is_copy_active(postgre_stream_t *pgs) {
        return pgs && pgs->copy_context && pgs->copy_context->state != COPY_STATE_NONE;
    }

    /*
    * 流复制协议简化处理函数
    */
    void process_replication_copy_data(postgre_stream_t *pgs, const char *data, int len);
    result_set_t* create_replication_result_set();
    bool add_row_to_replication_result_set(result_set_t *rs, const char *base64_content);
    result_set_t* find_or_create_replication_result_set(postgre_half_stream_t *server_stream);
    bool is_replication_command(const char *data, int len);

    /*
    * COPY上下文管理函数
    */
    bool ensure_copy_context(postgre_stream_t *pgs);
    void cleanup_copy_context(postgre_stream_t *pgs);

    /*
    * COPY DATA消息统一处理函数
    */
    int process_copy_data_message(postgre_stream_t *pgs, const char *data, int offset,
                                 uint32_t msg_len, bool is_client);

    /*
    * 二进制数据处理相关函数
    */
    pg_type_handler_t* init_binary_handlers();
    bool register_binary_handler(pg_type_handler_t **handlers, uint32_t type_oid, 
                               pg_binary_to_text_fn handler_fn, 
                               const char* type_name, size_t fixed_size);
    pg_binary_to_text_fn find_binary_handler(pg_type_handler_t *handlers, uint32_t type_oid);
    void cleanup_binary_handlers(pg_type_handler_t *handlers);
    postgre_field_value_t* decode_binary_field(const char* data, int len, uint32_t type_oid, 
                                             int16_t format_code, int16_t type_len, 
                                             pg_type_handler_t *handlers);
    void cleanup_field_value(postgre_field_value_t *field);
    void cleanup_row_data_extended(postgre_row_data_extended_t *row);
    postgre_row_data_extended_t* parse_data_row_extended(const char *data, int len, int offset, 
                                                       const column_def_t *col_defs, int col_cnt, 
                                                       pg_type_handler_t *handlers);

    // 处理SASL认证响应消息
    void handle_sasl_response_message(postgre_stream_t *pgs, const char *data, size_t len);

    // 处理传统密码认证消息
    void handle_traditional_password_message(postgre_stream_t *pgs, const char *data, size_t len);
    
    // 事务状态管理和对象生命周期管理函数
    void handle_transaction_state_change(postgre_stream_t *pgs, char old_status, char new_status);
    void cleanup_unnamed_objects(postgre_stream_t *pgs);
    void cleanup_exhausted_portals(postgre_stream_t *pgs);
    void mark_portals_for_validation(postgre_stream_t *pgs);
    void cleanup_invalid_objects(postgre_stream_t *pgs);
    void cleanup_single_prepared_statement(prepared_statement_t *stmt);
    void cleanup_single_portal(portal_t *portal);
    // 清理portal中对指定statement的引用
    void clear_portal_references_to_statement(postgre_stream_t *pgs, prepared_statement_t *stmt);
    
    // 字段数量验证辅助函数
    int get_expected_field_count(postgre_stream_t *pgs, result_set_t *current_rs);
    bool validate_field_count(postgre_stream_t *pgs, result_set_t *current_rs, int actual_fields, const char *context);
    
    // 匹配优化相关函数
    void* create_match_index();
    void destroy_match_index(void *index);
    void add_to_match_index(void *index, uint32_t tcp_seq, uint32_t tcp_ack, postgre_half_stream_t *stream, bool is_request);
    postgre_half_stream_t* find_match_by_seq(void *index, uint32_t target_seq, bool search_requests);
    void remove_from_match_index(void *index, uint32_t tcp_seq, uint32_t tcp_ack, bool is_request);
    void cleanup_match_index(void *index);
    
    // 优化后的匹配函数
    bool postgre_parser_merge_optimized(postgre_stream_t *p_stream, const struct conn *pcon, int dir);
    
    // 执行匹配逻辑的辅助函数
    bool execute_match_logic(postgre_stream_t *p_stream, const struct conn *pcon, 
                           postgre_half_stream_t *p_req, postgre_half_stream_t *p_resp);

    // 错误响应解析辅助函数
    void parse_error_response_fields(const char *error_data, size_t data_len, char **detailed_message, int *error_code);
    void cleanup_error_fields(char *err_msg, char *err_severity, char *err_detail,
                             char *err_hint, char *err_position, char *err_code_str);

    /*
    * 丢包处理相关函数
    */
    int process_incomplete_data_row(postgre_stream_t *pgs, const char *msg_data,
                                   int available_msg_len, struct half_stream *hlf);
    int process_incomplete_row_description(postgre_stream_t *pgs, const char *msg_data,
                                          int available_msg_len, struct half_stream *hlf);
    bool try_parse_single_column(const char *data, int available_len,
                                column_def_t **col, int *consumed_bytes);
    bool detect_packet_loss(struct half_stream *hlf, uint32_t msg_len,
                           int available_len, int *lost_bytes, int current_offset = 0);
    void add_packet_loss_marker_to_field(b_string_t **field, const char *original_data,
                                        size_t available_len, struct half_stream *hlf);
    int create_packet_loss_placeholder_data_row(postgre_stream_t *pgs, struct half_stream *hlf);
    bool create_dynamic_column_definitions_from_row_data(result_set_t *rs);
    void check_and_add_specific_prepared_statement(postgre_stream_t *pgs, const char *stmt_name);

};

#endif // __POSTGRE_PARSER_H__ 