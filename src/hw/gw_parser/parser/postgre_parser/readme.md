### PostgreSQL解析器配置
#### 概述
PostgreSQL解析器用于解析和处理PostgreSQL数据库流量，提取SQL请求和响应信息，并将其格式化为JSON数据进行上传。

#### 配置参数

PostgreSQL解析器支持以下配置参数，可在配置文件中的`[parser]`部分进行设置：

| 参数名称 | 说明 | 默认值 | 单位 |
|---------|------|-------|------|
| postgre_upload_results_num | 限制上传结果集的最大行数 | 100 | 行 |
| postgre_upload_field_max_size | 限制单个字段值的最大字节数 | 8192 | 字节 |
| postgre_upload_queue_num | 上传队列最大数量 | 1000 | 个 |
| postgre_upload_queue_memory_size | 上传队列内存大小 | 100 | MB |
| postgre_upload_thread_num | 上传线程数 | 1 | 个 |
| postgre_use_binary_extended | 是否启用二进制数据扩展处理 | 1 | 布尔值 |
| pcap_timestamp | 是否使用PCAP时间戳 | 1 | 布尔值 |

#### 结果集优化

为了防止大型结果集导致内存占用过高，我们添加了两个新的配置参数：

1. `postgre_upload_results_num`：限制每个SQL查询上传结果集的最大行数。例如，当SQL查询返回1000行数据时，如果该参数设置为100，则只会上传前100行数据。

2. `postgre_upload_field_max_size`：限制单个字段值的最大字节数。当字段值超过设定的大小时，会进行截断。这对于处理包含BLOB、TEXT等大型数据类型的字段特别有用。

这些限制可以有效减少内存使用并提高系统的稳定性，尤其是在处理大型结果集的情况下。

#### 配置示例

```ini
[parser]
# 限制最多上传50行结果
postgre_upload_results_num = 50

# 限制单个字段最大为1MB
postgre_upload_field_max_size = 1048576

# 上传队列配置
postgre_upload_queue_num = 2000
postgre_upload_queue_memory_size = 200
postgre_upload_thread_num = 2
```

#### 支持的PostgreSQL数据类型

PostgreSQL解析器支持以下数据类型的解析和转换：

- **基础类型**: BOOL, CHAR, INT2/4/8, FLOAT4/8, TEXT, VARCHAR
- **时间类型**: DATE, TIME, TIMESTAMP, TIMESTAMPTZ, INTERVAL
- **数值类型**: NUMERIC(任意精度)
- **二进制类型**: BYTEA
- **JSON类型**: JSON, JSONB
- **空间类型**: POINT, LSEG, PATH, BOX, POLYGON
- **网络地址类型**: INET, CIDR, MACADDR, MACADDR8
- **数组类型**: 支持所有基础类型的数组版本（如INT4[], TEXT[], BOOL[]等）
- **枚举类型**: 用户定义的枚举类型（ENUM）
- **其他类型**: UUID, XML, OID, NAME

#### COPY协议支持

PostgreSQL解析器完整支持COPY协议的处理：

- **COPY命令识别**: 自动识别COPY IN、COPY OUT和COPY BOTH操作
- **状态管理**: 完整的COPY操作状态机，包括启动、进行中、数据传输、完成和失败状态
- **数据格式**: 支持文本和二进制格式的COPY数据解析
- **错误处理**: 完善的COPY失败处理机制，包括错误信息记录和资源清理
- **资源管理**: 正确的COPY操作生命周期管理，防止内存泄漏