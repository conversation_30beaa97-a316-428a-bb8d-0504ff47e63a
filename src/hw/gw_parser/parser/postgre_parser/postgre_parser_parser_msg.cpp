#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <sys/socket.h>
#include <arpa/inet.h>
#include <netinet/in.h>
#include <sys/time.h>

#include "cJSON.h"
#include "postgre_parser.h"
#include "postgre_parser_upload_task_worker.hpp"
#include "gw_common.h"
#include "gw_i_upload.h"
#include "gw_logger.h"
#include "session_mgt.h"
#include "session.h"
#include "worker_queue.h"
#include "task_worker.h"
#include "utils.h"
#include "simple_json.h"
#include "gw_stats.h"
#include "ProtobufRawPostgreEvent.pb.h"

using namespace com::quanzhi::audit_core::common::model;

#define POSTGRE_DATA_UPLOAD_QUEUE "postgre data upload queue"
#define IF_FREE(x) if ( NULL != (x) ) { cJSON_free(x); }

static const char msg_type[] = "postgre";

// 格式化事件JSON
static char* format_event_json(const upload_postgre_info_t *data, size_t &s_len)
{
    // 增加初始缓冲区大小，考虑到所有字段和结果集的大小
    // 使用更智能的初始大小估算
    size_t initial_size = 8192;  // 基础大小8KB
    
    // 如果有结果集，额外增加空间
    if (data->pgsql_rsp_info.result_set.field_count > 0 && 
        data->pgsql_rsp_info.row_count > 0 && 
        data->pgsql_rsp_info.result_set.field_names && 
        data->pgsql_rsp_info.result_set.field_values)
    {
        // 基于字段数和行数估算结果集大小
        // 每行估计平均1KB + 额外的字段数*200字节
        size_t estimated_result_size = data->pgsql_rsp_info.row_count * 
                                     (1024 + data->pgsql_rsp_info.result_set.field_count * 200);
        
        // 设置合理的上限，避免过度分配
        if (estimated_result_size > 50 * 1024 * 1024) { // 最大50MB
            estimated_result_size = 50 * 1024 * 1024;
        }
        
        initial_size += estimated_result_size;
    }
    
    size_t current_size = initial_size;
    char *buffer = (char*)malloc(current_size);
    
    if (!buffer) {
        return NULL;
    }

    // 对所有可能包含特殊字符的字符串字段进行JSON转义处理
    char *escaped_app_name = cJSON_EscapeString(data->pgsql_meta_info.app_name);
    const char *safe_app_name = escaped_app_name ? escaped_app_name : data->pgsql_meta_info.app_name;
    
    char *escaped_server_version = cJSON_EscapeString(data->pgsql_meta_info.server_version);
    const char *safe_server_version = escaped_server_version ? escaped_server_version : data->pgsql_meta_info.server_version;
    
    char *escaped_db_name = cJSON_EscapeString(data->pgsql_req_info.db_name);
    const char *safe_db_name = escaped_db_name ? escaped_db_name : data->pgsql_req_info.db_name;
    
    char *escaped_db_user = cJSON_EscapeString(data->pgsql_req_info.db_user);
    const char *safe_db_user = escaped_db_user ? escaped_db_user : data->pgsql_req_info.db_user;
    
    char *escaped_db_password = cJSON_EscapeString(data->pgsql_req_info.db_password);
    const char *safe_db_password = escaped_db_password ? escaped_db_password : data->pgsql_req_info.db_password;
    
    char *escaped_cmd_type = cJSON_EscapeString(data->pgsql_req_info.cmd_type);
    const char *safe_cmd_type = escaped_cmd_type ? escaped_cmd_type : data->pgsql_req_info.cmd_type;
    
    char *escaped_sql = cJSON_EscapeString(data->pgsql_req_info.sql_text);
    const char *safe_sql = escaped_sql ? escaped_sql : data->pgsql_req_info.sql_text;
    
    // 格式化meta, net, mac, unique_id, req和rsp部分
    int written = snprintf(buffer, current_size,
        "{"
        "\"meta\":{"
        "\"tm\":%" PRIu64 ","
        "\"type\":\"%s\","
        "\"app_name\":%s,"
        "\"server_version\":%s"
        "},"
        "\"net\":{"
        "\"src_ip\":\"%s\","
        "\"src_port\":%d,"
        "\"dst_ip\":\"%s\","
        "\"dst_port\":%d,"
        "\"flow_source\":\"%s\""
        "},"
        "\"mac\":{"
        "\"mac\":\"%s\""
        "},"
        "\"unique_id\":{"
        "\"event_id\":\"%s\""
        "},"
        "\"req\":{"
        "\"db_name\":%s,"
        "\"db_user\":%s,"
        "\"db_password\":%s,"
        "\"cmd_type\":%s,"
        "\"sql\":%s"
        "},"
        "\"rsp\":{"
        "\"status\":%d,"
        "\"start_time\":%" PRIu64 ","
        "\"close_time\":%" PRIu64 ","
        "\"row_count\":%d",
        data->pgsql_meta_info.ts,
        data->pgsql_meta_info.type,
        safe_app_name,
        safe_server_version,
        data->pgsql_net_info.a_src_ip,
        data->pgsql_net_info.src_port,
        data->pgsql_net_info.a_dst_ip,
        data->pgsql_net_info.dst_port,
        data->pgsql_net_info.flow_source,
        data->pgsql_mac_info.mac,
        data->a_unique_id,
        safe_db_name,
        safe_db_user,
        safe_db_password,
        safe_cmd_type,
        safe_sql,
        data->pgsql_rsp_info.code,
        data->pgsql_rsp_info.start_time,
        data->pgsql_rsp_info.close_time,
        data->pgsql_rsp_info.row_count
    );
    
    // 释放转义后的字符串内存
    if (escaped_app_name) {
        cJSON_free(escaped_app_name);
    }
    if (escaped_server_version) {
        cJSON_free(escaped_server_version);
    }
    if (escaped_db_name) {
        cJSON_free(escaped_db_name);
    }
    if (escaped_db_user) {
        cJSON_free(escaped_db_user);
    }
    if (escaped_db_password) {
        cJSON_free(escaped_db_password);
    }
    if (escaped_cmd_type) {
        cJSON_free(escaped_cmd_type);
    }
    if (escaped_sql) {
        cJSON_free(escaped_sql);
    }

    // 扩展缓冲区大小检查 - 始终需要为result字段预留空间
    if (written + 1024 > static_cast<int>(current_size)) {
        size_t new_size = current_size * 2;
        char *new_buffer = (char*)realloc(buffer, new_size);
        if (!new_buffer) {
            free(buffer);
            return NULL;
        }
        buffer = new_buffer;
        current_size = new_size;
    }
    
    // 始终添加result字段，即使没有结果集也会返回空数组
    written += snprintf(buffer + written, current_size - written, ",\"result\":[");

    // 如果有结果集（有列定义），添加结果集到rsp字段中
    if (data->pgsql_rsp_info.result_set.field_count > 0 && 
        data->pgsql_rsp_info.result_set.field_names)
    {
        // 处理有行数据的情况
        if (data->pgsql_rsp_info.row_count > 0 && data->pgsql_rsp_info.result_set.field_values) {
            for (int row_idx = 0; row_idx < data->pgsql_rsp_info.row_count; row_idx++) {
                // 检查是否需要扩展缓冲区
                if (written + 1024 > static_cast<int>(current_size)) {
                    size_t new_size = current_size * 2;
                    char *new_buffer = (char*)realloc(buffer, new_size);
                    if (!new_buffer) {
                        free(buffer);
                        return NULL;
                    }
                    buffer = new_buffer;
                    current_size = new_size;
                }
                
                if (row_idx > 0) {
                    written += snprintf(buffer + written, current_size - written, ",");
                }
                written += snprintf(buffer + written, current_size - written, "{");

                for (int col_idx = 0; col_idx < data->pgsql_rsp_info.result_set.field_count; col_idx++) {
                    int value_idx = row_idx * data->pgsql_rsp_info.result_set.field_count + col_idx;
                    
                    if (data->pgsql_rsp_info.result_set.field_names[col_idx]) {
                        // 计算本次写入需要的最大空间
                        size_t field_name_len = strlen(data->pgsql_rsp_info.result_set.field_names[col_idx]);
                        const char* field_value = (data->pgsql_rsp_info.result_set.field_values[value_idx]) ? 
                                                 data->pgsql_rsp_info.result_set.field_values[value_idx] : "";
                        size_t field_value_len = strlen(field_value);
                        size_t needed_space = field_name_len + field_value_len + 10; // 额外的引号、冒号和逗号
                        
                        // 检查剩余空间是否足够，如果不够则扩展缓冲区
                        if (written + needed_space + 100 > current_size) { // 额外100字节作为安全缓冲
                            size_t new_size = current_size + needed_space + 4096; // 额外增加4KB
                            char *new_buffer = (char*)realloc(buffer, new_size);
                            if (!new_buffer) {
                                free(buffer);
                                return NULL;
                            }
                            buffer = new_buffer;
                            current_size = new_size;
                        }
                        
                        if (col_idx > 0) {
                            written += snprintf(buffer + written, current_size - written, ",");
                        }

                        // 对字段值进行JSON转义处理
                        char *escaped_value = cJSON_EscapeString(field_value);
                        const char *safe_value = escaped_value ? escaped_value : field_value;

                        written += snprintf(buffer + written, current_size - written,
                                         "\"%s\":%s",
                                         data->pgsql_rsp_info.result_set.field_names[col_idx],
                                         safe_value);

                        // 释放转义后的字符串内存
                        if (escaped_value) {
                            cJSON_free(escaped_value);
                        }
                    }
                }

                written += snprintf(buffer + written, current_size - written, "}");
            }
        } 
        // 处理空结果集的情况（有列定义但没有行数据）
        else if (data->pgsql_rsp_info.row_count == 0) {
            // 检查是否需要扩展缓冲区
            if (written + 1024 > static_cast<int>(current_size)) {
                size_t new_size = current_size * 2;
                char *new_buffer = (char*)realloc(buffer, new_size);
                if (!new_buffer) {
                    free(buffer);
                    return NULL;
                }
                buffer = new_buffer;
                current_size = new_size;
            }
            
            // 输出一个包含所有列名但值为空字符串的对象
            written += snprintf(buffer + written, current_size - written, "{");
            
            for (int col_idx = 0; col_idx < data->pgsql_rsp_info.result_set.field_count; col_idx++) {
                if (data->pgsql_rsp_info.result_set.field_names[col_idx]) {
                    // 计算本次写入需要的最大空间
                    size_t field_name_len = strlen(data->pgsql_rsp_info.result_set.field_names[col_idx]);
                    size_t needed_space = field_name_len + 10; // 额外的引号、冒号和逗号
                    
                    // 检查剩余空间是否足够，如果不够则扩展缓冲区
                    if (written + needed_space + 100 > current_size) { // 额外100字节作为安全缓冲
                        size_t new_size = current_size + needed_space + 4096; // 额外增加4KB
                        char *new_buffer = (char*)realloc(buffer, new_size);
                        if (!new_buffer) {
                            free(buffer);
                            return NULL;
                        }
                        buffer = new_buffer;
                        current_size = new_size;
                    }
                    
                    if (col_idx > 0) {
                        written += snprintf(buffer + written, current_size - written, ",");
                    }
                    written += snprintf(buffer + written, current_size - written, 
                                     "\"%s\":\"\"",
                                     data->pgsql_rsp_info.result_set.field_names[col_idx]);
                }
            }
            
            written += snprintf(buffer + written, current_size - written, "}");
        }
    }
    // 无论如何，始终关闭结果集数组
    written += snprintf(buffer + written, current_size - written, "]");

    // 关闭rsp字段和整个JSON对象
    written += snprintf(buffer + written, current_size - written, "}}");
    s_len = written;

    return buffer;
}

// 格式化事件protobuf
static char* format_event_protobuf(const upload_postgre_info_t *data, size_t &s_len)
{
    if (!data) {
        s_len = 0;
        return nullptr;
    }

    try {
        ProtobufRawPostgreEvent protobuf_event;

        // 设置请求信息
        PostgreRequest* req = protobuf_event.mutable_req();
        req->set_db_user(data->pgsql_req_info.db_user);
        req->set_db_name(data->pgsql_req_info.db_name);
        req->set_db_password(data->pgsql_req_info.db_password);
        req->set_cmd_type(data->pgsql_req_info.cmd_type);
        req->set_sql(data->pgsql_req_info.sql_text);
        req->set_errcode(0);

        // 设置响应信息
        PostgreResponse* rsp = protobuf_event.mutable_rsp();
        rsp->set_status(data->pgsql_rsp_info.code);
        rsp->set_start_time(data->pgsql_rsp_info.start_time);
        rsp->set_close_time(data->pgsql_rsp_info.close_time);
        rsp->set_row_count(data->pgsql_rsp_info.row_count);
        rsp->set_errcode(0);

        // 设置结果集
        if (data->pgsql_rsp_info.result_set.field_count > 0 &&
            data->pgsql_rsp_info.result_set.field_names) {
            ResultSet* result_set = rsp->mutable_result_set();
            result_set->set_field_count(data->pgsql_rsp_info.result_set.field_count);

            // 添加字段名
            for (int i = 0; i < data->pgsql_rsp_info.result_set.field_count; i++) {
                if (data->pgsql_rsp_info.result_set.field_names[i]) {
                    result_set->add_field_names(data->pgsql_rsp_info.result_set.field_names[i]);
                } else {
                    result_set->add_field_names("");
                }
            }

            // 添加行数据
            if (data->pgsql_rsp_info.row_count > 0 &&
                data->pgsql_rsp_info.result_set.field_values) {
                for (int row = 0; row < data->pgsql_rsp_info.row_count; row++) {
                    ResultRow* result_row = result_set->add_rows();
                    for (int col = 0; col < data->pgsql_rsp_info.result_set.field_count; col++) {
                        int index = row * data->pgsql_rsp_info.result_set.field_count + col;
                        if (data->pgsql_rsp_info.result_set.field_values[index]) {
                            result_row->add_field_values(data->pgsql_rsp_info.result_set.field_values[index]);
                        } else {
                            result_row->add_field_values("");
                        }
                    }
                }
            }
        }

        // 设置元数据
        Meta* meta = protobuf_event.mutable_meta();
        meta->set_tm(data->pgsql_meta_info.ts);
        meta->set_type(data->pgsql_meta_info.type);
        meta->set_app_name(data->pgsql_meta_info.app_name);
        meta->set_server_version(data->pgsql_meta_info.server_version);

        // 设置网络信息
        Net* net = protobuf_event.mutable_net();
        net->set_srcip(data->pgsql_net_info.a_src_ip);
        net->set_srcport(data->pgsql_net_info.src_port);
        net->set_dstip(data->pgsql_net_info.a_dst_ip);
        net->set_dstport(data->pgsql_net_info.dst_port);
        net->set_flowsource(data->pgsql_net_info.flow_source);

        // 设置唯一标识
        UniqueId* unique_id = protobuf_event.mutable_uniqueid();
        unique_id->set_eventid(data->a_unique_id);

        // 设置MAC信息
        Mac* mac = protobuf_event.mutable_mac();
        mac->set_mac(data->pgsql_mac_info.mac);

        // 设置数据源信息
        Source* source = protobuf_event.mutable_source();
        source->set_sourcetype(SourceTypeEnum::flow);
        source->set_taskid("");
        source->set_app("");

        // 序列化
        s_len = protobuf_event.ByteSizeLong();
        char* buffer = (char*)malloc(s_len);
        if (!buffer) {
            s_len = 0;
            return nullptr;
        }

        if (!protobuf_event.SerializeToArray(buffer, s_len)) {
            free(buffer);
            s_len = 0;
            return nullptr;
        }

        return buffer;
    } catch (const std::exception& e) {
        s_len = 0;
        return nullptr;
    }
}

// 处理登录事件
void CPostgreParser::handle_login_event(postgre_stream_t *pgs, const struct conn *pcon, const postgre_parsed_data_t *p_data)
{
    if (!pgs || !pcon)
    {
        return;
    }

    // 创建登录事件上传数据结构
    upload_postgre_info_t *login_info = new upload_postgre_info_t();
    if (!login_info)
    {
        GWLOG_ERROR(m_comm, "Failed to allocate memory for login_info");
        return;
    }

    // 添加meta信息(时间戳)
    login_info->pgsql_meta_info.ts = pgs->pg_stat.startup_time;

    // 设置数据库类型
    strcpy(login_info->pgsql_meta_info.type, "PostgreSQL");

    // 设置应用名称
    if (pgs->pg_stat.application_name.s && pgs->pg_stat.application_name.len > 0)
    {
        size_t copy_len = pgs->pg_stat.application_name.len;
        if (copy_len > sizeof(login_info->pgsql_meta_info.app_name) - 1) {
            copy_len = sizeof(login_info->pgsql_meta_info.app_name) - 1;
        }
        strncpy(login_info->pgsql_meta_info.app_name, pgs->pg_stat.application_name.s, copy_len);
    }

    // 设置服务器版本
    if (pgs->pg_stat.server_version.s && pgs->pg_stat.server_version.len > 0)
    {
        size_t copy_len = pgs->pg_stat.server_version.len;
        if (copy_len > sizeof(login_info->pgsql_meta_info.server_version) - 1) {
            copy_len = sizeof(login_info->pgsql_meta_info.server_version) - 1;
        }
        strncpy(login_info->pgsql_meta_info.server_version, pgs->pg_stat.server_version.s, copy_len);
    }

    // 设置网络信息
    add_net_json(&(login_info->pgsql_net_info), pcon, false);

    // 设置mac信息
    if (pcon->p_client_mac) {
        snprintf(login_info->pgsql_mac_info.mac, sizeof(login_info->pgsql_mac_info.mac), "%02x:%02x:%02x:%02x:%02x:%02x",
                pcon->p_client_mac[0], pcon->p_client_mac[1], pcon->p_client_mac[2],
                pcon->p_client_mac[3], pcon->p_client_mac[4], pcon->p_client_mac[5]);
    }

    pthread_rwlock_rdlock(&m_traffic_source_rwlock);
    // 设置流量来源
    if (m_agent_client_ip.find(login_info->pgsql_net_info.a_src_ip) != m_agent_client_ip.end())
    {
        strncpy(login_info->pgsql_net_info.flow_source, login_info->pgsql_net_info.a_src_ip, sizeof(login_info->pgsql_net_info.flow_source) - 1);
    }
    else if (m_agent_client_ip.find(login_info->pgsql_net_info.a_dst_ip) != m_agent_client_ip.end())
    {
        strncpy(login_info->pgsql_net_info.flow_source , login_info->pgsql_net_info.a_dst_ip, sizeof(login_info->pgsql_net_info.flow_source) - 1);
    }
    else
    {
        strncpy(login_info->pgsql_net_info.flow_source, m_str_gw_ip.c_str(), sizeof(login_info->pgsql_net_info.flow_source) - 1);
    }
    pthread_rwlock_unlock(&m_traffic_source_rwlock);

    // 设置用户信息
    if (pgs->pg_stat.user.s && pgs->pg_stat.user.len > 0)
    {
        snprintf(login_info->pgsql_req_info.db_user, sizeof(login_info->pgsql_req_info.db_user),
                "%.*s", (int)pgs->pg_stat.user.len, pgs->pg_stat.user.s);
    }

    // 设置密码
    if (pgs->pg_stat.password.s && pgs->pg_stat.password.len > 0)
    {
        snprintf(login_info->pgsql_req_info.db_password, sizeof(login_info->pgsql_req_info.db_password),
                "%.*s", (int)pgs->pg_stat.password.len, pgs->pg_stat.password.s);
    }

    // 设置数据库名称
    if (pgs->pg_stat.db_name.s && pgs->pg_stat.db_name.len > 0)
    {
        snprintf(login_info->pgsql_req_info.db_name, sizeof(login_info->pgsql_req_info.db_name),
                "%.*s", (int)pgs->pg_stat.db_name.len, pgs->pg_stat.db_name.s);
    }

    snprintf(login_info->pgsql_req_info.cmd_type, sizeof(login_info->pgsql_req_info.cmd_type), "login");

    // sql语句置空
    login_info->pgsql_req_info.sql_text[0] = '\0';

    // 设置错误信息
    login_info->pgsql_rsp_info.code = 0;
    
    // 检查是否有认证错误信息并从已有结果集拷贝数据
    if (p_data && p_data->err_code > 0) {
        login_info->pgsql_rsp_info.code = p_data->err_code;
        
        // 检查是否有已创建的结果集需要拷贝
        if (p_data->rs_list && p_data->rs_list->col_cnt > 0) {
            result_set_t *src_rs = p_data->rs_list;
            
            // 拷贝结果集基本信息
            login_info->pgsql_rsp_info.result_set.field_count = src_rs->col_cnt;
            login_info->pgsql_rsp_info.row_count = src_rs->row_cnt;
            
            // 从列定义中拷贝字段名
            if (src_rs->col_def && src_rs->col_cnt > 0) {
                login_info->pgsql_rsp_info.result_set.field_names = (char**)calloc(src_rs->col_cnt, sizeof(char*));
                if (login_info->pgsql_rsp_info.result_set.field_names) {
                    column_def_t *col = src_rs->col_def;
                    for (int i = 0; i < src_rs->col_cnt && col; i++, col = col->next) {
                        if (col->name.s && col->name.len > 0) {
                            login_info->pgsql_rsp_info.result_set.field_names[i] = strndup(col->name.s, col->name.len);
                        }
                    }
                }
            }
            
            // 从行数据中拷贝字段值
            if (src_rs->rows && src_rs->row_cnt > 0) {
                int total_fields = src_rs->col_cnt * src_rs->row_cnt;
                login_info->pgsql_rsp_info.result_set.field_values = (char**)calloc(total_fields, sizeof(char*));
                if (login_info->pgsql_rsp_info.result_set.field_values) {
                    for (int row_idx = 0; row_idx < src_rs->row_cnt; row_idx++) {
                        postgre_row_data_t *row = src_rs->rows[row_idx];
                        if (row && row->row) {
                            for (int col_idx = 0; col_idx < src_rs->col_cnt && col_idx < row->field_count; col_idx++) {
                                int flat_idx = row_idx * src_rs->col_cnt + col_idx;
                                if (row->row[col_idx] && row->row[col_idx]->s && row->row[col_idx]->len > 0) {
                                    login_info->pgsql_rsp_info.result_set.field_values[flat_idx] = 
                                        strndup(row->row[col_idx]->s, row->row[col_idx]->len);
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    login_info->pgsql_rsp_info.start_time = pgs->pg_stat.startup_start_time;
    login_info->pgsql_rsp_info.close_time = pgs->pg_stat.startup_close_time;

    // 添加unique_id
    add_event_id(login_info->a_unique_id);

    // 设置内存大小
    login_info->mem_size = sizeof(upload_postgre_info_t);

    // 发送登录事件数据
    send_postgre_data(login_info);
}

// 处理访问事件
void CPostgreParser::handle_access_event(postgre_stream_t *pgs, const struct conn *pcon, const postgre_parsed_data_t *p_data)
{
    if (!pgs || !pcon || !p_data)
    {
        return;
    }

    // 创建访问事件上传数据结构
    upload_postgre_info_t *access_info = new upload_postgre_info_t();
    if (!access_info)
    {
        GWLOG_ERROR(m_comm, "Failed to allocate memory for access_info");
        return;
    }

    // 添加meta信息(时间戳)
    access_info->pgsql_meta_info.ts = p_data->pcap_ts;

    // 设置数据库类型
    strcpy(access_info->pgsql_meta_info.type, "PostgreSQL");

    // 设置应用名称
    if (pgs->pg_stat.application_name.s && pgs->pg_stat.application_name.len > 0)
    {
        size_t copy_len = pgs->pg_stat.application_name.len;
        if (copy_len > sizeof(access_info->pgsql_meta_info.app_name) - 1) {
            copy_len = sizeof(access_info->pgsql_meta_info.app_name) - 1;
        }
        strncpy(access_info->pgsql_meta_info.app_name, pgs->pg_stat.application_name.s, copy_len);
    }

    // 设置服务器版本
    if (pgs->pg_stat.server_version.s && pgs->pg_stat.server_version.len > 0)
    {
        size_t copy_len = pgs->pg_stat.server_version.len;
        if (copy_len > sizeof(access_info->pgsql_meta_info.server_version) - 1) {
            copy_len = sizeof(access_info->pgsql_meta_info.server_version) - 1; 
        }
        strncpy(access_info->pgsql_meta_info.server_version, pgs->pg_stat.server_version.s, copy_len);
    }

    // 设置网络信息
    add_net_json(&(access_info->pgsql_net_info), pcon, false);

    // 设置mac信息
    if (pcon->p_client_mac) {
        snprintf(access_info->pgsql_mac_info.mac, sizeof(access_info->pgsql_mac_info.mac), "%02x:%02x:%02x:%02x:%02x:%02x",
                pcon->p_client_mac[0], pcon->p_client_mac[1], pcon->p_client_mac[2],
                pcon->p_client_mac[3], pcon->p_client_mac[4], pcon->p_client_mac[5]);
    }

    // 设置流量来源
    pthread_rwlock_rdlock(&m_traffic_source_rwlock);
    // 设置流量来源
    if (m_agent_client_ip.find(access_info->pgsql_net_info.a_src_ip) != m_agent_client_ip.end())
    {
        strncpy(access_info->pgsql_net_info.flow_source, access_info->pgsql_net_info.a_src_ip, sizeof(access_info->pgsql_net_info.flow_source) - 1);
    }
    else if (m_agent_client_ip.find(access_info->pgsql_net_info.a_dst_ip) != m_agent_client_ip.end())
    {
        strncpy(access_info->pgsql_net_info.flow_source , access_info->pgsql_net_info.a_dst_ip, sizeof(access_info->pgsql_net_info.flow_source) - 1);
    }
    else
    {
        strncpy(access_info->pgsql_net_info.flow_source, m_str_gw_ip.c_str(), sizeof(access_info->pgsql_net_info.flow_source) - 1);
    }
    pthread_rwlock_unlock(&m_traffic_source_rwlock);

    // 设置用户信息
    if (pgs->pg_stat.user.s && pgs->pg_stat.user.len > 0)
    {
        snprintf(access_info->pgsql_req_info.db_user, sizeof(access_info->pgsql_req_info.db_user),
                "%.*s", (int)pgs->pg_stat.user.len, pgs->pg_stat.user.s);
    }

    // 设置密码
    if (pgs->pg_stat.password.s && pgs->pg_stat.password.len > 0)
    {
        snprintf(access_info->pgsql_req_info.db_password, sizeof(access_info->pgsql_req_info.db_password),
                "%.*s", (int)pgs->pg_stat.password.len, pgs->pg_stat.password.s);
    }

    // 设置数据库名称
    if (pgs->pg_stat.db_name.s && pgs->pg_stat.db_name.len > 0)
    {
        snprintf(access_info->pgsql_req_info.db_name, sizeof(access_info->pgsql_req_info.db_name),
                "%.*s", (int)pgs->pg_stat.db_name.len, pgs->pg_stat.db_name.s);
    }

    // 设置SQL信息
    if (p_data->sql_list)
    {
        sql_statement_t *sql = p_data->sql_list;
        if (sql->sql.s && sql->sql.len > 0)
        {
            size_t copy_len = sql->sql.len;
            if (copy_len >= sizeof(access_info->pgsql_req_info.sql_text))
            {
                copy_len = sizeof(access_info->pgsql_req_info.sql_text) - 1;
            }
            
            memset(access_info->pgsql_req_info.sql_text, 0, sizeof(access_info->pgsql_req_info.sql_text));
            memcpy(access_info->pgsql_req_info.sql_text, sql->sql.s, copy_len);
            access_info->pgsql_req_info.sql_text[copy_len] = '\0';

            // 提取SQL类型（如select、insert等）和流复制命令，并填充到cmd_type字段
            // 跳过前导空格
            const char *sql_ptr = sql->sql.s;
            size_t sql_len = sql->sql.len;
            size_t i = 0;
            while (i < sql_len && (sql_ptr[i] == ' ' || sql_ptr[i] == '\t' || sql_ptr[i] == '\r' || sql_ptr[i] == '\n')) {
                i++;
            }

            // 检查是否为流复制命令（支持多单词命令）
            bool is_replication_cmd = false;
            const char *replication_commands[] = {
                "IDENTIFY_SYSTEM",
                "CREATE_REPLICATION_SLOT",
                "START_REPLICATION",
                "BASE_BACKUP",
                "TIMELINE_HISTORY"
            };

            for (size_t cmd_idx = 0; cmd_idx < sizeof(replication_commands) / sizeof(replication_commands[0]); cmd_idx++) {
                size_t repl_cmd_len = strlen(replication_commands[cmd_idx]);
                if (sql_len - i >= repl_cmd_len &&
                    strncasecmp(sql_ptr + i, replication_commands[cmd_idx], repl_cmd_len) == 0) {
                    // 确保命令后面是空格、结束符或其他分隔符
                    if (i + repl_cmd_len >= sql_len ||
                        sql_ptr[i + repl_cmd_len] == ' ' ||
                        sql_ptr[i + repl_cmd_len] == '\t' ||
                        sql_ptr[i + repl_cmd_len] == '\r' ||
                        sql_ptr[i + repl_cmd_len] == '\n' ||
                        sql_ptr[i + repl_cmd_len] == ';') {

                        // 复制流复制命令到cmd_type
                        size_t copy_len = repl_cmd_len;
                        if (copy_len >= sizeof(access_info->pgsql_req_info.cmd_type)) {
                            copy_len = sizeof(access_info->pgsql_req_info.cmd_type) - 1;
                        }
                        memset(access_info->pgsql_req_info.cmd_type, 0, sizeof(access_info->pgsql_req_info.cmd_type));
                        memcpy(access_info->pgsql_req_info.cmd_type, replication_commands[cmd_idx], copy_len);
                        access_info->pgsql_req_info.cmd_type[copy_len] = '\0';
                        is_replication_cmd = true;
                        break;
                    }
                }
            }

            // 如果不是流复制命令，按原有逻辑提取第一个单词
            if (!is_replication_cmd) {
                // 提取第一个单词
                size_t cmd_start = i;
                while (i < sql_len && ((sql_ptr[i] >= 'a' && sql_ptr[i] <= 'z') ||
                                     (sql_ptr[i] >= 'A' && sql_ptr[i] <= 'Z') ||
                                     (sql_ptr[i] >= '0' && sql_ptr[i] <= '9') ||
                                     sql_ptr[i] == '_')) {
                    i++;
                }
                size_t cmd_len = i - cmd_start;
                if (cmd_len > 0) {
                    if (cmd_len >= sizeof(access_info->pgsql_req_info.cmd_type)) {
                        cmd_len = sizeof(access_info->pgsql_req_info.cmd_type) - 1;
                    }
                    memset(access_info->pgsql_req_info.cmd_type, 0, sizeof(access_info->pgsql_req_info.cmd_type));
                    memcpy(access_info->pgsql_req_info.cmd_type, sql_ptr + cmd_start, cmd_len);
                    access_info->pgsql_req_info.cmd_type[cmd_len] = '\0';
                } else {
                    access_info->pgsql_req_info.cmd_type[0] = '\0';
                }
            }
        }
        else
        {
            GWLOG_ERROR(m_comm, "[Postgre][Access] Invalid SQL statement data\n");
            access_info->pgsql_req_info.sql_text[0] = '\0';
        }
    }

    // 设置错误信息
    access_info->pgsql_rsp_info.code = p_data->err_code;

    // 检查是否需要动态创建列定义
    if (p_data->rs_list && p_data->rs_list->row_cnt > 0 &&
        (!p_data->rs_list->col_def || p_data->rs_list->col_cnt == 0)) {

        GWLOG_WARN(m_comm, "[PostgreSQL][PacketLoss] Detected rows without column definitions, "
                   "creating dynamic column definitions\n");

        // 根据第一行数据动态创建列定义
        if (create_dynamic_column_definitions_from_row_data(p_data->rs_list)) {
            GWLOG_INFO(m_comm, "[PostgreSQL][PacketLoss] Successfully created %d dynamic column definitions\n",
                       p_data->rs_list->col_cnt);
        } else {
            GWLOG_ERROR(m_comm, "[PostgreSQL][PacketLoss] Failed to create dynamic column definitions\n");
        }
    }

    // 修改条件：只要有结果集和列定义就处理，不要求必须有行数据
    if (p_data->rs_list && p_data->rs_list->col_def && p_data->rs_list->col_cnt > 0)
    {
        result_set_t *first_rs = p_data->rs_list;
        
        // 处理行数：如果没有行数据或行数为0，则设置为0；否则限制在配置上限内
        int max_rows = 0;
        if (first_rs->rows && first_rs->row_cnt > 0) {
            max_rows = (first_rs->row_cnt > m_upload_results_num) ? m_upload_results_num : first_rs->row_cnt;
        }
        
        access_info->pgsql_rsp_info.row_count = max_rows;
        access_info->pgsql_rsp_info.result_set.field_count = first_rs->col_cnt;
        
        // 分配字段名数组
        access_info->pgsql_rsp_info.result_set.field_names = (char**)calloc(first_rs->col_cnt, sizeof(char*));
        
        // 分配字段值数组：即使没有行数据，也分配空间以保持一致性
        // 对于空结果集，field_values将为空数组
        if (max_rows > 0) {
            access_info->pgsql_rsp_info.result_set.field_values = (char**)calloc(first_rs->col_cnt * max_rows, sizeof(char*));
        } else {
            access_info->pgsql_rsp_info.result_set.field_values = NULL; // 空结果集不需要值数组
        }
        
        if (access_info->pgsql_rsp_info.result_set.field_names)
        {
            // 处理字段名
            column_def_t *col = first_rs->col_def;
            int col_idx = 0;
            while (col && col_idx < first_rs->col_cnt)
            {
                if (col->name.s && col->name.len > 0)
                {
                    access_info->pgsql_rsp_info.result_set.field_names[col_idx] = (char*)calloc(col->name.len + 1, sizeof(char));
                    if (access_info->pgsql_rsp_info.result_set.field_names[col_idx])
                    {
                        memcpy(access_info->pgsql_rsp_info.result_set.field_names[col_idx], col->name.s, col->name.len);
                    }
                }
                else
                {
                    access_info->pgsql_rsp_info.result_set.field_names[col_idx] = (char*)calloc(1, sizeof(char));
                    if (access_info->pgsql_rsp_info.result_set.field_names[col_idx])
                    {
                        access_info->pgsql_rsp_info.result_set.field_names[col_idx][0] = '\0';
                    }
                }
                col = col->next;
                col_idx++;
            }
            
            // 处理行数据：只有当有行数据时才处理
            if (max_rows > 0 && access_info->pgsql_rsp_info.result_set.field_values)
            {
                for (int row_idx = 0; row_idx < max_rows; row_idx++)
                {
                    postgre_row_data_t *row_data = first_rs->rows[row_idx];
                    if (!row_data)
                    {
                        GWLOG_ERROR(m_comm, "[Postgre][ResultSet] Row %d is NULL\n", row_idx);
                        continue;
                    }
                    if (!row_data->row)
                    {
                        GWLOG_ERROR(m_comm, "[Postgre][ResultSet] Row %d data is NULL\n", row_idx);
                        continue;
                    }
                    
                    // 关键修复：验证列定义链表完整性和行数据一致性
                    int actual_row_fields = 0;
                    column_def_t *col_check = first_rs->col_def;
                    while (col_check && actual_row_fields < first_rs->col_cnt) {
                        // 验证列定义的完整性
                        if (!col_check->name.s || col_check->name.len == 0) {
                            GWLOG_ERROR(m_comm, "[Postgre][ResultSet] Invalid column definition at index %d: name.s=%p, len=%zu\n", 
                                       actual_row_fields, col_check->name.s, col_check->name.len);
                            break;
                        }
                        // 检查列名是否包含垃圾数据
                        if (col_check->name.len > 1000 || (col_check->name.s && strlen(col_check->name.s) != col_check->name.len)) {
                            GWLOG_ERROR(m_comm, "[Postgre][ResultSet] Corrupted column definition at index %d: name='%.*s', len=%zu, strlen=%zu\n", 
                                       actual_row_fields, (int)std::min(col_check->name.len, (size_t)50), 
                                       col_check->name.s, col_check->name.len, 
                                       col_check->name.s ? strlen(col_check->name.s) : 0);
                            break;
                        }
                        actual_row_fields++;
                        col_check = col_check->next;
                    }
                    
                    // 如果列定义损坏，跳过此行数据处理
                    if (actual_row_fields != first_rs->col_cnt) {
                        GWLOG_ERROR(m_comm, "[Postgre][ResultSet] Column definition corruption detected: expected %d columns, found %d valid columns\n", 
                                   first_rs->col_cnt, actual_row_fields);
                        // 使用实际验证的列数作为安全边界
                        int safe_col_cnt = std::min(actual_row_fields, first_rs->col_cnt);
                        for (int col_idx = 0; col_idx < safe_col_cnt; col_idx++)
                        {
                            int value_idx = row_idx * first_rs->col_cnt + col_idx;
                            // 检查是否为NULL值或越界访问
                            if (col_idx >= safe_col_cnt || !row_data->row[col_idx])
                            {
                                // 对于NULL值或越界，我们存储一个空字符串
                                access_info->pgsql_rsp_info.result_set.field_values[value_idx] = (char*)calloc(1, sizeof(char));
                                if (access_info->pgsql_rsp_info.result_set.field_values[value_idx])
                                {
                                    access_info->pgsql_rsp_info.result_set.field_values[value_idx][0] = '\0';
                                }
                            }
                            else if (row_data->row[col_idx]->s && row_data->row[col_idx]->len > 0)
                            {
                                // 限制单个字段值的大小
                                size_t field_len = row_data->row[col_idx]->len;
                                if (field_len > static_cast<size_t>(m_upload_field_max_size)) {
                                    field_len = m_upload_field_max_size;
                                }
                                access_info->pgsql_rsp_info.result_set.field_values[value_idx] = (char*)calloc(field_len + 1, sizeof(char));
                                if (access_info->pgsql_rsp_info.result_set.field_values[value_idx])
                                {
                                    memcpy(access_info->pgsql_rsp_info.result_set.field_values[value_idx], 
                                          row_data->row[col_idx]->s, field_len);
                                }
                            }
                            else
                            {
                                // 处理空字符串的情况
                                access_info->pgsql_rsp_info.result_set.field_values[value_idx] = (char*)calloc(1, sizeof(char));
                                if (access_info->pgsql_rsp_info.result_set.field_values[value_idx])
                                {
                                    access_info->pgsql_rsp_info.result_set.field_values[value_idx][0] = '\0';
                                }
                            }
                        }
                        continue; // 跳过损坏的行
                    }
                    
                    // 正常处理：列定义完整性验证通过
                    for (int col_idx = 0; col_idx < first_rs->col_cnt; col_idx++)
                    {
                        int value_idx = row_idx * first_rs->col_cnt + col_idx;
                        // 检查是否为NULL值
                        if (!row_data->row[col_idx])
                        {
                            // 对于NULL值，我们存储一个空字符串
                            access_info->pgsql_rsp_info.result_set.field_values[value_idx] = (char*)calloc(1, sizeof(char));
                            if (access_info->pgsql_rsp_info.result_set.field_values[value_idx])
                            {
                                access_info->pgsql_rsp_info.result_set.field_values[value_idx][0] = '\0';
                            }
                        }
                        else if (row_data->row[col_idx]->s && row_data->row[col_idx]->len > 0)
                        {
                            // 限制单个字段值的大小
                            size_t field_len = row_data->row[col_idx]->len;
                            if (field_len > static_cast<size_t>(m_upload_field_max_size)) {
                                field_len = m_upload_field_max_size;
                            }
                            access_info->pgsql_rsp_info.result_set.field_values[value_idx] = (char*)calloc(field_len + 1, sizeof(char));
                            if (access_info->pgsql_rsp_info.result_set.field_values[value_idx])
                            {
                                memcpy(access_info->pgsql_rsp_info.result_set.field_values[value_idx],
                                      row_data->row[col_idx]->s, field_len);
                            }
                        }
                        else
                        {
                            // 处理空字符串的情况
                            access_info->pgsql_rsp_info.result_set.field_values[value_idx] = (char*)calloc(1, sizeof(char));
                            if (access_info->pgsql_rsp_info.result_set.field_values[value_idx])
                            {
                                access_info->pgsql_rsp_info.result_set.field_values[value_idx][0] = '\0';
                            }
                        }
                    }
                }
            }
        }
        else
        {
            GWLOG_ERROR(m_comm, "[Postgre][ResultSet] Failed to allocate memory for field names array\n");
            access_info->pgsql_rsp_info.result_set.field_count = 0;
            access_info->pgsql_rsp_info.row_count = 0;
            // 清理已分配的内存
            if (access_info->pgsql_rsp_info.result_set.field_values)
            {
                free(access_info->pgsql_rsp_info.result_set.field_values);
                access_info->pgsql_rsp_info.result_set.field_values = NULL;
            }
        }
    }

    // 设置时间信息
    access_info->pgsql_rsp_info.start_time = p_data->start_time;
    access_info->pgsql_rsp_info.close_time = p_data->close_time;

    // 添加unique_id
    add_event_id(access_info->a_unique_id);

    // 设置内存大小
    access_info->mem_size = sizeof(upload_postgre_info_t);

    // 发送访问事件数据
    send_postgre_data(access_info);
}

CWorkerQueue *CPostgreParser::new_wq_upload_msg()
{
    m_upload_data_wq = m_comm->create_worker_queue();
    if (m_upload_data_wq == NULL)
    {
        return NULL;
    }
    
    CTaskWorkerUploadMsg *ptw = new CTaskWorkerUploadMsg();
    ptw->set_wq(m_upload_data_wq);
    ptw->set_parser(this);

    m_upload_data_wk = ptw;

    m_upload_data_wq->set_gw_common(m_comm);
    m_upload_data_wq->set_watchdog(m_comm->get_watchdog());
    m_upload_data_wq->set_task_worker(ptw);

    m_upload_data_wq->set_queue_num_and_bytes(m_conf_postgre_upload_queue_max_num, m_conf_postgre_upload_queue_memory_max_size_bytes);
    m_upload_data_wq->set_queue_name(POSTGRE_DATA_UPLOAD_QUEUE);
    m_upload_data_wq->init();
    m_upload_data_wq->create_queue();

    m_upload_data_wq->adjust_worker_thread_num(m_conf_postgre_upload_thread_num);

    m_comm->get_gw_stats()->set_task(m_upload_data_wq->get_queue_name(), m_upload_data_wq, 50);
    m_comm->get_gw_stats()->set_mem_stat(m_upload_data_wq->get_queue_name(), &m_upload_data_wq->get_queue_mem_size(), &m_upload_data_wq->get_queue_max_mem_size());

    return m_upload_data_wq;
}

void CPostgreParser::send_postgre_data(upload_postgre_info_t *p)
{
    if (m_upload_data_wq == NULL)
    {
        GWLOG_ERROR(m_comm, "m_upload_data_wq is NULL\n");
        return;
    }

    // 放入队列
    if (!m_upload_data_wq->queue_put_data(p, p->mem_size)) {
        GWLOG_ERROR(m_comm, "[Postgre][send_postgre_data] Failed to queue put data\n");
        free_postgre_upload_data(p);  // 释放原始数据
    }
}

void CPostgreParser::postgre_cb_upload_msg(const char *s, size_t s_len)
{
    if (unlikely(m_upload == NULL))
    {
        GWLOG_ERROR(m_comm, "postgre parser, upload null(%s)\n", m_conf_upload_name.c_str());
        SAFE_FREE((void*)s);
        return;
    }

    UploadMsg *pum = new UploadMsg;
    memset(pum, 0, sizeof(UploadMsg));

    pum->cb = sizeof(UploadMsg);
    pum->destroy_func = free_upload_msg;
    pum->parser = this;
    pum->length = s_len;
    pum->s = s;
    pum->msgtype = msg_type;
    pum->mem_size = sizeof(UploadMsg) + pum->length;

    m_upload->put_msg(pum);
}

void CPostgreParser::free_upload_msg(const struct UploadMsg *pum)
{
    ASSERT(pum != NULL);
    delete pum;
}

void CPostgreParser::add_net_json(pgsql_net_info_t *p_net_info, const struct conn *pcon, bool reverse)
{
    if (NULL == p_net_info || NULL == pcon)
    {
        return;
    }

    if (pcon->client.v == 4)
    {
        if (reverse) 
        {
            strncpy(p_net_info->a_src_ip, int_ntoa(pcon->server.ipv4), COUNTOF(p_net_info->a_src_ip) - 1);
            strncpy(p_net_info->a_dst_ip, int_ntoa(pcon->client.ipv4), COUNTOF(p_net_info->a_dst_ip) - 1);
        }
        else
        {
            strncpy(p_net_info->a_src_ip, int_ntoa(pcon->client.ipv4), COUNTOF(p_net_info->a_src_ip) - 1);
            strncpy(p_net_info->a_dst_ip, int_ntoa(pcon->server.ipv4), COUNTOF(p_net_info->a_dst_ip) - 1);
        }
    }
    else
    {
        if (reverse) 
        {
            get_ip6addr_str((uint32_t*)pcon->server.ipv6, p_net_info->a_src_ip, COUNTOF(p_net_info->a_src_ip));
            get_ip6addr_str((uint32_t*)pcon->client.ipv6, p_net_info->a_dst_ip, COUNTOF(p_net_info->a_dst_ip));
        }
        else
        {
            get_ip6addr_str((uint32_t*)pcon->client.ipv6, p_net_info->a_src_ip, COUNTOF(p_net_info->a_src_ip));
            get_ip6addr_str((uint32_t*)pcon->server.ipv6, p_net_info->a_dst_ip, COUNTOF(p_net_info->a_dst_ip));
        }
    }

    if (reverse) 
    {
        p_net_info->src_port = pcon->server.port;
        p_net_info->dst_port = pcon->client.port;
    }
    else
    {
        p_net_info->src_port = pcon->client.port;
        p_net_info->dst_port = pcon->server.port;
    }

    return;
}

int CPostgreParser::worker_routine_postgre_upload_data_inner(upload_postgre_info_t *p_upload_postgre_info)
{
    if (!p_upload_postgre_info) {
        GWLOG_ERROR(m_comm, "[Postgre][Worker] Invalid null pointer\n");
        return -1;
    }

    // 验证关键字段
    if (p_upload_postgre_info->mem_size != sizeof(upload_postgre_info_t)) {
        GWLOG_ERROR(m_comm, "[Postgre][Worker] Invalid mem_size: expected %zu, got %zu\n",
                   sizeof(upload_postgre_info_t), p_upload_postgre_info->mem_size);
        return -1;
    }

    // 验证结果集数据
    if (p_upload_postgre_info->pgsql_rsp_info.result_set.field_count > 0) {
        if (!p_upload_postgre_info->pgsql_rsp_info.result_set.field_names) {
            GWLOG_ERROR(m_comm, "[Postgre][Worker] Invalid field_names pointer\n");
            return -1;
        }
        // 对于空结果集（row_count=0），field_values可以为NULL
        if (p_upload_postgre_info->pgsql_rsp_info.row_count > 0 && !p_upload_postgre_info->pgsql_rsp_info.result_set.field_values) {
            GWLOG_ERROR(m_comm, "[Postgre][Worker] Invalid field_values pointer for non-empty result set\n");
            return -1;
        }
        // 移除对row_count > 0的强制要求，允许空结果集（row_count = 0）
        if (p_upload_postgre_info->pgsql_rsp_info.row_count < 0) {
            GWLOG_ERROR(m_comm, "[Postgre][Worker] Invalid row count: %d\n", p_upload_postgre_info->pgsql_rsp_info.row_count);
            return -1;
        }
    }

    // 格式化事件数据
    size_t s_len = 0;
    char *s = nullptr;

    if (m_upload_protobuf_enable == 1) {
        // 使用protobuf格式
        s = format_event_protobuf(p_upload_postgre_info, s_len);
        if (!s) {
            GWLOG_ERROR(m_comm, "[Postgre][Worker] Failed to format protobuf\n");
            return -1;
        }
    } else {
        // 使用JSON格式
        s = format_event_json(p_upload_postgre_info, s_len);
        if (!s) {
            GWLOG_ERROR(m_comm, "[Postgre][Worker] Failed to format JSON\n");
            return -1;
        }
    }

    postgre_cb_upload_msg(s, s_len);
    return 0;
}

void CPostgreParser::free_postgre_upload_data(upload_postgre_info_t *p)
{
    if (!p) {
        return;
    }

    // 释放结果集内存
    if (p->pgsql_rsp_info.result_set.field_count > 0) {
        // 释放字段名数组
        if (p->pgsql_rsp_info.result_set.field_names) {
            for (int i = 0; i < p->pgsql_rsp_info.result_set.field_count; i++) {
                if (p->pgsql_rsp_info.result_set.field_names[i]) {
                    free(p->pgsql_rsp_info.result_set.field_names[i]);
                }
            }
            free(p->pgsql_rsp_info.result_set.field_names);
            p->pgsql_rsp_info.result_set.field_names = NULL;
        }
        
        // 释放字段值数组
        if (p->pgsql_rsp_info.result_set.field_values) {
            for (int i = 0; i < p->pgsql_rsp_info.result_set.field_count * p->pgsql_rsp_info.row_count; i++) {
                if (p->pgsql_rsp_info.result_set.field_values[i]) {
                    free(p->pgsql_rsp_info.result_set.field_values[i]);
                }
            }
            free(p->pgsql_rsp_info.result_set.field_values);
            p->pgsql_rsp_info.result_set.field_values = NULL;
        }
    }

    // 重置结果集相关字段
    p->pgsql_rsp_info.result_set.field_count = 0;
    p->pgsql_rsp_info.row_count = 0;
    p->pgsql_rsp_info.code = 0;
    p->pgsql_rsp_info.start_time = 0;
    p->pgsql_rsp_info.close_time = 0;

    delete p;
}

void CPostgreParser::add_event_id(char *p_event_id)
{
    if (NULL == p_event_id)
    {
      return;
    }

    char a_unique_code[64] = {0};
    uint64_t u64_time_val = 0;
    get_ms_timeval(&u64_time_val);

    if (m_u64_postgre_upload_ms == 0)
    {
        m_u64_postgre_upload_ms = u64_time_val;
        m_u32_postgre_upload_index = 1;
    }
    else
    {
        if (u64_time_val == m_u64_postgre_upload_ms)
        {
            m_u32_postgre_upload_index ++;
        }
        else
        {
            m_u64_postgre_upload_ms = u64_time_val;
            m_u32_postgre_upload_index = 1;
        }
    }

    /* 获取唯一标识ID */
    get_unique_event_id(m_str_gw_ip.c_str(), m_u64_postgre_upload_ms, m_u32_postgre_upload_index, a_unique_code, sizeof(a_unique_code) - 1);

    /* 将unique_code进行base64编码 */
    base64_encode((unsigned char*)p_event_id, (unsigned char*)a_unique_code, strlen(a_unique_code));

    return;
}

/**
 * 根据行数据动态创建列定义
 * @param rs 结果集指针
 * @return 是否创建成功
 */
bool CPostgreParser::create_dynamic_column_definitions_from_row_data(result_set_t *rs)
{
    if (!rs || !rs->rows || rs->row_cnt == 0 || !rs->rows[0]) {
        return false;
    }

    // 获取第一行数据来确定列数
    postgre_row_data_t *first_row = rs->rows[0];
    int field_count = first_row->field_count;

    if (field_count <= 0) {
        return false;
    }

    GWLOG_INFO(m_comm, "[PostgreSQL][PacketLoss] Creating %d dynamic column definitions based on row data\n",
               field_count);

    // 创建列定义链表
    column_def_t *col_head = NULL;
    column_def_t *col_tail = NULL;

    for (int i = 0; i < field_count; i++) {
        column_def_t *col = new column_def_t();
        if (!col) {
            // 清理已分配的列定义
            cleanup_column_definitions(col_head);
            return false;
        }

        memset(col, 0, sizeof(column_def_t));

        // 创建通用列名
        char col_name[32];
        snprintf(col_name, sizeof(col_name), "column%d", i + 1);

        col->name.s = strdup(col_name);
        if (!col->name.s) {
            delete col;
            cleanup_column_definitions(col_head);
            return false;
        }

        col->name.len = strlen(col_name);

        // 设置默认类型信息
        col->table_oid = 0;
        col->column_id = i + 1;
        col->type_oid = PG_TYPE_TEXT;  // 默认为TEXT类型
        col->type_len = -1;            // 变长
        col->type_mod = -1;
        col->format_code = PG_FORMAT_TEXT;
        col->next = NULL;

        // 添加到链表
        if (col_tail) {
            col_tail->next = col;
            col_tail = col;
        } else {
            col_head = col_tail = col;
        }
    }

    // 更新结果集的列定义
    rs->col_def = col_head;
    rs->col_cnt = field_count;

    return true;
}