/**
 * PostgreSQL二进制数据处理函数
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>
#include <time.h>
#include <arpa/inet.h>

#include "postgre_parser.h"
#include "gw_common.h"
#include "gw_logger.h"

// 辅助函数：网络字节序转主机字节序的双精度浮点数
static double ntohd(double value) {
    uint64_t tmp;
    double result;
    memcpy(&tmp, &value, sizeof(double));
    
    // 网络字节序是大端字节序
#if __BYTE_ORDER == __LITTLE_ENDIAN
    tmp = (((uint64_t)ntohl((uint32_t)(tmp >> 32))) | 
           (((uint64_t)ntohl((uint32_t)tmp)) << 32));
    memcpy(&result, &tmp, sizeof(double));
#else
    // 如果是大端系统，不需要转换
    memcpy(&result, &value, sizeof(double));
#endif
    
    return result;
}

// 辅助函数：网络字节序转主机字节序的单精度浮点数
static float ntohf(float value) {
    uint32_t tmp;
    float result;
    memcpy(&tmp, &value, sizeof(float));
    
    // 网络字节序是大端字节序
#if __BYTE_ORDER == __LITTLE_ENDIAN
    tmp = ntohl(tmp);
    memcpy(&result, &tmp, sizeof(float));
#else
    // 如果是大端系统，不需要转换
    memcpy(&result, &value, sizeof(float));
#endif
    
    return result;
}

// 处理布尔类型
char* pg_handle_bool(const char* data, int len, uint32_t type_oid, int16_t type_len) {
    if (len != 1) {
        return strdup("ERROR");
    }
    
    // PostgreSQL二进制布尔值：0=false, 1=true
    bool value = data[0];
    char* result = (char*)malloc(6); // 最大"false"+'\0'
    
    if (result) {
        strcpy(result, value ? "true" : "false");
    }
    
    return result;
}

// 处理1字节整数
char* pg_handle_char(const char* data, int len, uint32_t type_oid, int16_t type_len) {
    if (len != 1) {
        return strdup("ERROR");
    }
    
    char value = data[0];
    char* result = (char*)malloc(5); // 最大-128和'\0'
    
    if (result) {
        snprintf(result, 5, "%d", (int)value);
    }
    
    return result;
}

// 处理2字节整数
char* pg_handle_int2(const char* data, int len, uint32_t type_oid, int16_t type_len) {
    if (len != 2) {
        return strdup("ERROR");
    }
    
    int16_t value;
    memcpy(&value, data, sizeof(int16_t));
    value = ntohs(value); // 网络字节序转主机字节序
    
    char* result = (char*)malloc(8); // 最大-32768和'\0'
    
    if (result) {
        snprintf(result, 8, "%d", (int)value);
    }
    
    return result;
}

// 处理4字节整数
char* pg_handle_int4(const char* data, int len, uint32_t type_oid, int16_t type_len) {
    if (len != 4) {
        return strdup("ERROR");
    }
    
    int32_t value;
    memcpy(&value, data, sizeof(int32_t));
    value = ntohl(value); // 网络字节序转主机字节序
    
    char* result = (char*)malloc(12); // 最大-2147483648和'\0'
    
    if (result) {
        snprintf(result, 12, "%d", value);
    }
    
    return result;
}

// 处理8字节整数
char* pg_handle_int8(const char* data, int len, uint32_t type_oid, int16_t type_len) {
    if (len != 8) {
        return strdup("ERROR");
    }
    
    int64_t value;
    uint32_t hi, lo;
    
    memcpy(&hi, data, sizeof(uint32_t));
    memcpy(&lo, data + sizeof(uint32_t), sizeof(uint32_t));
    
    hi = ntohl(hi);
    lo = ntohl(lo);
    
    // 组合高32位和低32位
    value = ((int64_t)hi << 32) | lo;
    
    char* result = (char*)malloc(22); // 最大64位整数和'\0'
    
    if (result) {
        snprintf(result, 22, "%" PRId64, value);
    }
    
    return result;
}

// 处理单精度浮点数
char* pg_handle_float4(const char* data, int len, uint32_t type_oid, int16_t type_len) {
    if (len != 4) {
        return strdup("ERROR");
    }
    
    float value;
    memcpy(&value, data, sizeof(float));
    value = ntohf(value); // 网络字节序转主机字节序
    
    char* result = (char*)malloc(32); // 足够表示浮点数
    
    if (result) {
        snprintf(result, 32, "%.8g", value);
    }
    
    return result;
}

// 处理双精度浮点数
char* pg_handle_float8(const char* data, int len, uint32_t type_oid, int16_t type_len) {
    if (len != 8) {
        return strdup("ERROR");
    }
    
    double value;
    memcpy(&value, data, sizeof(double));
    value = ntohd(value); // 网络字节序转主机字节序
    
    char* result = (char*)malloc(32); // 足够表示双精度浮点数
    
    if (result) {
        snprintf(result, 32, "%.16g", value);
    }
    
    return result;
}

// 处理文本类型（变长字符串）
char* pg_handle_text(const char* data, int len, uint32_t type_oid, int16_t type_len) {
    char* result = (char*)malloc(len + 1);
    
    if (result) {
        memcpy(result, data, len);
        result[len] = '\0';
    }
    
    return result;
}

// 处理日期类型
char* pg_handle_date(const char* data, int len, uint32_t type_oid, int16_t type_len) {
    if (len != 4) {
        return strdup("ERROR");
    }
    
    // PostgreSQL日期是相对于2000-01-01的天数
    int32_t days;
    memcpy(&days, data, sizeof(int32_t));
    days = ntohl(days);
    
    // 转换为time_t（从1970-01-01开始的秒数）
    // 2000-01-01与1970-01-01相差10957天
    time_t epoch_seconds = (days + 10957) * 86400; // 86400秒/天
    
    struct tm *tm_info = gmtime(&epoch_seconds);
    
    char* result = (char*)malloc(11); // YYYY-MM-DD + '\0'
    
    if (result && tm_info) {
        strftime(result, 11, "%Y-%m-%d", tm_info);
    } else if (result) {
        strcpy(result, "ERROR");
    }
    
    return result;
}

// 处理时间类型（不带时区）
char* pg_handle_time(const char* data, int len, uint32_t type_oid, int16_t type_len) {
    if (len != 8) {
        return strdup("ERROR");
    }
    
    // PostgreSQL存储的是微秒
    int64_t microseconds;
    uint32_t hi, lo;
    
    memcpy(&hi, data, sizeof(uint32_t));
    memcpy(&lo, data + sizeof(uint32_t), sizeof(uint32_t));
    
    hi = ntohl(hi);
    lo = ntohl(lo);
    
    // 组合高32位和低32位
    microseconds = ((int64_t)hi << 32) | lo;
    
    // 计算时、分、秒、微秒
    int hour = (int)(microseconds / 3600000000LL);
    microseconds %= 3600000000LL;
    int minute = (int)(microseconds / 60000000LL);
    microseconds %= 60000000LL;
    int second = (int)(microseconds / 1000000LL);
    microseconds %= 1000000LL;
    
    char* result = (char*)malloc(20); // HH:MM:SS.mmmmmm + '\0'
    
    if (result) {
        snprintf(result, 20, "%02d:%02d:%02d.%06lld", hour, minute, second, (long long)microseconds);
    }
    
    return result;
}

// 处理时间戳类型（带或不带时区）
char* pg_handle_timestamp(const char* data, int len, uint32_t type_oid, int16_t type_len) {
    if (len != 8) {
        return strdup("ERROR");
    }
    
    // PostgreSQL存储的是微秒，从2000-01-01 00:00:00开始
    int64_t microseconds;
    uint32_t hi, lo;
    
    memcpy(&hi, data, sizeof(uint32_t));
    memcpy(&lo, data + sizeof(uint32_t), sizeof(uint32_t));
    
    hi = ntohl(hi);
    lo = ntohl(lo);
    
    // 组合高32位和低32位
    microseconds = ((int64_t)hi << 32) | lo;
    
    // 计算秒数和微秒部分
    time_t seconds = (time_t)(microseconds / 1000000LL);
    int usec = (int)(microseconds % 1000000LL);
    
    // 2000-01-01与1970-01-01相差946684800秒
    seconds += 946684800;
    
    struct tm *tm_info = gmtime(&seconds);
    
    char* result = (char*)malloc(32); // YYYY-MM-DD HH:MM:SS.mmmmmm + '\0'
    
    if (result && tm_info) {
        char datetime[20];
        strftime(datetime, 20, "%Y-%m-%d %H:%M:%S", tm_info);
        snprintf(result, 32, "%s.%06d", datetime, usec);
    } else if (result) {
        strcpy(result, "ERROR");
    }
    
    return result;
}

// 处理二进制数据（bytea）
char* pg_handle_bytea(const char* data, int len, uint32_t type_oid, int16_t type_len) {
    // 二进制数据以十六进制表示，每字节需要2个字符，再加上前缀"\x"和结束字符
    size_t result_len = 2 + (len * 2) + 1;
    char* result = (char*)malloc(result_len);
    
    if (!result) {
        return NULL;
    }
    
    // 添加前缀
    result[0] = '\\';
    result[1] = 'x';
    
    // 转换为十六进制
    const char hex_chars[] = "0123456789abcdef";
    for (int i = 0; i < len; i++) {
        unsigned char byte = (unsigned char)data[i];
        result[2 + (i * 2)] = hex_chars[byte >> 4];
        result[2 + (i * 2) + 1] = hex_chars[byte & 0x0F];
    }
    
    // 添加结束字符
    result[result_len - 1] = '\0';
    
    return result;
}

// 处理UUID类型
char* pg_handle_uuid(const char* data, int len, uint32_t type_oid, int16_t type_len) {
    if (len != 16) {
        return strdup("ERROR");
    }
    
    // UUID格式: xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx (36字符+'\0')
    char* result = (char*)malloc(37);
    
    if (result) {
        const unsigned char* bytes = (const unsigned char*)data;
        snprintf(result, 37, 
                "%02x%02x%02x%02x-%02x%02x-%02x%02x-%02x%02x-%02x%02x%02x%02x%02x%02x",
                bytes[0], bytes[1], bytes[2], bytes[3],
                bytes[4], bytes[5],
                bytes[6], bytes[7],
                bytes[8], bytes[9],
                bytes[10], bytes[11], bytes[12], bytes[13], bytes[14], bytes[15]);
    }
    
    return result;
}

// 处理点类型
char* pg_handle_point(const char* data, int len, uint32_t type_oid, int16_t type_len) {
    if (len != 16) {
        return strdup("ERROR");
    }
    
    double x, y;
    
    memcpy(&x, data, sizeof(double));
    memcpy(&y, data + 8, sizeof(double));
    
    x = ntohd(x);
    y = ntohd(y);
    
    char* result = (char*)malloc(64);
    
    if (result) {
        snprintf(result, 64, "(%g,%g)", x, y);
    }
    
    return result;
}

// 处理INET类型（IPv4/IPv6地址）
char* pg_handle_inet(const char* data, int len, uint32_t type_oid, int16_t type_len) {
    if (len < 4) {
        return strdup("ERROR");
    }

    // INET格式：family(1) + bits(1) + is_cidr(1) + nb(1) + address_bytes
    uint8_t family = data[0];
    uint8_t bits = data[1];
    uint8_t is_cidr = data[2];
    uint8_t nb = data[3];

    char* result = (char*)malloc(64);
    if (!result) {
        return NULL;
    }

    if (family == 2 && nb == 4) { // IPv4
        if (len < 8) {
            free(result);
            return strdup("ERROR");
        }
        const unsigned char* addr = (const unsigned char*)(data + 4);
        if (is_cidr) {
            snprintf(result, 64, "%d.%d.%d.%d/%d", addr[0], addr[1], addr[2], addr[3], bits);
        } else {
            snprintf(result, 64, "%d.%d.%d.%d", addr[0], addr[1], addr[2], addr[3]);
        }
    } else if (family == 3 && nb == 16) { // IPv6
        if (len < 20) {
            free(result);
            return strdup("ERROR");
        }
        const unsigned char* addr = (const unsigned char*)(data + 4);
        if (is_cidr) {
            snprintf(result, 64, "%02x%02x:%02x%02x:%02x%02x:%02x%02x:%02x%02x:%02x%02x:%02x%02x:%02x%02x/%d",
                    addr[0], addr[1], addr[2], addr[3], addr[4], addr[5], addr[6], addr[7],
                    addr[8], addr[9], addr[10], addr[11], addr[12], addr[13], addr[14], addr[15], bits);
        } else {
            snprintf(result, 64, "%02x%02x:%02x%02x:%02x%02x:%02x%02x:%02x%02x:%02x%02x:%02x%02x:%02x%02x",
                    addr[0], addr[1], addr[2], addr[3], addr[4], addr[5], addr[6], addr[7],
                    addr[8], addr[9], addr[10], addr[11], addr[12], addr[13], addr[14], addr[15]);
        }
    } else {
        strcpy(result, "UNKNOWN");
    }

    return result;
}

// 处理CIDR类型（网络地址）
char* pg_handle_cidr(const char* data, int len, uint32_t type_oid, int16_t type_len) {
    // CIDR与INET格式相同，但总是包含网络掩码
    return pg_handle_inet(data, len, type_oid, type_len);
}

// 处理MACADDR类型（MAC地址）
char* pg_handle_macaddr(const char* data, int len, uint32_t type_oid, int16_t type_len) {
    if (len != 6) {
        return strdup("ERROR");
    }

    char* result = (char*)malloc(18); // XX:XX:XX:XX:XX:XX + '\0'
    if (result) {
        const unsigned char* mac = (const unsigned char*)data;
        snprintf(result, 18, "%02x:%02x:%02x:%02x:%02x:%02x",
                mac[0], mac[1], mac[2], mac[3], mac[4], mac[5]);
    }

    return result;
}

// 处理MACADDR8类型（8字节MAC地址）
char* pg_handle_macaddr8(const char* data, int len, uint32_t type_oid, int16_t type_len) {
    if (len != 8) {
        return strdup("ERROR");
    }

    char* result = (char*)malloc(24); // XX:XX:XX:XX:XX:XX:XX:XX + '\0'
    if (result) {
        const unsigned char* mac = (const unsigned char*)data;
        snprintf(result, 24, "%02x:%02x:%02x:%02x:%02x:%02x:%02x:%02x",
                mac[0], mac[1], mac[2], mac[3], mac[4], mac[5], mac[6], mac[7]);
    }

    return result;
}

// 处理数组类型的通用函数
char* pg_handle_array(const char* data, int len, uint32_t type_oid, int16_t type_len) {
    if (len < 12) {
        return strdup("ERROR");
    }

    // PostgreSQL数组二进制格式：
    // ndim(4) + has_nulls(4) + element_type(4) + [dim_size(4) + lower_bound(4)]... + elements

    int32_t ndim, has_nulls, element_type;
    memcpy(&ndim, data, 4);
    memcpy(&has_nulls, data + 4, 4);
    memcpy(&element_type, data + 8, 4);

    ndim = ntohl(ndim);
    has_nulls = ntohl(has_nulls);
    element_type = ntohl(element_type);

    // 简化处理：只处理一维数组
    if (ndim != 1 || len < 20) {
        return strdup("{...}"); // 多维数组或格式错误
    }

    int32_t dim_size, lower_bound;
    memcpy(&dim_size, data + 12, 4);
    memcpy(&lower_bound, data + 16, 4);

    dim_size = ntohl(dim_size);
    lower_bound = ntohl(lower_bound);

    if (dim_size <= 0 || dim_size > 1000) { // 限制数组大小
        return strdup("{...}");
    }

    // 分配结果缓冲区
    size_t result_size = 2 + dim_size * 32; // 估算大小
    char* result = (char*)malloc(result_size);
    if (!result) {
        return NULL;
    }

    strcpy(result, "{");

    const char* element_data = data + 20;
    size_t pos = 20;

    for (int i = 0; i < dim_size && pos < (size_t)len; i++) {
        if (i > 0) {
            strcat(result, ",");
        }

        if (pos + 4 > (size_t)len) {
            strcat(result, "...}");
            return result;
        }

        int32_t element_len;
        memcpy(&element_len, element_data, 4);
        element_len = ntohl(element_len);

        element_data += 4;
        pos += 4;

        if (element_len == -1) {
            // NULL元素
            strcat(result, "NULL");
        } else if (element_len >= 0 && pos + element_len <= (size_t)len) {
            // 根据元素类型处理数据
            char* element_str = NULL;

            switch (element_type) {
                case PG_TYPE_INT4:
                    element_str = pg_handle_int4(element_data, element_len, element_type, -1);
                    break;
                case PG_TYPE_TEXT:
                case PG_TYPE_VARCHAR:
                    element_str = pg_handle_text(element_data, element_len, element_type, -1);
                    break;
                case PG_TYPE_BOOL:
                    element_str = pg_handle_bool(element_data, element_len, element_type, -1);
                    break;
                case PG_TYPE_INT2:
                    element_str = pg_handle_int2(element_data, element_len, element_type, -1);
                    break;
                case PG_TYPE_INT8:
                    element_str = pg_handle_int8(element_data, element_len, element_type, -1);
                    break;
                case PG_TYPE_FLOAT4:
                    element_str = pg_handle_float4(element_data, element_len, element_type, -1);
                    break;
                case PG_TYPE_FLOAT8:
                    element_str = pg_handle_float8(element_data, element_len, element_type, -1);
                    break;
                default:
                    element_str = strdup("?");
                    break;
            }

            if (element_str) {
                // 检查是否需要引号（文本类型）
                if (element_type == PG_TYPE_TEXT || element_type == PG_TYPE_VARCHAR) {
                    strcat(result, "\"");
                    strncat(result, element_str, 20); // 限制长度
                    strcat(result, "\"");
                } else {
                    strncat(result, element_str, 20); // 限制长度
                }
                free(element_str);
            }

            element_data += element_len;
            pos += element_len;
        } else {
            strcat(result, "?");
            break;
        }
    }

    strcat(result, "}");
    return result;
}

// 处理枚举类型
char* pg_handle_enum(const char* data, int len, uint32_t type_oid, int16_t type_len) {
    // 枚举值以文本形式存储
    return pg_handle_text(data, len, type_oid, type_len);
}

// 默认处理函数，将二进制数据转为十六进制字符串
char* pg_handle_default(const char* data, int len, uint32_t type_oid, int16_t type_len) {
    return pg_handle_bytea(data, len, type_oid, type_len);
}

// 初始化处理器注册表
pg_type_handler_t* CPostgreParser::init_binary_handlers() {
    pg_type_handler_t *handlers = NULL;
    
    // 添加各种数据类型的处理函数
    register_binary_handler(&handlers, PG_TYPE_BOOL, pg_handle_bool, "bool", 1);
    register_binary_handler(&handlers, PG_TYPE_CHAR, pg_handle_char, "char", 1);
    register_binary_handler(&handlers, PG_TYPE_INT2, pg_handle_int2, "int2", 2);
    register_binary_handler(&handlers, PG_TYPE_INT4, pg_handle_int4, "int4", 4);
    register_binary_handler(&handlers, PG_TYPE_INT8, pg_handle_int8, "int8", 8);
    register_binary_handler(&handlers, PG_TYPE_OID, pg_handle_int4, "oid", 4);
    register_binary_handler(&handlers, PG_TYPE_FLOAT4, pg_handle_float4, "float4", 4);
    register_binary_handler(&handlers, PG_TYPE_FLOAT8, pg_handle_float8, "float8", 8);
    
    // 变长类型
    register_binary_handler(&handlers, PG_TYPE_TEXT, pg_handle_text, "text", 0);
    register_binary_handler(&handlers, PG_TYPE_VARCHAR, pg_handle_text, "varchar", 0);
    register_binary_handler(&handlers, PG_TYPE_BYTEA, pg_handle_bytea, "bytea", 0);
    
    // 日期时间类型
    register_binary_handler(&handlers, PG_TYPE_DATE, pg_handle_date, "date", 4);
    register_binary_handler(&handlers, PG_TYPE_TIME, pg_handle_time, "time", 8);
    register_binary_handler(&handlers, PG_TYPE_TIMESTAMP, pg_handle_timestamp, "timestamp", 8);
    register_binary_handler(&handlers, PG_TYPE_TIMESTAMPTZ, pg_handle_timestamp, "timestamptz", 8);
    
    // 其他类型
    register_binary_handler(&handlers, PG_TYPE_UUID, pg_handle_uuid, "uuid", 16);
    register_binary_handler(&handlers, PG_TYPE_POINT, pg_handle_point, "point", 16);

    // 网络地址类型
    register_binary_handler(&handlers, PG_TYPE_INET, pg_handle_inet, "inet", 0);
    register_binary_handler(&handlers, PG_TYPE_CIDR, pg_handle_cidr, "cidr", 0);
    register_binary_handler(&handlers, PG_TYPE_MACADDR, pg_handle_macaddr, "macaddr", 6);
    register_binary_handler(&handlers, PG_TYPE_MACADDR8, pg_handle_macaddr8, "macaddr8", 8);

    // 数组类型
    register_binary_handler(&handlers, PG_TYPE_BOOL_ARRAY, pg_handle_array, "bool[]", 0);
    register_binary_handler(&handlers, PG_TYPE_INT2_ARRAY, pg_handle_array, "int2[]", 0);
    register_binary_handler(&handlers, PG_TYPE_INT4_ARRAY, pg_handle_array, "int4[]", 0);
    register_binary_handler(&handlers, PG_TYPE_INT8_ARRAY, pg_handle_array, "int8[]", 0);
    register_binary_handler(&handlers, PG_TYPE_FLOAT4_ARRAY, pg_handle_array, "float4[]", 0);
    register_binary_handler(&handlers, PG_TYPE_FLOAT8_ARRAY, pg_handle_array, "float8[]", 0);
    register_binary_handler(&handlers, PG_TYPE_TEXT_ARRAY, pg_handle_array, "text[]", 0);
    register_binary_handler(&handlers, PG_TYPE_VARCHAR_ARRAY, pg_handle_array, "varchar[]", 0);
    register_binary_handler(&handlers, PG_TYPE_DATE_ARRAY, pg_handle_array, "date[]", 0);
    register_binary_handler(&handlers, PG_TYPE_TIME_ARRAY, pg_handle_array, "time[]", 0);
    register_binary_handler(&handlers, PG_TYPE_TIMESTAMP_ARRAY, pg_handle_array, "timestamp[]", 0);
    register_binary_handler(&handlers, PG_TYPE_TIMESTAMPTZ_ARRAY, pg_handle_array, "timestamptz[]", 0);
    register_binary_handler(&handlers, PG_TYPE_UUID_ARRAY, pg_handle_array, "uuid[]", 0);
    register_binary_handler(&handlers, PG_TYPE_INET_ARRAY, pg_handle_array, "inet[]", 0);
    register_binary_handler(&handlers, PG_TYPE_CIDR_ARRAY, pg_handle_array, "cidr[]", 0);
    register_binary_handler(&handlers, PG_TYPE_MACADDR_ARRAY, pg_handle_array, "macaddr[]", 0);

    return handlers;
}

// 注册二进制数据处理函数
bool CPostgreParser::register_binary_handler(pg_type_handler_t **handlers, uint32_t type_oid, 
                                           pg_binary_to_text_fn handler_fn, 
                                           const char* type_name, size_t fixed_size) {
    pg_type_handler_t *new_handler = (pg_type_handler_t*)malloc(sizeof(pg_type_handler_t));
    if (!new_handler) {
        GWLOG_ERROR(m_comm, "[PostgreSQL][BinaryHandler] Failed to allocate memory for handler\n");
        return false;
    }
    
    new_handler->type_oid = type_oid;
    new_handler->handler_fn = handler_fn;
    new_handler->type_name = strdup(type_name);
    new_handler->fixed_size = fixed_size;
    new_handler->next = *handlers;
    
    *handlers = new_handler;
    return true;
}

// 查找类型处理函数
pg_binary_to_text_fn CPostgreParser::find_binary_handler(pg_type_handler_t *handlers, uint32_t type_oid) {
    pg_type_handler_t *curr = handlers;

    while (curr) {
        if (curr->type_oid == type_oid) {
            return curr->handler_fn;
        }
        curr = curr->next;
    }

    // 检查是否是枚举类型（用户定义类型）
    if (type_oid >= PG_TYPE_ENUM_MIN && type_oid <= PG_TYPE_ENUM_MAX) {
        return pg_handle_enum;
    }

    // 默认处理函数
    return pg_handle_default;
}

// 清理二进制处理器
void CPostgreParser::cleanup_binary_handlers(pg_type_handler_t *handlers) {
    while (handlers) {
        pg_type_handler_t *next = handlers->next;
        free((void*)handlers->type_name);
        free(handlers);
        handlers = next;
    }
}

// 从二进制数据解析字段值
postgre_field_value_t* CPostgreParser::decode_binary_field(const char* data, int len, 
                                                         uint32_t type_oid, int16_t format_code,
                                                         int16_t type_len, pg_type_handler_t *handlers) {
    postgre_field_value_t *field = (postgre_field_value_t*)calloc(1, sizeof(postgre_field_value_t));
    if (!field) {
        GWLOG_ERROR(m_comm, "[PostgreSQL][BinaryHandler] Failed to allocate memory for field\n");
        return NULL;
    }
    
    field->type_oid = type_oid;
    field->format_code = format_code;
    field->is_null = (len == -1);
    
    if (field->is_null) {
        // NULL值处理
        field->text_value.s = strdup("NULL");
        field->text_value.len = 4;
        field->binary_value = NULL;
        field->binary_len = 0;
    } else if (format_code == PG_FORMAT_BINARY) {
        // 二进制格式
        pg_binary_to_text_fn handler = find_binary_handler(handlers, type_oid);
        
        // 保存二进制数据（视需要）
        field->binary_len = len;
        field->binary_value = malloc(len);
        if (field->binary_value) {
            memcpy(field->binary_value, data, len);
        }
        
        // 转换为文本
        field->text_value.s = handler(data, len, type_oid, type_len);
        if (field->text_value.s) {
            field->text_value.len = strlen(field->text_value.s);
        } else {
            field->text_value.s = strdup("ERROR");
            field->text_value.len = 5;
        }
    } else {
        // 文本格式
        field->text_value.s = (char*)malloc(len + 1);
        if (field->text_value.s) {
            memcpy(field->text_value.s, data, len);
            field->text_value.s[len] = '\0';
            field->text_value.len = len;
        } else {
            field->text_value.s = strdup("ERROR");
            field->text_value.len = 5;
        }
        
        // 文本格式不需要保存二进制值
        field->binary_value = NULL;
        field->binary_len = 0;
    }
    
    return field;
}

// 释放字段值资源
void CPostgreParser::cleanup_field_value(postgre_field_value_t *field) {
    if (!field) return;
    
    if (field->text_value.s) {
        free(field->text_value.s);
        field->text_value.s = NULL;
    }
    
    if (field->binary_value) {
        free(field->binary_value);
        field->binary_value = NULL;
    }
    
    free(field);
}

// 释放扩展行数据资源
void CPostgreParser::cleanup_row_data_extended(postgre_row_data_extended_t *row) {
    if (!row) return;
    
    if (row->values) {
        for (int i = 0; i < row->field_count; i++) {
            if (row->values[i]) {
                cleanup_field_value(row->values[i]);
            }
        }
        free(row->values);
    }
    
    free(row);
}

// 解析数据行（支持二进制格式）
postgre_row_data_extended_t* CPostgreParser::parse_data_row_extended(
    const char *data, int len, int offset, 
    const column_def_t *col_defs, int col_cnt, 
    pg_type_handler_t *handlers)
{
    if (!data || len <= 0 || offset < 0 || len - offset < 5 || !col_defs || col_cnt <= 0) {
        GWLOG_ERROR(m_comm, "[PostgreSQL][BinaryData] Invalid parameters for parse_data_row_extended\n");
        return NULL;
    }
    
    // 解析字段数量
    uint16_t num_fields = ntohs(*((uint16_t*)(data + offset + 5)));
    if (num_fields != col_cnt) {
        GWLOG_ERROR(m_comm, "[PostgreSQL][BinaryData] Field count mismatch: expected %d, got %d\n", 
                   col_cnt, num_fields);
        return NULL;
    }
    
    // 创建扩展行数据结构
    postgre_row_data_extended_t *row = (postgre_row_data_extended_t*)calloc(1, sizeof(postgre_row_data_extended_t));
    if (!row) {
        GWLOG_ERROR(m_comm, "[PostgreSQL][BinaryData] Failed to allocate memory for row\n");
        return NULL;
    }
    
    row->field_count = num_fields;
    row->values = (postgre_field_value_t**)calloc(num_fields, sizeof(postgre_field_value_t*));
    if (!row->values) {
        GWLOG_ERROR(m_comm, "[PostgreSQL][BinaryData] Failed to allocate memory for field values\n");
        free(row);
        return NULL;
    }
    
    // 处理字段数据
    const char *field_data = data + offset + 7;
    const column_def_t *col = col_defs;
    for (int i = 0; i < num_fields && col; i++, col = col->next)
    {
        int32_t field_len = ntohl(*((int32_t*)field_data));
        field_data += 4;
        
        // 解析字段值
        postgre_field_value_t *field = decode_binary_field(
            field_data, field_len, col->type_oid, col->format_code, col->type_len, handlers);
        
        if (!field) {
            GWLOG_ERROR(m_comm, "[PostgreSQL][BinaryData] Failed to decode field %d\n", i);
            // 错误处理 - 释放已分配的资源
            for (int j = 0; j < i; j++) {
                if (row->values[j]) {
                    cleanup_field_value(row->values[j]);
                }
            }
            free(row->values);
            free(row);
            return NULL;
        }
        
        row->values[i] = field;
        
        // 跳过字段数据
        if (field_len > 0) {
            field_data += field_len;
        }
    }
    
    return row;
}
