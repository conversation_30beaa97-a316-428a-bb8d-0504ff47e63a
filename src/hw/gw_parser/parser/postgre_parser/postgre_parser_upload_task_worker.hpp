#ifndef __POSTGRE_PARSER_UPLOAD_TASK_WORKER_HPP__
#define __POSTGRE_PARSER_UPLOAD_TASK_WORKER_HPP__

#include "task_worker.h"

class CPostgreParser;

class CTaskWorkerUploadMsg: public CTaskWorker 
{
public:
    virtual int deal_data(const TaskWorkerData *);
    virtual void free_data(const TaskWorkerData *);
    inline void set_wq(CWorkerQueue *pwq)
    {
        m_pwq = pwq;
    }
    CWorkerQueue *get_wq() const
    {
        return m_pwq;
    }

    inline void set_parser(CPostgreParser *parser)
    {
        m_parser = parser;
    }
    void init(){}
    void fini(){}
    void release() const {}
protected:
    inline CPostgreParser *get_parser(void)
    {
        return m_parser;
    }

protected:
    CWorkerQueue *m_pwq;
    CPostgreParser *m_parser;
};

#endif // __POSTGRE_PARSER_UPLOAD_TASK_WORKER_HPP__ 