/*
 * Oracle协议解析器消息处理
 * <AUTHOR> @date 2025
 */

 #include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <sys/socket.h>
#include <arpa/inet.h>
#include <netinet/in.h>
#include <sys/time.h>

#include "cJSON.h"
#include "oracle_parser.h"
#include "oracle_parser_common.h"
#include "oracle_parser_upload_task_worker.hpp"
#include "gw_common.h"
#include "gw_i_upload.h"
#include "gw_logger.h"
#include "session_mgt.h"
#include "session.h"
#include "worker_queue.h"
#include "task_worker.h"
#include "utils.h"
#include "simple_json.h"
#include "gw_stats.h"


// Oracle解析器消息上传队列名称
#define ORACLE_DATA_UPLOAD_QUEUE "oracle_data_upload_queue"

static const char msg_type[] = "oracle";

// 格式化Oracle事件JSON
static char* format_oracle_event_json(const upload_oracle_info_t *data, size_t &s_len)
{
    if (!data) {
        s_len = 0;
        return nullptr;
    }

    // 初始缓冲区大小
    size_t initial_size = 8192;  // 基础大小8KB
    char *buffer = (char *)malloc(initial_size);
    if (!buffer) {
        s_len = 0;
        return nullptr;
    }

    size_t current_size = initial_size;

    // 对字符串字段进行JSON转义处理
    char *escaped_app_name = cJSON_EscapeString(data->oracle_meta_info.app_name);
    char *escaped_server_version = cJSON_EscapeString(data->oracle_meta_info.server_version);
    char *escaped_db_name = cJSON_EscapeString(data->oracle_req_info.db_name);
    char *escaped_db_user = cJSON_EscapeString(data->oracle_req_info.db_user);
    char *escaped_db_password = cJSON_EscapeString(data->oracle_req_info.db_password);
    char *escaped_cmd_type = cJSON_EscapeString(data->oracle_req_info.cmd_type);
    char *escaped_sql = cJSON_EscapeString(data->oracle_req_info.sql_text);

    // 使用安全的字符串，如果转义失败则使用原字符串
    const char *safe_app_name = escaped_app_name ? escaped_app_name : "\"\"";
    const char *safe_server_version = escaped_server_version ? escaped_server_version : "\"\"";
    const char *safe_db_name = escaped_db_name ? escaped_db_name : "\"\"";
    const char *safe_db_user = escaped_db_user ? escaped_db_user : "\"\"";
    const char *safe_db_password = escaped_db_password ? escaped_db_password : "\"\"";
    const char *safe_cmd_type = escaped_cmd_type ? escaped_cmd_type : "\"\"";
    const char *safe_sql = escaped_sql ? escaped_sql : "\"\"";

    // 格式化JSON事件
    int written = snprintf(buffer, current_size,
        "{"
        "\"meta\":{"
        "\"tm\":%" PRIu64 ","
        "\"type\":\"%s\","
        "\"app_name\":%s,"
        "\"server_version\":%s"
        "},"
        "\"net\":{"
        "\"src_ip\":\"%s\","
        "\"src_port\":%d,"
        "\"dst_ip\":\"%s\","
        "\"dst_port\":%d,"
        "\"flow_source\":\"%s\""
        "},"
        "\"mac\":{"
        "\"mac\":\"%s\""
        "},"
        "\"unique_id\":{"
        "\"event_id\":\"%s\""
        "},"
        "\"req\":{"
        "\"db_name\":%s,"
        "\"db_user\":%s,"
        "\"db_password\":%s,"
        "\"cmd_type\":%s,"
        "\"sql\":%s"
        "},"
        "\"rsp\":{"
        "\"status\":%d,"
        "\"start_time\":%" PRIu64 ","
        "\"close_time\":%" PRIu64 ","
        "\"row_count\":%d,"
        "}"
        "}",
        data->oracle_meta_info.ts,
        data->oracle_meta_info.type,
        safe_app_name,
        safe_server_version,
        data->oracle_net_info.a_src_ip,
        data->oracle_net_info.src_port,
        data->oracle_net_info.a_dst_ip,
        data->oracle_net_info.dst_port,
        data->oracle_net_info.flow_source,
        data->oracle_mac_info.mac,
        data->a_unique_id,
        safe_db_name,
        safe_db_user,
        safe_db_password,
        safe_cmd_type,
        safe_sql,
        data->oracle_rsp_info.code,
        data->oracle_rsp_info.start_time,
        data->oracle_rsp_info.close_time,
        data->oracle_rsp_info.row_count
    );

    // 释放转义后的字符串内存
    if (escaped_app_name) cJSON_free(escaped_app_name);
    if (escaped_server_version) cJSON_free(escaped_server_version);
    if (escaped_db_name) cJSON_free(escaped_db_name);
    if (escaped_db_user) cJSON_free(escaped_db_user);
    if (escaped_db_password) cJSON_free(escaped_db_password);
    if (escaped_cmd_type) cJSON_free(escaped_cmd_type);
    if (escaped_sql) cJSON_free(escaped_sql);

    // 检查缓冲区是否足够
    if (written >= (int)current_size) {
        // 缓冲区不够，重新分配
        size_t new_size = written + 1024;
        char *new_buffer = (char *)realloc(buffer, new_size);
        if (!new_buffer) {
            free(buffer);
            s_len = 0;
            return nullptr;
        }
        buffer = new_buffer;
        current_size = new_size;
    }

    s_len = written;
    return buffer;
}

// 创建上传消息工作队列
CWorkerQueue *COracleParser::new_wq_upload_msg()
{
    m_upload_data_wq = m_comm->create_worker_queue();
    if (m_upload_data_wq == NULL) {
        return NULL;
    }

    CTaskWorkerUploadMsg *ptw = new CTaskWorkerUploadMsg();
    ptw->set_wq(m_upload_data_wq);
    ptw->set_parser(this);

    m_upload_data_wk = ptw;

    m_upload_data_wq->set_gw_common(m_comm);
    m_upload_data_wq->set_watchdog(m_comm->get_watchdog());
    m_upload_data_wq->set_task_worker(ptw);

    m_upload_data_wq->set_queue_num_and_bytes(m_conf_oracle_upload_queue_max_num, m_conf_oracle_upload_queue_memory_max_size_bytes);
    m_upload_data_wq->set_queue_name(ORACLE_DATA_UPLOAD_QUEUE);
    m_upload_data_wq->init();
    m_upload_data_wq->create_queue();

    m_upload_data_wq->adjust_worker_thread_num(m_conf_oracle_upload_thread_num);

    m_comm->get_gw_stats()->set_task(m_upload_data_wq->get_queue_name(), m_upload_data_wq, 50);
    m_comm->get_gw_stats()->set_mem_stat(m_upload_data_wq->get_queue_name(), &m_upload_data_wq->get_queue_mem_size(), &m_upload_data_wq->get_queue_max_mem_size());

    return m_upload_data_wq;
}

// 发送Oracle数据到上传队列
void COracleParser::send_oracle_data(upload_oracle_info_t *p)
{
    if (m_upload_data_wq == NULL) {
        GWLOG_ERROR(m_comm, "%s upload data wq is NULL", ORACLE_LOG_PRE);
        return;
    }

    // 放入队列
    if (!m_upload_data_wq->queue_put_data(p, p->mem_size)) {
        GWLOG_ERROR(m_comm, "%s Failed to queue put data", ORACLE_LOG_PRE);
        free_oracle_upload_data(p);  // 释放原始数据
    }
}

// Oracle上传消息回调
void COracleParser::oracle_cb_upload_msg(const char *s, size_t s_len)
{
    if (unlikely(m_upload == NULL)) {
        GWLOG_ERROR(m_comm, "%s upload null(%s)", ORACLE_LOG_PRE, m_conf_upload_name.c_str());
        SAFE_FREE((void*)s);
        return;
    }

    UploadMsg *pum = new UploadMsg;
    memset(pum, 0, sizeof(UploadMsg));

    pum->cb = sizeof(UploadMsg);
    pum->destroy_func = free_upload_msg;
    pum->parser = this;
    pum->length = s_len;
    pum->s = s;
    pum->msgtype = msg_type;
    pum->mem_size = sizeof(UploadMsg) + pum->length;

    m_upload->put_msg(pum);
}

// 释放上传消息
void COracleParser::free_upload_msg(const struct UploadMsg *pum)
{
    ASSERT(pum != NULL);
    delete pum;
}

// 工作线程处理Oracle上传数据的内部实现
int COracleParser::worker_routine_oracle_upload_data_inner(upload_oracle_info_t *p_upload_oracle_info)
{
    if (!p_upload_oracle_info) {
        GWLOG_ERROR(m_comm, "%s upload oracle info is NULL", ORACLE_LOG_PRE);
        return -1;
    }

    // 格式化事件数据为JSON格式
    size_t s_len = 0;
    char *s = format_oracle_event_json(p_upload_oracle_info, s_len);
    if (!s) {
        GWLOG_ERROR(m_comm, "%s Failed to format JSON", ORACLE_LOG_PRE);
        return -1;
    }

    oracle_cb_upload_msg(s, s_len);
    return 0;
}

// 释放Oracle上传数据
void COracleParser::free_oracle_upload_data(upload_oracle_info_t *p)
{
    if (!p) {
        return;
    }

    delete p;
}

// 生成事件唯一ID
void COracleParser::add_event_id(char *p_event_id)
{
    if (NULL == p_event_id) {
        return;
    }

    char a_unique_code[64] = {0};
    uint64_t u64_time_val = 0;
    get_ms_timeval(&u64_time_val);

    if (m_u64_oracle_upload_ms == 0) {
        m_u64_oracle_upload_ms = u64_time_val;
        m_u32_oracle_upload_index = 1;
    } else {
        if (u64_time_val == m_u64_oracle_upload_ms) {
            m_u32_oracle_upload_index++;
        } else {
            m_u64_oracle_upload_ms = u64_time_val;
            m_u32_oracle_upload_index = 1;
        }
    }

    /* 获取唯一标识ID */
    get_unique_event_id(m_str_gw_ip.c_str(), m_u64_oracle_upload_ms, m_u32_oracle_upload_index, a_unique_code, sizeof(a_unique_code) - 1);

    /* 将unique_code进行base64编码 */
    base64_encode((unsigned char*)p_event_id, (unsigned char*)a_unique_code, strlen(a_unique_code));

    return;
}