/*
 * Oracle LOB数据处理器头文件
 * 实现CLOB、BL<PERSON><PERSON>、NCLOB等大对象数据的解析和处理
 * <AUTHOR> @date 2025
 */

#ifndef __ORACLE_LOB_HANDLER_H__
#define __ORACLE_LOB_HANDLER_H__

#include <inttypes.h>
#include <vector>
#include <map>
#include <string>
#include "oracle_parser_common.h"

// LOB处理结果状态
#define LOB_PARSE_SUCCESS           0
#define LOB_PARSE_NEED_MORE_DATA    1
#define LOB_PARSE_ERROR            -1
#define LOB_PARSE_INVALID_DATA     -2
#define LOB_PARSE_UNSUPPORTED      -3
#define LOB_PARSE_MEMORY_ERROR     -4

// LOB数据类型
#define ORACLE_LOB_TYPE_CLOB        1
#define ORACLE_LOB_TYPE_BLOB        2
#define ORACLE_LOB_TYPE_NCLOB       3
#define ORACLE_LOB_TYPE_BFILE       4

// LOB操作类型
#define ORACLE_LOB_OP_READ          1
#define ORACLE_LOB_OP_WRITE         2
#define ORACLE_LOB_OP_APPEND        3
#define ORACLE_LOB_OP_TRIM          4
#define ORACLE_LOB_OP_ERASE         5
#define ORACLE_LOB_OP_COPY          6
#define ORACLE_LOB_OP_COMPARE       7
#define ORACLE_LOB_OP_SUBSTR        8
#define ORACLE_LOB_OP_INSTR         9
#define ORACLE_LOB_OP_LENGTH        10

// LOB定位符结构
typedef struct oracle_lob_locator
{
    uint32_t locator_length;        // 定位符长度
    uint8_t  lob_type;              // LOB类型
    uint8_t  flags;                 // 标志位
    uint32_t lob_id;                // LOB ID
    uint64_t lob_size;              // LOB大小
    uint32_t chunk_size;            // 块大小
    char     *locator_data;         // 定位符数据
    bool     is_temporary;          // 是否为临时LOB
    bool     is_open;               // 是否已打开
} oracle_lob_locator_t;

// LOB数据块
typedef struct oracle_lob_chunk
{
    uint32_t chunk_id;              // 块ID
    uint32_t chunk_offset;          // 块偏移
    uint32_t chunk_size;            // 块大小
    uint32_t data_length;           // 实际数据长度
    char     *data;                 // 数据内容
    bool     is_last_chunk;         // 是否为最后一块
    bool     is_compressed;         // 是否压缩
} oracle_lob_chunk_t;

// LOB操作信息
typedef struct oracle_lob_operation
{
    uint8_t  operation_type;        // 操作类型
    oracle_lob_locator_t *locator;  // LOB定位符
    uint64_t offset;                // 操作偏移
    uint64_t length;                // 操作长度
    uint32_t chunk_count;           // 块数量
    oracle_lob_chunk_t *chunks;     // 数据块数组
    uint64_t total_bytes;           // 总字节数
    bool     operation_complete;    // 操作是否完成
} oracle_lob_operation_t;

// LOB会话状态
typedef struct oracle_lob_session
{
    uint32_t session_id;            // 会话ID
    std::map<uint32_t, oracle_lob_locator_t*> open_lobs; // 打开的LOB
    uint64_t total_lob_bytes;       // 总LOB字节数
    uint32_t active_operations;     // 活跃操作数
    uint64_t last_activity_time;    // 最后活动时间
} oracle_lob_session_t;

// 前向声明
struct oracle_lob_stats;
typedef struct oracle_lob_stats oracle_lob_stats_t;

// LOB处理器类
class OracleLobHandler
{
public:
    OracleLobHandler();
    ~OracleLobHandler();

    // LOB消息解析
    int parse_lob_message(const char *data, size_t data_len, uint8_t message_type,
                         oracle_status_t *status, oracle_parsed_data_t *result);

    // LOB定位符处理
    int parse_lob_locator(const char *data, size_t data_len, oracle_lob_locator_t *locator);
    int create_lob_locator(uint8_t lob_type, uint32_t lob_id, oracle_lob_locator_t **locator);
    void free_lob_locator(oracle_lob_locator_t *locator);

    // LOB数据块处理
    int parse_lob_chunk(const char *data, size_t data_len, oracle_lob_chunk_t *chunk);
    int assemble_lob_chunks(oracle_lob_chunk_t *chunks, uint32_t chunk_count, 
                           char **assembled_data, size_t *total_size);
    void free_lob_chunk(oracle_lob_chunk_t *chunk);

    // LOB操作处理
    int parse_lob_read_operation(const char *data, size_t data_len, oracle_lob_operation_t *operation);
    int parse_lob_write_operation(const char *data, size_t data_len, oracle_lob_operation_t *operation);
    int parse_lob_append_operation(const char *data, size_t data_len, oracle_lob_operation_t *operation);
    int parse_lob_trim_operation(const char *data, size_t data_len, oracle_lob_operation_t *operation);

    // LOB会话管理
    int create_lob_session(uint32_t session_id, oracle_lob_session_t **session);
    int register_lob_locator(oracle_lob_session_t *session, oracle_lob_locator_t *locator);
    int unregister_lob_locator(oracle_lob_session_t *session, uint32_t lob_id);
    void cleanup_lob_session(oracle_lob_session_t *session);

    // LOB数据类型转换
    int convert_clob_to_string(const oracle_lob_operation_t *operation, b_string_t *result);
    int convert_blob_to_hex(const oracle_lob_operation_t *operation, b_string_t *result);
    int extract_lob_metadata(const oracle_lob_locator_t *locator, oracle_parsed_data_t *result);

    // LOB性能统计
    void update_lob_statistics(const oracle_lob_operation_t *operation);
    void get_lob_statistics(oracle_lob_stats_t *stats);
    void reset_lob_statistics();

    // LOB缓存管理
    int cache_lob_chunk(uint32_t lob_id, uint32_t chunk_id, const oracle_lob_chunk_t *chunk);
    int get_cached_lob_chunk(uint32_t lob_id, uint32_t chunk_id, oracle_lob_chunk_t **chunk);
    void clear_lob_cache();

    // LOB压缩处理
    int decompress_lob_chunk(const oracle_lob_chunk_t *compressed_chunk, oracle_lob_chunk_t *decompressed_chunk);
    int compress_lob_chunk(const oracle_lob_chunk_t *original_chunk, oracle_lob_chunk_t *compressed_chunk);

    // LOB工具方法
    const char* get_lob_type_name(uint8_t lob_type);
    const char* get_lob_operation_name(uint8_t operation_type);
    bool is_lob_type_supported(uint8_t lob_type);
    size_t calculate_lob_memory_usage(const oracle_lob_operation_t *operation);

    // LOB调试和日志
    void dump_lob_locator(const oracle_lob_locator_t *locator);
    void dump_lob_operation(const oracle_lob_operation_t *operation);
    void dump_lob_chunk(const oracle_lob_chunk_t *chunk);

    // 配置管理
    void set_max_lob_cache_size(size_t max_size) { m_max_cache_size = max_size; }
    void set_max_chunk_size(size_t max_size) { m_max_chunk_size = max_size; }
    void set_compression_enabled(bool enabled) { m_compression_enabled = enabled; }
    void set_debug_enabled(bool enabled) { m_debug_enabled = enabled; }

private:
    // LOB消息解析方法
    int parse_lob_data_message(const char *data, size_t data_len, oracle_status_t *status, oracle_parsed_data_t *result);
    int parse_lob_operation_message(const char *data, size_t data_len, oracle_status_t *status, oracle_parsed_data_t *result);

    // 内部工具方法
    uint32_t read_uint32_be(const char *data);
    uint64_t read_uint64_be(const char *data);
    void write_uint32_be(char *data, uint32_t value);
    void write_uint64_be(char *data, uint64_t value);

    // LOB定位符内部处理
    int validate_lob_locator(const oracle_lob_locator_t *locator);
    int parse_locator_flags(uint8_t flags, oracle_lob_locator_t *locator);
    int serialize_lob_locator(const oracle_lob_locator_t *locator, char *buffer, size_t buffer_size);

    // LOB数据处理
    int process_lob_data_chunk(const char *data, size_t data_len, size_t *offset, oracle_lob_chunk_t *chunk);
    int validate_lob_chunk_integrity(const oracle_lob_chunk_t *chunk);
    int merge_lob_chunks(const std::vector<oracle_lob_chunk_t*> &chunks, oracle_lob_chunk_t *merged_chunk);

    // LOB缓存内部管理
    struct lob_cache_key {
        uint32_t lob_id;
        uint32_t chunk_id;
        bool operator<(const lob_cache_key& other) const {
            return lob_id < other.lob_id || (lob_id == other.lob_id && chunk_id < other.chunk_id);
        }
    };
    std::map<lob_cache_key, oracle_lob_chunk_t*> m_lob_cache;
    size_t m_cache_size;
    size_t m_max_cache_size;

    // LOB会话管理
    std::map<uint32_t, oracle_lob_session_t*> m_lob_sessions;
    uint32_t m_next_session_id;

    // 配置参数
    size_t m_max_chunk_size;
    bool m_compression_enabled;
    bool m_debug_enabled;

    // 统计信息
    uint64_t m_total_lob_operations;
    uint64_t m_total_lob_bytes_processed;
    uint64_t m_cache_hits;
    uint64_t m_cache_misses;
    uint64_t m_compression_ratio;
};

// LOB统计信息结构
typedef struct oracle_lob_stats
{
    uint64_t total_operations;      // 总操作数
    uint64_t total_bytes_processed; // 总处理字节数
    uint64_t clob_operations;       // CLOB操作数
    uint64_t blob_operations;       // BLOB操作数
    uint64_t nclob_operations;      // NCLOB操作数
    uint64_t read_operations;       // 读操作数
    uint64_t write_operations;      // 写操作数
    uint64_t cache_hits;            // 缓存命中数
    uint64_t cache_misses;          // 缓存未命中数
    uint64_t compression_ratio;     // 压缩比率
    uint64_t average_chunk_size;    // 平均块大小
    uint64_t max_lob_size;          // 最大LOB大小
} oracle_lob_stats_t;

// LOB工具函数命名空间
namespace OracleLobUtils
{
    // LOB类型判断
    bool is_character_lob(uint8_t lob_type);
    bool is_binary_lob(uint8_t lob_type);
    bool is_external_lob(uint8_t lob_type);
    bool is_temporary_lob(const oracle_lob_locator_t *locator);

    // LOB大小计算
    size_t calculate_lob_storage_size(uint64_t lob_size, uint32_t chunk_size);
    uint32_t calculate_chunk_count(uint64_t lob_size, uint32_t chunk_size);
    uint64_t calculate_lob_overhead(const oracle_lob_locator_t *locator);

    // LOB数据验证
    bool validate_lob_data_integrity(const oracle_lob_chunk_t *chunks, uint32_t chunk_count);
    bool validate_lob_locator_format(const char *locator_data, size_t data_len);
    bool is_valid_lob_operation(uint8_t operation_type, uint8_t lob_type);

    // LOB性能优化
    uint32_t get_optimal_chunk_size(uint64_t lob_size);
    bool should_compress_lob(uint8_t lob_type, uint64_t lob_size);
    uint32_t estimate_compression_ratio(const char *data, size_t data_len);
}

#endif /* __ORACLE_LOB_HANDLER_H__ */
