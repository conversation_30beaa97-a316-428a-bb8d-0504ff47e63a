/*
 * Oracle协议版本兼容性处理模块头文件
 * 基于ojdbc源码分析实现的多版本Oracle协议兼容性处理
 * <AUTHOR> @date 2025
 */

#ifndef __ORACLE_VERSION_COMPAT_H__
#define __ORACLE_VERSION_COMPAT_H__

#include <inttypes.h>
#include <map>
#include <vector>
#include <string>
#include "oracle_parser_common.h"

// 版本兼容性处理结果状态
#define VERSION_COMPAT_SUCCESS      0
#define VERSION_COMPAT_ERROR       -1
#define VERSION_COMPAT_UNSUPPORTED -2
#define VERSION_COMPAT_NEGOTIATION_FAILED -3

// Oracle版本信息结构
typedef struct oracle_version_info
{
    uint16_t major_version;         // 主版本号
    uint16_t minor_version;         // 次版本号
    uint16_t patch_version;         // 补丁版本号
    uint16_t build_number;          // 构建号
    char version_string[64];        // 版本字符串
    uint32_t version_number;        // 版本数字表示
} oracle_version_info_t;

// 协议能力标志
typedef struct protocol_capabilities
{
    // TNS协议能力
    bool supports_large_sdu;        // 支持大SDU
    bool supports_compression;      // 支持压缩
    bool supports_encryption;       // 支持加密
    bool supports_checksums;        // 支持校验和
    uint16_t max_sdu_size;          // 最大SDU大小
    
    // TTC协议能力
    bool supports_fast_auth;        // 支持快速认证
    bool supports_array_operations; // 支持数组操作
    bool supports_lob_operations;   // 支持LOB操作
    bool supports_unicode;          // 支持Unicode
    uint8_t max_charset_id;         // 最大字符集ID
    
    // TTI消息能力
    bool supports_describe_select;  // 支持DESCRIBE SELECT
    bool supports_parse_execute;    // 支持PARSE+EXECUTE
    bool supports_fetch_across_commit; // 支持跨提交获取
    bool supports_scrollable_cursors;  // 支持可滚动游标
    uint32_t max_open_cursors;      // 最大打开游标数
    
    // 数据类型能力
    bool supports_timestamp;        // 支持TIMESTAMP
    bool supports_interval;         // 支持INTERVAL
    bool supports_nchar;            // 支持NCHAR/NVARCHAR2
    bool supports_binary_float;     // 支持BINARY_FLOAT
    bool supports_binary_double;    // 支持BINARY_DOUBLE
    bool supports_xmltype;          // 支持XMLType
} protocol_capabilities_t;

// 版本特定的协议差异
typedef struct version_protocol_differences
{
    uint16_t version;               // 版本号
    
    // TNS协议差异
    uint8_t tns_header_format;      // TNS头部格式
    bool uses_large_packet_format;  // 使用大包格式
    uint16_t default_sdu_size;      // 默认SDU大小
    
    // TTC协议差异
    uint8_t ttc_message_format;     // TTC消息格式
    bool uses_extended_error_codes; // 使用扩展错误码
    uint8_t charset_negotiation_method; // 字符集协商方法
    
    // TTI消息差异
    uint8_t tti_data_format;        // TTI数据格式
    bool uses_new_number_format;    // 使用新NUMBER格式
    bool uses_new_date_format;      // 使用新DATE格式
    
    // 功能差异
    std::vector<uint8_t> supported_sql_types;    // 支持的SQL类型
    std::vector<uint8_t> supported_data_types;   // 支持的数据类型
    std::vector<uint32_t> supported_functions;   // 支持的功能码
} version_protocol_differences_t;

// Oracle版本兼容性管理器类
class OracleVersionCompat
{
public:
    OracleVersionCompat();
    ~OracleVersionCompat();

    // 版本信息解析和管理
    int parse_version_string(const char *version_str, oracle_version_info_t *version_info);
    int parse_version_number(uint32_t version_num, oracle_version_info_t *version_info);
    int compare_versions(const oracle_version_info_t *version1, const oracle_version_info_t *version2);
    bool is_version_supported(const oracle_version_info_t *version_info);
    
    // 协议能力协商
    int negotiate_protocol_capabilities(const oracle_version_info_t *client_version,
                                       const oracle_version_info_t *server_version,
                                       protocol_capabilities_t *negotiated_caps);
    
    int get_version_capabilities(const oracle_version_info_t *version_info, 
                                protocol_capabilities_t *capabilities);
    
    int merge_capabilities(const protocol_capabilities_t *client_caps,
                          const protocol_capabilities_t *server_caps,
                          protocol_capabilities_t *merged_caps);

    // 版本特定的协议处理
    int get_protocol_differences(const oracle_version_info_t *version_info,
                                version_protocol_differences_t *differences);
    
    int adapt_tns_parsing(const oracle_version_info_t *version_info, 
                         const char *data, size_t data_len,
                         char *adapted_data, size_t *adapted_len);
    
    int adapt_ttc_parsing(const oracle_version_info_t *version_info,
                         const char *data, size_t data_len,
                         char *adapted_data, size_t *adapted_len);
    
    int adapt_tti_parsing(const oracle_version_info_t *version_info,
                         const char *data, size_t data_len,
                         char *adapted_data, size_t *adapted_len);

    // 数据类型兼容性处理
    int get_compatible_data_type(const oracle_version_info_t *version_info,
                                uint8_t original_type, uint8_t *compatible_type);
    
    int convert_data_format(const oracle_version_info_t *from_version,
                           const oracle_version_info_t *to_version,
                           uint8_t data_type, const char *data, size_t data_len,
                           char *converted_data, size_t *converted_len);

    // 错误码兼容性处理
    int map_error_code(const oracle_version_info_t *version_info,
                      uint32_t original_error, uint32_t *mapped_error);
    
    const char* get_error_message(const oracle_version_info_t *version_info,
                                 uint32_t error_code);

    // 功能兼容性检查
    bool is_function_supported(const oracle_version_info_t *version_info, uint32_t function_code);
    bool is_data_type_supported(const oracle_version_info_t *version_info, uint8_t data_type);
    bool is_sql_type_supported(const oracle_version_info_t *version_info, uint8_t sql_type);

    // 版本升级和降级处理
    int upgrade_protocol_data(const oracle_version_info_t *from_version,
                             const oracle_version_info_t *to_version,
                             const char *data, size_t data_len,
                             char *upgraded_data, size_t *upgraded_len);
    
    int downgrade_protocol_data(const oracle_version_info_t *from_version,
                               const oracle_version_info_t *to_version,
                               const char *data, size_t data_len,
                               char *downgraded_data, size_t *downgraded_len);

    // 配置和初始化
    int load_version_compatibility_config(const char *config_file);
    int register_version_handler(const oracle_version_info_t *version_info,
                                void *handler_context);
    
    // 调试和诊断
    void dump_version_info(const oracle_version_info_t *version_info);
    void dump_capabilities(const protocol_capabilities_t *capabilities);
    void dump_protocol_differences(const version_protocol_differences_t *differences);
    void enable_debug(bool enable) { m_debug_enabled = enable; }

private:
    // 内部版本管理
    std::map<uint32_t, protocol_capabilities_t> m_version_capabilities;
    std::map<uint32_t, version_protocol_differences_t> m_version_differences;
    std::map<uint32_t, void*> m_version_handlers;
    
    // 版本解析辅助函数
    int extract_version_components(const char *version_str, 
                                  uint16_t *major, uint16_t *minor, 
                                  uint16_t *patch, uint16_t *build);
    
    uint32_t encode_version_number(uint16_t major, uint16_t minor, 
                                  uint16_t patch, uint16_t build);
    
    int decode_version_number(uint32_t version_num,
                             uint16_t *major, uint16_t *minor,
                             uint16_t *patch, uint16_t *build);

    // 能力协商辅助函数
    bool is_capability_compatible(const protocol_capabilities_t *caps1,
                                 const protocol_capabilities_t *caps2);
    
    int select_best_capability(const protocol_capabilities_t *caps1,
                              const protocol_capabilities_t *caps2,
                              protocol_capabilities_t *best_caps);

    // 协议适配辅助函数
    int adapt_tns_header(const oracle_version_info_t *version_info,
                        const char *original_header, char *adapted_header);
    
    int adapt_ttc_message(const oracle_version_info_t *version_info,
                         const char *original_message, char *adapted_message);
    
    int adapt_tti_data(const oracle_version_info_t *version_info,
                      const char *original_data, char *adapted_data);

    // 数据转换辅助函数
    int convert_number_format(const oracle_version_info_t *from_version,
                             const oracle_version_info_t *to_version,
                             const char *data, size_t data_len,
                             char *converted_data, size_t *converted_len);
    
    int convert_date_format(const oracle_version_info_t *from_version,
                           const oracle_version_info_t *to_version,
                           const char *data, size_t data_len,
                           char *converted_data, size_t *converted_len);

    // 版本能力初始化
    void initialize_version_capabilities();

    // 内部状态
    bool m_debug_enabled;
    oracle_version_info_t m_min_supported_version;
    oracle_version_info_t m_max_supported_version;
    protocol_capabilities_t m_default_capabilities;
};

// Oracle版本常量定义
namespace OracleVersionConstants
{
    // 主要Oracle版本
    const uint16_t ORACLE_8i_MAJOR = 8;
    const uint16_t ORACLE_9i_MAJOR = 9;
    const uint16_t ORACLE_10g_MAJOR = 10;
    const uint16_t ORACLE_11g_MAJOR = 11;
    const uint16_t ORACLE_12c_MAJOR = 12;
    const uint16_t ORACLE_18c_MAJOR = 18;
    const uint16_t ORACLE_19c_MAJOR = 19;
    const uint16_t ORACLE_21c_MAJOR = 21;
    const uint16_t ORACLE_23c_MAJOR = 23;

    // TNS协议版本
    const uint16_t TNS_VERSION_8i = 300;
    const uint16_t TNS_VERSION_9i = 310;
    const uint16_t TNS_VERSION_10g = 312;
    const uint16_t TNS_VERSION_11g = 314;
    const uint16_t TNS_VERSION_12c = 315;
    const uint16_t TNS_VERSION_18c = 318;
    const uint16_t TNS_VERSION_19c = 319;

    // 支持的最小和最大版本
    const uint32_t MIN_SUPPORTED_VERSION = 0x08010000; // 8.1.0.0
    const uint32_t MAX_SUPPORTED_VERSION = 0x17000000; // 23.0.0.0

    // 默认能力标志
    const uint32_t DEFAULT_CLIENT_CAPABILITIES = 0x00000FFF;
    const uint32_t DEFAULT_SERVER_CAPABILITIES = 0x0000FFFF;
}

// 版本兼容性工具函数
namespace OracleVersionUtils
{
    // 版本字符串处理
    bool is_valid_version_string(const char *version_str);
    int normalize_version_string(const char *input, char *output, size_t output_size);
    
    // 版本比较
    bool is_newer_version(const oracle_version_info_t *version1, const oracle_version_info_t *version2);
    bool is_same_major_version(const oracle_version_info_t *version1, const oracle_version_info_t *version2);
    
    // 能力检查
    bool has_minimum_capabilities(const protocol_capabilities_t *caps);
    bool are_capabilities_compatible(const protocol_capabilities_t *caps1, const protocol_capabilities_t *caps2);
    
    // 版本特性检查
    bool supports_feature(const oracle_version_info_t *version_info, const char *feature_name);
    const char* get_version_name(const oracle_version_info_t *version_info);
}

#endif /* __ORACLE_VERSION_COMPAT_H__ */
