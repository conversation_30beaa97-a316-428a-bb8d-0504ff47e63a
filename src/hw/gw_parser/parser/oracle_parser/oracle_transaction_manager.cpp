/*
 * Oracle事务处理管理器实现
 * 实现完整的事务状态管理、分布式事务支持和保存点处理
 * 支持Oracle事务的完整生命周期管理
 * <AUTHOR> @date 2025
 */

#include "oracle_transaction_manager.h"
#include "oracle_parser_common.h"
#include "gw_logger.h"
#include <string.h>
#include <stdlib.h>
#include <algorithm>

// 日志宏定义
#define TX_LOG_DEBUG(fmt, ...) printf("[TX-DEBUG] " fmt "\n", ##__VA_ARGS__)
#define TX_LOG_INFO(fmt, ...)  printf("[TX-INFO] " fmt "\n", ##__VA_ARGS__)
#define TX_LOG_WARN(fmt, ...)  printf("[TX-WARN] " fmt "\n", ##__VA_ARGS__)
#define TX_LOG_ERROR(fmt, ...) printf("[TX-ERROR] " fmt "\n", ##__VA_ARGS__)

OracleTransactionManager::OracleTransactionManager()
    : m_next_transaction_id(1)
    , m_default_timeout(3600)  // 1小时默认超时
    , m_max_transactions(10000)
    , m_max_savepoints_per_tx(100)
{
    memset(&m_statistics, 0, sizeof(m_statistics));
    TX_LOG_INFO("Oracle Transaction Manager initialized");
}

OracleTransactionManager::~OracleTransactionManager()
{
    // 清理所有事务上下文
    for (auto& pair : m_transactions) {
        destroy_transaction_context(pair.second);
    }
    m_transactions.clear();
    
    TX_LOG_INFO("Oracle Transaction Manager destroyed, processed %llu transactions",
                m_statistics.total_transactions);
}

int OracleTransactionManager::begin_transaction(uint32_t session_id, uint32_t transaction_id, 
                                               oracle_isolation_level_t isolation_level)
{
    TX_LOG_INFO("Beginning transaction: session=%u, tx_id=%u, isolation=%d", 
               session_id, transaction_id, isolation_level);

    // 检查事务数量限制
    if (m_transactions.size() >= m_max_transactions) {
        TX_LOG_ERROR("Maximum transaction limit reached: %u", m_max_transactions);
        return -1;
    }

    // 检查事务是否已存在
    uint64_t tx_key = ((uint64_t)session_id << 32) | transaction_id;
    if (m_transactions.find(tx_key) != m_transactions.end()) {
        TX_LOG_ERROR("Transaction already exists: session=%u, tx_id=%u", session_id, transaction_id);
        return -1;
    }

    // 创建事务上下文
    oracle_transaction_context_t *context = create_transaction_context(session_id, transaction_id);
    if (!context) {
        TX_LOG_ERROR("Failed to create transaction context");
        return -1;
    }

    // 设置事务属性
    context->isolation_level = isolation_level;
    context->state = TRANSACTION_STATE_ACTIVE;
    context->start_time = get_current_timestamp();
    context->last_activity_time = context->start_time;
    context->timeout_seconds = m_default_timeout;

    // 检查会话的自动提交设置
    auto autocommit_it = m_session_autocommit.find(session_id);
    if (autocommit_it != m_session_autocommit.end()) {
        context->is_autocommit = autocommit_it->second;
    } else {
        context->is_autocommit = false; // Oracle默认不自动提交
        m_session_autocommit[session_id] = false;
    }

    // 添加到事务映射
    m_transactions[tx_key] = context;

    // 更新统计信息
    m_statistics.total_transactions++;
    m_statistics.current_active_transactions++;
    if (m_statistics.current_active_transactions > m_statistics.max_concurrent_transactions) {
        m_statistics.max_concurrent_transactions = m_statistics.current_active_transactions;
    }

    TX_LOG_INFO("Transaction started successfully: session=%u, tx_id=%u, isolation=%s", 
               session_id, transaction_id, get_isolation_level_name(isolation_level));

    return 0;
}

int OracleTransactionManager::commit_transaction(uint32_t session_id, uint32_t transaction_id)
{
    TX_LOG_INFO("Committing transaction: session=%u, tx_id=%u", session_id, transaction_id);

    oracle_transaction_context_t *context = find_transaction(session_id, transaction_id);
    if (!context) {
        TX_LOG_ERROR("Transaction not found: session=%u, tx_id=%u", session_id, transaction_id);
        return -1;
    }

    // 检查事务状态
    if (context->state != TRANSACTION_STATE_ACTIVE && context->state != TRANSACTION_STATE_PREPARED) {
        TX_LOG_ERROR("Invalid transaction state for commit: %s", get_transaction_state_name(context->state));
        return -1;
    }

    // 检查是否只能回滚
    if (context->is_rollback_only) {
        TX_LOG_ERROR("Transaction is marked rollback-only, cannot commit");
        return -1;
    }

    // 执行提交操作
    int ret = transition_transaction_state(context, TRANSACTION_STATE_COMMITTED);
    if (ret != 0) {
        TX_LOG_ERROR("Failed to transition transaction state to committed");
        return ret;
    }

    // 更新时间戳
    context->commit_time = get_current_timestamp();
    context->last_activity_time = context->commit_time;

    // 释放所有锁
    release_all_transaction_locks(session_id, transaction_id);

    // 清理保存点
    context->savepoints.clear();

    // 更新统计信息
    m_statistics.committed_transactions++;
    m_statistics.current_active_transactions--;
    
    uint64_t duration = context->commit_time - context->start_time;
    m_statistics.total_execution_time += duration;
    m_statistics.total_statements += context->statements_executed;
    m_statistics.total_rows_affected += context->rows_affected;

    // 计算平均事务持续时间
    if (m_statistics.committed_transactions > 0) {
        m_statistics.avg_transaction_duration = 
            (double)m_statistics.total_execution_time / m_statistics.committed_transactions;
    }

    // 计算平均每事务语句数
    if (m_statistics.total_transactions > 0) {
        m_statistics.avg_statements_per_transaction = 
            (double)m_statistics.total_statements / m_statistics.total_transactions;
    }

    TX_LOG_INFO("Transaction committed successfully: session=%u, tx_id=%u, duration=%llu us, statements=%u", 
               session_id, transaction_id, duration, context->statements_executed);

    return 0;
}

int OracleTransactionManager::rollback_transaction(uint32_t session_id, uint32_t transaction_id)
{
    TX_LOG_INFO("Rolling back transaction: session=%u, tx_id=%u", session_id, transaction_id);

    oracle_transaction_context_t *context = find_transaction(session_id, transaction_id);
    if (!context) {
        TX_LOG_ERROR("Transaction not found: session=%u, tx_id=%u", session_id, transaction_id);
        return -1;
    }

    // 检查事务状态
    if (context->state == TRANSACTION_STATE_COMMITTED) {
        TX_LOG_ERROR("Cannot rollback committed transaction");
        return -1;
    }

    if (context->state == TRANSACTION_STATE_ROLLED_BACK) {
        TX_LOG_WARN("Transaction already rolled back");
        return 0; // 不是错误，可能是重复调用
    }

    // 执行回滚操作
    int ret = transition_transaction_state(context, TRANSACTION_STATE_ROLLED_BACK);
    if (ret != 0) {
        TX_LOG_ERROR("Failed to transition transaction state to rolled back");
        return ret;
    }

    // 更新时间戳
    context->rollback_time = get_current_timestamp();
    context->last_activity_time = context->rollback_time;

    // 释放所有锁
    release_all_transaction_locks(session_id, transaction_id);

    // 清理保存点
    context->savepoints.clear();

    // 更新统计信息
    m_statistics.rolled_back_transactions++;
    m_statistics.current_active_transactions--;

    TX_LOG_INFO("Transaction rolled back successfully: session=%u, tx_id=%u", session_id, transaction_id);

    return 0;
}

int OracleTransactionManager::create_savepoint(uint32_t session_id, uint32_t transaction_id, const char *savepoint_name)
{
    if (!savepoint_name || strlen(savepoint_name) == 0) {
        TX_LOG_ERROR("Invalid savepoint name");
        return -1;
    }

    TX_LOG_INFO("Creating savepoint: session=%u, tx_id=%u, name=%s", 
               session_id, transaction_id, savepoint_name);

    oracle_transaction_context_t *context = find_transaction(session_id, transaction_id);
    if (!context) {
        TX_LOG_ERROR("Transaction not found: session=%u, tx_id=%u", session_id, transaction_id);
        return -1;
    }

    // 检查事务状态
    if (context->state != TRANSACTION_STATE_ACTIVE) {
        TX_LOG_ERROR("Cannot create savepoint in non-active transaction");
        return -1;
    }

    // 检查保存点数量限制
    if (context->savepoints.size() >= m_max_savepoints_per_tx) {
        TX_LOG_ERROR("Maximum savepoints per transaction limit reached: %u", m_max_savepoints_per_tx);
        return -1;
    }

    // 验证保存点名称
    if (validate_savepoint_name(savepoint_name) != 0) {
        TX_LOG_ERROR("Invalid savepoint name: %s", savepoint_name);
        return -1;
    }

    // 检查保存点是否已存在
    if (find_savepoint(context, savepoint_name) != nullptr) {
        TX_LOG_ERROR("Savepoint already exists: %s", savepoint_name);
        return -1;
    }

    // 创建保存点
    oracle_savepoint_t savepoint;
    memset(&savepoint, 0, sizeof(savepoint));
    strncpy(savepoint.name, savepoint_name, sizeof(savepoint.name) - 1);
    savepoint.savepoint_id = generate_savepoint_id();
    savepoint.create_time = get_current_timestamp();
    savepoint.sequence_number = context->savepoints.size() + 1;
    savepoint.is_active = true;

    // 添加到保存点列表
    context->savepoints.push_back(savepoint);

    // 更新统计信息
    m_statistics.total_savepoints++;

    TX_LOG_INFO("Savepoint created successfully: session=%u, tx_id=%u, name=%s, id=%u", 
               session_id, transaction_id, savepoint_name, savepoint.savepoint_id);

    return 0;
}

int OracleTransactionManager::rollback_to_savepoint(uint32_t session_id, uint32_t transaction_id, const char *savepoint_name)
{
    if (!savepoint_name || strlen(savepoint_name) == 0) {
        TX_LOG_ERROR("Invalid savepoint name");
        return -1;
    }

    TX_LOG_INFO("Rolling back to savepoint: session=%u, tx_id=%u, name=%s", 
               session_id, transaction_id, savepoint_name);

    oracle_transaction_context_t *context = find_transaction(session_id, transaction_id);
    if (!context) {
        TX_LOG_ERROR("Transaction not found: session=%u, tx_id=%u", session_id, transaction_id);
        return -1;
    }

    // 检查事务状态
    if (context->state != TRANSACTION_STATE_ACTIVE) {
        TX_LOG_ERROR("Cannot rollback to savepoint in non-active transaction");
        return -1;
    }

    // 查找保存点
    oracle_savepoint_t *savepoint = find_savepoint(context, savepoint_name);
    if (!savepoint) {
        TX_LOG_ERROR("Savepoint not found: %s", savepoint_name);
        return -1;
    }

    if (!savepoint->is_active) {
        TX_LOG_ERROR("Savepoint is not active: %s", savepoint_name);
        return -1;
    }

    // 清理该保存点之后创建的所有保存点
    int ret = cleanup_savepoints_after(context, savepoint->savepoint_id);
    if (ret != 0) {
        TX_LOG_ERROR("Failed to cleanup savepoints after %s", savepoint_name);
        return ret;
    }

    // 更新活动时间
    context->last_activity_time = get_current_timestamp();

    // 更新统计信息
    m_statistics.rollback_to_savepoint_count++;

    TX_LOG_INFO("Rolled back to savepoint successfully: session=%u, tx_id=%u, name=%s", 
               session_id, transaction_id, savepoint_name);

    return 0;
}

// 工具方法实现
oracle_transaction_context_t* OracleTransactionManager::create_transaction_context(uint32_t session_id, uint32_t transaction_id)
{
    oracle_transaction_context_t *context = new oracle_transaction_context_t();
    if (!context) {
        return nullptr;
    }

    memset(context, 0, sizeof(oracle_transaction_context_t));
    context->session_id = session_id;
    context->transaction_id = transaction_id;
    context->state = TRANSACTION_STATE_INACTIVE;
    context->isolation_level = ISOLATION_READ_COMMITTED;
    context->next_savepoint_id = 1;

    return context;
}

int OracleTransactionManager::destroy_transaction_context(oracle_transaction_context_t *context)
{
    if (!context) {
        return 0;
    }

    // 清理分布式事务信息
    if (context->distributed_tx) {
        cleanup_distributed_transaction_info(context);
    }

    // 清理保存点
    context->savepoints.clear();

    delete context;
    return 0;
}

oracle_transaction_context_t* OracleTransactionManager::find_transaction(uint32_t session_id, uint32_t transaction_id)
{
    uint64_t tx_key = ((uint64_t)session_id << 32) | transaction_id;
    auto it = m_transactions.find(tx_key);
    return (it != m_transactions.end()) ? it->second : nullptr;
}

uint64_t OracleTransactionManager::get_current_timestamp()
{
    struct timespec ts;
    clock_gettime(CLOCK_MONOTONIC, &ts);
    return ts.tv_sec * 1000000ULL + ts.tv_nsec / 1000ULL;
}

const char* OracleTransactionManager::get_transaction_state_name(oracle_transaction_state_t state)
{
    switch (state) {
        case TRANSACTION_STATE_INACTIVE:    return "INACTIVE";
        case TRANSACTION_STATE_ACTIVE:      return "ACTIVE";
        case TRANSACTION_STATE_PREPARED:    return "PREPARED";
        case TRANSACTION_STATE_COMMITTED:   return "COMMITTED";
        case TRANSACTION_STATE_ROLLED_BACK: return "ROLLED_BACK";
        default:                            return "UNKNOWN";
    }
}

const char* OracleTransactionManager::get_isolation_level_name(oracle_isolation_level_t level)
{
    switch (level) {
        case ISOLATION_READ_UNCOMMITTED:    return "READ_UNCOMMITTED";
        case ISOLATION_READ_COMMITTED:      return "READ_COMMITTED";
        case ISOLATION_REPEATABLE_READ:     return "REPEATABLE_READ";
        case ISOLATION_SERIALIZABLE:       return "SERIALIZABLE";
        default:                           return "UNKNOWN";
    }
}

int OracleTransactionManager::cleanup_savepoints_after(oracle_transaction_context_t *context, uint32_t savepoint_id)
{
    if (!context) {
        TX_LOG_ERROR("Invalid transaction context");
        return -1;
    }

    TX_LOG_DEBUG("Cleaning up savepoints after savepoint_id: %u", savepoint_id);

    // 使用迭代器删除所有ID大于指定savepoint_id的保存点
    auto it = context->savepoints.begin();
    while (it != context->savepoints.end()) {
        if (it->savepoint_id > savepoint_id) {
            TX_LOG_DEBUG("Removing savepoint: %s (id: %u)", it->name, it->savepoint_id);
            it = context->savepoints.erase(it);
        } else {
            ++it;
        }
    }

    TX_LOG_DEBUG("Cleanup completed, remaining savepoints: %zu", context->savepoints.size());
    return 0;
}

// ===== 缺失的私有方法实现 =====

// 查找保存点
oracle_savepoint_t* OracleTransactionManager::find_savepoint(oracle_transaction_context_t *context, const char *savepoint_name)
{
    if (!context || !savepoint_name) {
        TX_LOG_ERROR("Invalid parameters for find_savepoint");
        return nullptr;
    }

    TX_LOG_DEBUG("Finding savepoint: %s in transaction %u:%u", savepoint_name, context->session_id, context->transaction_id);

    // 在保存点列表中查找
    for (auto& savepoint : context->savepoints) {
        if (savepoint.is_active && strcmp(savepoint.name, savepoint_name) == 0) {
            TX_LOG_DEBUG("Found savepoint: %s (id=%u)", savepoint_name, savepoint.savepoint_id);
            return &savepoint;
        }
    }

    TX_LOG_DEBUG("Savepoint not found: %s", savepoint_name);
    return nullptr;
}

// 生成保存点ID
uint32_t OracleTransactionManager::generate_savepoint_id()
{
    static uint32_t savepoint_counter = 1;
    uint32_t id = savepoint_counter++;
    TX_LOG_DEBUG("Generated savepoint ID: %u", id);
    return id;
}

// 验证保存点名称
int OracleTransactionManager::validate_savepoint_name(const char *savepoint_name)
{
    if (!savepoint_name) {
        TX_LOG_ERROR("Savepoint name is null");
        return -1;
    }

    size_t name_len = strlen(savepoint_name);
    if (name_len == 0) {
        TX_LOG_ERROR("Savepoint name is empty");
        return -1;
    }

    if (name_len > 128) { // Oracle限制保存点名称长度
        TX_LOG_ERROR("Savepoint name too long: %zu characters (max 128)", name_len);
        return -1;
    }

    // 检查名称是否包含有效字符
    for (size_t i = 0; i < name_len; i++) {
        char c = savepoint_name[i];
        if (!isalnum(c) && c != '_' && c != '$' && c != '#') {
            TX_LOG_ERROR("Invalid character in savepoint name: '%c'", c);
            return -1;
        }
    }

    // 检查是否以字母或下划线开头
    if (!isalpha(savepoint_name[0]) && savepoint_name[0] != '_') {
        TX_LOG_ERROR("Savepoint name must start with letter or underscore");
        return -1;
    }

    TX_LOG_DEBUG("Savepoint name validation passed: %s", savepoint_name);
    return 0;
}

// 事务状态转换
int OracleTransactionManager::transition_transaction_state(oracle_transaction_context_t *context, oracle_transaction_state_t new_state)
{
    if (!context) {
        TX_LOG_ERROR("Invalid context for state transition");
        return -1;
    }

    oracle_transaction_state_t old_state = context->state;

    TX_LOG_DEBUG("Transitioning transaction %u:%u from %s to %s",
                 context->session_id, context->transaction_id,
                 get_transaction_state_name(old_state),
                 get_transaction_state_name(new_state));

    // 验证状态转换是否有效
    if (!is_valid_state_transition(old_state, new_state)) {
        TX_LOG_ERROR("Invalid state transition from %s to %s",
                     get_transaction_state_name(old_state),
                     get_transaction_state_name(new_state));
        return -1;
    }

    // 执行状态转换
    context->state = new_state;
    context->last_activity_time = get_current_timestamp();

    // 更新统计信息
    switch (new_state) {
        case TRANSACTION_STATE_COMMITTED:
            m_statistics.committed_transactions++;
            break;
        case TRANSACTION_STATE_ROLLED_BACK:
            m_statistics.rolled_back_transactions++;
            break;
        case TRANSACTION_STATE_PREPARED:
            // 准备状态不在统计结构中，跳过
            break;
        default:
            break;
    }

    TX_LOG_INFO("Transaction %u:%u state changed: %s -> %s",
                context->session_id, context->transaction_id,
                get_transaction_state_name(old_state),
                get_transaction_state_name(new_state));

    return 0;
}

// 释放所有事务锁
int OracleTransactionManager::release_all_transaction_locks(uint32_t session_id, uint32_t transaction_id)
{
    TX_LOG_DEBUG("Releasing all locks for transaction %u:%u", session_id, transaction_id);

    // 查找事务上下文
    uint64_t tx_key = ((uint64_t)session_id << 32) | transaction_id;
    auto it = m_transactions.find(tx_key);
    if (it == m_transactions.end()) {
        TX_LOG_WARN("Transaction not found for lock release: %u:%u", session_id, transaction_id);
        return -1;
    }

    oracle_transaction_context_t *context = it->second;

    // 释放所有锁（简化实现）
    int locks_released = 0;

    // 这里应该调用锁管理器来释放锁，简化实现只记录日志
    // 使用context来避免未使用变量警告
    if (context) {
        TX_LOG_INFO("Released %d locks for transaction %u:%u", locks_released, session_id, transaction_id);
    }

    return 0;
}

// 清理分布式事务信息
int OracleTransactionManager::cleanup_distributed_transaction_info(oracle_transaction_context_t *context)
{
    if (!context) {
        TX_LOG_ERROR("Invalid context for distributed transaction cleanup");
        return -1;
    }

    TX_LOG_DEBUG("Cleaning up distributed transaction info for %u:%u",
                 context->session_id, context->transaction_id);

    if (context->distributed_tx) {
        // 清理分布式事务信息
        // global_transaction_id 和 branch_qualifier 是数组，不需要释放
        memset(context->distributed_tx->global_transaction_id, 0, sizeof(context->distributed_tx->global_transaction_id));
        memset(context->distributed_tx->branch_qualifier, 0, sizeof(context->distributed_tx->branch_qualifier));
        memset(context->distributed_tx->resource_manager, 0, sizeof(context->distributed_tx->resource_manager));

        free(context->distributed_tx);
        context->distributed_tx = nullptr;

        TX_LOG_DEBUG("Distributed transaction info cleaned up");
    }

    return 0;
}

// 验证状态转换是否有效
bool OracleTransactionManager::is_valid_state_transition(oracle_transaction_state_t from_state, oracle_transaction_state_t to_state)
{
    TX_LOG_DEBUG("Validating state transition: %s -> %s",
                 get_transaction_state_name(from_state),
                 get_transaction_state_name(to_state));

    // 定义有效的状态转换
    switch (from_state) {
        case TRANSACTION_STATE_INACTIVE:
            // 从非活动状态只能转换到活动状态
            return (to_state == TRANSACTION_STATE_ACTIVE);

        case TRANSACTION_STATE_ACTIVE:
            // 从活动状态可以转换到准备、提交或回滚状态
            return (to_state == TRANSACTION_STATE_PREPARED ||
                    to_state == TRANSACTION_STATE_COMMITTED ||
                    to_state == TRANSACTION_STATE_ROLLED_BACK);

        case TRANSACTION_STATE_PREPARED:
            // 从准备状态只能转换到提交或回滚状态
            return (to_state == TRANSACTION_STATE_COMMITTED ||
                    to_state == TRANSACTION_STATE_ROLLED_BACK);

        case TRANSACTION_STATE_COMMITTED:
            // 已提交状态是终态，不能转换到其他状态
            return false;

        case TRANSACTION_STATE_ROLLED_BACK:
            // 已回滚状态是终态，不能转换到其他状态
            return false;

        default:
            TX_LOG_ERROR("Unknown transaction state: %d", from_state);
            return false;
    }
}
