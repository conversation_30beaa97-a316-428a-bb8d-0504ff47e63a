/*
 * Oracle批量操作处理器头文件
 * 实现批量INSERT、UPDATE、DELETE操作的解析和统计
 * <AUTHOR> @date 2025
 */

#ifndef __ORACLE_BATCH_HANDLER_H__
#define __ORACLE_BATCH_HANDLER_H__

#include <inttypes.h>
#include <vector>
#include <map>
#include <string>
#include "oracle_parser_common.h"

// 批量操作处理结果状态
#define BATCH_PARSE_SUCCESS           0
#define BATCH_PARSE_NEED_MORE_DATA    1
#define BATCH_PARSE_ERROR            -1
#define BATCH_PARSE_INVALID_DATA     -2
#define BATCH_PARSE_UNSUPPORTED      -3
#define BATCH_PARSE_MEMORY_ERROR     -4

// 批量操作类型
#define ORACLE_BATCH_OP_INSERT        1
#define ORACLE_BATCH_OP_UPDATE        2
#define ORACLE_BATCH_OP_DELETE        3
#define ORACLE_BATCH_OP_MERGE         4
#define ORACLE_BATCH_OP_CALL          5

// 批量执行模式
#define ORACLE_BATCH_MODE_ARRAY       1  // 数组绑定模式
#define ORACLE_BATCH_MODE_FORALL      2  // FORALL模式
#define ORACLE_BATCH_MODE_BULK        3  // BULK COLLECT模式
#define ORACLE_BATCH_MODE_MULTI       4  // 多语句模式

// 批量操作状态
#define ORACLE_BATCH_STATUS_PENDING   0
#define ORACLE_BATCH_STATUS_EXECUTING 1
#define ORACLE_BATCH_STATUS_COMPLETED 2
#define ORACLE_BATCH_STATUS_FAILED    3
#define ORACLE_BATCH_STATUS_PARTIAL   4

// 批量绑定变量信息（扩展版本，用于批量操作）
typedef struct oracle_batch_bind_variable
{
    uint16_t bind_index;            // 绑定变量索引
    uint8_t  data_type;             // 数据类型
    uint16_t max_length;            // 最大长度
    uint32_t array_size;            // 数组大小
    char     *bind_name;            // 绑定变量名
    void     **values;              // 值数组
    uint16_t *lengths;              // 长度数组
    uint8_t  *indicators;           // NULL指示符数组
    bool     is_array_bind;         // 是否为数组绑定
} oracle_batch_bind_variable_t;

// 批量操作行信息
typedef struct oracle_batch_row
{
    uint32_t row_index;             // 行索引
    uint8_t  row_status;            // 行状态（成功/失败）
    uint32_t error_code;            // 错误码
    char     *error_message;        // 错误消息
    uint64_t execution_time;        // 执行时间
    uint32_t affected_rows;         // 影响行数
} oracle_batch_row_t;

// 批量操作信息
typedef struct oracle_batch_operation
{
    uint8_t  operation_type;        // 操作类型
    uint8_t  batch_mode;            // 批量模式
    uint8_t  batch_status;          // 批量状态
    uint32_t batch_size;            // 批量大小
    uint32_t current_row;           // 当前行
    uint32_t successful_rows;       // 成功行数
    uint32_t failed_rows;           // 失败行数
    
    b_string_t sql_template;        // SQL模板
    uint16_t bind_count;            // 绑定变量数量
    oracle_batch_bind_variable_t *bind_variables; // 绑定变量数组
    oracle_batch_row_t *row_results; // 行结果数组
    
    uint64_t start_time;            // 开始时间
    uint64_t end_time;              // 结束时间
    uint64_t total_execution_time;  // 总执行时间
    uint64_t total_bytes_processed; // 总处理字节数
    
    bool auto_commit;               // 自动提交
    bool continue_on_error;         // 出错时继续
    uint32_t commit_batch_size;     // 提交批次大小
} oracle_batch_operation_t;

// 批量操作会话
typedef struct oracle_batch_session
{
    uint32_t session_id;            // 会话ID
    uint32_t active_batches;        // 活跃批量操作数
    uint64_t total_batch_operations; // 总批量操作数
    uint64_t total_rows_processed;  // 总处理行数
    uint64_t total_execution_time;  // 总执行时间
    std::vector<oracle_batch_operation_t*> pending_batches; // 待处理批量操作
} oracle_batch_session_t;

// 前向声明
struct oracle_batch_progress;
typedef struct oracle_batch_progress oracle_batch_progress_t;
struct oracle_batch_bottleneck;
typedef struct oracle_batch_bottleneck oracle_batch_bottleneck_t;
struct oracle_batch_stats;
typedef struct oracle_batch_stats oracle_batch_stats_t;

// 批量操作处理器类
class OracleBatchHandler
{
public:
    OracleBatchHandler();
    ~OracleBatchHandler();

    // 批量操作解析
    int parse_batch_operation(const char *data, size_t data_len, uint8_t message_type,
                             oracle_status_t *status, oracle_parsed_data_t *result);

    // 批量INSERT解析
    int parse_batch_insert(const char *data, size_t data_len, oracle_batch_operation_t *batch_op);
    int parse_array_insert(const char *data, size_t data_len, oracle_batch_operation_t *batch_op);
    int parse_multi_insert(const char *data, size_t data_len, oracle_batch_operation_t *batch_op);

    // 批量UPDATE解析
    int parse_batch_update(const char *data, size_t data_len, oracle_batch_operation_t *batch_op);
    int parse_array_update(const char *data, size_t data_len, oracle_batch_operation_t *batch_op);
    int parse_forall_update(const char *data, size_t data_len, oracle_batch_operation_t *batch_op);

    // 批量DELETE解析
    int parse_batch_delete(const char *data, size_t data_len, oracle_batch_operation_t *batch_op);
    int parse_array_delete(const char *data, size_t data_len, oracle_batch_operation_t *batch_op);
    int parse_bulk_delete(const char *data, size_t data_len, oracle_batch_operation_t *batch_op);

    // 绑定变量处理
    int parse_bind_variables(const char *data, size_t data_len, size_t *offset, oracle_batch_operation_t *batch_op);
    int parse_array_bind_variable(const char *data, size_t data_len, size_t *offset, oracle_batch_bind_variable_t *bind_var);
    int extract_bind_values(const oracle_batch_bind_variable_t *bind_var, uint32_t row_index, oracle_value_t *value);

    // 批量结果处理
    int parse_batch_results(const char *data, size_t data_len, oracle_batch_operation_t *batch_op);
    int parse_row_result(const char *data, size_t data_len, size_t *offset, oracle_batch_row_t *row_result);
    int update_batch_statistics(oracle_batch_operation_t *batch_op);

    // 批量操作会话管理
    int create_batch_session(uint32_t session_id, oracle_batch_session_t **session);
    int register_batch_operation(oracle_batch_session_t *session, oracle_batch_operation_t *batch_op);
    int complete_batch_operation(oracle_batch_session_t *session, uint32_t batch_id);
    void cleanup_batch_session(oracle_batch_session_t *session);

    // 批量操作优化
    int optimize_batch_operation(oracle_batch_operation_t *batch_op);
    int estimate_batch_performance(const oracle_batch_operation_t *batch_op, uint64_t *estimated_time);
    int suggest_batch_size(const oracle_batch_operation_t *batch_op, uint32_t *suggested_size);

    // 批量操作监控
    int monitor_batch_progress(const oracle_batch_operation_t *batch_op, oracle_batch_progress_t *progress);
    int detect_batch_bottlenecks(const oracle_batch_operation_t *batch_op, oracle_batch_bottleneck_t *bottleneck);
    int generate_batch_report(const oracle_batch_operation_t *batch_op, char *report, size_t report_size);

    // 内存管理
    oracle_batch_operation_t* create_batch_operation(uint8_t operation_type, uint32_t batch_size);
    void free_batch_operation(oracle_batch_operation_t *batch_op);
    void free_bind_variable(oracle_batch_bind_variable_t *bind_var);
    void free_batch_row(oracle_batch_row_t *row);

    // 工具方法
    const char* get_batch_operation_name(uint8_t operation_type);
    const char* get_batch_mode_name(uint8_t batch_mode);
    const char* get_batch_status_name(uint8_t batch_status);
    bool is_batch_operation_supported(uint8_t operation_type);

    // 统计信息
    void get_batch_statistics(oracle_batch_stats_t *stats);
    void reset_batch_statistics();
    void update_performance_metrics(const oracle_batch_operation_t *batch_op);

    // 调试和日志
    void dump_batch_operation(const oracle_batch_operation_t *batch_op);
    void dump_bind_variables(const oracle_batch_bind_variable_t *bind_vars, uint16_t count);
    void dump_batch_results(const oracle_batch_row_t *results, uint32_t count);

    // 配置管理
    void set_max_batch_size(uint32_t max_size) { m_max_batch_size = max_size; }
    void set_default_commit_batch_size(uint32_t size) { m_default_commit_batch_size = size; }
    void set_performance_monitoring_enabled(bool enabled) { m_performance_monitoring = enabled; }
    void set_debug_enabled(bool enabled) { m_debug_enabled = enabled; }

private:
    // 内部工具方法
    uint16_t read_uint16_le(const char *data);
    uint32_t read_uint32_le(const char *data);
    uint64_t read_uint64_le(const char *data);

    // 批量操作内部处理
    int detect_batch_mode(const char *data, size_t data_len, uint8_t *batch_mode);
    int parse_batch_header(const char *data, size_t data_len, size_t *offset, oracle_batch_operation_t *batch_op);
    int validate_batch_operation(const oracle_batch_operation_t *batch_op);

    // 绑定变量内部处理
    int allocate_bind_arrays(oracle_batch_bind_variable_t *bind_var, uint32_t array_size);
    int parse_bind_value(const char *data, size_t data_len, size_t *offset,
                        uint8_t data_type, oracle_value_t *value);
    int convert_bind_value_to_string(const oracle_value_t *value, char *buffer, size_t buffer_size);

    // 性能分析
    int analyze_batch_performance(const oracle_batch_operation_t *batch_op);
    int calculate_throughput(const oracle_batch_operation_t *batch_op, double *rows_per_second);
    int identify_slow_operations(const oracle_batch_operation_t *batch_op, std::vector<uint32_t> &slow_rows);

    // 会话管理
    std::map<uint32_t, oracle_batch_session_t*> m_batch_sessions;
    uint32_t m_next_session_id;
    uint32_t m_next_batch_id;

    // 配置参数
    uint32_t m_max_batch_size;
    uint32_t m_default_commit_batch_size;
    bool m_performance_monitoring;
    bool m_debug_enabled;

    // 统计信息
    uint64_t m_total_batch_operations;
    uint64_t m_total_rows_processed;
    uint64_t m_total_execution_time;
    uint64_t m_successful_batches;
    uint64_t m_failed_batches;
    double m_average_batch_size;
    double m_average_throughput;
};

// 批量操作统计信息
typedef struct oracle_batch_stats
{
    uint64_t total_operations;      // 总操作数
    uint64_t total_rows_processed;  // 总处理行数
    uint64_t total_execution_time;  // 总执行时间
    uint64_t insert_operations;     // INSERT操作数
    uint64_t update_operations;     // UPDATE操作数
    uint64_t delete_operations;     // DELETE操作数
    uint64_t successful_batches;    // 成功批次数
    uint64_t failed_batches;        // 失败批次数
    double   average_batch_size;    // 平均批次大小
    double   average_throughput;    // 平均吞吐量（行/秒）
    uint64_t max_batch_size;        // 最大批次大小
    uint64_t min_batch_size;        // 最小批次大小
} oracle_batch_stats_t;

// 批量操作进度信息
typedef struct oracle_batch_progress
{
    uint32_t total_rows;            // 总行数
    uint32_t processed_rows;        // 已处理行数
    uint32_t successful_rows;       // 成功行数
    uint32_t failed_rows;           // 失败行数
    double   completion_percentage; // 完成百分比
    uint64_t elapsed_time;          // 已用时间
    uint64_t estimated_remaining_time; // 预计剩余时间
    double   current_throughput;    // 当前吞吐量
} oracle_batch_progress_t;

// 批量操作瓶颈信息
typedef struct oracle_batch_bottleneck
{
    uint8_t  bottleneck_type;       // 瓶颈类型
    char     description[256];      // 描述
    double   impact_factor;         // 影响因子
    char     suggestion[512];       // 优化建议
} oracle_batch_bottleneck_t;

// 批量操作工具函数命名空间
namespace OracleBatchUtils
{
    // 批量操作类型判断
    bool is_dml_batch_operation(uint8_t operation_type);
    bool is_array_bind_operation(uint8_t batch_mode);
    bool supports_continue_on_error(uint8_t operation_type);
    bool requires_transaction_control(uint8_t operation_type);

    // 批量大小优化
    uint32_t calculate_optimal_batch_size(uint64_t total_rows, uint64_t available_memory);
    uint32_t adjust_batch_size_for_performance(uint32_t current_size, double current_throughput);
    bool should_use_array_binding(uint32_t batch_size, uint8_t operation_type);

    // 性能估算
    uint64_t estimate_batch_memory_usage(const oracle_batch_operation_t *batch_op);
    double estimate_batch_execution_time(const oracle_batch_operation_t *batch_op);
    double calculate_batch_efficiency(const oracle_batch_operation_t *batch_op);

    // 错误处理
    bool is_recoverable_batch_error(uint32_t error_code);
    uint32_t get_retry_delay_for_error(uint32_t error_code);
    bool should_abort_batch_on_error(uint32_t error_code, uint32_t failed_row_count);
}

#endif /* __ORACLE_BATCH_HANDLER_H__ */
