/*
 * Oracle协议解析器通用定义
 * <AUTHOR> @date 2025
 */

#ifndef __ORACLE_PARSER_COMMON_H__
#define __ORACLE_PARSER_COMMON_H__

#include <stdlib.h>
#include <inttypes.h>
#include <string>

// 字符串结构定义
typedef struct b_string
{
    const char *s;
    unsigned int len;
} b_string_t;

// Oracle协议日志前缀
#define ORACLE_LOG_PRE   "[Oracle]"

// Oracle解析器统计常量定义 - 参照PostgreSQL解析器
#define ORACLE_SHOW                    "oracle"
#define ORACLE_SESSION_QPS             "oracle_session_qps"
#define ORACLE_PARSER_TOTAL            "oracle_parser_total"
#define ORACLE_PARSER_BYTES            "oracle_parser_bytes"
#define ORACLE_MATCH_SUC_BYTES         "oracle_match_suc_bytes"

enum oracle_parser_type { ORACLE_REQUEST, ORACLE_RESPONSE, ORACLE_BOTH };

// TNS协议包类型 - 基于ojdbc分析的完整定义
#define TNS_PACKET_TYPE_CONNECT       1    // 连接请求
#define TNS_PACKET_TYPE_ACCEPT        2    // 连接接受
#define TNS_PACKET_TYPE_ACK           3    // 确认
#define TNS_PACKET_TYPE_REFUSE        4    // 连接拒绝
#define TNS_PACKET_TYPE_REDIRECT      5    // 重定向
#define TNS_PACKET_TYPE_DATA          6    // 数据包
#define TNS_PACKET_TYPE_NULL          7    // 空包
#define TNS_PACKET_TYPE_ABORT         9    // 中止连接
#define TNS_PACKET_TYPE_RESEND        11   // 重发请求
#define TNS_PACKET_TYPE_MARKER        12   // 标记包
#define TNS_PACKET_TYPE_ATTENTION     13   // 注意包
#define TNS_PACKET_TYPE_CONTROL       14   // 控制包
#define TNS_PACKET_TYPE_DATA_DESCRIPTOR 15 // 数据描述符包

// TNS协议标志位 - 基于ojdbc源码完整定义
#define TNS_FLAG_NONE                 0x00
#define TNS_FLAG_SESSION_ID            0x01  // 会话ID标志
#define TNS_FLAG_READ_DATA             0x02  // 读取数据标志
#define TNS_FLAG_REDIRECT              0x04  // 重定向标志
#define TNS_FLAG_SECURITY_RENEG        0x08  // 安全重新协商标志
#define TNS_FLAG_LARGE_SDU             0x20  // 大SDU标志
#define TNS_FLAG_EOF                   0x40  // 数据结束标志

// TNS DATA包标志位 - 载荷前2字节的标志位
#define TNS_DATA_FLAG_TOKEN            0x0001  // 令牌
#define TNS_DATA_FLAG_REQ_CONFIRM      0x0002  // 请求确认
#define TNS_DATA_FLAG_CONFIRM          0x0004  // 确认
#define TNS_DATA_FLAG_MORE_DATA        0x0020  // 后续有更多数据
#define TNS_DATA_FLAG_EOF              0x0040  // 数据结束
#define TNS_DATA_FLAG_IMMEDIATE        0x0080  // 立即处理
#define TNS_DATA_FLAG_COMPRESSED       0x0400  // 压缩数据
#define TNS_DATA_FLAG_PIPE_START       0x1000  // 管道开始
#define TNS_DATA_FLAG_PIPE_RETURN      0x4000  // 管道返回
#define TNS_DATA_FLAG_SSL_RENEG        0x8000  // SSL重新协商

// TNS协议版本
#define TNS_VERSION_MIN               300   // 最小支持版本
#define TNS_VERSION_MAX               400   // 最大支持版本
#define TNS_VERSION_DEFAULT           315   // 默认版本

// TNS数据包大小限制
#define TNS_HEADER_SIZE               8     // TNS标准头部大小
#define TNS_LARGE_HEADER_SIZE         8     // TNS大包头部大小
#define TNS_MIN_PACKET_SIZE           8     // 最小包大小
#define TNS_MAX_PACKET_SIZE           65535 // 标准最大包大小
#define TNS_MAX_LARGE_PACKET_SIZE     0x7FFFFFFF // 大包最大大小
#define TNS_DEFAULT_SDU_SIZE          2048  // 默认SDU大小

// TTC消息类型码 - 基于ojdbc T4CTTIMsgCodes.java的完整定义
#define TTIPRO                            1   // 协议协商
#define TTIDTY                            2   // 数据类型定义
#define TTIFUN                            3   // 函数调用
#define TTIOER                            4   // 错误消息
#define TTIRXH                            6   // 结果集头部
#define TTIRXD                            7   // 结果集数据
#define TTIRPA                            8   // 返回参数
#define TTISTA                            9   // 状态信息
#define TTIIOV                            11  // I/O向量
#define TTISLG                            12  // 会话日志
#define TTIOAC                            13  // 输出参数
#define TTILOBD                           14  // LOB数据
#define TTIWRN                            15  // 警告消息
#define TTIDCB                            16  // 数据库回调
#define TTIPFN                            17  // 预取函数
#define TTIFOB                            19  // 函数对象
#define TTIBVC                            21  // 批量变量
#define TTISPF                            23  // 特殊函数
#define TTIQC                             24  // 查询缓存
#define TTIRSH                            25  // 结果集句柄
#define TTIONEWAYFN                       26  // 单向函数
#define TTIIMPLRES                        27  // 隐式结果
#define TTIRENEG                          28  // 重新协商
#define TTIKEYVAL                         29  // 键值对
#define TTICOOKIE                         30  // Cookie
#define TTITKN                            33  // 令牌
#define TTIINIT                           34  // 初始化

// TTC函数码 - 基于ojdbc T4CTTIfunCodes.java的完整定义
#define OPARSE                            1   // 解析SQL
#define OOPEN                             2   // 打开游标
#define OEXEC                             3   // 执行SQL
#define ODEFIN                            4   // 定义输出变量
#define OBIND                             5   // 绑定变量
#define OFETCH                            6   // 获取数据
#define OEXFET                            7   // 执行并获取
#define OFLNG                             8   // 获取长数据
#define OCLOSE                            9   // 关闭游标
#define OLOGOFF                           10  // 登出
#define OCOMON                            12  // 通用开启
#define OCOMOFF                           13  // 通用关闭
#define OCOMMIT                           14  // 提交
#define OROLLBACK                         15  // 回滚
#define OCANCEL                           20  // 取消
#define ODSCRARR                          43  // 描述数组
#define OVERSION                          59  // 版本
#define OK2RPC                            67  // K2RPC
#define OALL7                             71  // ALL7
#define OSQL7                             74  // SQL7
#define OEXFEN                            78  // EXFEN
#define O3LOGON                           81  // 3LOGON
#define O3LOGA                            82  // 3LOGA
#define OKOD                              92  // KOD
#define OALL8                             94  // ALL8
#define OLOBOPS                           96  // LOB操作
#define ODNY                              98  // DNY
#define OTXSE                             103 // 事务开始
#define OTXEN                             104 // 事务结束
#define OCCA                              105 // CCA
#define O80SES                            107 // 80会话
#define OAUTH                             115 // 认证
#define OSESSKEY                          118 // 会话密钥
#define ODSY                              119 // DSY
#define OCANA                             120 // CANA
#define OKPN                              125 // KPN
#define OOTCM                             127 // OTCM
#define OSCID                             135 // SCID
#define OSPFPPUT                          138 // SPFPPUT

// Oracle功能码定义 - 兼容旧代码
#define ORACLE_FUNCTION_CODE_LOGON        O3LOGON
#define ORACLE_FUNCTION_CODE_LOGOFF       OLOGOFF
#define ORACLE_FUNCTION_CODE_COMMIT       OCOMMIT
#define ORACLE_FUNCTION_CODE_ROLLBACK     OROLLBACK

// TTI消息类型码 - 与TTC消息类型保持一致
#define TTI_MSG_TYPE_TTIPRO              1   // 协议协商
#define TTI_MSG_TYPE_TTIDTY              2   // 数据类型定义
#define TTI_MSG_TYPE_TTIFUN              3   // 函数调用
#define TTI_MSG_TYPE_TTIOER              4   // 错误消息
#define TTI_MSG_TYPE_TTIRXH              6   // 结果集头部
#define TTI_MSG_TYPE_TTIRXD              7   // 结果集数据
#define TTI_MSG_TYPE_TTIRPA              8   // 返回参数
#define TTI_MSG_TYPE_TTISTA              9   // 状态信息
#define TTI_MSG_TYPE_TTIIOV              11  // I/O向量
#define TTI_MSG_TYPE_TTISLG              12  // 会话日志
#define TTI_MSG_TYPE_TTIOAC              13  // 输出参数
#define TTI_MSG_TYPE_TTILOBD             14  // LOB数据
#define TTI_MSG_TYPE_TTIWRN              15  // 警告消息
#define TTI_MSG_TYPE_TTIDCB              16  // 数据库回调
#define TTI_MSG_TYPE_TTIPFN              17  // 预取函数
#define TTI_MSG_TYPE_TTIFOB              19  // 函数对象
#define TTI_MSG_TYPE_TTIBVC              21  // 批量变量
#define TTI_MSG_TYPE_TTISPF              23  // 特殊函数
#define TTI_MSG_TYPE_TTIQC               24  // 查询缓存
#define TTI_MSG_TYPE_TTIRSH              25  // 结果集句柄
#define TTI_MSG_TYPE_TTIONEWAYFN         26  // 单向函数
#define TTI_MSG_TYPE_TTIIMPLRES          27  // 隐式结果
#define TTI_MSG_TYPE_TTIRENEG            28  // 重新协商
#define TTI_MSG_TYPE_TTIKEYVAL           29  // 键值对
#define TTI_MSG_TYPE_TTICOOKIE           30  // Cookie
#define TTI_MSG_TYPE_TTITKN              33  // 令牌
#define TTI_MSG_TYPE_TTIINIT             34  // 初始化
#define TTILOB                           35  // LOB操作

// TTI功能消息类型 - 用于解析器内部逻辑
#define TTI_MSG_TYPE_PARSE               100  // SQL解析
#define TTI_MSG_TYPE_EXECUTE             101  // SQL执行
#define TTI_MSG_TYPE_FETCH               102  // 数据获取
#define TTI_MSG_TYPE_COMMIT              103  // 事务提交
#define TTI_MSG_TYPE_ROLLBACK            104  // 事务回滚
#define TTI_MSG_TYPE_LOGON               105  // 用户登录

// TTC 函数类型定义
#define TTC_FUNCTION_PROTOCOL_NEGOTIATION    1   // 协议协商
#define TTC_FUNCTION_DATA_TYPES              2   // 数据类型协商
#define TTC_FUNCTION_LOGON                   3   // 登录
#define TTC_FUNCTION_LOGOFF                  4   // 登出
#define TTC_FUNCTION_OPEN                    5   // 打开游标
#define TTC_FUNCTION_PARSE                   6   // 解析SQL
#define TTC_FUNCTION_EXECUTE                 7   // 执行SQL
#define TTC_FUNCTION_FETCH                   8   // 获取数据
#define TTC_FUNCTION_CLOSE                   9   // 关闭游标
#define TTC_FUNCTION_COMMIT                  10  // 提交事务
#define TTC_FUNCTION_ROLLBACK                11  // 回滚事务
#define TTC_FUNCTION_DESCRIBE                12  // 描述
#define TTC_FUNCTION_CANCEL                  13  // 取消操作
#define TTC_FUNCTION_PING                    14  // 心跳检测

// Oracle数据类型定义 - 基于ojdbc源码和文档的完整SQLT常量
#define ORACLE_SQLT_CHR               1    // VARCHAR2
#define ORACLE_SQLT_NUM               2    // NUMBER
#define ORACLE_SQLT_INT               3    // INTEGER
#define ORACLE_SQLT_FLT               4    // FLOAT
#define ORACLE_SQLT_STR               5    // NULL-terminated STRING
#define ORACLE_SQLT_VNU               6    // VARNUM
#define ORACLE_SQLT_PDN               7    // PACKED DECIMAL
#define ORACLE_SQLT_LNG               8    // LONG
#define ORACLE_SQLT_VCS               9    // VARCHAR
#define ORACLE_SQLT_NON               10   // NULL
#define ORACLE_SQLT_RID               11   // ROWID
#define ORACLE_SQLT_DAT               12   // DATE
#define ORACLE_SQLT_VBI               15   // VARRAW
#define ORACLE_SQLT_BIN               23   // RAW
#define ORACLE_SQLT_LBI               24   // LONG RAW
#define ORACLE_SQLT_UIN               68   // UNSIGNED INTEGER
#define ORACLE_SQLT_SLS               91   // DISPLAY SIGN LEADING SEPARATE
#define ORACLE_SQLT_LVC               94   // LONGER LONGS (CHAR)
#define ORACLE_SQLT_LVB               95   // LONGER LONG (BINARY)
#define ORACLE_SQLT_AFC               96   // ANSI FIXED CHAR
#define ORACLE_SQLT_AVC               97   // ANSI VAR CHAR
#define ORACLE_SQLT_BINARY_FLOAT      100  // BINARY_FLOAT (IEEE 754)
#define ORACLE_SQLT_BINARY_DOUBLE     101  // BINARY_DOUBLE (IEEE 754)
#define ORACLE_SQLT_CUR               102  // CURSOR
#define ORACLE_SQLT_RDD               104  // ROWID DESCRIPTOR
#define ORACLE_SQLT_LAB               105  // LABEL
#define ORACLE_SQLT_OSL               106  // MLSLABEL
#define ORACLE_SQLT_NTY               108  // NAMED OBJECT TYPE
#define ORACLE_SQLT_XMLTYPE           109  // XMLType
#define ORACLE_SQLT_REF               110  // REF
#define ORACLE_SQLT_CLOB              112  // CLOB
#define ORACLE_SQLT_BLOB              113  // BLOB
#define ORACLE_SQLT_BFILEE            114  // BFILE
#define ORACLE_SQLT_CFILEE            115  // CFILE
#define ORACLE_SQLT_RSET              116  // RESULT SET
#define ORACLE_SQLT_JSON              119  // JSON (21c+)
#define ORACLE_SQLT_NCO               122  // NAMED COLLECTION
#define ORACLE_SQLT_VST               155  // OCI STRING TYPE
#define ORACLE_SQLT_ODT               156  // OCI DATE TYPE
#define ORACLE_SQLT_TIMESTAMP         180  // TIMESTAMP
#define ORACLE_SQLT_TIMESTAMP_TZ      181  // TIMESTAMP WITH TIME ZONE
#define ORACLE_SQLT_INTERVAL_YM       182  // INTERVAL YEAR TO MONTH
#define ORACLE_SQLT_INTERVAL_DS       183  // INTERVAL DAY TO SECOND
#define ORACLE_SQLT_TIMESTAMP_LTZ     231  // TIMESTAMP WITH LOCAL TIME ZONE
#define ORACLE_SQLT_BOOLEAN           252  // BOOLEAN (12c+)

// SQL操作类型
#define ORACLE_OP_SELECT              1
#define ORACLE_OP_INSERT              2
#define ORACLE_OP_UPDATE              3
#define ORACLE_OP_DELETE              4
#define ORACLE_OP_CREATE              5
#define ORACLE_OP_ALTER               6
#define ORACLE_OP_DROP                7
#define ORACLE_OP_TRUNCATE            8
#define ORACLE_OP_COMMIT              9
#define ORACLE_OP_ROLLBACK            10
#define ORACLE_OP_MERGE               11
#define ORACLE_OP_CALL                12  // 存储过程调用
#define ORACLE_OP_EXPLAIN             13  // 执行计划
#define ORACLE_OP_LOCK                14  // 锁表
#define ORACLE_OP_LOGIN               15  // 登录
#define ORACLE_OP_LOGOUT              16  // 登出
#define ORACLE_OP_FETCH               17  // 获取数据
#define ORACLE_OP_CURSOR_OPEN         18  // 打开游标
#define ORACLE_OP_CURSOR_CLOSE        19  // 关闭游标
#define ORACLE_OP_SQL_PARSE           20  // SQL解析
#define ORACLE_OP_SQL_EXECUTE         21  // SQL执行
#define ORACLE_OP_DEFINE              22  // 定义变量
#define ORACLE_OP_BIND                23  // 绑定变量
#define ORACLE_OP_FETCH_LONG          24  // 获取长数据
#define ORACLE_OP_UNKNOWN             99  // 未知操作

// SQL类型枚举（优先级3新增）
typedef enum {
    SQL_TYPE_SELECT,            // SELECT查询
    SQL_TYPE_INSERT,            // INSERT插入
    SQL_TYPE_UPDATE,            // UPDATE更新
    SQL_TYPE_DELETE,            // DELETE删除
    SQL_TYPE_MERGE,             // MERGE合并
    SQL_TYPE_CREATE,            // CREATE创建
    SQL_TYPE_ALTER,             // ALTER修改
    SQL_TYPE_DROP,              // DROP删除
    SQL_TYPE_TRUNCATE,          // TRUNCATE截断
    SQL_TYPE_PLSQL_BLOCK,       // PL/SQL块
    SQL_TYPE_CALL_PROCEDURE,    // 存储过程调用
    SQL_TYPE_CALL_FUNCTION,     // 函数调用
    SQL_TYPE_COMMIT,            // 提交
    SQL_TYPE_ROLLBACK,          // 回滚
    SQL_TYPE_SAVEPOINT,         // 保存点
    SQL_TYPE_UNKNOWN            // 未知类型
} oracle_sql_type_t;

// 解析状态
#define ORACLE_PARSER_STATUS_CONTINUE   0
#define ORACLE_PARSER_STATUS_FINISH     1
#define ORACLE_PARSER_STATUS_DROP_DATA -1

// 通用解析器状态常量
#define PARSER_STATUS_CONTINUE      ORACLE_PARSER_STATUS_CONTINUE
#define PARSER_STATUS_FINISH        ORACLE_PARSER_STATUS_FINISH
#define PARSER_STATUS_DROP_DATA     ORACLE_PARSER_STATUS_DROP_DATA



// TNS数据包头部结构
typedef struct tns_header
{
    uint16_t length;        // 数据包长度（网络字节序）
    uint16_t checksum;      // 数据包校验和
    uint8_t  type;          // 数据包类型
    uint8_t  flags;         // 标志位
    uint16_t header_checksum; // 头部校验和
} __attribute__((packed)) tns_header_t;

// TNS大包头部结构（用于大于65535字节的包）
typedef struct tns_large_header
{
    uint32_t length;        // 数据包长度（4字节）
    uint8_t  type;          // 数据包类型
    uint8_t  flags;         // 标志位
    uint16_t header_checksum; // 头部校验和
} __attribute__((packed)) tns_large_header_t;

// TNS连接数据结构
typedef struct tns_connect_data
{
    uint16_t version;       // 协议版本
    uint16_t version_compatible; // 兼容版本
    uint16_t service_options;    // 服务选项
    uint16_t session_data_unit_size; // SDU大小
    uint16_t max_transmission_data_unit_size; // 最大传输单元大小
    uint16_t nt_protocol_characteristics; // 网络协议特性
    uint16_t line_turnaround; // 线路周转
    uint16_t value_of_one;    // 值为1的字段
    uint16_t connect_data_length; // 连接数据长度
    uint16_t connect_data_offset; // 连接数据偏移
    uint32_t connect_data_max_recv_size; // 最大接收大小
    uint8_t  connect_flags_0; // 连接标志0
    uint8_t  connect_flags_1; // 连接标志1
} __attribute__((packed)) tns_connect_data_t;

// Oracle连接状态
typedef enum __oracle_conn_status
{
    ORACLE_CONN_INIT,           // 初始状态
    ORACLE_CONN_TNS_CONNECT,    // TNS连接阶段
    ORACLE_CONN_TNS_ACCEPT,     // TNS接受阶段
    ORACLE_CONN_HANDSHAKE,      // 握手阶段
    ORACLE_CONN_PROTOCOL_NEG,   // 协议协商阶段
    ORACLE_CONN_DATA_TYPE_NEG,  // 数据类型协商阶段
    ORACLE_CONN_AUTH,           // 认证阶段
    ORACLE_CONN_AUTHENTICATED,  // 已认证
    ORACLE_CONN_EXECUTE,        // 执行阶段
    ORACLE_CONN_FETCH,          // 获取数据阶段
    ORACLE_CONN_COMMIT,         // 提交阶段
    ORACLE_CONN_ROLLBACK,       // 回滚阶段
    ORACLE_CONN_CLOSED,         // 连接关闭
    ORACLE_CONN_NETWORK_ERROR,  // 网络错误
    ORACLE_CONN_PROTOCOL_ERROR, // 协议错误
    ORACLE_CONN_AUTH_FAILED     // 认证失败
} oracle_conn_status;

// Oracle协议状态
typedef struct oracle_status
{
    oracle_conn_status conn_stat;

    // 连接信息
    b_string_t user;
    b_string_t password;
    b_string_t service_name;
    b_string_t instance_name;
    b_string_t client_program;
    b_string_t client_version;
    b_string_t server_version;

    // 网络信息
    char client_ip[64 + 1];
    int client_port;
    char server_ip[64 + 1];
    int server_port;

    // 会话信息
    uint32_t session_id;
    uint32_t serial_num;
    int is_authenticated;

    // TNS协议信息
    uint16_t tns_version;
    uint16_t tns_compatible_version;
    uint16_t sdu_size;
    uint16_t max_transmission_unit;
    uint8_t  tns_flags;

    // TTC协议信息
    uint8_t  ttc_version;
    uint8_t  charset_id;
    uint8_t  ncharset_id;
    uint32_t server_capabilities;
    uint32_t client_capabilities;

    // 当前事务状态
    int in_transaction;
    int transaction_active;         // 事务是否活跃（别名）
    uint32_t transaction_id;

    // 错误信息
    int last_error_code;
    char last_error_msg[512];

    // 网络统计信息
    uint32_t resend_requests;       // 重发请求次数
    uint64_t last_activity_time;    // 最后活动时间
    uint64_t bytes_sent;            // 发送字节数
    uint64_t bytes_received;        // 接收字节数
    uint32_t packets_sent;          // 发送包数
    uint32_t packets_received;      // 接收包数

    // DATA_DESCRIPTOR相关信息
    int data_descriptor_received;   // 是否接收到数据描述符
    uint32_t expected_data_length;  // 期望的数据长度
    uint16_t expected_fragments;    // 期望的分片数量
    uint32_t data_sequence_number;  // 数据序列号
} oracle_status_t;

// Oracle值类型结构
typedef struct oracle_value
{
    uint8_t  data_type;         // 数据类型
    uint32_t data_length;       // 数据长度
    char    *data;              // 数据指针
    bool     is_null;           // 是否为NULL
    uint32_t max_length;        // 最大长度
} oracle_value_t;

// Oracle绑定变量结构
typedef struct oracle_bind_variable
{
    uint16_t bind_index;            // 绑定索引
    uint8_t  data_type;             // Oracle数据类型
    uint16_t max_length;            // 最大长度
    uint16_t actual_length;         // 实际长度
    void     *data;                 // 数据指针
    bool     is_null;               // 是否为NULL
    uint8_t  precision;             // 精度（数字类型）
    uint8_t  scale;                 // 小数位数（数字类型）
    char     *name;                 // 绑定变量名（如果有）
} oracle_bind_variable_t;

// Oracle定义变量结构（用于SELECT语句的输出列）
typedef struct oracle_define_variable
{
    uint16_t define_index;          // 定义索引
    uint8_t  data_type;             // Oracle数据类型
    uint16_t max_length;            // 最大长度
    char     *column_name;          // 列名
    uint8_t  precision;             // 精度
    uint8_t  scale;                 // 小数位数
    bool     nullable;              // 是否可为空
    uint32_t flags;                 // 标志位
    uint16_t actual_length;         // 实际长度
    bool     is_null;               // 是否为NULL
} oracle_define_variable_t;

// Oracle解析数据 - 增强版
typedef struct oracle_parsed_data
{
    char id[48];                    // 事务ID
    const b_string_t *user;         // 用户名
    const b_string_t *service_name; // 服务名
    const b_string_t *instance_name; // 实例名
    b_string_t sql_text;            // SQL语句文本
    uint32_t sql_hash;              // SQL语句哈希值

    // 操作信息
    int op_type;                    // 操作类型（SELECT/INSERT/UPDATE等）
    int function_code;              // Oracle函数码（TTIFUN等）
    int success;                    // 执行成功标志
    int err_code;                   // 错误码
    char *err_msg;                  // 错误消息

    // 执行统计
    uint32_t rows_processed;        // 处理行数
    uint64_t execution_time;        // 执行时间（微秒）
    uint64_t parse_time;            // 解析时间
    uint64_t fetch_time;            // 获取时间

    // 网络信息
    const char *client_ip;          // 客户端IP
    int client_port;                // 客户端端口
    const char *server_ip;          // 服务器IP
    int server_port;                // 服务器端口
    uint64_t timestamp;             // 时间戳

    // 扩展信息
    const char *c_mac;              // 客户端MAC
    const char *s_mac;              // 服务器MAC
    uint64_t req_size;              // 请求大小
    uint64_t resp_size;             // 响应大小

    // 协议信息
    uint16_t tns_version;           // TNS协议版本
    uint8_t ttc_version;            // TTC协议版本
    uint32_t session_id;            // 会话ID
    uint32_t serial_num;            // 序列号

    // 结果集信息（用于SELECT操作）
    uint32_t column_count;          // 列数
    uint32_t row_count;             // 行数
    b_string_t *column_names;       // 列名数组
    uint8_t *column_types;          // 列类型数组

    // 事务信息
    int in_transaction;             // 是否在事务中
    uint32_t transaction_id;        // 事务ID
    char *savepoint_name;           // 保存点名称

    // 游标信息
    uint32_t cursor_id;             // 游标ID
    uint32_t cursor_options;        // 游标选项
    uint32_t fetch_rows;            // 获取行数
    uint16_t fetch_direction;       // 获取方向

    // 绑定变量信息
    oracle_bind_variable_t *bind_variables;  // 绑定变量数组
    uint16_t bind_count;            // 绑定变量数量

    // 定义变量信息
    oracle_define_variable_t *define_variables; // 定义变量数组
    uint16_t define_count;          // 定义变量数量

    // 优先级3新增字段
    uint8_t parse_options;          // SQL解析选项
    uint16_t column_index;          // 列索引（用于OFLNG）
    uint32_t data_offset;           // 数据偏移（用于OFLNG）
    uint32_t request_length;        // 请求长度（用于OFLNG）
    char *long_data;                // 长数据内容
    size_t long_data_length;        // 长数据长度
    oracle_sql_type_t sql_type;     // SQL类型

    // 性能信息
    uint64_t cpu_time;              // CPU时间
    uint64_t io_time;               // IO时间
    uint32_t logical_reads;         // 逻辑读次数
    uint32_t physical_reads;        // 物理读次数
} oracle_parsed_data_t;

// Oracle半流数据
typedef struct oracle_half_stream
{
    oracle_parsed_data_t data;
    struct oracle_half_stream *next;
} oracle_half_stream_t;

// Oracle流数据 - 增强版
typedef struct oracle_stream
{
    oracle_half_stream_t *p_oracle_server;      // 服务器端半流
    oracle_half_stream_t *p_oracle_client;      // 客户端半流

    oracle_half_stream_t *p_oracle_server_last; // 服务器端最后一个半流
    oracle_half_stream_t *p_oracle_client_last; // 客户端最后一个半流

    oracle_status_t ora_stat;                   // Oracle连接状态
    int is_oracle;                              // 是否为Oracle协议

    // 协议解析状态
    int tns_parse_state;                        // TNS解析状态
    int ttc_parse_state;                        // TTC解析状态
    int tti_parse_state;                        // TTI解析状态

    // 缓冲区管理
    char *partial_packet_buffer;                // 部分包缓冲区
    size_t partial_packet_size;                 // 部分包大小
    size_t partial_packet_capacity;             // 部分包容量

    // 消息堆叠状态
    int pending_messages;                       // 待处理消息数
    uint32_t expected_total_length;             // 期望的总长度
    uint32_t received_length;                   // 已接收长度

    // 性能统计
    uint64_t packets_processed;                 // 已处理包数
    uint64_t bytes_processed;                   // 已处理字节数
    uint64_t parse_errors;                      // 解析错误数
} oracle_stream_t;

// Oracle协议解析器配置
typedef struct oracle_parser_config
{
    // 功能开关
    int enable_sql_extraction;                 // 启用SQL提取
    int enable_result_set_parsing;             // 启用结果集解析
    int enable_lob_parsing;                    // 启用LOB解析
    int enable_auth_parsing;                   // 启用认证解析
    int enable_performance_stats;              // 启用性能统计

    // 解析限制
    size_t max_sql_length;                     // 最大SQL长度
    size_t max_result_set_size;                // 最大结果集大小
    size_t max_column_count;                   // 最大列数
    size_t max_row_count;                      // 最大行数

    // 内存管理
    size_t buffer_pool_size;                   // 缓冲池大小
    size_t max_memory_usage;                   // 最大内存使用

    // 调试选项
    int debug_level;                           // 调试级别
    int dump_packets;                          // 转储数据包
    int log_sql_statements;                    // 记录SQL语句
} oracle_parser_config_t;

// Oracle协议解析器统计信息 - 增强版
typedef struct oracle_parser_stats
{
    // 基础统计
    volatile uint64_t total_packets;           // 总包数
    volatile uint64_t total_bytes;             // 总字节数
    volatile uint64_t parse_errors;            // 解析错误数
    volatile uint64_t memory_usage;            // 内存使用量

    // TNS层统计
    volatile uint64_t tns_connect_packets;     // TNS连接包数
    volatile uint64_t tns_data_packets;        // TNS数据包数
    volatile uint64_t tns_control_packets;     // TNS控制包数
    volatile uint64_t tns_parse_errors;        // TNS解析错误数

    // TTC层统计
    volatile uint64_t ttc_messages;            // TTC消息数
    volatile uint64_t ttc_stacked_messages;    // TTC堆叠消息数
    volatile uint64_t ttc_parse_errors;        // TTC解析错误数

    // TTI层统计
    volatile uint64_t tti_function_calls;      // TTI函数调用数
    volatile uint64_t tti_result_sets;         // TTI结果集数
    volatile uint64_t tti_parse_errors;        // TTI解析错误数

    // SQL操作统计
    volatile uint64_t sql_select_count;        // SELECT操作数
    volatile uint64_t sql_insert_count;        // INSERT操作数
    volatile uint64_t sql_update_count;        // UPDATE操作数
    volatile uint64_t sql_delete_count;        // DELETE操作数
    volatile uint64_t sql_ddl_count;           // DDL操作数
    volatile uint64_t sql_commit_count;        // COMMIT操作数
    volatile uint64_t sql_rollback_count;      // ROLLBACK操作数

    // 性能统计
    volatile uint64_t avg_parse_time;          // 平均解析时间
    volatile uint64_t max_parse_time;          // 最大解析时间
    volatile uint64_t total_parse_time;        // 总解析时间
} oracle_parser_stats_t;

#endif /* __ORACLE_PARSER_COMMON_H__ */