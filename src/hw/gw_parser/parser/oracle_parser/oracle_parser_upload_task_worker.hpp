/*
 * Oracle协议解析器上传任务工作线程头文件
 * <AUTHOR> @date 2025
 */

#ifndef __ORACLE_PARSER_UPLOAD_TASK_WORKER_HPP__
#define __ORACLE_PARSER_UPLOAD_TASK_WORKER_HPP__

#include "task_worker.h"

class COracleParser;

class CTaskWorkerUploadMsg: public CTaskWorker 
{
public:
    virtual int deal_data(const TaskWorkerData *);
    virtual void free_data(const TaskWorkerData *);
    
    inline void set_wq(CWorkerQueue *pwq)
    {
        m_pwq = pwq;
    }
    
    CWorkerQueue *get_wq() const
    {
        return m_pwq;
    }

    inline void set_parser(COracleParser *parser)
    {
        m_parser = parser;
    }
    
    void init(){}
    void fini(){}
    void release() const {}
    
protected:
    inline COracleParser *get_parser(void)
    {
        return m_parser;
    }

protected:
    CWorkerQueue *m_pwq;
    COracleParser *m_parser;
};

#endif // __ORACLE_PARSER_UPLOAD_TASK_WORKER_HPP__
