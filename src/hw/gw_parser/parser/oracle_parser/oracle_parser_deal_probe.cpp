/*
 * Oracle协议探测处理
 * <AUTHOR> @date 2025
 */

#include "oracle_parser.h"
#include "oracle_parser_common.h"
#include "gw_i_parser.h"
#include "gw_i_source.h"
#include "gw_logger.h"
#include "session.h"
#include "session_mgt.h"
#include "utils.h"

#include <string.h>

// 详细的Oracle协议探测逻辑
bool COracleParser::is_oracle_protocol(CSession *p_sess, int dir)
{
    if (!p_sess) {
        return false;
    }
    
    StreamData *p_stream_data = get_stream_data_from_session(p_sess, dir);
    if (!p_stream_data || !p_stream_data->p_oracle_stream) {
        return false;
    }
    
    oracle_stream_t *p_ora_stream = p_stream_data->p_oracle_stream;
    return p_ora_stream->is_oracle;
}

// 设置IP端口信息
void COracleParser::set_ip_port(const struct conn *p_conn, oracle_status_t *p_status)
{
    if (!p_conn || !p_status) {
        return;
    }
    
    // 设置客户端IP和端口
    if (p_conn->client.v != 6) {
        // ipv4
        strcpy(p_status->client_ip, inet_ntoa(*(struct in_addr*)&p_conn->client.ipv4));
    } else {
        // ipv6
        get_ip6addr_str((uint32_t *)p_conn->client.ipv6, p_status->client_ip, sizeof(p_status->client_ip) - 1);
    }
    p_status->client_port = p_conn->client.port;
    
    // 设置服务器IP和端口
    if (p_conn->server.v != 6) {
        // ipv4
        strcpy(p_status->server_ip, inet_ntoa(*(struct in_addr*)&p_conn->server.ipv4));
    } else {
        // ipv6
        get_ip6addr_str((uint32_t *)p_conn->server.ipv6, p_status->server_ip, sizeof(p_status->server_ip) - 1);
    }
    p_status->server_port = p_conn->server.port;
}

// 从会话获取流数据
StreamData *COracleParser::get_stream_data_from_session(CSession *p_session, int dir)
{
    if (!p_session) {
        return NULL;
    }
    
    StreamData *p_stream_data = p_session->get_stream_data_from_parser(this);
    if (!p_stream_data) {
        // 创建 StreamData
        p_stream_data = new StreamData();
        if (!p_session->set_parser(this, p_stream_data)) {
            delete p_stream_data;
            return NULL;
        }
        
        p_stream_data->p_oracle_stream = new oracle_stream_t();
        
        SessionMgtData *p_sess_mgt_data = p_session->get_session_mgt()->get_session_data_from_parser(this);
        if (p_sess_mgt_data == NULL) {
            p_sess_mgt_data = new SessionMgtData;
            p_session->get_session_mgt()->set_parser_data(this, p_sess_mgt_data);
        }
    }
    
    return p_stream_data;
}

// 保持字符串数据
void COracleParser::keep_bstring_data(b_string_t *bstr)
{
    if (!bstr || !bstr->s || bstr->len == 0) {
        return;
    }
    
    // 分配新内存并复制数据
    char *new_s = (char *)malloc(bstr->len);
    if (new_s) {
        memcpy(new_s, bstr->s, bstr->len);
        bstr->s = new_s;
    }
}

// 检查并显示Oracle信息
void COracleParser::oracle_check_show_info(CSession *p_session, const struct conn *pcon)
{
    if (!p_session || !pcon) {
        return;
    }
    
    StreamData *p_stream_data = get_stream_data_from_session(p_session, STREAM_REQ);
    if (!p_stream_data || !p_stream_data->p_oracle_stream) {
        return;
    }
    
    oracle_stream_t *p_ora_stream = p_stream_data->p_oracle_stream;
    
    GWLOG_INFO(m_comm, "%s Oracle connection: %s:%d -> %s:%d, state=%d", 
           ORACLE_LOG_PRE,
           p_ora_stream->ora_stat.client_ip, p_ora_stream->ora_stat.client_port,
           p_ora_stream->ora_stat.server_ip, p_ora_stream->ora_stat.server_port,
           p_ora_stream->ora_stat.conn_stat);
}

// 打印统计信息
void COracleParser::print_oracle_stats(void)
{
    GWLOG_INFO(m_comm, "%s Statistics:", ORACLE_LOG_PRE);
    GWLOG_INFO(m_comm, "%s   Total sessions: %lu", ORACLE_LOG_PRE, m_stats_oracle.cnt_session_total);
    GWLOG_INFO(m_comm, "%s   Closed sessions: %lu", ORACLE_LOG_PRE, m_stats_oracle.cnt_session_closed);
    GWLOG_INFO(m_comm, "%s   Total parsed: %lu", ORACLE_LOG_PRE, m_stats_oracle.cnt_parser_total);
    GWLOG_INFO(m_comm, "%s   Remaining parsed: %lu", ORACLE_LOG_PRE, m_stats_oracle.cnt_parser_remaining);
    GWLOG_INFO(m_comm, "%s   Matched parsed: %lu", ORACLE_LOG_PRE, m_stats_oracle.cnt_parser_matched);
    GWLOG_INFO(m_comm, "%s   Login operations: %lu", ORACLE_LOG_PRE, m_stats_oracle.cnt_login);
    GWLOG_INFO(m_comm, "%s   Logout operations: %lu", ORACLE_LOG_PRE, m_stats_oracle.cnt_logout);
    GWLOG_INFO(m_comm, "%s   SELECT operations: %lu", ORACLE_LOG_PRE, m_stats_oracle.cnt_select);
    GWLOG_INFO(m_comm, "%s   DML operations: %lu", ORACLE_LOG_PRE, m_stats_oracle.cnt_dml);
    GWLOG_INFO(m_comm, "%s   DDL operations: %lu", ORACLE_LOG_PRE, m_stats_oracle.cnt_ddl);
    GWLOG_INFO(m_comm, "%s   COMMIT operations: %lu", ORACLE_LOG_PRE, m_stats_oracle.cnt_commit);
    GWLOG_INFO(m_comm, "%s   ROLLBACK operations: %lu", ORACLE_LOG_PRE, m_stats_oracle.cnt_rollback);
}

// 统计信息回调
void COracleParser::print_oracle_stats_callback(void *p)
{
    COracleParser *p_parser = (COracleParser *)p;
    if (p_parser) {
        p_parser->print_oracle_stats();
    }
}

// 内存打印工具（调试用）
void COracleParser::print_mem(const void *mem, size_t size)
{
    if (!mem || size == 0) {
        return;
    }
    
    const unsigned char *p = (const unsigned char *)mem;
    char buf[128];
    char *ptr = buf;
    
    for (size_t i = 0; i < size && i < 16; i++) {
        ptr += sprintf(ptr, "%02x ", p[i]);
    }
    
    GWLOG_DEBUG(m_comm, "%s Memory dump: %s", ORACLE_LOG_PRE, buf);
}