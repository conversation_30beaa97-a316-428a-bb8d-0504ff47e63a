/*
 * Oracle TTC协议解析器头文件
 * 基于ojdbc源码分析实现的TTC协议解析功能
 * <AUTHOR> @date 2025
 */

#ifndef __ORACLE_TTC_PARSER_H__
#define __ORACLE_TTC_PARSER_H__

#include <inttypes.h>
#include <vector>
#include <map>
#include "oracle_parser_common.h"

// TTC解析结果状态
#define TTC_PARSE_SUCCESS           0
#define TTC_PARSE_NEED_MORE_DATA    1
#define TTC_PARSE_ERROR            -1
#define TTC_PARSE_INVALID_MESSAGE  -2
#define TTC_PARSE_INVALID_DATA     -3
#define TTC_PARSE_UNSUPPORTED      -4

// TTC消息头部结构
typedef struct ttc_message_header
{
    uint8_t  message_type;      // 消息类型
    uint8_t  flags;             // 标志位
    uint16_t message_length;    // 消息长度
    uint32_t sequence_number;   // 序列号
} __attribute__((packed)) ttc_message_header_t;

// TTC协议协商数据
typedef struct ttc_protocol_negotiation
{
    uint8_t  ttc_version;       // TTC版本
    uint8_t  charset_id;        // 字符集ID
    uint8_t  ncharset_id;       // 国家字符集ID
    uint32_t server_capabilities; // 服务器能力
    uint32_t client_capabilities; // 客户端能力
    uint16_t compile_time_caps;   // 编译时能力
    uint16_t runtime_caps;        // 运行时能力
} __attribute__((packed)) ttc_protocol_negotiation_t;

// TTC数据类型协商数据
typedef struct ttc_data_type_negotiation
{
    uint8_t  data_type_count;   // 数据类型数量
    uint8_t  *data_types;       // 数据类型列表
    uint16_t *type_lengths;     // 类型长度列表
} ttc_data_type_negotiation_t;

// TTC会话信息
typedef struct ttc_session_info
{
    uint32_t session_id;        // 会话ID
    uint32_t serial_number;     // 序列号
    uint16_t server_version;    // 服务器版本
    uint16_t server_release;    // 服务器发布版本
    char     server_banner[256]; // 服务器横幅
    uint32_t process_id;        // 进程ID
} ttc_session_info_t;

// TTC解析器类
class OracleTtcParser
{
public:
    OracleTtcParser();
    ~OracleTtcParser();

    // TTC消息解析主入口
    int parse_ttc_message(const char *data, size_t data_len, int direction, 
                         oracle_status_t *status, oracle_parsed_data_t *result);

    // TTC消息头部解析
    int parse_ttc_header(const char *data, size_t data_len, ttc_message_header_t *header);
    
    // 各种TTC消息类型的解析方法 - 基于完整的TTC消息类型
    int parse_protocol_negotiation(const char *data, size_t data_len, oracle_status_t *status);
    int parse_data_type_negotiation(const char *data, size_t data_len, oracle_status_t *status);
    int parse_function_call(const char *data, size_t data_len, oracle_status_t *status, oracle_parsed_data_t *result);
    int parse_error_message(const char *data, size_t data_len, oracle_status_t *status, oracle_parsed_data_t *result);
    int parse_resultset_header(const char *data, size_t data_len, oracle_status_t *status, oracle_parsed_data_t *result);
    int parse_resultset_data(const char *data, size_t data_len, oracle_status_t *status, oracle_parsed_data_t *result);
    int parse_return_parameters(const char *data, size_t data_len, oracle_status_t *status, oracle_parsed_data_t *result);
    int parse_status_info(const char *data, size_t data_len, oracle_status_t *status, oracle_parsed_data_t *result);
    int parse_io_vector(const char *data, size_t data_len, oracle_status_t *status, oracle_parsed_data_t *result);
    int parse_session_log(const char *data, size_t data_len, oracle_status_t *status, oracle_parsed_data_t *result);
    int parse_output_parameters(const char *data, size_t data_len, oracle_status_t *status, oracle_parsed_data_t *result);
    int parse_lob_data(const char *data, size_t data_len, oracle_status_t *status, oracle_parsed_data_t *result);
    int parse_warning_message(const char *data, size_t data_len, oracle_status_t *status, oracle_parsed_data_t *result);
    int parse_database_callback(const char *data, size_t data_len, oracle_status_t *status, oracle_parsed_data_t *result);
    int parse_prefetch_function(const char *data, size_t data_len, oracle_status_t *status, oracle_parsed_data_t *result);
    int parse_function_object(const char *data, size_t data_len, oracle_status_t *status, oracle_parsed_data_t *result);
    int parse_bulk_variables(const char *data, size_t data_len, oracle_status_t *status, oracle_parsed_data_t *result);
    int parse_special_function(const char *data, size_t data_len, oracle_status_t *status, oracle_parsed_data_t *result);
    int parse_query_cache(const char *data, size_t data_len, oracle_status_t *status, oracle_parsed_data_t *result);
    int parse_resultset_handle(const char *data, size_t data_len, oracle_status_t *status, oracle_parsed_data_t *result);
    int parse_oneway_function(const char *data, size_t data_len, oracle_status_t *status, oracle_parsed_data_t *result);
    int parse_implicit_result(const char *data, size_t data_len, oracle_status_t *status, oracle_parsed_data_t *result);
    int parse_renegotiation(const char *data, size_t data_len, oracle_status_t *status, oracle_parsed_data_t *result);
    int parse_key_value(const char *data, size_t data_len, oracle_status_t *status, oracle_parsed_data_t *result);
    int parse_cookie(const char *data, size_t data_len, oracle_status_t *status, oracle_parsed_data_t *result);
    int parse_token(const char *data, size_t data_len, oracle_status_t *status, oracle_parsed_data_t *result);
    int parse_initialization(const char *data, size_t data_len, oracle_status_t *status, oracle_parsed_data_t *result);

    // TTC会话管理
    int initialize_session(oracle_status_t *status);
    int update_session_info(const ttc_session_info_t *session_info, oracle_status_t *status);
    int cleanup_session(oracle_status_t *status);

    // TTC事务管理
    int begin_transaction(oracle_status_t *status);
    int commit_transaction(oracle_status_t *status);
    int rollback_transaction(oracle_status_t *status);
    bool is_in_transaction(const oracle_status_t *status);

    // TTC协议协商
    int negotiate_protocol(const ttc_protocol_negotiation_t *client_caps, 
                          const ttc_protocol_negotiation_t *server_caps, 
                          oracle_status_t *status);
    int negotiate_data_types(const ttc_data_type_negotiation_t *negotiation, oracle_status_t *status);

    // TTC消息验证
    bool validate_ttc_message(const char *data, size_t data_len);
    bool validate_message_sequence(uint32_t sequence_number, oracle_status_t *status);

    // TTC错误处理
    int handle_ttc_error(uint8_t error_code, const char *error_msg, oracle_status_t *status);
    const char* get_ttc_message_type_name(uint8_t message_type);
    const char* get_ttc_error_message(int error_code);

    // TTC游标管理
    int open_cursor(uint32_t cursor_id, oracle_status_t *status);
    int close_cursor(uint32_t cursor_id, oracle_status_t *status);
    bool is_cursor_open(uint32_t cursor_id, const oracle_status_t *status);

    // TTC函数调用解析方法
    int parse_open_cursor_function(const char *data, size_t data_len, oracle_status_t *status, oracle_parsed_data_t *result);
    int parse_fetch_function(const char *data, size_t data_len, oracle_status_t *status, oracle_parsed_data_t *result);
    int parse_close_cursor_function(const char *data, size_t data_len, oracle_status_t *status, oracle_parsed_data_t *result);
    int parse_logoff_function(const char *data, size_t data_len, oracle_status_t *status, oracle_parsed_data_t *result);
    int parse_commit_function(const char *data, size_t data_len, oracle_status_t *status, oracle_parsed_data_t *result);
    int parse_rollback_function(const char *data, size_t data_len, oracle_status_t *status, oracle_parsed_data_t *result);
    int parse_cancel_function(const char *data, size_t data_len, oracle_status_t *status, oracle_parsed_data_t *result);
    int parse_all7_function(const char *data, size_t data_len, oracle_status_t *status, oracle_parsed_data_t *result);
    int parse_sql7_function(const char *data, size_t data_len, oracle_status_t *status, oracle_parsed_data_t *result);
    int parse_3logon_function(const char *data, size_t data_len, oracle_status_t *status, oracle_parsed_data_t *result);
    int parse_all8_function(const char *data, size_t data_len, oracle_status_t *status, oracle_parsed_data_t *result);
    int parse_lob_operations_function(const char *data, size_t data_len, oracle_status_t *status, oracle_parsed_data_t *result);
    int parse_auth_function(const char *data, size_t data_len, oracle_status_t *status, oracle_parsed_data_t *result);

    // 调试和日志
    void dump_ttc_header(const ttc_message_header_t *header);
    void dump_ttc_message(const char *data, size_t data_len);
    void enable_debug(bool enable) { m_debug_enabled = enable; }

private:
    // 内部工具方法
    uint8_t read_uint8(const char *data);
    uint16_t read_uint16(const char *data);
    uint32_t read_uint32(const char *data);
    uint64_t read_uint64(const char *data);
    uint16_t read_uint16_be(const char *data);  // 大端序读取
    uint32_t read_uint32_be(const char *data);  // 大端序读取
    
    // TTC数据读取
    int read_ttc_string(const char *data, size_t data_len, size_t *offset, b_string_t *str);
    int read_ttc_number(const char *data, size_t data_len, size_t *offset, uint64_t *number);
    int read_ttc_date(const char *data, size_t data_len, size_t *offset, uint64_t *date);
    
    // TTC消息构建
    int build_logon_response(const oracle_status_t *status, char *buffer, size_t buffer_size);
    int build_error_response(int error_code, const char *error_msg, char *buffer, size_t buffer_size);
    
    // TTC能力协商
    uint32_t merge_capabilities(uint32_t client_caps, uint32_t server_caps);
    bool is_capability_supported(uint32_t capabilities, uint32_t capability_flag);
    
    // 内部状态
    bool m_debug_enabled;
    uint32_t m_next_sequence_number;
    std::map<uint32_t, bool> m_open_cursors;
    size_t m_bytes_processed;
};

// TTC协议工具函数
namespace OracleTtcUtils
{
    // TTC消息类型判断
    bool is_ttc_request_message(uint8_t message_type);
    bool is_ttc_response_message(uint8_t message_type);
    bool is_ttc_control_message(uint8_t message_type);
    
    // TTC版本处理
    bool is_supported_ttc_version(uint8_t version);
    uint8_t get_compatible_ttc_version(uint8_t client_version, uint8_t server_version);
    
    // TTC能力标志
    const uint32_t TTC_CAP_FAST_AUTH        = 0x00000001;
    const uint32_t TTC_CAP_LARGE_ROWSETS    = 0x00000002;
    const uint32_t TTC_CAP_ARRAY_OPERATIONS = 0x00000004;
    const uint32_t TTC_CAP_LOB_SUPPORT      = 0x00000008;
    const uint32_t TTC_CAP_UNICODE_SUPPORT  = 0x00000010;
    const uint32_t TTC_CAP_COMPRESSION      = 0x00000020;
    const uint32_t TTC_CAP_ENCRYPTION       = 0x00000040;
    
    // TTC字符集映射
    const char* map_charset_id_to_name(uint8_t charset_id);
    uint8_t map_charset_name_to_id(const char *charset_name);
    
    // TTC错误码映射
    const char* map_ttc_error_to_string(uint8_t error_code);
    int map_ttc_error_to_oracle_error(uint8_t ttc_error);
}

#endif /* __ORACLE_TTC_PARSER_H__ */
