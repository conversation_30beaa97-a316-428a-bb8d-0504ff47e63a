/*
 * Oracle协议版本兼容性处理模块实现
 * 基于ojdbc源码分析实现的多版本Oracle协议兼容性处理
 * <AUTHOR> @date 2025
 */

#include "oracle_version_compat.h"
#include "gw_logger.h"
#include <string.h>
#include <stdlib.h>
#include <stdio.h>
#include <ctype.h>

// 简化的日志宏定义
#define VER_LOG_DEBUG(fmt, ...) printf("[VER-DEBUG] " fmt "\n", ##__VA_ARGS__)
#define VER_LOG_INFO(fmt, ...)  printf("[VER-INFO] " fmt "\n", ##__VA_ARGS__)
#define VER_LOG_WARN(fmt, ...)  printf("[VER-WARN] " fmt "\n", ##__VA_ARGS__)
#define VER_LOG_ERROR(fmt, ...) printf("[VER-ERROR] " fmt "\n", ##__VA_ARGS__)

OracleVersionCompat::OracleVersionCompat()
    : m_debug_enabled(false)
{
    // 初始化默认支持的版本范围
    m_min_supported_version.major_version = 8;
    m_min_supported_version.minor_version = 1;
    m_min_supported_version.patch_version = 0;
    m_min_supported_version.build_number = 0;
    m_min_supported_version.version_number = OracleVersionConstants::MIN_SUPPORTED_VERSION;

    m_max_supported_version.major_version = 23;
    m_max_supported_version.minor_version = 0;
    m_max_supported_version.patch_version = 0;
    m_max_supported_version.build_number = 0;
    m_max_supported_version.version_number = OracleVersionConstants::MAX_SUPPORTED_VERSION;

    // 初始化默认能力
    memset(&m_default_capabilities, 0, sizeof(protocol_capabilities_t));
    m_default_capabilities.supports_large_sdu = true;
    m_default_capabilities.supports_compression = false;
    m_default_capabilities.supports_encryption = false;
    m_default_capabilities.supports_checksums = true;
    m_default_capabilities.max_sdu_size = 2048;
    m_default_capabilities.supports_fast_auth = true;
    m_default_capabilities.supports_array_operations = true;
    m_default_capabilities.supports_lob_operations = true;
    m_default_capabilities.supports_unicode = true;
    m_default_capabilities.max_charset_id = 255;
    m_default_capabilities.max_open_cursors = 1000;

    // 初始化版本特定的能力映射
    initialize_version_capabilities();
}

OracleVersionCompat::~OracleVersionCompat()
{
    m_version_capabilities.clear();
    m_version_differences.clear();
    m_version_handlers.clear();
}

int OracleVersionCompat::parse_version_string(const char *version_str, oracle_version_info_t *version_info)
{
    if (!version_str || !version_info) {
        return VERSION_COMPAT_ERROR;
    }

    memset(version_info, 0, sizeof(oracle_version_info_t));
    strncpy(version_info->version_string, version_str, sizeof(version_info->version_string) - 1);

    // 解析版本字符串，例如："12.2.0.1.0"
    uint16_t major = 0, minor = 0, patch = 0, build = 0;
    int ret = extract_version_components(version_str, &major, &minor, &patch, &build);
    if (ret != VERSION_COMPAT_SUCCESS) {
        return ret;
    }

    version_info->major_version = major;
    version_info->minor_version = minor;
    version_info->patch_version = patch;
    version_info->build_number = build;
    version_info->version_number = encode_version_number(major, minor, patch, build);

    return VERSION_COMPAT_SUCCESS;
}

int OracleVersionCompat::parse_version_number(uint32_t version_num, oracle_version_info_t *version_info)
{
    if (!version_info) {
        return VERSION_COMPAT_ERROR;
    }

    memset(version_info, 0, sizeof(oracle_version_info_t));
    version_info->version_number = version_num;

    uint16_t major, minor, patch, build;
    int ret = decode_version_number(version_num, &major, &minor, &patch, &build);
    if (ret != VERSION_COMPAT_SUCCESS) {
        return ret;
    }

    version_info->major_version = major;
    version_info->minor_version = minor;
    version_info->patch_version = patch;
    version_info->build_number = build;

    snprintf(version_info->version_string, sizeof(version_info->version_string),
             "%d.%d.%d.%d", major, minor, patch, build);

    return VERSION_COMPAT_SUCCESS;
}

int OracleVersionCompat::compare_versions(const oracle_version_info_t *version1, const oracle_version_info_t *version2)
{
    if (!version1 || !version2) {
        return 0;
    }

    if (version1->version_number < version2->version_number) {
        return -1;
    } else if (version1->version_number > version2->version_number) {
        return 1;
    } else {
        return 0;
    }
}

bool OracleVersionCompat::is_version_supported(const oracle_version_info_t *version_info)
{
    if (!version_info) {
        return false;
    }

    return (version_info->version_number >= m_min_supported_version.version_number &&
            version_info->version_number <= m_max_supported_version.version_number);
}

int OracleVersionCompat::negotiate_protocol_capabilities(const oracle_version_info_t *client_version,
                                                        const oracle_version_info_t *server_version,
                                                        protocol_capabilities_t *negotiated_caps)
{
    if (!client_version || !server_version || !negotiated_caps) {
        return VERSION_COMPAT_ERROR;
    }

    // 获取客户端和服务器的能力
    protocol_capabilities_t client_caps, server_caps;
    int ret = get_version_capabilities(client_version, &client_caps);
    if (ret != VERSION_COMPAT_SUCCESS) {
        return ret;
    }

    ret = get_version_capabilities(server_version, &server_caps);
    if (ret != VERSION_COMPAT_SUCCESS) {
        return ret;
    }

    // 合并能力
    ret = merge_capabilities(&client_caps, &server_caps, negotiated_caps);
    if (ret != VERSION_COMPAT_SUCCESS) {
        return ret;
    }

    if (m_debug_enabled) {
        VER_LOG_DEBUG("[Oracle Version] Capabilities negotiated between client %s and server %s",
                    client_version->version_string, server_version->version_string);
        dump_capabilities(negotiated_caps);
    }

    return VERSION_COMPAT_SUCCESS;
}

int OracleVersionCompat::get_version_capabilities(const oracle_version_info_t *version_info, 
                                                 protocol_capabilities_t *capabilities)
{
    if (!version_info || !capabilities) {
        return VERSION_COMPAT_ERROR;
    }

    // 查找版本特定的能力
    auto it = m_version_capabilities.find(version_info->version_number);
    if (it != m_version_capabilities.end()) {
        *capabilities = it->second;
        return VERSION_COMPAT_SUCCESS;
    }

    // 根据主版本号设置默认能力
    *capabilities = m_default_capabilities;

    switch (version_info->major_version) {
        case OracleVersionConstants::ORACLE_8i_MAJOR:
            capabilities->supports_large_sdu = false;
            capabilities->supports_unicode = false;
            capabilities->supports_timestamp = false;
            capabilities->supports_interval = false;
            capabilities->max_sdu_size = 1024;
            break;

        case OracleVersionConstants::ORACLE_9i_MAJOR:
            capabilities->supports_timestamp = true;
            capabilities->supports_interval = true;
            capabilities->supports_xmltype = false;
            break;

        case OracleVersionConstants::ORACLE_10g_MAJOR:
            capabilities->supports_binary_float = true;
            capabilities->supports_binary_double = true;
            capabilities->supports_xmltype = true;
            break;

        case OracleVersionConstants::ORACLE_11g_MAJOR:
        case OracleVersionConstants::ORACLE_12c_MAJOR:
            capabilities->supports_compression = true;
            capabilities->supports_encryption = true;
            capabilities->max_sdu_size = 8192;
            break;

        case OracleVersionConstants::ORACLE_18c_MAJOR:
        case OracleVersionConstants::ORACLE_19c_MAJOR:
        case OracleVersionConstants::ORACLE_21c_MAJOR:
        case OracleVersionConstants::ORACLE_23c_MAJOR:
            capabilities->supports_scrollable_cursors = true;
            capabilities->max_sdu_size = 65535;
            capabilities->max_open_cursors = 10000;
            break;

        default:
            // 使用默认能力
            break;
    }

    return VERSION_COMPAT_SUCCESS;
}

int OracleVersionCompat::merge_capabilities(const protocol_capabilities_t *client_caps,
                                           const protocol_capabilities_t *server_caps,
                                           protocol_capabilities_t *merged_caps)
{
    if (!client_caps || !server_caps || !merged_caps) {
        return VERSION_COMPAT_ERROR;
    }

    // 合并能力：选择两者都支持的功能
    merged_caps->supports_large_sdu = client_caps->supports_large_sdu && server_caps->supports_large_sdu;
    merged_caps->supports_compression = client_caps->supports_compression && server_caps->supports_compression;
    merged_caps->supports_encryption = client_caps->supports_encryption && server_caps->supports_encryption;
    merged_caps->supports_checksums = client_caps->supports_checksums && server_caps->supports_checksums;
    
    // 选择较小的SDU大小
    merged_caps->max_sdu_size = (client_caps->max_sdu_size < server_caps->max_sdu_size) ? 
                               client_caps->max_sdu_size : server_caps->max_sdu_size;

    // TTC能力合并
    merged_caps->supports_fast_auth = client_caps->supports_fast_auth && server_caps->supports_fast_auth;
    merged_caps->supports_array_operations = client_caps->supports_array_operations && server_caps->supports_array_operations;
    merged_caps->supports_lob_operations = client_caps->supports_lob_operations && server_caps->supports_lob_operations;
    merged_caps->supports_unicode = client_caps->supports_unicode && server_caps->supports_unicode;
    
    // 选择较小的字符集ID
    merged_caps->max_charset_id = (client_caps->max_charset_id < server_caps->max_charset_id) ?
                                 client_caps->max_charset_id : server_caps->max_charset_id;

    // TTI能力合并
    merged_caps->supports_describe_select = client_caps->supports_describe_select && server_caps->supports_describe_select;
    merged_caps->supports_parse_execute = client_caps->supports_parse_execute && server_caps->supports_parse_execute;
    merged_caps->supports_fetch_across_commit = client_caps->supports_fetch_across_commit && server_caps->supports_fetch_across_commit;
    merged_caps->supports_scrollable_cursors = client_caps->supports_scrollable_cursors && server_caps->supports_scrollable_cursors;
    
    // 选择较小的游标数
    merged_caps->max_open_cursors = (client_caps->max_open_cursors < server_caps->max_open_cursors) ?
                                   client_caps->max_open_cursors : server_caps->max_open_cursors;

    // 数据类型能力合并
    merged_caps->supports_timestamp = client_caps->supports_timestamp && server_caps->supports_timestamp;
    merged_caps->supports_interval = client_caps->supports_interval && server_caps->supports_interval;
    merged_caps->supports_nchar = client_caps->supports_nchar && server_caps->supports_nchar;
    merged_caps->supports_binary_float = client_caps->supports_binary_float && server_caps->supports_binary_float;
    merged_caps->supports_binary_double = client_caps->supports_binary_double && server_caps->supports_binary_double;
    merged_caps->supports_xmltype = client_caps->supports_xmltype && server_caps->supports_xmltype;

    return VERSION_COMPAT_SUCCESS;
}

bool OracleVersionCompat::is_function_supported(const oracle_version_info_t *version_info, uint32_t function_code)
{
    if (!version_info) {
        return false;
    }

    // 根据版本检查功能支持
    switch (function_code) {
        case TTC_FUNCTION_PROTOCOL_NEGOTIATION:
        case TTC_FUNCTION_DATA_TYPES:
        case TTC_FUNCTION_LOGON:
        case TTC_FUNCTION_LOGOFF:
        case TTC_FUNCTION_OPEN:
        case TTC_FUNCTION_PARSE:
        case TTC_FUNCTION_EXECUTE:
        case TTC_FUNCTION_FETCH:
        case TTC_FUNCTION_CLOSE:
        case TTC_FUNCTION_COMMIT:
        case TTC_FUNCTION_ROLLBACK:
            // 基本功能，所有版本都支持
            return true;

        case TTC_FUNCTION_DESCRIBE:
            // DESCRIBE功能从9i开始支持
            return version_info->major_version >= OracleVersionConstants::ORACLE_9i_MAJOR;

        case TTC_FUNCTION_CANCEL:
            // CANCEL功能从10g开始支持
            return version_info->major_version >= OracleVersionConstants::ORACLE_10g_MAJOR;

        case TTC_FUNCTION_PING:
            // PING功能从11g开始支持
            return version_info->major_version >= OracleVersionConstants::ORACLE_11g_MAJOR;

        default:
            return false;
    }
}

bool OracleVersionCompat::is_data_type_supported(const oracle_version_info_t *version_info, uint8_t data_type)
{
    if (!version_info) {
        return false;
    }

    switch (data_type) {
        case ORACLE_SQLT_CHR:
        case ORACLE_SQLT_NUM:
        case ORACLE_SQLT_INT:
        case ORACLE_SQLT_FLT:
        case ORACLE_SQLT_STR:
        case ORACLE_SQLT_VNU:
        case ORACLE_SQLT_LNG:
        case ORACLE_SQLT_VCS:
        case ORACLE_SQLT_DAT:
        case ORACLE_SQLT_BIN:
        case ORACLE_SQLT_LBI:
        case ORACLE_SQLT_AFC:
        case ORACLE_SQLT_AVC:
        case ORACLE_SQLT_RID:
            // 基本数据类型，所有版本都支持
            return true;

        case ORACLE_SQLT_CLOB:
        case ORACLE_SQLT_BLOB:
        case ORACLE_SQLT_BFILEE:
            // LOB类型从8i开始支持
            return version_info->major_version >= OracleVersionConstants::ORACLE_8i_MAJOR;

        case ORACLE_SQLT_CUR:
        case ORACLE_SQLT_RSET:
            // 游标类型从9i开始支持
            return version_info->major_version >= OracleVersionConstants::ORACLE_9i_MAJOR;

        default:
            return false;
    }
}

// 内部辅助函数实现
int OracleVersionCompat::extract_version_components(const char *version_str, 
                                                   uint16_t *major, uint16_t *minor, 
                                                   uint16_t *patch, uint16_t *build)
{
    if (!version_str || !major || !minor || !patch || !build) {
        return VERSION_COMPAT_ERROR;
    }

    *major = *minor = *patch = *build = 0;

    // 解析版本字符串，例如："12.2.0.1.0"
    int parsed = sscanf(version_str, "%hu.%hu.%hu.%hu", major, minor, patch, build);
    if (parsed < 2) {
        // 至少需要主版本号和次版本号
        return VERSION_COMPAT_ERROR;
    }

    return VERSION_COMPAT_SUCCESS;
}

uint32_t OracleVersionCompat::encode_version_number(uint16_t major, uint16_t minor, 
                                                   uint16_t patch, uint16_t build)
{
    return ((uint32_t)major << 24) | ((uint32_t)minor << 16) | 
           ((uint32_t)patch << 8) | (uint32_t)build;
}

int OracleVersionCompat::decode_version_number(uint32_t version_num,
                                              uint16_t *major, uint16_t *minor,
                                              uint16_t *patch, uint16_t *build)
{
    if (!major || !minor || !patch || !build) {
        return VERSION_COMPAT_ERROR;
    }

    *major = (version_num >> 24) & 0xFF;
    *minor = (version_num >> 16) & 0xFF;
    *patch = (version_num >> 8) & 0xFF;
    *build = version_num & 0xFF;

    return VERSION_COMPAT_SUCCESS;
}

void OracleVersionCompat::initialize_version_capabilities()
{
    // 这里可以初始化特定版本的能力映射
    // 实际实现中可以从配置文件加载
    
    protocol_capabilities_t caps_8i = m_default_capabilities;
    caps_8i.supports_large_sdu = false;
    caps_8i.supports_unicode = false;
    caps_8i.supports_timestamp = false;
    caps_8i.supports_interval = false;
    caps_8i.max_sdu_size = 1024;
    
    uint32_t version_8i = encode_version_number(8, 1, 0, 0);
    m_version_capabilities[version_8i] = caps_8i;
    
    // 可以继续添加其他版本的能力映射
}

void OracleVersionCompat::dump_version_info(const oracle_version_info_t *version_info)
{
    if (!version_info || !m_debug_enabled) {
        return;
    }

    VER_LOG_DEBUG("[Oracle Version] Version Info:");
    VER_LOG_DEBUG("  Version String: %s", version_info->version_string);
    VER_LOG_DEBUG("  Major: %d, Minor: %d, Patch: %d, Build: %d",
                version_info->major_version, version_info->minor_version,
                version_info->patch_version, version_info->build_number);
    VER_LOG_DEBUG("  Version Number: 0x%08X", version_info->version_number);
}

void OracleVersionCompat::dump_capabilities(const protocol_capabilities_t *capabilities)
{
    if (!capabilities || !m_debug_enabled) {
        return;
    }

    VER_LOG_DEBUG("[Oracle Version] Protocol Capabilities:");
    VER_LOG_DEBUG("  TNS: large_sdu=%d, compression=%d, encryption=%d, max_sdu=%d",
                capabilities->supports_large_sdu, capabilities->supports_compression,
                capabilities->supports_encryption, capabilities->max_sdu_size);
    VER_LOG_DEBUG("  TTC: fast_auth=%d, array_ops=%d, lob_ops=%d, unicode=%d",
                capabilities->supports_fast_auth, capabilities->supports_array_operations,
                capabilities->supports_lob_operations, capabilities->supports_unicode);
    VER_LOG_DEBUG("  TTI: describe_select=%d, parse_execute=%d, max_cursors=%d",
                capabilities->supports_describe_select, capabilities->supports_parse_execute,
                capabilities->max_open_cursors);
    VER_LOG_DEBUG("  Data Types: timestamp=%d, interval=%d, nchar=%d, xmltype=%d",
                capabilities->supports_timestamp, capabilities->supports_interval,
                capabilities->supports_nchar, capabilities->supports_xmltype);
}

// 版本兼容性工具函数命名空间实现
namespace OracleVersionUtils
{
    bool is_valid_version_string(const char *version_str)
    {
        if (!version_str) {
            return false;
        }

        // 简单的版本字符串格式检查
        int dot_count = 0;
        for (const char *p = version_str; *p; p++) {
            if (*p == '.') {
                dot_count++;
            } else if (!isdigit(*p)) {
                return false;
            }
        }

        return dot_count >= 1 && dot_count <= 4;
    }

    bool is_newer_version(const oracle_version_info_t *version1, const oracle_version_info_t *version2)
    {
        if (!version1 || !version2) {
            return false;
        }

        return version1->version_number > version2->version_number;
    }

    bool is_same_major_version(const oracle_version_info_t *version1, const oracle_version_info_t *version2)
    {
        if (!version1 || !version2) {
            return false;
        }

        return version1->major_version == version2->major_version;
    }

    const char* get_version_name(const oracle_version_info_t *version_info)
    {
        if (!version_info) {
            return "Unknown";
        }

        switch (version_info->major_version) {
            case OracleVersionConstants::ORACLE_8i_MAJOR:  return "Oracle 8i";
            case OracleVersionConstants::ORACLE_9i_MAJOR:  return "Oracle 9i";
            case OracleVersionConstants::ORACLE_10g_MAJOR: return "Oracle 10g";
            case OracleVersionConstants::ORACLE_11g_MAJOR: return "Oracle 11g";
            case OracleVersionConstants::ORACLE_12c_MAJOR: return "Oracle 12c";
            case OracleVersionConstants::ORACLE_18c_MAJOR: return "Oracle 18c";
            case OracleVersionConstants::ORACLE_19c_MAJOR: return "Oracle 19c";
            case OracleVersionConstants::ORACLE_21c_MAJOR: return "Oracle 21c";
            case OracleVersionConstants::ORACLE_23c_MAJOR: return "Oracle 23c";
            default:                                        return "Oracle Unknown";
        }
    }
}
