/*
 * Oracle协议解析器上传任务工作线程实现
 * <AUTHOR> @date 2025
 */

#include "oracle_parser.h"
#include "oracle_parser_upload_task_worker.hpp"

int CTaskWorkerUploadMsg::deal_data(const TaskWorkerData *data)
{
    upload_oracle_info_t *p = (upload_oracle_info_t *)data;
    return m_parser->worker_routine_oracle_upload_data_inner(p);
}

void CTaskWorkerUploadMsg::free_data(const TaskWorkerData *data)
{
    upload_oracle_info_t *p = (upload_oracle_info_t *)data;
    m_parser->free_oracle_upload_data(p);
}
