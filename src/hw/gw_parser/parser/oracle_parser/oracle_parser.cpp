/*
 * Oracle协议解析器主实现
 * <AUTHOR> @date 2025
 */
#include <stdio.h>
#include <string.h>
#include <stdlib.h>

#include "oracle_parser.h"
#include "oracle_parser_common.h"
#include "oracle_tns_parser.h"
#include "oracle_ttc_parser.h"
#include "oracle_tti_parser.h"
#include "oracle_data_types.h"
#include "oracle_version_compat.h"
#include "gw_i_parser.h"
#include "gw_i_source.h"
#include "gw_i_upload.h"
#include "gw_stats.h"
#include "session.h"
#include "session_mgt.h"
#include "utils.h"
#include "json.h"
#include "gw_logger.h"
#include "gw_config.h"
#include "display_stats_define.h"
#include "worker_queue.h"
#include "utils.h"

#define ORACLE_PARSER_TOTAL "Oracle"
#define ORACLE_PARSER_REQ_FAILED "Oracle request failed"
#define OR<PERSON>LE_PARSER_RSP_FAILED "Oracle response failed"
#define ORACLE_PARSER_BYTES "Oracle parser bytes"
#define ORACLE_MATCH_SUC_BYTES "suc bytes"

COracleParser::COracleParser(void) : m_quit_signal(0)
                             , m_comm(NULL)
                             , m_name{0}
                             , m_u64_oracle_upload_ms(0)
                             , m_u32_oracle_upload_index(0)
                             , m_str_gw_ip("127.0.0.1")
                             , m_conf_upload_name("log")
                             , m_stats_oracle{0}
                             , m_oracle_type(8)
                             , m_upload(NULL)
                             , m_upload_file(false)
                             , m_upload_data_wq(NULL)
                             , m_upload_data_wk(NULL)
                             , m_conf_oracle_upload_queue_max_num(1000)
                             , m_conf_oracle_upload_queue_memory_max_size_bytes(100 * 1024 * 1024UL)
                             , m_conf_oracle_upload_thread_num(1)
                             , m_conf_parser_enable(1)
                             , m_conf_oracle_probe_cnt(3)
                             , m_agent_client_ip_file_path("/opt/qzkj_agent_server/tmp/client_ip.txt")
                             , m_conf_pcap_timestamp(1)
{
    snprintf(m_name, sizeof(m_name) - 1, "COracleParser-%" PRIu64 "", ((uint64_t)this) & 0xffff);

    // 初始化协议解析器组件
    m_tns_parser = new OracleTnsParser();
    m_ttc_parser = new OracleTtcParser();
    m_tti_parser = new OracleTtiParser();
    m_version_compat = new OracleVersionCompat();
}

COracleParser::~COracleParser(void)
{
    fini();

    // 清理协议解析器组件
    if (m_tns_parser) {
        delete m_tns_parser;
        m_tns_parser = NULL;
    }

    if (m_ttc_parser) {
        delete m_ttc_parser;
        m_ttc_parser = NULL;
    }

    if (m_tti_parser) {
        delete m_tti_parser;
        m_tti_parser = NULL;
    }

    if (m_version_compat) {
        delete m_version_compat;
        m_version_compat = NULL;
    }
}

void COracleParser::cache_clean()
{
    // 清理缓存数据
    if (m_upload_data_wq)
    {
        m_upload_data_wq->flush_queue();
    }
}

uint32_t COracleParser::parser_status() const
{
    if (m_upload_data_wq && m_upload_data_wq->queue_elements_num())
    {
        return 1;
    }
    return 0;
}

const char *COracleParser::get_data(const struct StreamData *psd, int dir, int *data_len, int *offset_out)
{
    *data_len = 0;
    *offset_out = 0;
    return NULL;
}

bool COracleParser::discard(struct StreamData *psd, int dir, int num)
{
    return false;
}

bool COracleParser::discard_and_update(struct StreamData *psd, int dir, int num)
{
    return false;
}

void COracleParser::del_session_stream(StreamData *psd)
{
    return;
}

void COracleParser::del_session_param(SessionMgtData *psmd)
{
}

// 初始化函数 - 参照PostgreSQL解析器标准实现
void COracleParser::init()
{
    ASSERT(m_comm != NULL);
    m_quit_signal = 0;
    load_conf(NULL);

    m_comm->get_gw_stats()->set_stats_callback(ORACLE_SHOW, print_oracle_stats_callback, this);
    m_comm->get_gw_stats()->set_qps(ORACLE_SESSION_QPS, &m_stats_oracle.cnt_session_total, 90);

    // 初始化读写锁
    if (pthread_rwlock_init(&m_traffic_source_rwlock, NULL) != 0) {
        GWLOG_ERROR(m_comm, "init traffic source rw lock failed!\n");
    }
}

void COracleParser::fini()
{
    // 清理工作队列
    if (m_upload_data_wq) {
        delete m_upload_data_wq;
        m_upload_data_wq = NULL;
    }
    if (m_upload_data_wk) {
        // 安全删除抽象基类指针，避免虚析构函数警告
        CTaskWorker *worker = m_upload_data_wk;
        m_upload_data_wk = NULL;
        delete worker;
    }
    pthread_rwlock_destroy(&m_traffic_source_rwlock);
}

void COracleParser::run()
{
}

const char *COracleParser::get_name(void) const
{
    return m_name;
}

const char *COracleParser::get_version(void) const
{
    return ORACLEPARSER_VER;
}

void COracleParser::set_gw_common(CGwCommon *comm)
{
    ASSERT(comm != NULL);
    m_comm = comm;
}

bool COracleParser::load_conf(const char *conf)
{
    if (!m_comm) {
        return false;
    }

    CGwConfig *pgwc = m_comm->get_gw_config();
    if (!pgwc) {
        return false;
    }

    std::string str_upload_name = pgwc->read_conf_string("parser", "upload_mode");
    if (str_upload_name.size() > 0)
    {
        m_conf_upload_name = str_upload_name;
    }

    m_conf_pcap_timestamp = pgwc->read_conf_int("parser", "pcap_timestamp", m_conf_pcap_timestamp);

    std::string str_gw_ip = pgwc->read_conf_string("parser", "gw_ip");
    if (str_gw_ip.size() > 0)
    {
        m_str_gw_ip = str_gw_ip;
    }

    m_upload = m_comm->get_upload_from_parser(this, m_conf_upload_name.c_str());
    if (m_upload == NULL)
    {
        GWLOG_ERROR(m_comm, "upload null(%s)\n", m_conf_upload_name.c_str());
    }

    m_conf_oracle_upload_queue_max_num = pgwc->read_conf_int("parser", "oracle_upload_queue_num", m_conf_oracle_upload_queue_max_num);
    m_conf_oracle_upload_queue_memory_max_size_bytes = pgwc->read_conf_int("parser", "oracle_upload_queue_memory_size", 
                                                                             m_conf_oracle_upload_queue_memory_max_size_bytes / (1 << 20ULL)) * (1 << 20ULL);
    m_conf_oracle_upload_thread_num = pgwc->read_conf_int("parser", "oracle_upload_thread_num", m_conf_oracle_upload_thread_num);


    GWLOG_INFO(m_comm, "load_conf: queue_max_num=%d, thread_num=%d, memory_max_size=%lu",
               m_conf_oracle_upload_queue_max_num,
               m_conf_oracle_upload_thread_num, m_conf_oracle_upload_queue_memory_max_size_bytes);

    m_agent_client_ip_file_path = pgwc->read_conf_string("parser", "agent_client_ip_file_path");
    FILE* fp = fopen(m_agent_client_ip_file_path.c_str(), "r");
    if (NULL != fp)
    {
      char buf[32] = {0};
      char* sign = NULL;
      while (fgets(buf, sizeof(buf), fp) != NULL)
      {
        if ((sign = strchr(buf, '\n')) != NULL)
        {
          *sign = 0;
        }

        GWLOG_INFO(m_comm, "get agent ip=%s\n", buf);

        m_agent_client_ip.insert(buf);
      }

      fclose(fp);
    }
    
    new_wq_upload_msg();
    return true;
}

void COracleParser::set_quit_signal(void)
{
    m_quit_signal = 1;
}

void COracleParser::wait_for_stop(void)
{
    if (m_upload_data_wq) {
        m_upload_data_wq->flush_queue();
    }
}

void COracleParser::set_url_filter_rule(CFilterRule *rule)
{
}

void COracleParser::set_accout_filter_rule(CFilterRule *rule)
{
}

void COracleParser::set_upload_filter_rule(CFilterRule *client_rule, CFilterRule *server_rule)
{
}

void COracleParser::add_upstream(CParser *parser)
{
}

void COracleParser::reset_upstream(void)
{
}

void COracleParser::push_upstream_msg(char *s, size_t length)
{
}

bool COracleParser::is_parsed(const struct StreamData *) const
{
    return true;
}

struct StreamData *COracleParser::clone_stream_data(const struct StreamData *)
{
    return NULL;
}

uint64_t COracleParser::get_parser_http_cnt()
{
    return 0;
}

uint64_t COracleParser::get_succ_parser_http_cnt()
{
    return 0;
}

void* COracleParser::get_parser_status()
{
    return NULL;
}

void COracleParser::set_parser_type(int type)
{
    m_oracle_type = type;
}

void COracleParser::read_conf_urlbase_for_mon()
{
    std::unordered_set<std::string> traffic_source;
    FILE* fp = fopen(m_agent_client_ip_file_path.c_str(), "r");
    if (NULL != fp)
    {
        char buf[32] = {0};
        char* sign = NULL;
        while (fgets(buf, sizeof(buf), fp) != NULL)
        {
            if ((sign = strchr(buf, '\n')) != NULL)
            {
                *sign = 0;
            }

            GWLOG_INFO(m_comm, "get agent ip=%s\n", buf);

            traffic_source.insert(buf);
        }

        fclose(fp);
    }

    pthread_rwlock_wrlock(&m_traffic_source_rwlock);
    std::unordered_set<std::string>().swap(m_agent_client_ip);
    m_agent_client_ip = std::move(traffic_source);
    pthread_rwlock_unlock(&m_traffic_source_rwlock);
}

void COracleParser::read_conf_filetype_for_mon()
{
}

void COracleParser::get_log_buf(char *log_buf, size_t log_buf_len) const
{
    if (!m_comm) return;

    GWLOG_INFO(m_comm, "%s Statistics:", ORACLE_LOG_PRE);
    GWLOG_INFO(m_comm, "%s   Total Sessions: %lu", ORACLE_LOG_PRE, m_stats_oracle.cnt_session_total);
    GWLOG_INFO(m_comm, "%s   Closed Sessions: %lu", ORACLE_LOG_PRE, m_stats_oracle.cnt_session_closed);
    GWLOG_INFO(m_comm, "%s   Total Parses: %lu", ORACLE_LOG_PRE, m_stats_oracle.cnt_parser_total);
    GWLOG_INFO(m_comm, "%s   Remaining Parses: %lu", ORACLE_LOG_PRE, m_stats_oracle.cnt_parser_remaining);
    GWLOG_INFO(m_comm, "%s   Matched Parses: %lu", ORACLE_LOG_PRE, m_stats_oracle.cnt_parser_matched);
    GWLOG_INFO(m_comm, "%s   Logins: %lu", ORACLE_LOG_PRE, m_stats_oracle.cnt_login);
    GWLOG_INFO(m_comm, "%s   Logouts: %lu", ORACLE_LOG_PRE, m_stats_oracle.cnt_logout);
    GWLOG_INFO(m_comm, "%s   SELECTs: %lu", ORACLE_LOG_PRE, m_stats_oracle.cnt_select);
    GWLOG_INFO(m_comm, "%s   DMLs: %lu", ORACLE_LOG_PRE, m_stats_oracle.cnt_dml);
    GWLOG_INFO(m_comm, "%s   DDLs: %lu", ORACLE_LOG_PRE, m_stats_oracle.cnt_ddl);
    GWLOG_INFO(m_comm, "%s   Commits: %lu", ORACLE_LOG_PRE, m_stats_oracle.cnt_commit);
    GWLOG_INFO(m_comm, "%s   Rollbacks: %lu", ORACLE_LOG_PRE, m_stats_oracle.cnt_rollback);
}

// 统计信息打印回调函数
void COracleParser::print_oracle_stats_callback(void *p)
{
    ASSERT(p != NULL);
    COracleParser *parser = (COracleParser *)p;
    if (parser) {
        parser->print_oracle_stats();
    }
}

void COracleParser::print_oracle_stats(void) const
{
    char log_buf[LOG_BUF_LEN] = {0};
    get_log_buf(log_buf, LOG_BUF_LEN);
    printf("%s", log_buf);
}