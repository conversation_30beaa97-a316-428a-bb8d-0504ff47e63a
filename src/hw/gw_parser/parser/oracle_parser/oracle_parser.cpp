/*
 * Oracle协议解析器主实现
 * <AUTHOR> @date 2025
 */

#include "oracle_parser.h"
#include "oracle_parser_common.h"
#include "oracle_tns_parser.h"
#include "oracle_ttc_parser.h"
#include "oracle_tti_parser.h"
#include "oracle_data_types.h"
#include "oracle_version_compat.h"
#include "gw_i_parser.h"
#include "gw_i_source.h"
#include "gw_i_upload.h"
#include "gw_stats.h"
#include "session.h"
#include "session_mgt.h"
#include "utils.h"
#include "json.h"
#include <stdio.h>

// 简化的日志宏定义
#define ORACLE_LOG_DEBUG(fmt, ...) printf("[DEBUG] " fmt "\n", ##__VA_ARGS__)
#define ORACLE_LOG_INFO(fmt, ...)  printf("[INFO] " fmt "\n", ##__VA_ARGS__)
#define ORACLE_LOG_WARN(fmt, ...)  printf("[WARN] " fmt "\n", ##__VA_ARGS__)
#define ORACLE_LOG_ERROR(fmt, ...) printf("[ERROR] " fmt "\n", ##__VA_ARGS__)

#include <string.h>
#include <stdlib.h>
#include <stdio.h>

COracleParser::COracleParser(void)
{
    memset(m_name, 0, sizeof(m_name));
    m_quit_signal = 0;
    m_comm = NULL;

    for (int i = 0; i < ORACLE_PARSER_WQ_MAX_NUM; i++) {
        m_p_wq[i] = NULL;
    }

    memset(&m_stats_oracle, 0, sizeof(m_stats_oracle));
    m_pUpload = NULL;

    // 初始化协议解析器组件
    m_tns_parser = new OracleTnsParser();
    m_ttc_parser = new OracleTtcParser();
    m_tti_parser = new OracleTtiParser();
    m_version_compat = new OracleVersionCompat();

    // 默认配置
    m_conf_oracle_parser_queue_max_num = 10000;
    m_conf_oracle_parser_thread_num = 2;
    m_conf_oracle_parser_queue_memory_max_size_bytes = 100 * 1024 * 1024;
    m_conf_run_mode = 0;
    m_conf_pcap_timestamp = 0;
    m_conf_upload_name = "";
    m_conf_oracle_upstream_thread_num = 1;
    m_conf_oracle_upstream_queue_max_num = 1000;
    m_conf_oracle_upstream_queue_memory_max_size_bytes = 10 * 1024 * 1024;
    m_conf_upstream = 0;
}

COracleParser::~COracleParser(void)
{
    fini();

    // 清理协议解析器组件
    if (m_tns_parser) {
        delete m_tns_parser;
        m_tns_parser = NULL;
    }

    if (m_ttc_parser) {
        delete m_ttc_parser;
        m_ttc_parser = NULL;
    }

    if (m_tti_parser) {
        delete m_tti_parser;
        m_tti_parser = NULL;
    }

    if (m_version_compat) {
        delete m_version_compat;
        m_version_compat = NULL;
    }
}

void COracleParser::cache_clean()
{
    // 清理缓存数据
}

bool COracleParser::probe(CSessionMgt *p_session_mgt, const app_stream *a_stream, const struct conn *pcon, CSession *p_session)
{
    if (!a_stream || !pcon || !p_session) {
        return false;
    }
    
    // 检查端口：Oracle默认端口1521
    if (pcon->client.port != 1521 && pcon->server.port != 1521) {
        return false;
    }
    else
    {
        return true;
    }
    
    int data_len = 0;
    int tcp_stream_offset = 0;
    const char *data = p_session->get_data(this, a_stream->dir, &data_len, &tcp_stream_offset);
    
    // 检查TNS协议头
    if (!data || data_len < 8) {
        return false;
    }
    
    // TNS包长度检查
    uint32_t pkt_len = read_tns_packet_length(data);
    if (pkt_len < 8 || pkt_len > 65535) {
        return false;
    }
    
    // 检查TNS包类型
    uint8_t pkt_type = data[4];
    if (pkt_type < TNS_PACKET_TYPE_CONNECT || pkt_type > TNS_PACKET_TYPE_CONTROL) {
        return false;
    }

    // 使用TNS解析器验证数据包
    if (!m_tns_parser->validate_tns_packet(data, data_len)) {
        return false;
    }

    ORACLE_LOG_DEBUG("%s probe success: pkt_len=%u, pkt_type=%u (%s)",
                     ORACLE_LOG_PRE, pkt_len, pkt_type,
                     m_tns_parser->get_tns_packet_type_name(pkt_type));
    
    // 更新会话统计
    m_stats_oracle.cnt_session_total++;
    
    return true;
}

bool COracleParser::probe_on_close(CSessionMgt *p_session_mgt, const app_stream *a_stream, const struct conn *pcon, CSession *p_session)
{
    // 更新关闭会话统计
    m_stats_oracle.cnt_session_closed++;
    return false;
}

bool COracleParser::probe_on_reset(CSessionMgt *p_session_mgt, const app_stream *a_stream, const struct conn *pcon, CSession *p_session)
{
    return false;
}

int COracleParser::parse(CSessionMgt *p_session_mgt, const app_stream *a_stream, const struct conn *pcon, CSession *p_session)
{
    if (!a_stream || !pcon || !p_session) {
        return PARSER_STATUS_DROP_DATA;
    }
    
    StreamData *p_stream_data = get_stream_data_from_session(p_session, a_stream->dir);
    if (!p_stream_data) {
        return PARSER_STATUS_DROP_DATA;
    }
    
    oracle_stream_t *p_ora_stream = p_stream_data->p_oracle_stream;
    if (!p_ora_stream) {
        p_ora_stream = (oracle_stream_t *)calloc(1, sizeof(oracle_stream_t));
        if (!p_ora_stream) {
            ORACLE_LOG_ERROR("%s alloc oracle stream failed", ORACLE_LOG_PRE);
            return PARSER_STATUS_DROP_DATA;
        }
        p_stream_data->p_oracle_stream = p_ora_stream;
        set_ip_port(pcon, &p_ora_stream->ora_stat);
    }
    
    int data_len = 0;
    int tcp_stream_offset = 0;
    const char *data = p_session->get_data(this, a_stream->dir, &data_len, &tcp_stream_offset);
    
    oracle_parsed_data_t parse_result;
    memset(&parse_result, 0, sizeof(parse_result));
    
    // 使用TNS解析器解析数据包
    int ret = m_tns_parser->parse_tns_packet(data, data_len, a_stream->dir,
                                             &p_ora_stream->ora_stat, &parse_result);

    if (ret == TNS_PARSE_SUCCESS) {
        // TNS解析成功，处理结果
        if (parse_result.op_type != 0) {
            oracle_send_data(&parse_result);
        }
        return PARSER_STATUS_FINISH;
    } else if (ret == TNS_PARSE_NEED_MORE_DATA) {
        // 需要更多数据
        return PARSER_STATUS_CONTINUE;
    } else {
        // 解析错误，丢弃数据
        ORACLE_LOG_ERROR("%s TNS parse failed, ret=%d", ORACLE_LOG_PRE, ret);
        return PARSER_STATUS_DROP_DATA;
    }
}

int COracleParser::parse_clear(CSessionMgt *p_session_mgt, const app_stream *a_stream, const struct conn *pcon, CSession *p_session)
{
    return parse(p_session_mgt, a_stream, pcon, p_session);
}

int COracleParser::parse_on_close(CSessionMgt *p_session_mgt, const app_stream *a_stream, const struct conn *pcon, CSession *p_session)
{
    return PARSER_STATUS_DROP_DATA;
}

int COracleParser::parse_on_reset(CSessionMgt *p_session_mgt, const app_stream *a_stream, const struct conn *pcon, CSession *p_session)
{
    return PARSER_STATUS_DROP_DATA;
}

// TNS协议包解析主函数
int COracleParser::parse_tns_packet(const char *pkt, size_t len, int dir, oracle_status_t *p_ora_status, oracle_parsed_data_t *p_result)
{
    if (!pkt || len < 8 || !p_ora_status || !m_tns_parser) {
        return ORACLE_PARSER_STATUS_DROP_DATA;
    }

    // 使用新的TNS解析器
    int ret = m_tns_parser->parse_tns_packet(pkt, len, dir, p_ora_status, p_result);

    switch (ret) {
        case TNS_PARSE_SUCCESS:
            // TNS解析成功，检查是否需要进一步的TTC/TTI解析
            if (p_result && p_result->sql_text.len > 0) {
                // 有SQL文本，说明是DATA包，可能需要TTC/TTI解析
                return parse_ttc_message(pkt, len, dir, p_ora_status, p_result);
            }
            return ORACLE_PARSER_STATUS_FINISH;

        case TNS_PARSE_NEED_MORE_DATA:
            return ORACLE_PARSER_STATUS_CONTINUE;

        case TNS_PARSE_ERROR:
        case TNS_PARSE_INVALID_PACKET:
        case TNS_PARSE_UNSUPPORTED:
        default:
            ORACLE_LOG_DEBUG("%s TNS parse failed: ret=%d", ORACLE_LOG_PRE, ret);
            return ORACLE_PARSER_STATUS_DROP_DATA;
    }
}

// 解析TTC消息
int COracleParser::parse_ttc_message(const char *data, size_t len, int dir, oracle_status_t *p_ora_status, oracle_parsed_data_t *p_result)
{
    if (!data || len < 8 || !p_ora_status || !m_ttc_parser) {
        return ORACLE_PARSER_STATUS_DROP_DATA;
    }

    // 跳过TNS头部，获取TTC数据
    const char *ttc_data = data + TNS_HEADER_SIZE;
    size_t ttc_len = len - TNS_HEADER_SIZE;

    if (ttc_len < sizeof(ttc_message_header_t)) {
        return ORACLE_PARSER_STATUS_CONTINUE;
    }

    // 使用TTC解析器
    int ret = m_ttc_parser->parse_ttc_message(ttc_data, ttc_len, dir, p_ora_status, p_result);

    switch (ret) {
        case TTC_PARSE_SUCCESS:
            // TTC解析成功，检查是否需要TTI解析
            if (p_result && p_result->sql_text.len > 0) {
                return parse_tti_message(ttc_data, ttc_len, p_ora_status->ttc_version, p_ora_status, p_result);
            }
            return ORACLE_PARSER_STATUS_FINISH;

        case TTC_PARSE_NEED_MORE_DATA:
            return ORACLE_PARSER_STATUS_CONTINUE;

        case TTC_PARSE_ERROR:
        case TTC_PARSE_INVALID_MESSAGE:
        case TTC_PARSE_UNSUPPORTED:
        default:
            ORACLE_LOG_DEBUG("%s TTC parse failed: ret=%d", ORACLE_LOG_PRE, ret);
            return ORACLE_PARSER_STATUS_DROP_DATA;
    }
}

// 解析TTI消息
int COracleParser::parse_tti_message(const char *data, size_t len, uint8_t message_type, oracle_status_t *p_ora_status, oracle_parsed_data_t *p_result)
{
    if (!data || len == 0 || !p_ora_status || !m_tti_parser) {
        return ORACLE_PARSER_STATUS_DROP_DATA;
    }

    // 使用TTI解析器
    int ret = m_tti_parser->parse_tti_message(data, len, message_type, p_ora_status, p_result);

    switch (ret) {
        case TTI_PARSE_SUCCESS:
            return ORACLE_PARSER_STATUS_FINISH;

        case TTI_PARSE_NEED_MORE_DATA:
            return ORACLE_PARSER_STATUS_CONTINUE;

        case TTI_PARSE_ERROR:
        case TTI_PARSE_INVALID_DATA:
        case TTI_PARSE_UNSUPPORTED:
        default:
            ORACLE_LOG_DEBUG("%s TTI parse failed: ret=%d", ORACLE_LOG_PRE, ret);
            return ORACLE_PARSER_STATUS_DROP_DATA;
    }
}

// 解析连接包（保留原有方法作为备用）
int COracleParser::parse_connect_packet(const char *pkt, size_t len, oracle_status_t *p_ora_status)
{
    // 这个方法现在主要由TNS解析器处理，这里保留作为备用
    if (len < 64) {
        return ORACLE_PARSER_STATUS_CONTINUE;
    }

    p_ora_status->conn_stat = ORACLE_CONN_HANDSHAKE;
    return ORACLE_PARSER_STATUS_FINISH;
}

// 解析数据包
int COracleParser::parse_data_packet(const char *pkt, size_t len, oracle_status_t *p_ora_status, oracle_parsed_data_t *p_result)
{
    if (len < 16) {
        return ORACLE_PARSER_STATUS_CONTINUE;
    }
    
    // 根据连接状态处理不同的数据包
    switch (p_ora_status->conn_stat) {
        case ORACLE_CONN_AUTH:
            return parse_login_packet(pkt, len, p_ora_status, p_result);
            
        case ORACLE_CONN_EXECUTE:
            return parse_sql_packet(pkt, len, p_ora_status, p_result);
            
        default:
            ORACLE_LOG_DEBUG("%s data packet in state: %d", ORACLE_LOG_PRE, p_ora_status->conn_stat);
            return ORACLE_PARSER_STATUS_CONTINUE;
    }
}

// 其他方法实现...
void COracleParser::init()
{
    snprintf(m_name, sizeof(m_name), "oracle_parser-%p", this);
    ORACLE_LOG_INFO("%s init", ORACLE_LOG_PRE);
}

void COracleParser::fini()
{
    ORACLE_LOG_INFO("%s fini", ORACLE_LOG_PRE);
}

void COracleParser::run()
{
    // 工作线程运行
}

const char *COracleParser::get_name(void) const
{
    return m_name;
}

const char *COracleParser::get_version(void) const
{
    return ORACLEPARSER_VER;
}

void COracleParser::set_gw_common(CGwCommon *comm)
{
    m_comm = comm;
}

bool COracleParser::load_conf(const char *conf)
{
    // 配置加载
    return true;
}

// 其他接口方法实现...

// 占位符实现，后续需要完善
void COracleParser::free_worker_queue(CWorkerQueue *p) {}
const char *COracleParser::get_data(const struct StreamData *, int dir, int *data_len, int *offset_out) { return NULL; }
bool COracleParser::discard(struct StreamData *, int dir, int num) { return false; }
bool COracleParser::discard_and_update(struct StreamData *, int dir, int num) { return false; }
void COracleParser::del_session_stream(StreamData *) {}
void COracleParser::del_session_param(SessionMgtData *) {}
void COracleParser::set_quit_signal(void) {}
void COracleParser::wait_for_stop(void) {}
void COracleParser::set_accout_filter_rule(CFilterRule *rule) {}
void COracleParser::set_upload_filter_rule(CFilterRule *client_rule, CFilterRule *server_rule) {}
void COracleParser::set_url_filter_rule(CFilterRule *rule) {}
void COracleParser::add_upstream(CParser *parser) {}
void COracleParser::reset_upstream(void) {}
void COracleParser::push_upstream_msg(char *s, size_t length) {}
bool COracleParser::is_parsed(const struct StreamData *) const { return false; }
struct StreamData *COracleParser::clone_stream_data(const struct StreamData *) { return NULL; }
uint64_t COracleParser::get_parser_http_cnt() { return 0; }
uint64_t COracleParser::get_succ_parser_http_cnt() { return 0; }
void* COracleParser::get_parser_status() { return &m_stats_oracle; }

// 统计信息打印
void COracleParser::print_stats()
{
    ORACLE_LOG_INFO("%s Statistics:", ORACLE_LOG_PRE);
    ORACLE_LOG_INFO("%s   Total Sessions: %lu", ORACLE_LOG_PRE, m_stats_oracle.cnt_session_total);
    ORACLE_LOG_INFO("%s   Closed Sessions: %lu", ORACLE_LOG_PRE, m_stats_oracle.cnt_session_closed);
    ORACLE_LOG_INFO("%s   Total Parses: %lu", ORACLE_LOG_PRE, m_stats_oracle.cnt_parser_total);
    ORACLE_LOG_INFO("%s   Remaining Parses: %lu", ORACLE_LOG_PRE, m_stats_oracle.cnt_parser_remaining);
    ORACLE_LOG_INFO("%s   Matched Parses: %lu", ORACLE_LOG_PRE, m_stats_oracle.cnt_parser_matched);
    ORACLE_LOG_INFO("%s   Logins: %lu", ORACLE_LOG_PRE, m_stats_oracle.cnt_login);
    ORACLE_LOG_INFO("%s   Logouts: %lu", ORACLE_LOG_PRE, m_stats_oracle.cnt_logout);
    ORACLE_LOG_INFO("%s   SELECTs: %lu", ORACLE_LOG_PRE, m_stats_oracle.cnt_select);
    ORACLE_LOG_INFO("%s   DMLs: %lu", ORACLE_LOG_PRE, m_stats_oracle.cnt_dml);
    ORACLE_LOG_INFO("%s   DDLs: %lu", ORACLE_LOG_PRE, m_stats_oracle.cnt_ddl);
    ORACLE_LOG_INFO("%s   Commits: %lu", ORACLE_LOG_PRE, m_stats_oracle.cnt_commit);
    ORACLE_LOG_INFO("%s   Rollbacks: %lu", ORACLE_LOG_PRE, m_stats_oracle.cnt_rollback);
}

// 新增纯虚函数实现
void COracleParser::set_parser_type(int type) {}
void COracleParser::read_conf_urlbase_for_mon() {}
void COracleParser::read_conf_filetype_for_mon() {}
void COracleParser::get_log_buf(char *log_buf, size_t log_buf_len) const {}
uint32_t COracleParser::parser_status() const { return 0; }