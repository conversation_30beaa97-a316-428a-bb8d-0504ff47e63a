/*
 * Oracle复杂数据类型支持实现文件
 * 实现LOB、游标、对象类型、集合类型、JSON、XML等复杂数据类型的处理
 * 优先级3功能：完善复杂数据类型支持
 * <AUTHOR> Protocol Parser Team
 * @date 2025
 */

#include "oracle_complex_types.h"
#include "oracle_memory_manager.h"
#include <cstring>
#include <cstdlib>
#include <algorithm>

// 日志宏定义
#define COMPLEX_LOG_DEBUG(fmt, ...) printf("[COMPLEX_DEBUG] " fmt "\n", ##__VA_ARGS__)
#define COMPLEX_LOG_INFO(fmt, ...)  printf("[COMPLEX_INFO] " fmt "\n", ##__VA_ARGS__)
#define COMPLEX_LOG_WARN(fmt, ...)  printf("[COMPLEX_WARN] " fmt "\n", ##__VA_ARGS__)
#define COMPLEX_LOG_ERROR(fmt, ...) printf("[COMPLEX_ERROR] " fmt "\n", ##__VA_ARGS__)

// 返回码定义
#define COMPLEX_TYPE_SUCCESS        0
#define COMPLEX_TYPE_ERROR         -1
#define COMPLEX_TYPE_MEMORY_ERROR  -2
#define COMPLEX_TYPE_INVALID_PARAM -3
#define COMPLEX_TYPE_NOT_FOUND     -4
#define COMPLEX_TYPE_NOT_SUPPORTED -5

// ========== OracleComplexTypes类实现 ==========

OracleComplexTypes::OracleComplexTypes()
    : m_next_lob_id(1000)
    , m_next_cursor_id(2000)
    , m_lob_operations(0)
    , m_cursor_operations(0)
    , m_object_operations(0)
    , m_collection_operations(0)
{
    COMPLEX_LOG_INFO("OracleComplexTypes initialized");
}

OracleComplexTypes::~OracleComplexTypes()
{
    // 清理所有LOB定位符
    for (auto& pair : m_lob_locators) {
        free_lob_locator(pair.second);
    }
    m_lob_locators.clear();

    // 清理所有游标信息
    for (auto& pair : m_cursor_infos) {
        free_cursor_info(pair.second);
    }
    m_cursor_infos.clear();

    // 清理所有对象类型
    for (auto& pair : m_object_types) {
        free_object_type(pair.second);
    }
    m_object_types.clear();

    COMPLEX_LOG_INFO("OracleComplexTypes destroyed - LOB ops: %llu, Cursor ops: %llu, Object ops: %llu, Collection ops: %llu",
                    m_lob_operations, m_cursor_operations, m_object_operations, m_collection_operations);
}

// ========== LOB类型处理实现 ==========

int OracleComplexTypes::create_lob_locator(oracle_lob_type_t lob_type, uint32_t lob_id, oracle_lob_locator_t **locator)
{
    if (!locator) {
        return COMPLEX_TYPE_INVALID_PARAM;
    }

    // 分配LOB定位符内存
    oracle_lob_locator_t *new_locator = (oracle_lob_locator_t*)calloc(1, sizeof(oracle_lob_locator_t));
    if (!new_locator) {
        COMPLEX_LOG_ERROR("Failed to allocate memory for LOB locator");
        return COMPLEX_TYPE_MEMORY_ERROR;
    }

    // 初始化LOB定位符
    new_locator->lob_type = lob_type;
    new_locator->lob_id = (lob_id == 0) ? generate_lob_id() : lob_id;
    new_locator->lob_length = 0;
    new_locator->chunk_size = 8192; // 默认8KB块大小
    new_locator->is_temporary = false;
    new_locator->is_open = false;
    new_locator->lob_data = nullptr;
    new_locator->cached_size = 0;

    // 存储到映射表
    m_lob_locators[new_locator->lob_id] = new_locator;
    *locator = new_locator;

    m_lob_operations++;
    COMPLEX_LOG_DEBUG("Created LOB locator: ID=%u, type=%d", new_locator->lob_id, lob_type);
    
    return COMPLEX_TYPE_SUCCESS;
}

int OracleComplexTypes::read_lob_data(oracle_lob_locator_t *locator, uint64_t offset, uint32_t length, void **data, size_t *data_size)
{
    if (!locator || !data || !data_size) {
        return COMPLEX_TYPE_INVALID_PARAM;
    }

    if (validate_lob_locator(locator) != COMPLEX_TYPE_SUCCESS) {
        return COMPLEX_TYPE_INVALID_PARAM;
    }

    // 检查偏移和长度
    if (offset >= locator->lob_length) {
        *data = nullptr;
        *data_size = 0;
        return COMPLEX_TYPE_SUCCESS;
    }

    // 计算实际读取长度
    uint64_t actual_length = std::min((uint64_t)length, locator->lob_length - offset);
    
    // 分配数据缓冲区
    void *buffer = malloc(actual_length);
    if (!buffer) {
        COMPLEX_LOG_ERROR("Failed to allocate memory for LOB data");
        return COMPLEX_TYPE_MEMORY_ERROR;
    }

    // 模拟LOB数据读取（实际实现中需要从数据库读取）
    if (locator->lob_data && offset < locator->cached_size) {
        size_t copy_size = std::min(actual_length, locator->cached_size - offset);
        memcpy(buffer, (char*)locator->lob_data + offset, copy_size);
        
        // 如果需要更多数据，填充零（实际实现中应该从数据库读取）
        if (copy_size < actual_length) {
            memset((char*)buffer + copy_size, 0, actual_length - copy_size);
        }
    } else {
        // 没有缓存数据，填充零（实际实现中应该从数据库读取）
        memset(buffer, 0, actual_length);
    }

    *data = buffer;
    *data_size = actual_length;

    m_lob_operations++;
    COMPLEX_LOG_DEBUG("Read LOB data: ID=%u, offset=%llu, length=%u", locator->lob_id, offset, (uint32_t)actual_length);
    
    return COMPLEX_TYPE_SUCCESS;
}

int OracleComplexTypes::write_lob_data(oracle_lob_locator_t *locator, uint64_t offset, const void *data, size_t data_size)
{
    if (!locator || !data || data_size == 0) {
        return COMPLEX_TYPE_INVALID_PARAM;
    }

    if (validate_lob_locator(locator) != COMPLEX_TYPE_SUCCESS) {
        return COMPLEX_TYPE_INVALID_PARAM;
    }

    // 检查是否需要扩展LOB
    uint64_t new_length = offset + data_size;
    if (new_length > locator->lob_length) {
        locator->lob_length = new_length;
    }

    // 扩展缓存（如果需要）
    if (new_length > locator->cached_size) {
        void *new_cache = realloc(locator->lob_data, new_length);
        if (!new_cache) {
            COMPLEX_LOG_ERROR("Failed to expand LOB cache");
            return COMPLEX_TYPE_MEMORY_ERROR;
        }
        
        // 清零新分配的区域
        if (new_length > locator->cached_size) {
            memset((char*)new_cache + locator->cached_size, 0, new_length - locator->cached_size);
        }
        
        locator->lob_data = new_cache;
        locator->cached_size = new_length;
    }

    // 写入数据到缓存
    memcpy((char*)locator->lob_data + offset, data, data_size);

    m_lob_operations++;
    COMPLEX_LOG_DEBUG("Write LOB data: ID=%u, offset=%llu, size=%zu", locator->lob_id, offset, data_size);
    
    return COMPLEX_TYPE_SUCCESS;
}

int OracleComplexTypes::get_lob_length(oracle_lob_locator_t *locator, uint64_t *length)
{
    if (!locator || !length) {
        return COMPLEX_TYPE_INVALID_PARAM;
    }

    if (validate_lob_locator(locator) != COMPLEX_TYPE_SUCCESS) {
        return COMPLEX_TYPE_INVALID_PARAM;
    }

    *length = locator->lob_length;
    return COMPLEX_TYPE_SUCCESS;
}

int OracleComplexTypes::trim_lob(oracle_lob_locator_t *locator, uint64_t new_length)
{
    if (!locator) {
        return COMPLEX_TYPE_INVALID_PARAM;
    }

    if (validate_lob_locator(locator) != COMPLEX_TYPE_SUCCESS) {
        return COMPLEX_TYPE_INVALID_PARAM;
    }

    if (new_length >= locator->lob_length) {
        return COMPLEX_TYPE_SUCCESS; // 无需截断
    }

    locator->lob_length = new_length;

    // 如果缓存大于新长度，可以选择缩小缓存
    if (locator->cached_size > new_length && new_length > 0) {
        void *new_cache = realloc(locator->lob_data, new_length);
        if (new_cache) {
            locator->lob_data = new_cache;
            locator->cached_size = new_length;
        }
    } else if (new_length == 0) {
        // 清空LOB
        free(locator->lob_data);
        locator->lob_data = nullptr;
        locator->cached_size = 0;
    }

    m_lob_operations++;
    COMPLEX_LOG_DEBUG("Trim LOB: ID=%u, new_length=%llu", locator->lob_id, new_length);
    
    return COMPLEX_TYPE_SUCCESS;
}

int OracleComplexTypes::free_lob_locator(oracle_lob_locator_t *locator)
{
    if (!locator) {
        return COMPLEX_TYPE_INVALID_PARAM;
    }

    // 释放LOB数据缓存
    if (locator->lob_data) {
        free(locator->lob_data);
        locator->lob_data = nullptr;
    }

    // 从映射表中移除
    m_lob_locators.erase(locator->lob_id);

    // 释放定位符内存
    free(locator);

    COMPLEX_LOG_DEBUG("Freed LOB locator");
    return COMPLEX_TYPE_SUCCESS;
}

// ========== 游标类型处理实现 ==========

int OracleComplexTypes::create_cursor_info(oracle_cursor_type_t cursor_type, uint32_t cursor_id, 
                                          const char *cursor_name, oracle_cursor_info_t **cursor_info)
{
    if (!cursor_info) {
        return COMPLEX_TYPE_INVALID_PARAM;
    }

    // 分配游标信息内存
    oracle_cursor_info_t *new_cursor = (oracle_cursor_info_t*)calloc(1, sizeof(oracle_cursor_info_t));
    if (!new_cursor) {
        COMPLEX_LOG_ERROR("Failed to allocate memory for cursor info");
        return COMPLEX_TYPE_MEMORY_ERROR;
    }

    // 初始化游标信息
    new_cursor->cursor_type = cursor_type;
    new_cursor->cursor_id = (cursor_id == 0) ? generate_cursor_id() : cursor_id;
    new_cursor->is_open = false;
    new_cursor->is_scrollable = false;
    new_cursor->current_row = 0;
    new_cursor->total_rows = 0;
    new_cursor->columns = nullptr;
    new_cursor->column_count = 0;

    if (cursor_name) {
        strncpy(new_cursor->cursor_name, cursor_name, sizeof(new_cursor->cursor_name) - 1);
        new_cursor->cursor_name[sizeof(new_cursor->cursor_name) - 1] = '\0';
    } else {
        snprintf(new_cursor->cursor_name, sizeof(new_cursor->cursor_name), "CURSOR_%u", new_cursor->cursor_id);
    }

    // 存储到映射表
    m_cursor_infos[new_cursor->cursor_id] = new_cursor;
    *cursor_info = new_cursor;

    m_cursor_operations++;
    COMPLEX_LOG_DEBUG("Created cursor info: ID=%u, type=%d, name=%s", 
                     new_cursor->cursor_id, cursor_type, new_cursor->cursor_name);
    
    return COMPLEX_TYPE_SUCCESS;
}

int OracleComplexTypes::open_ref_cursor(oracle_cursor_info_t *cursor_info, const char *sql_text)
{
    if (!cursor_info || !sql_text) {
        return COMPLEX_TYPE_INVALID_PARAM;
    }

    if (validate_cursor_info(cursor_info) != COMPLEX_TYPE_SUCCESS) {
        return COMPLEX_TYPE_INVALID_PARAM;
    }

    if (cursor_info->is_open) {
        COMPLEX_LOG_WARN("Cursor %u is already open", cursor_info->cursor_id);
        return COMPLEX_TYPE_SUCCESS;
    }

    // 模拟打开REF CURSOR（实际实现中需要执行SQL）
    cursor_info->is_open = true;
    cursor_info->current_row = 0;
    cursor_info->total_rows = 0; // 未知行数

    m_cursor_operations++;
    COMPLEX_LOG_DEBUG("Opened REF cursor: ID=%u, SQL=%s", cursor_info->cursor_id, sql_text);
    
    return COMPLEX_TYPE_SUCCESS;
}

int OracleComplexTypes::fetch_cursor_data(oracle_cursor_info_t *cursor_info, uint32_t fetch_rows, void **row_data)
{
    if (!cursor_info || !row_data) {
        return COMPLEX_TYPE_INVALID_PARAM;
    }

    if (validate_cursor_info(cursor_info) != COMPLEX_TYPE_SUCCESS) {
        return COMPLEX_TYPE_INVALID_PARAM;
    }

    if (!cursor_info->is_open) {
        COMPLEX_LOG_ERROR("Cursor %u is not open", cursor_info->cursor_id);
        return COMPLEX_TYPE_ERROR;
    }

    // 模拟获取游标数据（实际实现中需要从数据库获取）
    *row_data = nullptr; // 暂时返回空数据

    cursor_info->current_row += fetch_rows;

    m_cursor_operations++;
    COMPLEX_LOG_DEBUG("Fetched cursor data: ID=%u, rows=%u", cursor_info->cursor_id, fetch_rows);
    
    return COMPLEX_TYPE_SUCCESS;
}

int OracleComplexTypes::close_cursor(oracle_cursor_info_t *cursor_info)
{
    if (!cursor_info) {
        return COMPLEX_TYPE_INVALID_PARAM;
    }

    if (validate_cursor_info(cursor_info) != COMPLEX_TYPE_SUCCESS) {
        return COMPLEX_TYPE_INVALID_PARAM;
    }

    cursor_info->is_open = false;
    cursor_info->current_row = 0;

    m_cursor_operations++;
    COMPLEX_LOG_DEBUG("Closed cursor: ID=%u", cursor_info->cursor_id);
    
    return COMPLEX_TYPE_SUCCESS;
}

int OracleComplexTypes::free_cursor_info(oracle_cursor_info_t *cursor_info)
{
    if (!cursor_info) {
        return COMPLEX_TYPE_INVALID_PARAM;
    }

    // 释放列定义数组
    if (cursor_info->columns) {
        free(cursor_info->columns);
        cursor_info->columns = nullptr;
    }

    // 从映射表中移除
    m_cursor_infos.erase(cursor_info->cursor_id);

    // 释放游标信息内存
    free(cursor_info);

    COMPLEX_LOG_DEBUG("Freed cursor info");
    return COMPLEX_TYPE_SUCCESS;
}

// ========== 内部工具方法实现 ==========

uint32_t OracleComplexTypes::generate_lob_id()
{
    return m_next_lob_id++;
}

uint32_t OracleComplexTypes::generate_cursor_id()
{
    return m_next_cursor_id++;
}

int OracleComplexTypes::validate_lob_locator(const oracle_lob_locator_t *locator)
{
    if (!locator) {
        return COMPLEX_TYPE_INVALID_PARAM;
    }

    // 检查LOB类型是否有效
    if (locator->lob_type < LOB_TYPE_CLOB || locator->lob_type > LOB_TYPE_NCLOB) {
        return COMPLEX_TYPE_INVALID_PARAM;
    }

    // 检查LOB ID是否在映射表中
    if (m_lob_locators.find(locator->lob_id) == m_lob_locators.end()) {
        return COMPLEX_TYPE_NOT_FOUND;
    }

    return COMPLEX_TYPE_SUCCESS;
}

int OracleComplexTypes::validate_cursor_info(const oracle_cursor_info_t *cursor_info)
{
    if (!cursor_info) {
        return COMPLEX_TYPE_INVALID_PARAM;
    }

    // 检查游标类型是否有效
    if (cursor_info->cursor_type < CURSOR_TYPE_EXPLICIT || cursor_info->cursor_type > CURSOR_TYPE_SYS_REFCURSOR) {
        return COMPLEX_TYPE_INVALID_PARAM;
    }

    // 检查游标ID是否在映射表中
    if (m_cursor_infos.find(cursor_info->cursor_id) == m_cursor_infos.end()) {
        return COMPLEX_TYPE_NOT_FOUND;
    }

    return COMPLEX_TYPE_SUCCESS;
}

// ===== 缺失的对象类型处理方法实现 =====

// 释放对象类型
int OracleComplexTypes::free_object_type(oracle_object_type_t *object_type)
{
    if (!object_type) {
        COMPLEX_LOG_WARN("Attempting to free null object type");
        return COMPLEX_TYPE_INVALID_PARAM;
    }

    COMPLEX_LOG_DEBUG("Freeing object type: name=%s, type_oid=%u",
                     object_type->type_name, object_type->type_oid);

    // 释放属性数组
    if (object_type->attributes) {
        for (uint16_t i = 0; i < object_type->attribute_count; i++) {
            oracle_object_attribute_t *attr = &object_type->attributes[i];

            // 属性名称是数组，不需要释放
            // 只需要清零
            memset(attr->attribute_name, 0, sizeof(attr->attribute_name));
        }
        free(object_type->attributes);
        object_type->attributes = nullptr;
    }

    // 清零类型名称和模式名称（它们是数组）
    memset(object_type->type_name, 0, sizeof(object_type->type_name));
    memset(object_type->schema_name, 0, sizeof(object_type->schema_name));

    // 重置其他字段
    object_type->attribute_count = 0;
    object_type->type_oid = 0;
    object_type->type_version = 0;

    // 释放对象类型内存
    free(object_type);

    COMPLEX_LOG_DEBUG("Object type freed successfully");

    return COMPLEX_TYPE_SUCCESS;
}
