/*
 * Oracle SQL执行流程管理器实现
 * 实现完整的SQL执行流程：解析→绑定→执行→获取结果
 * 支持SQL类型识别、游标生命周期管理和结果集处理
 * <AUTHOR> @date 2025
 */

#include "oracle_sql_executor.h"
#include "oracle_parser_common.h"
#include "gw_logger.h"
#include <string.h>
#include <stdlib.h>
#include <ctype.h>
#include <algorithm>
#include <sstream>

// 日志宏定义
#define SQL_LOG_DEBUG(fmt, ...) printf("[SQL-DEBUG] " fmt "\n", ##__VA_ARGS__)
#define SQL_LOG_INFO(fmt, ...)  printf("[SQL-INFO] " fmt "\n", ##__VA_ARGS__)
#define SQL_LOG_WARN(fmt, ...)  printf("[SQL-WARN] " fmt "\n", ##__VA_ARGS__)
#define SQL_LOG_ERROR(fmt, ...) printf("[SQL-ERROR] " fmt "\n", ##__VA_ARGS__)

// 大端序读取函数
static uint16_t read_uint16_be(const char *data) {
    return (((uint8_t)data[0]) << 8) | ((uint8_t)data[1]);
}

static uint32_t read_uint32_be(const char *data) {
    return (((uint8_t)data[0]) << 24) | (((uint8_t)data[1]) << 16) |
           (((uint8_t)data[2]) << 8) | ((uint8_t)data[3]);
}

OracleSqlExecutor::OracleSqlExecutor()
    : m_next_context_id(1)
    , m_max_contexts(10000)
    , m_context_timeout(3600)  // 1小时超时
    , m_default_fetch_array_size(100)
{
    memset(&m_statistics, 0, sizeof(m_statistics));
    SQL_LOG_INFO("Oracle SQL Executor initialized");
}

OracleSqlExecutor::~OracleSqlExecutor()
{
    // 清理所有SQL上下文
    for (auto& pair : m_sql_contexts) {
        cleanup_sql_context(pair.second);
    }
    m_sql_contexts.clear();
    
    SQL_LOG_INFO("Oracle SQL Executor destroyed, processed %llu SQL statements",
                m_statistics.total_sql_executed);
}

int OracleSqlExecutor::create_sql_context(uint32_t session_id, uint32_t cursor_id, 
                                         const char *sql_text, size_t sql_length,
                                         oracle_sql_context_t **context)
{
    if (!sql_text || sql_length == 0 || !context) {
        SQL_LOG_ERROR("Invalid parameters for SQL context creation");
        return -1;
    }

    // 检查上下文数量限制
    if (m_sql_contexts.size() >= m_max_contexts) {
        SQL_LOG_WARN("Maximum SQL contexts limit reached: %u", m_max_contexts);
        cleanup_expired_contexts(m_context_timeout / 2);
    }

    // 创建新的SQL上下文
    oracle_sql_context_t *ctx = (oracle_sql_context_t*)calloc(1, sizeof(oracle_sql_context_t));
    if (!ctx) {
        return -1;
    }

    // 初始化基本信息
    ctx->session_id = session_id;
    ctx->cursor_id = cursor_id;
    ctx->state = SQL_STATE_INIT;
    ctx->create_time = get_current_timestamp();
    ctx->last_activity_time = ctx->create_time;
    ctx->fetch_array_size = m_default_fetch_array_size;

    // 复制SQL文本
    ctx->sql_text = (char*)malloc(sql_length + 1);
    if (!ctx->sql_text) {
        free(ctx);
        return -1;
    }
    memcpy(ctx->sql_text, sql_text, sql_length);
    ctx->sql_text[sql_length] = '\0';
    ctx->sql_length = sql_length;

    // 识别SQL类型
    ctx->sql_type = identify_sql_type(sql_text, sql_length);
    ctx->sql_hash = calculate_sql_hash(sql_text, sql_length);

    // 分析SQL结构
    analyze_sql_structure(ctx);

    // 添加到上下文映射
    uint64_t context_key = ((uint64_t)session_id << 32) | cursor_id;
    m_sql_contexts[context_key] = ctx;

    *context = ctx;

    SQL_LOG_INFO("Created SQL context: session=%u, cursor=%u, type=%s, hash=0x%08x",
                session_id, cursor_id, get_sql_type_name(ctx->sql_type), ctx->sql_hash);

    return 0;
}

oracle_sql_type_t OracleSqlExecutor::identify_sql_type(const char *sql_text, size_t sql_length)
{
    if (!sql_text || sql_length == 0) {
        return SQL_TYPE_UNKNOWN;
    }

    // 跳过前导空白字符
    const char *p = sql_text;
    while (p < sql_text + sql_length && isspace(*p)) {
        p++;
    }

    if (p >= sql_text + sql_length) {
        return SQL_TYPE_UNKNOWN;
    }

    // 检查SQL关键字（不区分大小写）
    if (strncasecmp(p, "SELECT", 6) == 0) {
        return SQL_TYPE_SELECT;
    } else if (strncasecmp(p, "INSERT", 6) == 0) {
        return SQL_TYPE_INSERT;
    } else if (strncasecmp(p, "UPDATE", 6) == 0) {
        return SQL_TYPE_UPDATE;
    } else if (strncasecmp(p, "DELETE", 6) == 0) {
        return SQL_TYPE_DELETE;
    } else if (strncasecmp(p, "MERGE", 5) == 0) {
        return SQL_TYPE_MERGE;
    } else if (strncasecmp(p, "CREATE", 6) == 0) {
        return SQL_TYPE_CREATE;
    } else if (strncasecmp(p, "ALTER", 5) == 0) {
        return SQL_TYPE_ALTER;
    } else if (strncasecmp(p, "DROP", 4) == 0) {
        return SQL_TYPE_DROP;
    } else if (strncasecmp(p, "TRUNCATE", 8) == 0) {
        return SQL_TYPE_TRUNCATE;
    } else if (strncasecmp(p, "COMMIT", 6) == 0) {
        return SQL_TYPE_COMMIT;
    } else if (strncasecmp(p, "ROLLBACK", 8) == 0) {
        return SQL_TYPE_ROLLBACK;
    } else if (strncasecmp(p, "SAVEPOINT", 9) == 0) {
        return SQL_TYPE_SAVEPOINT;
    } else if (strncasecmp(p, "CALL", 4) == 0) {
        return SQL_TYPE_CALL_PROCEDURE;
    } else if (strncasecmp(p, "BEGIN", 5) == 0 || strncasecmp(p, "DECLARE", 7) == 0) {
        return SQL_TYPE_PLSQL_BLOCK;
    }

    return SQL_TYPE_UNKNOWN;
}

int OracleSqlExecutor::analyze_sql_structure(oracle_sql_context_t *context)
{
    if (!context || !context->sql_text) {
        return -1;
    }

    SQL_LOG_DEBUG("Analyzing SQL structure for context %u:%u", context->session_id, context->cursor_id);

    // 设置结果集标志
    context->has_result_set = (context->sql_type == SQL_TYPE_SELECT);

    // 标准化SQL文本
    int ret = normalize_sql_text(context->sql_text, context->sql_length, &context->normalized_sql);
    if (ret != 0) {
        SQL_LOG_WARN("Failed to normalize SQL text");
    }

    // 检查是否包含绑定变量
    bool has_bind_vars = OracleSqlExecutorUtils::contains_bind_variables(context->sql_text, context->sql_length);
    if (has_bind_vars) {
        SQL_LOG_DEBUG("SQL contains bind variables");
    }

    // 设置自动提交标志（DDL语句通常自动提交）
    context->is_autocommit = (context->sql_type == SQL_TYPE_CREATE ||
                             context->sql_type == SQL_TYPE_ALTER ||
                             context->sql_type == SQL_TYPE_DROP ||
                             context->sql_type == SQL_TYPE_TRUNCATE);

    SQL_LOG_DEBUG("SQL analysis completed: type=%s, has_result_set=%s, autocommit=%s",
                 get_sql_type_name(context->sql_type),
                 context->has_result_set ? "yes" : "no",
                 context->is_autocommit ? "yes" : "no");

    return 0;
}

int OracleSqlExecutor::process_sql_parse(oracle_sql_context_t *context, const char *parse_data, size_t parse_data_len)
{
    if (!context) {
        return -1;
    }

    SQL_LOG_DEBUG("Processing SQL parse for context %u:%u", context->session_id, context->cursor_id);

    uint64_t start_time = get_current_timestamp();

    // 转换到解析状态
    int ret = transition_to_state(context, SQL_STATE_PARSE);
    if (ret != 0) {
        return ret;
    }

    // 这里可以解析Oracle特定的解析数据
    // 例如：解析计划、优化器提示等
    if (parse_data && parse_data_len > 0) {
        SQL_LOG_DEBUG("Parse data length: %zu bytes", parse_data_len);
        // 实际的解析数据处理逻辑
    }

    // 更新统计信息
    context->parse_time = get_current_timestamp() - start_time;
    context->is_prepared = true;
    context->last_activity_time = get_current_timestamp();

    SQL_LOG_INFO("SQL parse completed: session=%u, cursor=%u, time=%llu us",
                context->session_id, context->cursor_id, context->parse_time);

    m_statistics.total_parse_time += context->parse_time;
    return 0;
}

int OracleSqlExecutor::process_sql_bind(oracle_sql_context_t *context, 
                                       const oracle_bind_variable_t *bind_vars, uint16_t bind_count)
{
    if (!context) {
        return -1;
    }

    SQL_LOG_DEBUG("Processing SQL bind for context %u:%u, %u variables", 
                 context->session_id, context->cursor_id, bind_count);

    uint64_t start_time = get_current_timestamp();

    // 转换到绑定状态
    int ret = transition_to_state(context, SQL_STATE_BIND);
    if (ret != 0) {
        return ret;
    }

    // 分配绑定变量数组
    if (bind_count > 0) {
        ret = allocate_bind_variables(context, bind_count);
        if (ret != 0) {
            return ret;
        }

        // 复制绑定变量
        for (uint16_t i = 0; i < bind_count; i++) {
            ret = copy_bind_variable(&context->bind_vars[i], &bind_vars[i]);
            if (ret != 0) {
                SQL_LOG_ERROR("Failed to copy bind variable %u", i);
                return ret;
            }
        }

        context->bind_count = bind_count;

        // 验证绑定变量
        ret = validate_bind_variables(context);
        if (ret != 0) {
            SQL_LOG_ERROR("Bind variable validation failed");
            return ret;
        }
    }

    // 更新统计信息
    context->bind_time = get_current_timestamp() - start_time;
    context->is_bound = true;
    context->last_activity_time = get_current_timestamp();

    SQL_LOG_INFO("SQL bind completed: session=%u, cursor=%u, variables=%u, time=%llu us",
                context->session_id, context->cursor_id, bind_count, context->bind_time);

    return 0;
}

int OracleSqlExecutor::process_sql_execute(oracle_sql_context_t *context, 
                                          const char *execute_data, size_t execute_data_len)
{
    if (!context) {
        return -1;
    }

    SQL_LOG_DEBUG("Processing SQL execute for context %u:%u", context->session_id, context->cursor_id);

    uint64_t start_time = get_current_timestamp();

    // 转换到执行状态
    int ret = transition_to_state(context, SQL_STATE_EXECUTE);
    if (ret != 0) {
        return ret;
    }

    // 处理执行数据
    if (execute_data && execute_data_len > 0) {
        SQL_LOG_DEBUG("Execute data length: %zu bytes", execute_data_len);
        // 这里可以解析执行结果、影响行数等信息
        
        // 简化的行数解析（实际需要根据Oracle协议格式）
        if (execute_data_len >= 4) {
            context->rows_processed = read_uint32_be(execute_data);
            SQL_LOG_DEBUG("Rows processed: %u", context->rows_processed);
        }
    }

    // 更新统计信息
    context->execute_time = get_current_timestamp() - start_time;
    context->is_executed = true;
    context->last_activity_time = get_current_timestamp();

    // 更新全局统计
    m_statistics.total_sql_executed++;
    m_statistics.total_execute_time += context->execute_time;
    m_statistics.total_rows_processed += context->rows_processed;

    // 根据SQL类型更新统计
    switch (context->sql_type) {
        case SQL_TYPE_SELECT:
            m_statistics.select_statements++;
            break;
        case SQL_TYPE_INSERT:
            m_statistics.insert_statements++;
            break;
        case SQL_TYPE_UPDATE:
            m_statistics.update_statements++;
            break;
        case SQL_TYPE_DELETE:
            m_statistics.delete_statements++;
            break;
        case SQL_TYPE_CREATE:
        case SQL_TYPE_ALTER:
        case SQL_TYPE_DROP:
        case SQL_TYPE_TRUNCATE:
            m_statistics.ddl_statements++;
            break;
        case SQL_TYPE_PLSQL_BLOCK:
            m_statistics.plsql_blocks++;
            break;
        case SQL_TYPE_CALL_PROCEDURE:
        case SQL_TYPE_CALL_FUNCTION:
            m_statistics.procedure_calls++;
            break;
        default:
            break;
    }

    SQL_LOG_INFO("SQL execute completed: session=%u, cursor=%u, type=%s, rows=%u, time=%llu us",
                context->session_id, context->cursor_id, get_sql_type_name(context->sql_type),
                context->rows_processed, context->execute_time);

    return 0;
}

// 工具方法实现
uint64_t OracleSqlExecutor::get_current_timestamp()
{
    struct timespec ts;
    clock_gettime(CLOCK_MONOTONIC, &ts);
    return ts.tv_sec * 1000000ULL + ts.tv_nsec / 1000ULL;
}

const char* OracleSqlExecutor::get_sql_type_name(oracle_sql_type_t sql_type)
{
    switch (sql_type) {
        case SQL_TYPE_SELECT:           return "SELECT";
        case SQL_TYPE_INSERT:           return "INSERT";
        case SQL_TYPE_UPDATE:           return "UPDATE";
        case SQL_TYPE_DELETE:           return "DELETE";
        case SQL_TYPE_MERGE:            return "MERGE";
        case SQL_TYPE_CREATE:           return "CREATE";
        case SQL_TYPE_ALTER:            return "ALTER";
        case SQL_TYPE_DROP:             return "DROP";
        case SQL_TYPE_TRUNCATE:         return "TRUNCATE";
        case SQL_TYPE_PLSQL_BLOCK:      return "PL/SQL_BLOCK";
        case SQL_TYPE_CALL_PROCEDURE:   return "CALL_PROCEDURE";
        case SQL_TYPE_CALL_FUNCTION:    return "CALL_FUNCTION";
        case SQL_TYPE_COMMIT:           return "COMMIT";
        case SQL_TYPE_ROLLBACK:         return "ROLLBACK";
        case SQL_TYPE_SAVEPOINT:        return "SAVEPOINT";
        default:                        return "UNKNOWN";
    }
}

uint32_t OracleSqlExecutor::calculate_sql_hash(const char *sql_text, size_t sql_length)
{
    // 简单的哈希算法（实际可以使用更复杂的算法）
    uint32_t hash = 0;
    for (size_t i = 0; i < sql_length; i++) {
        hash = hash * 31 + (unsigned char)sql_text[i];
    }
    return hash;
}

// 状态转换方法实现
int OracleSqlExecutor::transition_to_state(oracle_sql_context_t *context, oracle_sql_execution_state_t new_state)
{
    if (!context) {
        SQL_LOG_ERROR("Invalid context for state transition");
        return -1;
    }

    SQL_LOG_DEBUG("Transitioning context %u:%u to state %d",
                 context->session_id, context->cursor_id, new_state);

    // 更新最后活动时间
    context->last_activity_time = get_current_timestamp();

    // 根据新状态设置相应的标志
    switch (new_state) {
        case SQL_STATE_INIT:
            // 初始化状态，重置所有标志
            context->is_prepared = false;
            context->is_bound = false;
            context->is_executed = false;
            context->is_complete = false;
            context->parse_time = 0;
            context->bind_time = 0;
            context->execute_time = 0;
            context->fetch_time = 0;
            break;

        case SQL_STATE_PARSE:
            // 解析状态
            context->is_prepared = true;
            context->is_bound = false;
            context->is_executed = false;
            context->is_complete = false;
            break;

        case SQL_STATE_BIND:
            // 绑定状态
            context->is_bound = true;
            context->is_executed = false;
            context->is_complete = false;
            break;

        case SQL_STATE_EXECUTE:
            // 执行状态
            context->is_executed = true;
            context->is_complete = false;
            break;

        case SQL_STATE_FETCH:
            // 获取状态，保持执行状态
            break;

        case SQL_STATE_COMPLETE:
            // 完成状态
            context->is_complete = true;
            context->complete_time = get_current_timestamp();
            context->total_time = context->complete_time - context->create_time;
            break;

        case SQL_STATE_ERROR:
            // 错误状态，设置错误码
            if (context->error_code == 0) {
                context->error_code = -1; // 通用错误
            }
            break;

        default:
            SQL_LOG_WARN("Unknown state %d for context %u:%u", new_state, context->session_id, context->cursor_id);
            return -1;
    }

    SQL_LOG_DEBUG("State transition completed for context %u:%u", context->session_id, context->cursor_id);

    return 0;
}

bool OracleSqlExecutor::is_valid_state_transition(oracle_sql_execution_state_t from_state, oracle_sql_execution_state_t to_state)
{
    // 定义有效的状态转换
    switch (from_state) {
        case SQL_STATE_INIT:
            return (to_state == SQL_STATE_PARSE || to_state == SQL_STATE_ERROR);

        case SQL_STATE_PARSE:
            return (to_state == SQL_STATE_BIND || to_state == SQL_STATE_EXECUTE || to_state == SQL_STATE_ERROR);

        case SQL_STATE_BIND:
            return (to_state == SQL_STATE_EXECUTE || to_state == SQL_STATE_ERROR);

        case SQL_STATE_EXECUTE:
            return (to_state == SQL_STATE_FETCH || to_state == SQL_STATE_COMPLETE || to_state == SQL_STATE_ERROR);

        case SQL_STATE_FETCH:
            return (to_state == SQL_STATE_FETCH || to_state == SQL_STATE_COMPLETE || to_state == SQL_STATE_ERROR);

        case SQL_STATE_COMPLETE:
            return (to_state == SQL_STATE_INIT || to_state == SQL_STATE_ERROR);

        case SQL_STATE_ERROR:
            return (to_state == SQL_STATE_INIT);

        default:
            return false;
    }
}

uint32_t OracleSqlExecutor::generate_context_id()
{
    static uint32_t context_counter = 1;
    return context_counter++;
}

// 内存管理方法实现
void OracleSqlExecutor::cleanup_expired_contexts(uint32_t timeout_seconds)
{
    if (m_sql_contexts.empty()) {
        return;
    }

    SQL_LOG_DEBUG("Cleaning up expired SQL contexts (timeout: %u seconds)", timeout_seconds);

    uint64_t current_time = get_current_timestamp();
    uint64_t timeout_threshold = timeout_seconds * 1000000ULL; // 转换为微秒

    std::vector<uint32_t> expired_contexts;

    // 查找过期的上下文
    for (auto& pair : m_sql_contexts) {
        oracle_sql_context_t *ctx = pair.second;
        if (ctx && (current_time - ctx->last_activity_time) > timeout_threshold) {
            expired_contexts.push_back(pair.first); // 使用map的key作为context_id
        }
    }

    // 清理过期的上下文
    for (uint32_t context_id : expired_contexts) {
        auto it = m_sql_contexts.find(context_id);
        if (it != m_sql_contexts.end()) {
            oracle_sql_context_t *ctx = it->second;

            SQL_LOG_DEBUG("Cleaning up expired context %u:%u (age: %llu seconds)",
                         ctx->session_id, ctx->cursor_id,
                         (current_time - ctx->last_activity_time) / 1000000ULL);

            cleanup_sql_context(ctx);
            m_sql_contexts.erase(it);
        }
    }

    if (!expired_contexts.empty()) {
        SQL_LOG_INFO("Cleaned up %zu expired SQL contexts", expired_contexts.size());
    }
}

void OracleSqlExecutor::cleanup_sql_context(oracle_sql_context_t *context)
{
    if (!context) {
        SQL_LOG_WARN("Attempting to cleanup null SQL context");
        return;
    }

    SQL_LOG_DEBUG("Cleaning up SQL context %u:%u", context->session_id, context->cursor_id);

    // 清理绑定变量
    if (context->bind_vars) {
        for (uint16_t i = 0; i < context->bind_count; i++) {
            oracle_bind_variable_t *bind_var = &context->bind_vars[i];
            if (bind_var->data) {
                free(bind_var->data);
                bind_var->data = nullptr;
            }
        }
        free(context->bind_vars);
        context->bind_vars = nullptr;
    }

    // 清理SQL文本
    if (context->sql_text) {
        free(context->sql_text);
        context->sql_text = nullptr;
        context->sql_length = 0;
    }

    // 清理标准化SQL
    if (context->normalized_sql) {
        free(context->normalized_sql);
        context->normalized_sql = nullptr;
    }

    // 清理定义变量
    if (context->define_vars) {
        free(context->define_vars);
        context->define_vars = nullptr;
    }

    // 重置上下文状态
    memset(context, 0, sizeof(oracle_sql_context_t));

    // 释放上下文内存
    free(context);

    SQL_LOG_DEBUG("SQL context cleanup completed");
}

// 统计和诊断方法实现
void OracleSqlExecutor::get_sql_statistics(oracle_sql_statistics_t *stats)
{
    if (!stats) {
        SQL_LOG_ERROR("Invalid statistics parameter");
        return;
    }

    memset(stats, 0, sizeof(oracle_sql_statistics_t));

    // 简化实现：填充基本统计信息
    stats->total_sql_executed = (uint64_t)m_sql_contexts.size();
    stats->total_parse_time = 0;
    stats->total_execute_time = 0;
    stats->total_fetch_time = 0;
    stats->parse_errors = 0;
    stats->execute_errors = 0;
    stats->fetch_errors = 0;

    SQL_LOG_DEBUG("Retrieved SQL executor statistics");
}

void OracleSqlExecutor::reset_statistics()
{
    SQL_LOG_INFO("Resetting SQL executor statistics");
    // 简化实现：只记录日志
    SQL_LOG_INFO("SQL executor statistics reset completed");
}

// 调试和诊断方法实现
void OracleSqlExecutor::dump_sql_context(const oracle_sql_context_t *context)
{
    if (!context) {
        SQL_LOG_ERROR("Cannot dump null SQL context");
        return;
    }

    SQL_LOG_INFO("=== SQL Context Dump ===");
    SQL_LOG_INFO("Session ID: %u", context->session_id);
    SQL_LOG_INFO("Cursor ID: %u", context->cursor_id);
    SQL_LOG_INFO("SQL Text: %.*s", (int)context->sql_length,
                 context->sql_text ? context->sql_text : "NULL");
    SQL_LOG_INFO("SQL Hash: %u", context->sql_hash);
    SQL_LOG_INFO("Bind Count: %u", context->bind_count);
    SQL_LOG_INFO("Define Count: %u", context->define_count);
    SQL_LOG_INFO("Is Prepared: %s", context->is_prepared ? "true" : "false");
    SQL_LOG_INFO("Is Bound: %s", context->is_bound ? "true" : "false");
    SQL_LOG_INFO("Is Executed: %s", context->is_executed ? "true" : "false");
    SQL_LOG_INFO("Is Complete: %s", context->is_complete ? "true" : "false");
    SQL_LOG_INFO("Has Result Set: %s", context->has_result_set ? "true" : "false");
    SQL_LOG_INFO("Error Code: %d", context->error_code);
    SQL_LOG_INFO("Error Message: %s", context->error_message);
    SQL_LOG_INFO("Create Time: %llu", context->create_time);
    SQL_LOG_INFO("Last Activity: %llu", context->last_activity_time);
    SQL_LOG_INFO("Complete Time: %llu", context->complete_time);
    SQL_LOG_INFO("Parse Time: %llu", context->parse_time);
    SQL_LOG_INFO("Bind Time: %llu", context->bind_time);
    SQL_LOG_INFO("Execute Time: %llu", context->execute_time);
    SQL_LOG_INFO("Fetch Time: %llu", context->fetch_time);
    SQL_LOG_INFO("Total Time: %llu", context->total_time);
    SQL_LOG_INFO("Rows Processed: %u", context->rows_processed);
    SQL_LOG_INFO("Rows Fetched: %u", context->rows_fetched);
    SQL_LOG_INFO("========================");
}

void OracleSqlExecutor::dump_execution_plan(const oracle_sql_context_t *context)
{
    if (!context) {
        SQL_LOG_ERROR("Cannot dump execution plan for null context");
        return;
    }

    SQL_LOG_INFO("=== Execution Plan Dump ===");
    SQL_LOG_INFO("Context: %u:%u", context->session_id, context->cursor_id);
    SQL_LOG_INFO("SQL Type: %d", context->sql_type);
    SQL_LOG_INFO("SQL Hash: %u", context->sql_hash);
    SQL_LOG_INFO("Execution State: %d", context->state);
    SQL_LOG_INFO("No detailed execution plan available (simplified implementation)");
    SQL_LOG_INFO("===========================");
}

// OracleSqlExecutorUtils 命名空间实现
namespace OracleSqlExecutorUtils
{
    // SQL类型工具实现
    const char* get_sql_type_description(oracle_sql_type_t sql_type)
    {
        switch (sql_type) {
            case SQL_TYPE_SELECT:
                return "SELECT";
            case SQL_TYPE_INSERT:
                return "INSERT";
            case SQL_TYPE_UPDATE:
                return "UPDATE";
            case SQL_TYPE_DELETE:
                return "DELETE";
            case SQL_TYPE_CREATE:
                return "CREATE";
            case SQL_TYPE_DROP:
                return "DROP";
            case SQL_TYPE_ALTER:
                return "ALTER";
            case SQL_TYPE_TRUNCATE:
                return "TRUNCATE";
            case SQL_TYPE_COMMIT:
                return "COMMIT";
            case SQL_TYPE_ROLLBACK:
                return "ROLLBACK";
            case SQL_TYPE_SAVEPOINT:
                return "SAVEPOINT";
            case SQL_TYPE_CALL_PROCEDURE:
                return "CALL PROCEDURE";
            case SQL_TYPE_CALL_FUNCTION:
                return "CALL FUNCTION";
            case SQL_TYPE_PLSQL_BLOCK:
                return "PL/SQL BLOCK";
            case SQL_TYPE_MERGE:
                return "MERGE";
            case SQL_TYPE_UNKNOWN:
                return "UNKNOWN";
            default:
                return "UNKNOWN";
        }
    }

    bool is_query_statement(oracle_sql_type_t sql_type)
    {
        return (sql_type == SQL_TYPE_SELECT);
    }

    bool is_modification_statement(oracle_sql_type_t sql_type)
    {
        return (sql_type == SQL_TYPE_INSERT ||
                sql_type == SQL_TYPE_UPDATE ||
                sql_type == SQL_TYPE_DELETE ||
                sql_type == SQL_TYPE_MERGE ||
                sql_type == SQL_TYPE_TRUNCATE);
    }

    bool requires_result_set(oracle_sql_type_t sql_type)
    {
        return (sql_type == SQL_TYPE_SELECT);
    }

    // SQL文本工具实现
    std::string trim_sql_text(const std::string& sql_text)
    {
        if (sql_text.empty()) {
            return sql_text;
        }

        size_t start = sql_text.find_first_not_of(" \t\n\r");
        if (start == std::string::npos) {
            return "";
        }

        size_t end = sql_text.find_last_not_of(" \t\n\r");
        return sql_text.substr(start, end - start + 1);
    }

    std::string uppercase_sql_keywords(const std::string& sql_text)
    {
        std::string result = sql_text;

        // 简化实现：将常见的SQL关键字转换为大写
        const char* keywords[] = {
            "select", "from", "where", "insert", "into", "update", "set",
            "delete", "create", "drop", "alter", "table", "index", "view",
            "commit", "rollback", "savepoint", "and", "or", "not", "null",
            "order", "by", "group", "having", "union", "join", "inner",
            "left", "right", "outer", "on", "as", "distinct", "count",
            "sum", "avg", "max", "min", "case", "when", "then", "else", "end"
        };

        for (const char* keyword : keywords) {
            size_t pos = 0;
            std::string lower_keyword = keyword;
            std::string upper_keyword = keyword;

            // 转换为大写
            for (char& c : upper_keyword) {
                c = toupper(c);
            }

            // 替换所有出现的关键字
            while ((pos = result.find(lower_keyword, pos)) != std::string::npos) {
                // 检查是否为完整单词
                bool is_word_start = (pos == 0 || !isalnum(result[pos - 1]));
                bool is_word_end = (pos + lower_keyword.length() >= result.length() ||
                                   !isalnum(result[pos + lower_keyword.length()]));

                if (is_word_start && is_word_end) {
                    result.replace(pos, lower_keyword.length(), upper_keyword);
                    pos += upper_keyword.length();
                } else {
                    pos += lower_keyword.length();
                }
            }
        }

        return result;
    }

    bool contains_bind_variables(const char *sql_text, size_t sql_length)
    {
        if (!sql_text || sql_length == 0) {
            return false;
        }

        // 检查Oracle绑定变量标记
        for (size_t i = 0; i < sql_length; i++) {
            char c = sql_text[i];

            // 检查命名绑定变量 (:variable)
            if (c == ':' && i + 1 < sql_length) {
                char next_char = sql_text[i + 1];
                if (isalpha(next_char) || next_char == '_') {
                    return true;
                }
            }

            // 检查位置绑定变量 (?)
            if (c == '?') {
                return true;
            }

            // 跳过字符串字面量
            if (c == '\'' || c == '"') {
                char quote = c;
                i++; // 跳过开始引号
                while (i < sql_length && sql_text[i] != quote) {
                    if (sql_text[i] == '\\' && i + 1 < sql_length) {
                        i++; // 跳过转义字符
                    }
                    i++;
                }
            }

            // 跳过注释
            if (c == '-' && i + 1 < sql_length && sql_text[i + 1] == '-') {
                // 单行注释，跳到行尾
                while (i < sql_length && sql_text[i] != '\n') {
                    i++;
                }
            } else if (c == '/' && i + 1 < sql_length && sql_text[i + 1] == '*') {
                // 多行注释，跳到注释结束
                i += 2;
                while (i + 1 < sql_length) {
                    if (sql_text[i] == '*' && sql_text[i + 1] == '/') {
                        i += 2;
                        break;
                    }
                    i++;
                }
            }
        }

        return false;
    }

    // 性能工具实现
    double calculate_execution_efficiency(const oracle_sql_context_t *context)
    {
        if (!context || context->total_time == 0) {
            return 0.0;
        }

        // 简化的效率计算：处理行数 / 总时间（微秒）
        double rows_per_microsecond = (double)context->rows_processed / context->total_time;
        return rows_per_microsecond * 1000000.0; // 转换为每秒处理行数
    }

    uint32_t estimate_result_set_size(const oracle_sql_context_t *context)
    {
        if (!context) {
            return 0;
        }

        // 简化的结果集大小估算
        if (context->rows_fetched > 0) {
            return context->rows_fetched;
        }

        // 基于SQL类型的估算
        switch (context->sql_type) {
            case SQL_TYPE_SELECT:
                return 100; // 默认估算100行
            case SQL_TYPE_INSERT:
            case SQL_TYPE_UPDATE:
            case SQL_TYPE_DELETE:
                return context->rows_processed;
            default:
                return 0;
        }
    }

    bool should_use_array_fetch(const oracle_sql_context_t *context)
    {
        if (!context) {
            return false;
        }

        // 对于大结果集的SELECT语句建议使用数组获取
        return (context->sql_type == SQL_TYPE_SELECT &&
                estimate_result_set_size(context) > 10);
    }
}

// ===== 缺失的SQL执行器方法实现 =====

// 复制绑定变量
int OracleSqlExecutor::copy_bind_variable(oracle_bind_variable_t *dest, const oracle_bind_variable_t *src)
{
    if (!dest || !src) {
        SQL_LOG_ERROR("Invalid parameters for copy_bind_variable");
        return -1;
    }

    // 复制基本信息
    dest->bind_index = src->bind_index;
    dest->data_type = src->data_type;
    dest->max_length = src->max_length;
    dest->actual_length = src->actual_length;
    dest->is_null = src->is_null;

    // 复制数据
    if (src->data && src->actual_length > 0) {
        dest->data = malloc(src->actual_length);
        if (!dest->data) {
            SQL_LOG_ERROR("Failed to allocate memory for bind variable data");
            return -1;
        }
        memcpy(dest->data, src->data, src->actual_length);
    } else {
        dest->data = nullptr;
    }

    SQL_LOG_DEBUG("Bind variable copied: index=%u, type=%u, length=%u",
                 dest->bind_index, dest->data_type, dest->actual_length);

    return 0;
}

// 标准化SQL文本
int OracleSqlExecutor::normalize_sql_text(const char *sql_text, size_t sql_length, char **normalized_sql)
{
    if (!sql_text || sql_length == 0 || !normalized_sql) {
        SQL_LOG_ERROR("Invalid parameters for normalize_sql_text");
        return -1;
    }

    // 分配内存
    char *result = (char*)malloc(sql_length + 1);
    if (!result) {
        SQL_LOG_ERROR("Failed to allocate memory for normalized SQL");
        return -1;
    }

    size_t write_pos = 0;
    bool in_string = false;
    bool in_comment = false;

    // 简化的SQL标准化：移除多余空格，转换为大写
    for (size_t i = 0; i < sql_length; i++) {
        char c = sql_text[i];

        // 处理字符串字面量
        if (c == '\'' && !in_comment) {
            in_string = !in_string;
            result[write_pos++] = c;
            continue;
        }

        if (in_string) {
            result[write_pos++] = c;
            continue;
        }

        // 处理注释
        if (c == '-' && i + 1 < sql_length && sql_text[i + 1] == '-') {
            in_comment = true;
            continue;
        }

        if (in_comment && c == '\n') {
            in_comment = false;
            result[write_pos++] = ' ';
            continue;
        }

        if (in_comment) {
            continue;
        }

        // 标准化空白字符
        if (isspace(c)) {
            if (write_pos > 0 && result[write_pos - 1] != ' ') {
                result[write_pos++] = ' ';
            }
        } else {
            // 转换为大写（仅对关键字）
            result[write_pos++] = toupper(c);
        }
    }

    // 移除尾部空格
    while (write_pos > 0 && result[write_pos - 1] == ' ') {
        write_pos--;
    }

    result[write_pos] = '\0';
    *normalized_sql = result;

    SQL_LOG_DEBUG("SQL normalized: original_len=%zu, normalized_len=%zu", sql_length, write_pos);

    return 0;
}

// 分配绑定变量
int OracleSqlExecutor::allocate_bind_variables(oracle_sql_context_t *context, uint16_t bind_count)
{
    if (!context || bind_count == 0) {
        SQL_LOG_ERROR("Invalid parameters for allocate_bind_variables");
        return -1;
    }

    // 释放现有的绑定变量
    if (context->bind_vars) {
        for (uint16_t i = 0; i < context->bind_count; i++) {
            if (context->bind_vars[i].data) {
                free(context->bind_vars[i].data);
            }
        }
        free(context->bind_vars);
    }

    // 分配新的绑定变量数组
    context->bind_vars = (oracle_bind_variable_t*)calloc(bind_count, sizeof(oracle_bind_variable_t));
    if (!context->bind_vars) {
        SQL_LOG_ERROR("Failed to allocate memory for bind variables");
        return -1;
    }

    context->bind_count = bind_count;

    SQL_LOG_DEBUG("Allocated %u bind variables", bind_count);

    return 0;
}

// 验证绑定变量
int OracleSqlExecutor::validate_bind_variables(oracle_sql_context_t *context)
{
    if (!context) {
        SQL_LOG_ERROR("Invalid context for validate_bind_variables");
        return -1;
    }

    if (context->bind_count == 0) {
        SQL_LOG_DEBUG("No bind variables to validate");
        return 0;
    }

    if (!context->bind_vars) {
        SQL_LOG_ERROR("Bind variables array is null");
        return -1;
    }

    // 验证每个绑定变量
    for (uint16_t i = 0; i < context->bind_count; i++) {
        oracle_bind_variable_t *bind_var = &context->bind_vars[i];

        // 检查索引
        if (bind_var->bind_index == 0 || bind_var->bind_index > context->bind_count) {
            SQL_LOG_ERROR("Invalid bind variable index: %u", bind_var->bind_index);
            return -1;
        }

        // 检查数据类型
        if (bind_var->data_type == 0) {
            SQL_LOG_ERROR("Invalid bind variable data type at index %u", bind_var->bind_index);
            return -1;
        }

        // 检查长度
        if (!bind_var->is_null && bind_var->actual_length > bind_var->max_length) {
            SQL_LOG_ERROR("Bind variable actual length exceeds max length at index %u", bind_var->bind_index);
            return -1;
        }

        // 检查数据指针
        if (!bind_var->is_null && bind_var->actual_length > 0 && !bind_var->data) {
            SQL_LOG_ERROR("Bind variable data is null but length > 0 at index %u", bind_var->bind_index);
            return -1;
        }
    }

    SQL_LOG_DEBUG("All %u bind variables validated successfully", context->bind_count);

    return 0;
}
