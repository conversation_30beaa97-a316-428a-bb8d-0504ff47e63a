/*
 * Oracle协议解析处理
 * <AUTHOR> @date 2025
 */

#include "oracle_parser.h"
#include "oracle_parser_common.h"
#include "oracle_tns_parser.h"
#include "oracle_ttc_parser.h"
#include "oracle_tti_parser.h"
#include "gw_i_parser.h"
#include "gw_i_source.h"
#include "gw_logger.h"
#include "session.h"
#include "session_mgt.h"
#include "utils.h"
#include "../../utils/cjson/cJSON.h"

#include <string.h>
#include <stdlib.h>

// TNS协议包解析主函数
int COracleParser::parse_tns_packet(const char *pkt, size_t len, int dir, oracle_status_t *p_ora_status, oracle_parsed_data_t *p_result)
{
    if (!pkt || len < 8 || !p_ora_status || !m_tns_parser) {
        return ORACLE_PARSER_STATUS_DROP_DATA;
    }

    // 使用新的TNS解析器
    int ret = m_tns_parser->parse_tns_packet(pkt, len, dir, p_ora_status, p_result);

    switch (ret) {
        case TNS_PARSE_SUCCESS:
            // TNS解析成功，检查是否需要进一步的TTC/TTI解析
            if (p_result && p_result->sql_text.len > 0) {
                // 有SQL文本，说明是DATA包，可能需要TTC/TTI解析
                return parse_ttc_message(pkt, len, dir, p_ora_status, p_result);
            }
            return ORACLE_PARSER_STATUS_FINISH;

        case TNS_PARSE_NEED_MORE_DATA:
            return ORACLE_PARSER_STATUS_CONTINUE;

        case TNS_PARSE_ERROR:
        case TNS_PARSE_INVALID_PACKET:
        case TNS_PARSE_UNSUPPORTED:
        default:
            GWLOG_DEBUG(m_comm, "%s TNS parse failed: ret=%d", ORACLE_LOG_PRE, ret);
            return ORACLE_PARSER_STATUS_DROP_DATA;
    }
}

// 解析TTC消息
int COracleParser::parse_ttc_message(const char *data, size_t len, int dir, oracle_status_t *p_ora_status, oracle_parsed_data_t *p_result)
{
    if (!data || len < 8 || !p_ora_status || !m_ttc_parser) {
        return ORACLE_PARSER_STATUS_DROP_DATA;
    }

    // 跳过TNS头部，获取TTC数据
    const char *ttc_data = data + TNS_HEADER_SIZE;
    size_t ttc_len = len - TNS_HEADER_SIZE;

    if (ttc_len < sizeof(ttc_message_header_t)) {
        return ORACLE_PARSER_STATUS_CONTINUE;
    }

    // 使用TTC解析器
    int ret = m_ttc_parser->parse_ttc_message(ttc_data, ttc_len, dir, p_ora_status, p_result);

    switch (ret) {
        case TTC_PARSE_SUCCESS:
            // TTC解析成功，检查是否需要TTI解析
            if (p_result && p_result->sql_text.len > 0) {
                return parse_tti_message(ttc_data, ttc_len, p_ora_status->ttc_version, p_ora_status, p_result);
            }
            return ORACLE_PARSER_STATUS_FINISH;

        case TTC_PARSE_NEED_MORE_DATA:
            return ORACLE_PARSER_STATUS_CONTINUE;

        case TTC_PARSE_ERROR:
        case TTC_PARSE_INVALID_MESSAGE:
        case TTC_PARSE_UNSUPPORTED:
        default:
            GWLOG_DEBUG(m_comm, "%s TTC parse failed: ret=%d", ORACLE_LOG_PRE, ret);
            return ORACLE_PARSER_STATUS_DROP_DATA;
    }
}

// 解析TTI消息
int COracleParser::parse_tti_message(const char *data, size_t len, uint8_t message_type, oracle_status_t *p_ora_status, oracle_parsed_data_t *p_result)
{
    if (!data || len == 0 || !p_ora_status || !m_tti_parser) {
        return ORACLE_PARSER_STATUS_DROP_DATA;
    }

    // 使用TTI解析器
    int ret = m_tti_parser->parse_tti_message(data, len, message_type, p_ora_status, p_result);

    switch (ret) {
        case TTI_PARSE_SUCCESS:
            return ORACLE_PARSER_STATUS_FINISH;

        case TTI_PARSE_NEED_MORE_DATA:
            return ORACLE_PARSER_STATUS_CONTINUE;

        case TTI_PARSE_ERROR:
        case TTI_PARSE_INVALID_DATA:
        case TTI_PARSE_UNSUPPORTED:
        default:
            GWLOG_DEBUG(m_comm, "%s TTI parse failed: ret=%d", ORACLE_LOG_PRE, ret);
            return ORACLE_PARSER_STATUS_DROP_DATA;
    }
}

// 解析连接包（保留原有方法作为备用）
int COracleParser::parse_connect_packet(const char *pkt, size_t len, oracle_status_t *p_ora_status)
{
    // 这个方法现在主要由TNS解析器处理，这里保留作为备用
    if (len < 64) {
        return ORACLE_PARSER_STATUS_CONTINUE;
    }

    p_ora_status->conn_stat = ORACLE_CONN_HANDSHAKE;
    return ORACLE_PARSER_STATUS_FINISH;
}

// 解析数据包
int COracleParser::parse_data_packet(const char *pkt, size_t len, oracle_status_t *p_ora_status, oracle_parsed_data_t *p_result)
{
    if (len < 16) {
        return ORACLE_PARSER_STATUS_CONTINUE;
    }
    
    // 根据连接状态处理不同的数据包
    switch (p_ora_status->conn_stat) {
        case ORACLE_CONN_AUTH:
            return parse_login_packet(pkt, len, p_ora_status, p_result);
            
        case ORACLE_CONN_EXECUTE:
            return parse_sql_packet(pkt, len, p_ora_status, p_result);
            
        default:
            GWLOG_DEBUG(m_comm, "%s data packet in state: %d", ORACLE_LOG_PRE, p_ora_status->conn_stat);
            return ORACLE_PARSER_STATUS_CONTINUE;
    }
}

// Oracle协议解析主函数
int COracleParser::parse(CSessionMgt *p_session_mgt, const app_stream *a_stream, const struct conn *pcon, CSession *p_session)
{
    if (!a_stream || !pcon || !p_session) {
        return PARSER_STATUS_DROP_DATA;
    }

    StreamData *p_stream_data = get_stream_data_from_session(p_session, a_stream->dir);
    if (!p_stream_data) {
        return PARSER_STATUS_DROP_DATA;
    }

    oracle_stream_t *p_ora_stream = p_stream_data->p_oracle_stream;
    if (!p_ora_stream) {
        p_ora_stream = (oracle_stream_t *)calloc(1, sizeof(oracle_stream_t));
        if (!p_ora_stream) {
            GWLOG_ERROR(m_comm, "%s alloc oracle stream failed", ORACLE_LOG_PRE);
            return PARSER_STATUS_DROP_DATA;
        }
        p_stream_data->p_oracle_stream = p_ora_stream;
        set_ip_port(pcon, &p_ora_stream->ora_stat);
    }

    int data_len = 0;
    int tcp_stream_offset = 0;
    const char *data = p_session->get_data(this, a_stream->dir, &data_len, &tcp_stream_offset);

    oracle_parsed_data_t parse_result;
    memset(&parse_result, 0, sizeof(parse_result));

    // 使用TNS解析器解析数据包
    int ret = m_tns_parser->parse_tns_packet(data, data_len, a_stream->dir,
                                             &p_ora_stream->ora_stat, &parse_result);

    if (ret == TNS_PARSE_SUCCESS) {
        // TNS解析成功，处理结果
        if (parse_result.op_type != 0) {
            oracle_send_data(&parse_result);
        }
        // 更新统计信息
        __sync_fetch_and_add(&m_stats_oracle.cnt_parser_total, 1);
        return PARSER_STATUS_FINISH;
    } else if (ret == TNS_PARSE_NEED_MORE_DATA) {
        // 需要更多数据
        return PARSER_STATUS_CONTINUE;
    } else {
        // 解析错误，丢弃数据
        GWLOG_ERROR(m_comm, "%s TNS parse failed, ret=%d", ORACLE_LOG_PRE, ret);
        return PARSER_STATUS_DROP_DATA;
    }
}

int COracleParser::parse_clear(CSessionMgt *p_session_mgt, const app_stream *a_stream, const struct conn *pcon, CSession *p_session)
{
    return PARSER_STATUS_FINISH;
}

int COracleParser::parse_on_close(CSessionMgt *p_session_mgt, const app_stream *a_stream, const struct conn *pcon, CSession *p_session)
{
    // 清理会话相关的Oracle数据
    if (p_session) {
        StreamData *p_stream_data = get_stream_data_from_session(p_session, a_stream->dir);
        if (p_stream_data && p_stream_data->p_oracle_stream) {
            //free_oracle_parsed_data_in_session(p_stream_data->p_oracle_stream, a_stream->dir);
        }
    }
    return PARSER_STATUS_FINISH;
}

int COracleParser::parse_on_reset(CSessionMgt *p_session_mgt, const app_stream *a_stream, const struct conn *pcon, CSession *p_session)
{
    // 重置会话状态
    if (p_session) {
        StreamData *p_stream_data = get_stream_data_from_session(p_session, a_stream->dir);
        if (p_stream_data && p_stream_data->p_oracle_stream) {
            memset(&p_stream_data->p_oracle_stream->ora_stat, 0, sizeof(oracle_status_t));
            set_ip_port(pcon, &p_stream_data->p_oracle_stream->ora_stat);
        }
    }
    return PARSER_STATUS_FINISH;
}

// 解析登录认证包
int COracleParser::parse_login_packet(const char *pkt, size_t len, oracle_status_t *p_ora_status, oracle_parsed_data_t *p_result)
{
    if (len < 32) {
        return ORACLE_PARSER_STATUS_CONTINUE;
    }
    
    // 解析TNS数据包中的认证信息
    // 这里需要解析Oracle的认证协议，提取用户名、服务名等信息
    
    // 简单的示例：查找用户名和服务名
    const char *user_start = NULL;
    const char *service_start = NULL;
    
    // 在实际实现中，需要详细解析TNS数据包结构
    // 这里只是示例逻辑
    
    for (size_t i = 8; i < len - 4; i++) {
        if (strncmp(pkt + i, "USER", 4) == 0) {
            user_start = pkt + i + 4;
        } else if (strncmp(pkt + i, "SERVICE", 7) == 0) {
            service_start = pkt + i + 7;
        }
    }
    
    // 提取用户名
    if (user_start && p_ora_status->user.s == NULL) {
        const char *user_end = (const char *)memchr(user_start, 0, len - (user_start - pkt));
        if (user_end) {
            p_ora_status->user.s = user_start;
            p_ora_status->user.len = user_end - user_start;
            keep_bstring_data(&p_ora_status->user);
        }
    }
    
    // 提取服务名
    if (service_start && p_ora_status->service_name.s == NULL) {
        const char *service_end = (const char *)memchr(service_start, 0, len - (service_start - pkt));
        if (service_end) {
            p_ora_status->service_name.s = service_start;
            p_ora_status->service_name.len = service_end - service_start;
            keep_bstring_data(&p_ora_status->service_name);
        }
    }
    
    // 更新连接状态
    p_ora_status->conn_stat = ORACLE_CONN_EXECUTE;
    p_ora_status->is_authenticated = 1;
    
    // 填充解析结果
    if (p_result) {
        p_result->op_type = ORACLE_FUNCTION_CODE_LOGON;
        p_result->user = &p_ora_status->user;
        p_result->service_name = &p_ora_status->service_name;
        p_result->success = 1;
        
        // 更新统计信息
        m_stats_oracle.cnt_login++;
    }
    
    return ORACLE_PARSER_STATUS_FINISH;
}

// 解析SQL数据包
int COracleParser::parse_sql_packet(const char *pkt, size_t len, oracle_status_t *p_ora_status, oracle_parsed_data_t *p_result)
{
    if (len < 16) {
        return ORACLE_PARSER_STATUS_CONTINUE;
    }
    
    // 解析SQL语句
    // 在实际实现中，需要解析Oracle的SQL协议格式
    
    // 简单的SQL语句检测
    const char *sql_start = NULL;
    
    // 查找SQL语句的开始位置
    for (size_t i = 8; i < len - 6; i++) {
        if (strncmp(pkt + i, "SELECT", 6) == 0) {
            sql_start = pkt + i;
            p_result->op_type = ORACLE_OP_SELECT;
            m_stats_oracle.cnt_select++;
            break;
        } else if (strncmp(pkt + i, "INSERT", 6) == 0) {
            sql_start = pkt + i;
            p_result->op_type = ORACLE_OP_INSERT;
            m_stats_oracle.cnt_dml++;
            break;
        } else if (strncmp(pkt + i, "UPDATE", 6) == 0) {
            sql_start = pkt + i;
            p_result->op_type = ORACLE_OP_UPDATE;
            m_stats_oracle.cnt_dml++;
            break;
        } else if (strncmp(pkt + i, "DELETE", 6) == 0) {
            sql_start = pkt + i;
            p_result->op_type = ORACLE_OP_DELETE;
            m_stats_oracle.cnt_dml++;
            break;
        } else if (strncmp(pkt + i, "CREATE", 6) == 0) {
            sql_start = pkt + i;
            p_result->op_type = ORACLE_OP_CREATE;
            m_stats_oracle.cnt_ddl++;
            break;
        } else if (strncmp(pkt + i, "ALTER", 5) == 0) {
            sql_start = pkt + i;
            p_result->op_type = ORACLE_OP_ALTER;
            m_stats_oracle.cnt_ddl++;
            break;
        } else if (strncmp(pkt + i, "DROP", 4) == 0) {
            sql_start = pkt + i;
            p_result->op_type = ORACLE_OP_DROP;
            m_stats_oracle.cnt_ddl++;
            break;
        }
    }
    
    if (sql_start) {
        // 提取SQL语句
        const char *sql_end = (const char *)memchr(sql_start, ';', len - (sql_start - pkt));
        if (!sql_end) {
            sql_end = pkt + len;
        }
        
        p_result->sql_text.s = sql_start;
        p_result->sql_text.len = sql_end - sql_start;
        keep_bstring_data(&p_result->sql_text);
        
        p_result->user = &p_ora_status->user;
        p_result->service_name = &p_ora_status->service_name;
        p_result->success = 1;
        
        return ORACLE_PARSER_STATUS_FINISH;
    }
    
    return ORACLE_PARSER_STATUS_CONTINUE;
}

// Oracle解析数据合并
oracle_parsed_data_t *COracleParser::oracle_parsed_data_merge(oracle_parsed_data_t *p_dst, oracle_parsed_data_t *p_src, int src_dir)
{
    if (!p_dst || !p_src) {
        return p_dst;
    }
    
    // 合并解析数据
    if (p_src->sql_text.s && p_src->sql_text.len > 0) {
        p_dst->sql_text = p_src->sql_text;
    }
    
    if (p_src->user && p_src->user->s) {
        p_dst->user = p_src->user;
    }
    
    if (p_src->service_name && p_src->service_name->s) {
        p_dst->service_name = p_src->service_name;
    }
    
    p_dst->op_type = p_src->op_type;
    p_dst->success = p_src->success;
    p_dst->err_code = p_src->err_code;
    
    if (p_src->err_msg) {
        if (p_dst->err_msg) {
            free(p_dst->err_msg);
        }
        p_dst->err_msg = strdup(p_src->err_msg);
    }
    
    return p_dst;
}

// 字符串转换为JSON字符串
char *COracleParser::bstring_to_cjson_str(const b_string_t *bstr)
{
    if (!bstr || !bstr->s || bstr->len == 0) {
        return strdup("");
    }
    
    // 简单的字符串转义
    char *result = (char *)malloc(bstr->len * 2 + 1);
    if (!result) {
        return NULL;
    }
    
    char *ptr = result;
    for (unsigned int i = 0; i < bstr->len; i++) {
        unsigned char c = bstr->s[i];
        if (c == '"' || c == '\\') {
            *ptr++ = '\\';
        }
        *ptr++ = c;
    }
    *ptr = '\0';
    
    return result;
}