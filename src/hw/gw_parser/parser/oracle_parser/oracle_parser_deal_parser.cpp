/*
 * Oracle协议解析处理
 * <AUTHOR> @date 2025
 */

#include "oracle_parser.h"
#include "oracle_parser_common.h"
#include "gw_i_parser.h"
#include "gw_logger.h"
#include "session.h"
#include "utils.h"
#include "../../utils/cjson/cJSON.h"

#include <string.h>
#include <stdlib.h>

// 解析登录认证包
int COracleParser::parse_login_packet(const char *pkt, size_t len, oracle_status_t *p_ora_status, oracle_parsed_data_t *p_result)
{
    if (len < 32) {
        return ORACLE_PARSER_STATUS_CONTINUE;
    }
    
    // 解析TNS数据包中的认证信息
    // 这里需要解析Oracle的认证协议，提取用户名、服务名等信息
    
    // 简单的示例：查找用户名和服务名
    const char *user_start = NULL;
    const char *service_start = NULL;
    
    // 在实际实现中，需要详细解析TNS数据包结构
    // 这里只是示例逻辑
    
    for (size_t i = 8; i < len - 4; i++) {
        if (strncmp(pkt + i, "USER", 4) == 0) {
            user_start = pkt + i + 4;
        } else if (strncmp(pkt + i, "SERVICE", 7) == 0) {
            service_start = pkt + i + 7;
        }
    }
    
    // 提取用户名
    if (user_start && p_ora_status->user.s == NULL) {
        const char *user_end = (const char *)memchr(user_start, 0, len - (user_start - pkt));
        if (user_end) {
            p_ora_status->user.s = user_start;
            p_ora_status->user.len = user_end - user_start;
            keep_bstring_data(&p_ora_status->user);
        }
    }
    
    // 提取服务名
    if (service_start && p_ora_status->service_name.s == NULL) {
        const char *service_end = (const char *)memchr(service_start, 0, len - (service_start - pkt));
        if (service_end) {
            p_ora_status->service_name.s = service_start;
            p_ora_status->service_name.len = service_end - service_start;
            keep_bstring_data(&p_ora_status->service_name);
        }
    }
    
    // 更新连接状态
    p_ora_status->conn_stat = ORACLE_CONN_EXECUTE;
    p_ora_status->is_authenticated = 1;
    
    // 填充解析结果
    if (p_result) {
        p_result->op_type = ORACLE_FUNCTION_CODE_LOGON;
        p_result->user = &p_ora_status->user;
        p_result->service_name = &p_ora_status->service_name;
        p_result->success = 1;
        
        // 更新统计信息
        m_stats_oracle.cnt_login++;
    }
    
    return ORACLE_PARSER_STATUS_FINISH;
}

// 解析SQL数据包
int COracleParser::parse_sql_packet(const char *pkt, size_t len, oracle_status_t *p_ora_status, oracle_parsed_data_t *p_result)
{
    if (len < 16) {
        return ORACLE_PARSER_STATUS_CONTINUE;
    }
    
    // 解析SQL语句
    // 在实际实现中，需要解析Oracle的SQL协议格式
    
    // 简单的SQL语句检测
    const char *sql_start = NULL;
    
    // 查找SQL语句的开始位置
    for (size_t i = 8; i < len - 6; i++) {
        if (strncmp(pkt + i, "SELECT", 6) == 0) {
            sql_start = pkt + i;
            p_result->op_type = ORACLE_OP_SELECT;
            m_stats_oracle.cnt_select++;
            break;
        } else if (strncmp(pkt + i, "INSERT", 6) == 0) {
            sql_start = pkt + i;
            p_result->op_type = ORACLE_OP_INSERT;
            m_stats_oracle.cnt_dml++;
            break;
        } else if (strncmp(pkt + i, "UPDATE", 6) == 0) {
            sql_start = pkt + i;
            p_result->op_type = ORACLE_OP_UPDATE;
            m_stats_oracle.cnt_dml++;
            break;
        } else if (strncmp(pkt + i, "DELETE", 6) == 0) {
            sql_start = pkt + i;
            p_result->op_type = ORACLE_OP_DELETE;
            m_stats_oracle.cnt_dml++;
            break;
        } else if (strncmp(pkt + i, "CREATE", 6) == 0) {
            sql_start = pkt + i;
            p_result->op_type = ORACLE_OP_CREATE;
            m_stats_oracle.cnt_ddl++;
            break;
        } else if (strncmp(pkt + i, "ALTER", 5) == 0) {
            sql_start = pkt + i;
            p_result->op_type = ORACLE_OP_ALTER;
            m_stats_oracle.cnt_ddl++;
            break;
        } else if (strncmp(pkt + i, "DROP", 4) == 0) {
            sql_start = pkt + i;
            p_result->op_type = ORACLE_OP_DROP;
            m_stats_oracle.cnt_ddl++;
            break;
        }
    }
    
    if (sql_start) {
        // 提取SQL语句
        const char *sql_end = (const char *)memchr(sql_start, ';', len - (sql_start - pkt));
        if (!sql_end) {
            sql_end = pkt + len;
        }
        
        p_result->sql_text.s = sql_start;
        p_result->sql_text.len = sql_end - sql_start;
        keep_bstring_data(&p_result->sql_text);
        
        p_result->user = &p_ora_status->user;
        p_result->service_name = &p_ora_status->service_name;
        p_result->success = 1;
        
        return ORACLE_PARSER_STATUS_FINISH;
    }
    
    return ORACLE_PARSER_STATUS_CONTINUE;
}

// Oracle解析数据合并
oracle_parsed_data_t *COracleParser::oracle_parsed_data_merge(oracle_parsed_data_t *p_dst, oracle_parsed_data_t *p_src, int src_dir)
{
    if (!p_dst || !p_src) {
        return p_dst;
    }
    
    // 合并解析数据
    if (p_src->sql_text.s && p_src->sql_text.len > 0) {
        p_dst->sql_text = p_src->sql_text;
    }
    
    if (p_src->user && p_src->user->s) {
        p_dst->user = p_src->user;
    }
    
    if (p_src->service_name && p_src->service_name->s) {
        p_dst->service_name = p_src->service_name;
    }
    
    p_dst->op_type = p_src->op_type;
    p_dst->success = p_src->success;
    p_dst->err_code = p_src->err_code;
    
    if (p_src->err_msg) {
        if (p_dst->err_msg) {
            free(p_dst->err_msg);
        }
        p_dst->err_msg = strdup(p_src->err_msg);
    }
    
    return p_dst;
}

// 字符串转换为JSON字符串
char *COracleParser::bstring_to_cjson_str(const b_string_t *bstr)
{
    if (!bstr || !bstr->s || bstr->len == 0) {
        return strdup("");
    }
    
    // 简单的字符串转义
    char *result = (char *)malloc(bstr->len * 2 + 1);
    if (!result) {
        return NULL;
    }
    
    char *ptr = result;
    for (unsigned int i = 0; i < bstr->len; i++) {
        unsigned char c = bstr->s[i];
        if (c == '"' || c == '\\') {
            *ptr++ = '\\';
        }
        *ptr++ = c;
    }
    *ptr = '\0';
    
    return result;
}

// Oracle事件JSON格式化
char *COracleParser::oracle_json_dump_event(const oracle_parsed_data_t *p_opd)
{
    if (!p_opd) {
        return NULL;
    }
    
    cJSON *root = cJSON_CreateObject();
    if (!root) {
        return NULL;
    }
    
    // 添加基础信息
    cJSON_AddStringToObject(root, "protocol", "oracle");
    
    if (p_opd->user && p_opd->user->s) {
        char *user_str = bstring_to_cjson_str(p_opd->user);
        if (user_str) {
            cJSON_AddStringToObject(root, "user", user_str);
            free(user_str);
        }
    }
    
    if (p_opd->service_name && p_opd->service_name->s) {
        char *service_str = bstring_to_cjson_str(p_opd->service_name);
        if (service_str) {
            cJSON_AddStringToObject(root, "service_name", service_str);
            free(service_str);
        }
    }
    
    if (p_opd->sql_text.s && p_opd->sql_text.len > 0) {
        char *sql_str = bstring_to_cjson_str(&p_opd->sql_text);
        if (sql_str) {
            cJSON_AddStringToObject(root, "sql", sql_str);
            free(sql_str);
        }
    }
    
    // 添加操作类型
    const char *op_type_str = "UNKNOWN";
    switch (p_opd->op_type) {
        case ORACLE_OP_SELECT: op_type_str = "SELECT"; break;
        case ORACLE_OP_INSERT: op_type_str = "INSERT"; break;
        case ORACLE_OP_UPDATE: op_type_str = "UPDATE"; break;
        case ORACLE_OP_DELETE: op_type_str = "DELETE"; break;
        case ORACLE_OP_CREATE: op_type_str = "CREATE"; break;
        case ORACLE_OP_ALTER: op_type_str = "ALTER"; break;
        case ORACLE_OP_DROP: op_type_str = "DROP"; break;
        case ORACLE_FUNCTION_CODE_LOGON: op_type_str = "LOGON"; break;
        case ORACLE_FUNCTION_CODE_LOGOFF: op_type_str = "LOGOFF"; break;
        case ORACLE_FUNCTION_CODE_COMMIT: op_type_str = "COMMIT"; break;
        case ORACLE_FUNCTION_CODE_ROLLBACK: op_type_str = "ROLLBACK"; break;
    }
    cJSON_AddStringToObject(root, "operation", op_type_str);
    
    // 添加执行结果
    cJSON_AddBoolToObject(root, "success", p_opd->success);
    if (p_opd->err_code != 0) {
        cJSON_AddNumberToObject(root, "error_code", p_opd->err_code);
    }
    if (p_opd->err_msg) {
        cJSON_AddStringToObject(root, "error_message", p_opd->err_msg);
    }
    
    // 添加网络信息
    if (p_opd->client_ip) {
        cJSON_AddStringToObject(root, "client_ip", p_opd->client_ip);
    }
    if (p_opd->server_ip) {
        cJSON_AddStringToObject(root, "server_ip", p_opd->server_ip);
    }
    if (p_opd->client_port != 0) {
        cJSON_AddNumberToObject(root, "client_port", p_opd->client_port);
    }
    if (p_opd->server_port != 0) {
        cJSON_AddNumberToObject(root, "server_port", p_opd->server_port);
    }
    
    // 生成JSON字符串
    char *json_str = cJSON_PrintUnformatted(root);
    cJSON_Delete(root);
    
    return json_str;
}

// 发送解析数据
void COracleParser::oracle_send_data(const oracle_parsed_data_t *p_result)
{
    if (!p_result || !m_pUpload) {
        return;
    }
    
    // 生成JSON格式的事件数据
    char *json_data = oracle_json_dump_event(p_result);
    if (!json_data) {
        return;
    }
    
    // 上传数据
    oracle_cb_upload_msg(json_data, "oracle_event");
    
    free(json_data);
    
    // 更新统计信息
    m_stats_oracle.cnt_parser_total++;
    m_stats_oracle.cnt_parser_matched++;
}

// 上传消息回调
void COracleParser::oracle_cb_upload_msg(const char *s, const char* msgtype)
{
    if (!s || !m_pUpload) {
        return;
    }
    
    // 创建上传消息
    struct UploadMsg *p_um = (struct UploadMsg *)calloc(1, sizeof(struct UploadMsg));
    if (!p_um) {
        return;
    }
    
    p_um->s = strdup(s);
    p_um->length = strlen(s);
    
    if (msgtype) {
        p_um->msgtype = strdup(msgtype);
    }
    
    // 设置回调函数
    p_um->destroy_func = free_upload_msg_cb;
    
    // 上传消息
    m_pUpload->put_msg(p_um);
}

// 释放上传消息
void COracleParser::free_upload_msg_cb(const struct UploadMsg *p_um)
{
    if (!p_um) {
        return;
    }
    
    if (p_um->s) {
        free((void *)p_um->s);
    }
    
    if (p_um->msgtype) {
        free((void *)p_um->msgtype);
    }
    
    free((void *)p_um);
}