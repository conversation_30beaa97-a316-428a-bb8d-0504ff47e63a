/*
 * Oracle协议解析器
 * <AUTHOR> @date 2025
 */

#ifndef __ORACLE_PARSER_H__
#define __ORACLE_PARSER_H__

#include <string>
#include <memory.h>
#include <vector>
#include <map>
#include <inttypes.h>
#include <arpa/inet.h>
#include <pthread.h>
#include <stdint.h>
#include <string.h>
#include <unordered_set>

#include "task_worker.h"
#include "simple_json.h"
#include "pp.h"
#include "gw_i_parser.h"
#include "oracle_parser_common.h"
#include "gw_ver.h"
#include "gw_i_upload.h"

// 前向声明
class OracleTnsParser;
class OracleTtcParser;
class OracleTtiParser;
class OracleVersionCompat;

#define ORACLEPARSER_VER GW_VER_STRING(GW_VER_MAJOR, GW_VER_MINOR, GW_VER_REVISION)

class CWorkerQueue;
class CTaskWorker;
class CTaskWorkerUploadMsg;

#define ORACLE_PARSER_WQ_ORACLE_PARSER_MSG 0
#define ORACLE_PARSER_WQ_UPSTREAM 1
#define ORACLE_PARSER_WQ_MAX_NUM 4

typedef struct oracle_req_info{
    char db_user[64];             // 数据库用户
    char db_name[64];             // 数据库名
    char db_password[64];         // 数据库密码
    char cmd_type[32];            // 操作类型
    char sql_text[4096];          // 原始SQL文本
}oracle_req_info_t;

typedef struct oracle_rsp_info{
    int code;                       // 状态码
    uint64_t start_time;            // 开始时间(毫秒级)
    uint64_t close_time;            // 结束时间(毫秒级)
    int row_count;                  // 影响行数
    struct {
        char **field_names;      // 字段名数组
        char **field_values;     // 字段值数组
        int field_count;         // 字段数量
    } result_set;                // 结果集
}oracle_rsp_info_t;

typedef struct oracle_meta_info{
    uint64_t ts;                    // 时间戳(毫秒级)
    char type[32];                  // 数据库类型，当前解析器为oracleSQL
    char app_name[32];              // 应用名称
    char server_version[64];        // 服务器版本
}oracle_meta_info_t;

typedef struct oracle_mac_info{
    char mac[18];
}oracle_mac_info_t;

typedef struct oracle_net_info{
    unsigned short src_port;
    unsigned short dst_port;
    char a_src_ip[64];
    char a_dst_ip[64];
    char flow_source[64];
}oracle_net_info_t;

// 事件上传数据结构，涵盖了登录事件和访问事件
typedef struct upload_oracle_info : public TaskWorkerData{
    oracle_req_info_t oracle_req_info;
    oracle_rsp_info_t oracle_rsp_info;
    oracle_meta_info_t oracle_meta_info;
    oracle_net_info_t oracle_net_info;
    oracle_mac_info_t oracle_mac_info;
    char a_unique_id[128];
    size_t mem_size;            // 内存大小

    upload_oracle_info() {
        memset(this, 0, sizeof(upload_oracle_info));
        mem_size = sizeof(upload_oracle_info);  // 继承自 TaskWorkerData
    }
}upload_oracle_info_t;

// 统计信息结构
typedef struct stats_oracle_parser
{
    volatile uint64_t cnt_session_total;
    volatile uint64_t cnt_session_closed;
    
    volatile uint64_t cnt_parser_total;
    volatile uint64_t cnt_parser_remaining;
    volatile uint64_t cnt_parser_matched;
    
    volatile uint64_t cnt_login;
    volatile uint64_t cnt_logout;
    volatile uint64_t cnt_select;
    volatile uint64_t cnt_dml;
    volatile uint64_t cnt_ddl;
    volatile uint64_t cnt_commit;
    volatile uint64_t cnt_rollback;
} stats_oracle_t;


struct tcp_stream;
struct oracle_stream;
struct StreamData;
struct UploadMsg;
struct cJSON;
struct net_info;

class CGwCommon;
class CSession;
class CUpload;

class COracleParser : public CParser
{
public:
    COracleParser(void);
    virtual ~COracleParser(void);

public:
    virtual void cache_clean();
    
    // 协议探测方法
    virtual bool probe(CSessionMgt *, const app_stream_t *, const struct conn *, CSession*);
    virtual bool probe_on_close(CSessionMgt *, const app_stream_t *, const struct conn *, CSession*);
    virtual bool probe_on_reset(CSessionMgt *, const app_stream_t *, const struct conn *, CSession*);
    
    // 协议解析方法
    virtual int parse(CSessionMgt *, const app_stream_t *, const struct conn *, CSession*);
    virtual int parse_clear(CSessionMgt *, const app_stream_t *, const struct conn *, CSession*);
    virtual int parse_on_close(CSessionMgt *, const app_stream_t *, const struct conn *, CSession*);
    virtual int parse_on_reset(CSessionMgt *, const app_stream_t *, const struct conn *, CSession*);
    
    // 数据获取和处理方法
    virtual const char *get_data(const struct StreamData *, int dir, int *data_len, int *offset_out);
    virtual bool discard(struct StreamData *, int dir, int num);
    virtual bool discard_and_update(struct StreamData *, int dir, int num);
    virtual void del_session_stream(StreamData *);
    virtual void del_session_param(SessionMgtData *);
    
    // 生命周期管理
    virtual void init();
    virtual void fini();
    virtual void run();
    
    // 信息获取
    virtual const char *get_name(void) const;
    virtual const char *get_version(void) const;
    virtual void set_gw_common(CGwCommon *comm);
    
    // 配置管理
    virtual bool load_conf(const char *);
    virtual void set_quit_signal(void);
    virtual void wait_for_stop(void);
    
    // 过滤规则设置
    virtual void set_accout_filter_rule(CFilterRule *rule);
    virtual void set_upload_filter_rule(CFilterRule *client_rule, CFilterRule *server_rule);
    virtual void set_url_filter_rule(CFilterRule *rule);
    
    // 上游协议处理
    virtual void add_upstream(CParser *parser);
    virtual void reset_upstream(void);
    virtual void push_upstream_msg(char *s, size_t length);
    
    // 状态查询
    virtual bool is_parsed(const struct StreamData *) const;
    virtual struct StreamData *clone_stream_data(const struct StreamData *);
    virtual uint64_t get_parser_http_cnt();
    virtual uint64_t get_succ_parser_http_cnt();
    virtual void* get_parser_status();
    
    // 新增纯虚函数实现
    virtual void set_parser_type(int type);
    virtual void read_conf_urlbase_for_mon();
    virtual void read_conf_filetype_for_mon();
    virtual void get_log_buf(char *log_buf, size_t log_buf_len) const;
    virtual uint32_t parser_status() const;

    
    void free_worker_queue(CWorkerQueue *p);

private:
    // 内部工具方法
    StreamData *get_stream_data_from_session(CSession *p_session, int dir);
    bool is_oracle_protocol(CSession *p_sess, int dir);
    void keep_bstring_data(b_string_t *bstr);
    void set_ip_port(const struct conn *p_conn, oracle_status_t *p_status);
    void free_oracle_parsed_data_in_session(oracle_stream_t *p_stream, int a_dir);
    
    // 协议解析方法
    int parse_tns_packet(const char *pkt, size_t len, int dir, oracle_status_t *p_ora_status, oracle_parsed_data_t *p_result);
    int parse_ttc_message(const char *data, size_t len, int dir, oracle_status_t *p_ora_status, oracle_parsed_data_t *p_result);
    int parse_tti_message(const char *data, size_t len, uint8_t message_type, oracle_status_t *p_ora_status, oracle_parsed_data_t *p_result);

    // 兼容性方法（保留原有接口）
    int parse_connect_packet(const char *pkt, size_t len, oracle_status_t *p_ora_status);
    int parse_data_packet(const char *pkt, size_t len, oracle_status_t *p_ora_status, oracle_parsed_data_t *p_result);
    int parse_login_packet(const char *pkt, size_t len, oracle_status_t *p_ora_status, oracle_parsed_data_t *p_result);
    int parse_sql_packet(const char *pkt, size_t len, oracle_status_t *p_ora_status, oracle_parsed_data_t *p_result);
    
    // 数据组装和发送
    oracle_parsed_data_t *oracle_parsed_data_merge(oracle_parsed_data_t *p_dst, oracle_parsed_data_t *p_src, int src_dir);
    char *bstring_to_cjson_str(const b_string_t *bstr);
    char *oracle_json_dump_event(const oracle_parsed_data_t *p_opd);
    void oracle_send_data(const oracle_parsed_data_t *p_result);
    
    // 会话管理
    void free_session_oracle_parser_data(oracle_stream_t *p_stream, int dir, oracle_parsed_data_t *p_data);
    bool oracle_parser_merge(oracle_stream_t *p_stream, oracle_parsed_data_t *p_data, int from_dir);
    void insert_into_parser_header(oracle_stream_t *p_stream, int dir, oracle_half_stream_t *p_ohs);
    
    // 消息上传
    void oracle_cb_upload_msg(const char *s, const char* msgtype);
    static void free_upload_msg_cb(const struct UploadMsg *p_um);
    
    // 调试工具
    void print_mem(const void * mem, size_t size);
    
    // 数据读取工具函数
    inline uint16_t read_uint2(const char *A)
    {
        return (uint16_t)(((uint8_t)(A[0])) + ((uint8_t)(A[1]) << 8));
    }
    
    inline uint32_t read_uint4(const char *A)
    {
        return (uint32_t)(((uint8_t)(A[0])) +
                        (((uint8_t)(A[1])) << 8) +
                        (((uint8_t)(A[2])) << 16) +
                        (((uint8_t)(A[3])) << 24));
    }
    
    inline uint64_t read_uint8(const char *A)
    {
        return ((uint64_t)(((uint8_t)(A[0])) +
                        (((uint8_t)(A[1])) << 8) +
                        (((uint8_t)(A[2])) << 16) +
                        (((uint8_t)(A[3])) << 24)) +
            (((uint64_t)(((uint8_t)(A[4])) +
                        (((uint8_t)(A[5])) << 8) +
                        (((uint8_t)(A[6])) << 16) +
                        (((uint8_t)(A[7])) << 24))) << 32));
    }
    
    // TNS包长度读取
    inline uint32_t read_tns_packet_length(const char *A)
    {
        return (uint32_t)((uint8_t)(A[0]) + ((uint8_t)(A[1]) << 8) + ((uint8_t)(A[2]) << 16));
    }

    void oracle_check_show_info(CSession *p_session, const struct conn *pcon);
    static void print_oracle_stats_callback(void *p);
    void print_oracle_stats(void);

protected:
    int m_quit_signal;
    CGwCommon *m_comm;
    char m_name[32];
    uint64_t m_u64_oracle_upload_ms;          // 上传时间戳(毫秒)
    uint32_t m_u32_oracle_upload_index;       // 上传索引
    std::string m_str_gw_ip;
    std::string m_conf_upload_name;
    stats_oracle_t m_stats_oracle;
    int m_oracle_type;
    CUpload *m_upload;
    bool m_upload_file;
    CWorkerQueue *m_upload_data_wq;
    CTaskWorker *m_upload_data_wk;
    int m_conf_oracle_upload_queue_max_num;
    int m_conf_oracle_upload_queue_memory_max_size_bytes;
    int m_conf_oracle_upload_thread_num;
    int m_conf_parser_enable;
    int m_conf_oracle_probe_cnt;
    std::unordered_set<std::string> m_agent_client_ip;
    std::string m_agent_client_ip_file_path;
    pthread_rwlock_t m_traffic_source_rwlock;
    int m_conf_pcap_timestamp;

protected:
    friend class CTaskWorkerUploadMsg;
    inline CWorkerQueue *get_wq_upload_msg(void) const
    {
      return m_upload_data_wq;
    }
    CWorkerQueue *new_wq_upload_msg();
    int worker_routine_oracle_upload_data_inner(upload_oracle_info_t*);
    void free_oracle_upload_data(upload_oracle_info_t *p_upload_oracle_info);

    void modify_stats(int enable);
    static void print_oracle_stats_callback(void *p);
    void print_oracle_stats(void) const;

    // 解析统计信息更新辅助函数
    void update_parse_stats(int success, int data_len, int dir = ORACLE_BOTH);
    void update_match_stats(bool success, int dir);
    void update_session_stats(int data_len);

private:
    struct half_stream* get_half_stream(int dir, tcp_stream* a_tcp);
    StreamData *get_stream_data_from_session(CSession *p_session, int dir);
    static void free_upload_msg(const struct UploadMsg *pum);
    void upload_oracle_data(struct StreamData *p_stream_data, const struct conn *pcon, double ts, CSession *p_session);
    void handle_access_event(oracle_stream_t *pgs, const struct conn *pcon, const oracle_parsed_data_t *p_data = NULL);
    void handle_login_event(oracle_stream_t *pgs, const struct conn *pcon, const oracle_parsed_data_t *p_data = NULL);
    void oracle_cb_upload_msg(const char *s, size_t s_len);
    void send_oracle_data(upload_oracle_info_t *p_upload_oracle_info);
    void add_net_json(oracle_net_info_t *p_net_info, const struct conn *pcon, bool reverse);
    char* upload_oracle_data_encode(upload_oracle_info_t& st_upload_oracle_info, size_t *p_s_len);
    void add_event_id(char *p_event_id);

private:
    // Oracle协议解析器组件
    OracleTnsParser *m_tns_parser;
    OracleTtcParser *m_ttc_parser;
    OracleTtiParser *m_tti_parser;
    OracleVersionCompat *m_version_compat;
};

#endif /* __ORACLE_PARSER_H__ */