/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>
#include <unistd.h>
#include <inttypes.h>
#include <sys/types.h>
#include <dirent.h>
#include <sys/stat.h>
#include <errno.h>
#include <sys/sysinfo.h>
#include <sys/time.h>

#include <algorithm>

#include "gw_stats.h"

#include "utils.h"

#include "gw_common.h"
#include "gw_logger.h"

#include "gw_config.h"
#include "gw_i_source.h"
#include "gw_i_parser.h"
#include "gw_i_upload.h"
#include "display_stats_define.h"
#include "get_cpu_mem_info.h"
#include "cJSON.h"
#include "pp.h"
#include "parser/http_parser/http_parser.h"
#include "session_mgt.h"
#include "get_file_type.h"

#include "event_analyze.h"
#include "pp_tcp.h"
#include "tcp_parser.h"
using namespace std;

#define SECONDS_OF_ONE_DAY (86400)
const unsigned int c_qps_data_max = (1 + 24 * 3600);
int g_conf_stream_debug = 0;
int g_conf_single_stream_debug = 0;

// extern stats_app_cnt_t g_app_cnt;

static const char msg_status_type[] = "sys_status";
CIpfilterRule CGwStats::st_collect_flow_ip_white;
//int CGwStats::st_only_collect_ip_white = 0;

static inline void intIp2string(uint32_t addr, char*p_str);

/**
 * CGwStats implementation
 *
 * 状态统计及输出类。
 */

CGwStats::CGwStats(void) : m_thread_report_stat(0)
                         , m_thread_report_stat_stats(0)
                         , m_pthread_qps_stat_stats(0)
                         , m_comm(NULL)
                         , m_quit_signal(0)
                         , m_qps_cur(0)
                         , m_qps_show_idx(0)
                         , m_gw_time_base(0UL)
                         , m_i_source_flag(0)
                         , m_parser(NULL)
                         , m_conf_show_stats(1)
                         , m_collect_stats(0)
                         , m_stats_fp(NULL)
                         , m_conf_limit_mbps(0)
                         , m_conf_part_limit_mbps(0)
                         , m_conf_white_part_limit_mbps(0)
                         , m_stats_speed_limit(0)
                         , m_conf_stream_debug(0)
                         , m_conf_stream_order_field(STREAM_ORDER_BY_FLOW)
                         , m_conf_addr_port_max_num(SHOW_ADDR_PORT_NUM)
                         , m_stats_data_interval(0)
                         , m_stats_run_days(0)
                         , m_stats_file_hold_days(1)
                         , m_last_eth_info({{0}})
{
  g_conf_stream_debug = 0;
  g_conf_single_stream_debug = 0;
  m_conf_stream_order_field = STREAM_ORDER_BY_FLOW;
  m_conf_addr_port_max_num = SHOW_ADDR_PORT_NUM;  
}

CGwStats::~CGwStats(void)
{
  fini();
}

void CGwStats::init()
{
  ASSERT(m_comm != NULL);
  int i_ret = 0;
  m_quit_signal = 0;

  load_conf(NULL);
  i_ret = pre_collect_gw_parser_stats();
  if (i_ret == 0)
  {
    m_collect_stats = 1;
  }
  else
  {
    GWLOG_ERROR(m_comm, "init collect gw parser stats failed\n");
  }
}

void CGwStats::update_eth_info(gw_parser_stats_t & st_gw_parser_stats)
{
  eth_info_t * eth_info = st_gw_parser_stats.a_st_eth_info;
  for (uint32_t i = 0; i < st_gw_parser_stats.u32_eth_num; ++i) {
    uint64_t avg_bytes_per_packet = 0;
    uint64_t diff_pkts  =  eth_info[i].u64_in_total_packets - m_last_eth_info[i].u64_in_total_packets;
    uint64_t diff_bytes =  eth_info[i].u64_in_total_bytes - m_last_eth_info[i].u64_in_total_bytes;
    if (diff_pkts > 0) {
        avg_bytes_per_packet = diff_bytes / diff_pkts;
    }

    if (m_last_eth_info[i].u64_avg_bytes_per_packet == 0) {
      m_last_eth_info[i].u64_avg_bytes_per_packet = avg_bytes_per_packet;
    }
    else {
      m_last_eth_info[i].u64_avg_bytes_per_packet = (m_last_eth_info[i].u64_avg_bytes_per_packet*3 + avg_bytes_per_packet) >> 2;
    }
   
    diff_pkts = eth_info[i].u64_in_err_packets - m_last_eth_info[i].u64_in_err_packets;
    m_last_eth_info[i].u64_in_err_packets = eth_info[i].u64_in_err_packets;
    m_last_eth_info[i].u64_in_err_bytes  += diff_pkts * m_last_eth_info[i].u64_avg_bytes_per_packet;
    eth_info[i].u64_in_err_bytes = m_last_eth_info[i].u64_in_err_bytes;

    diff_pkts = eth_info[i].u64_in_drop_packets - m_last_eth_info[i].u64_in_drop_packets;
    m_last_eth_info[i].u64_in_drop_packets = eth_info[i].u64_in_drop_packets;
    m_last_eth_info[i].u64_in_drop_bytes  += diff_pkts * m_last_eth_info[i].u64_avg_bytes_per_packet;
    eth_info[i].u64_in_drop_bytes = m_last_eth_info[i].u64_in_drop_bytes;

    m_last_eth_info[i].u64_in_total_packets = eth_info[i].u64_in_total_packets;
    m_last_eth_info[i].u64_in_total_bytes = eth_info[i].u64_in_total_bytes;
  }
}

void CGwStats::fini()
{
  ASSERT(m_comm != NULL);

  m_vec_task_name.clear();
  m_map_task_data.clear();
  m_vec_qps_name.clear();
  m_map_qps_count.clear();

  for (std::map<std::string, StatsQpsData *>::iterator iter = m_map_qps_data.begin(); iter != m_map_qps_data.end(); ++iter)
  {
    delete[] iter->second;
  }
  m_map_qps_data.clear();

  st_collect_flow_ip_white.fini();
  if (m_stats_fp)
  {
    fclose(m_stats_fp);
    m_stats_fp = NULL;
  }
  CEventAnalyze::instance().fini();
}

void CGwStats::set_souce(CSource *csource)
{
  ASSERT(csource != NULL);
  m_vec_source.push_back(csource);
}

void CGwStats::run()
{
  int err;
  ASSERT(m_comm != NULL);

  if (m_vec_source.size() == 0)
  {
    GWLOG_WARN(m_comm, "no source for stats\n");
  }
  else
  {
    for (const auto& p_soucre : m_vec_source)
    {
      const std::string str_source_name = p_soucre->get_name();
      if (str_source_name.find("CDpdkSource") != std::string::npos)
      {
        m_i_source_flag |= SOURCE_FLAG_DPDK;
      }
      if (str_source_name.find("CFileSource") != std::string::npos)
      {
        m_i_source_flag |= SOURCE_FLAG_AGENT;
      }
      if (str_source_name.find("CNicSource") != std::string::npos)
      {
        m_i_source_flag |= SOURCE_FLAG_NIC;
      }
      if (str_source_name.find("CPcapSource") != std::string::npos)
      {
        m_i_source_flag |= SOURCE_FLAG_PCAP;
      }
    }
  }

  if (0 != (err = pthread_create(&m_thread_report_stat, NULL, (void *(*)(void *))report_stat_run, this)))
  {
    GWLOG_ERROR(m_comm, "report stat thread create failed %s\n", strerror(err));
  }
  else
  {
    m_thread_report_stat_stats = 1;
    GWLOG_INFO(m_comm, "report stat thread created successfully\n");
  }

  if (0 != (err = pthread_create(&m_thread_qps_stat, NULL, (void *(*)(void *))qps_stat_run, this)))
  {
    GWLOG_ERROR(m_comm, "qps stat thread create failed %s\n", strerror(err));
  }
  else
  {
    m_pthread_qps_stat_stats = 1;
    GWLOG_INFO(m_comm, "qps stat thread created successfully\n");
  }

  CEventAnalyze::instance().run();
}

/**
 * 触发退出信号时处理
 */
void CGwStats::set_quit_signal(void)
{
  m_quit_signal = 1;
  CEventAnalyze::instance().set_quit_signal();
}

/**
 * 等待运行结束
 */
void CGwStats::wait_for_stop(void)
{
  if (m_thread_report_stat_stats == 1)
  {
    pthread_join(m_thread_report_stat, NULL);
    m_thread_report_stat_stats = 0;
  }

  if (m_pthread_qps_stat_stats == 1)
  {
    pthread_join(m_thread_qps_stat, NULL);
    m_pthread_qps_stat_stats = 0;
  }

  CEventAnalyze::instance().wait_for_stop();
}

/**
 * 设置全局公共类对象实例。
 * @param CGwCommon *comm
 */
void CGwStats::set_gw_common(CGwCommon *comm)
{
  m_comm = comm;
  CEventAnalyze::instance().set_gw_common(comm);
}

/**
 * 设置状态输出回调函数
 * @param const char *name
 * @param CALLBACK_STATS cb
 * @param void *ud
 * @param int priority 数字越小越优先显示
 */
void CGwStats::set_stats_callback(const char *name, CALLBACK_STATS cb, void *ud, int priority)
{

  if (likely(cb != NULL))
  {
    m_vec_custom_show_name.push_back(std::make_pair(priority, name));
    std::sort(m_vec_custom_show_name.begin(), m_vec_custom_show_name.end());
    m_map_custom_show_cb[name].first = cb;
    m_map_custom_show_cb[name].second = ud;
    return;
  }

  for (size_t i = 0; i < m_vec_custom_show_name.size(); i++)
  {
    if (m_vec_custom_show_name[i].second == name)
    {
      m_vec_custom_show_name.erase(m_vec_custom_show_name.begin() + i);
      i--;
    }
  }
  m_map_custom_show_cb.erase(name);
}

/**
 * 设置队列任务状态输出对象
 * @param const char *name
 * @param const StatsTaskData*
 * @param int
 */

void CGwStats::set_task(const char *name, CWorkerQueue *pwq, int priority)
{
  if (likely(pwq != NULL))
  {
    m_vec_task_name.push_back(std::make_pair(priority, name));
    std::sort(m_vec_task_name.begin(), m_vec_task_name.end());
    m_map_task_data[name] = pwq;
    return;
  }

  for (size_t i = 0; i < m_vec_task_name.size(); i++)
  {
    if (m_vec_task_name[i].second == name)
    {
      m_vec_task_name.erase(m_vec_task_name.begin() + i);
      i--;
    }
  }
  m_map_task_data.erase(name);
}

/**
 * 设置QPS输出对象，
 * 更新计算值在状态线程中统一处理。
 * @param const char *name
 * @param const volatile uint64_t *
 * @param int priority 数字越小越优先显示
 */
void CGwStats::set_qps(const char *name, const volatile uint64_t *p, int priority)
{
  if (likely(p != NULL))
  {
    m_vec_qps_name.push_back(std::make_pair(priority, name));
    std::sort(m_vec_qps_name.begin(), m_vec_qps_name.end());
    m_map_qps_count[name] = p;
    if (m_map_qps_data.find(name) == m_map_qps_data.end())
    {
      StatsQpsData *psqd;
      m_map_qps_data[name] = psqd = new StatsQpsData[c_qps_data_max] ();

      for (size_t i = 0; i < c_qps_data_max; i++)
      {
        psqd[i].cb = sizeof(StatsQpsData);
      }
    }
    return;
  }

  for (size_t i = 0; i < m_vec_qps_name.size(); i++)
  {
    if (m_vec_qps_name[i].second == name)
    {
      m_vec_qps_name.erase(m_vec_qps_name.begin() + i);
      i--;
    }
  }

  m_map_qps_count.erase(name);
  if (m_map_qps_data.find(name) != m_map_qps_data.end())
  {
    delete[] m_map_qps_data[name];
    m_map_qps_data.erase(name);
  }
}

/**
 * 设置内存使用统计对象，
 * @param const char *name
 * @param const volatile uint64_t *
 * @param const volatile uint64_t *
 * @param int priority 数字越小越优先显示
 */
void CGwStats::set_mem_stat(const char *name, const volatile uint64_t *p_mem, const volatile uint64_t *p_max_mem, int priority)
{
  if (likely(p_mem != NULL && p_max_mem != NULL))
  {
    m_vec_mem_name.push_back(std::make_pair(priority, name));
    std::sort(m_vec_mem_name.begin(), m_vec_mem_name.end());
    m_map_mem_data[name] = p_mem;
    m_map_max_mem_data[name] = p_max_mem;
    return;
  }

  for (size_t i = 0; i < m_vec_mem_name.size(); i++)
  {
    if (m_vec_mem_name[i].second == name)
    {
      m_vec_mem_name.erase(m_vec_mem_name.begin() + i);
      i--;
    }
  }

  m_map_mem_data.erase(name);
  m_map_max_mem_data.erase(name);
}

/**
 * 根据name获取qps值 
 * @param const char *name
 */
uint32_t CGwStats::get_name_qps(const char *name)
{
  ASSERT(name != NULL);
  std::map<std::string, StatsQpsData *>::iterator iter = m_map_qps_data.find(name);
  if (iter != m_map_qps_data.end())
  {
    const StatsQpsData *psqd = iter->second + m_qps_show_idx;
    return ((psqd->qps_last_30/1024/1024)*8);
  }

  return 0;
}


void CGwStats::set_stats(const char* name,const char* branch_name,const volatile uint64_t* value,int priority)
{
  if(unlikely(name == nullptr))
  {
    return;
  }

  if (likely(value!=nullptr))
  {
    std::map<std::string, Stats_Data_t>::iterator it = m_map_stats_data.find(name);
    if(it == m_map_stats_data.end()){
      Stats_Data_t cd;
      m_map_stats_data.insert(std::make_pair(name,cd));
      m_vec_stats_name.push_back(std::make_pair(priority,name));
      std::sort(m_vec_stats_name.begin(),m_vec_stats_name.end());
    }

    if (branch_name != nullptr )
    { 
      Stats_Data_t cd;
      cd.cur_value = value;
      m_map_stats_data[name].branches.insert(std::make_pair(branch_name,cd));  
    }else{
      m_map_stats_data[name].cur_value = value;
    }
    return;
  }
  if(branch_name!= nullptr){
    m_map_stats_data[name].branches.erase(branch_name);
  }else{
    m_map_stats_data.erase(name);
    for(auto it = m_vec_stats_name.begin();it !=m_vec_stats_name.end();++it){
      if(it->second == name){
        m_vec_stats_name.erase(it);
        break;
      }
    }
  }
}

void CGwStats::set_byte_stats(const char* name,const char* branch_name,const volatile uint64_t* value,int priority) {
  if(unlikely(name == nullptr))
  {
    return;
  }

  if (likely(value!=nullptr))
  {
    std::map<std::string, Stats_Data_t>::iterator it = m_map_stats_data.find(name);
    if(it == m_map_stats_data.end()){
      Stats_Data_t cd;
      m_map_stats_data.insert(std::make_pair(name,cd));
      m_vec_bytes_stats_name.push_back(std::make_pair(priority,name));
      std::sort(m_vec_bytes_stats_name.begin(),m_vec_bytes_stats_name.end());
    }

    if (branch_name != nullptr )
    { 
      Stats_Data_t cd;
      cd.cur_value = value;
      m_map_stats_data[name].branches.insert(std::make_pair(branch_name,cd));  
    }else{
      m_map_stats_data[name].cur_value = value;
    }
    return;
  }
  if(branch_name!= nullptr){
    m_map_stats_data[name].branches.erase(branch_name);
  }else{
    m_map_stats_data.erase(name);
    for(auto it = m_vec_bytes_stats_name.begin();it !=m_vec_bytes_stats_name.end();++it){
      if(it->second == name){
        m_vec_bytes_stats_name.erase(it);
        break;
      }
    }
  }
}

/**
 *  获取限流参数 
 */
int CGwStats::get_stats_speed_limit()
{
  return m_stats_speed_limit;
}


/**
 * qps更新计算。
 */
void CGwStats::update(void)
{
  const int qps_data_max = c_qps_data_max;
  int qps_cur = m_qps_cur;
  int qps_idx = qps_cur % c_qps_data_max;
  StatsQpsData *p_qps;
  StatsQpsData *qps_data;

  // int qps_base = 0;
  time_t gw_time_delta = m_comm->gw_time() - m_gw_time_base;
  int delta = (gw_time_delta > 0 ? gw_time_delta : 1);

  if (qps_cur > qps_data_max)
  {
    // qps_base = (qps_cur + qps_data_max - 1) % qps_data_max;
  }

#define CALC_QPS_ALL(p_dst, delta)                   \
  do                                                 \
  {                                                  \
    (p_dst)->qps = (((p_dst)->cnt) * 1.0 / (delta)); \
  } while (0)

#define CALC_QPS_N(p_dst, p_src, mem_qps, delta)                          \
  do                                                                      \
  {                                                                       \
    if (delta < qps_data_max && qps_cur >= delta)                         \
    {                                                                     \
      (p_dst)->mem_qps = (((p_dst)->cnt - (p_src)->cnt) * 1.0 / (delta)); \
    }                                                                     \
    else                                                                  \
    {                                                                     \
      (p_dst)->mem_qps = NAN;                                             \
    }                                                                     \
  } while (0)

#define CALC_QPS()                                                                                        \
  do                                                                                                      \
  {                                                                                                       \
    CALC_QPS_ALL(p_qps, delta);                                                                           \
    CALC_QPS_N(p_qps, &qps_data[(qps_cur - 2 + qps_data_max) % qps_data_max], qps_last_2, 2);             \
    CALC_QPS_N(p_qps, &qps_data[(qps_cur - 30 + qps_data_max) % qps_data_max], qps_last_30, 30);          \
    CALC_QPS_N(p_qps, &qps_data[(qps_cur - 60 + qps_data_max) % qps_data_max], qps_last_60, 60);          \
    CALC_QPS_N(p_qps, &qps_data[(qps_cur - 300 + qps_data_max) % qps_data_max], qps_last_300, 300);       \
    CALC_QPS_N(p_qps, &qps_data[(qps_cur - 1800 + qps_data_max) % qps_data_max], qps_last_1800, 1800);    \
    CALC_QPS_N(p_qps, &qps_data[(qps_cur - 3600 + qps_data_max) % qps_data_max], qps_last_3600, 3600);    \
    CALC_QPS_N(p_qps, &qps_data[(qps_cur - 7200 + qps_data_max) % qps_data_max], qps_last_7200, 7200);    \
    CALC_QPS_N(p_qps, &qps_data[(qps_cur - 28800 + qps_data_max) % qps_data_max], qps_last_28800, 28800); \
    CALC_QPS_N(p_qps, &qps_data[(qps_cur - 43200 + qps_data_max) % qps_data_max], qps_last_43200, 43200); \
    CALC_QPS_N(p_qps, &qps_data[(qps_cur - 86400 + qps_data_max) % qps_data_max], qps_last_86400, 86400); \
  } while (0)

  for (std::map<std::string, StatsQpsData *>::iterator iter = m_map_qps_data.begin(); iter != m_map_qps_data.end(); ++iter)
  {
    qps_data = iter->second;
    p_qps = &iter->second[qps_idx];
    p_qps->cnt = *m_map_qps_count[iter->first];

    // GWLOG_TEST(m_comm, "qps %s=%" PRIu64 "\n", iter->first.c_str(), p_qps->cnt);

    CALC_QPS();

    if (iter->first == IP_FORWATD_BYTES_QPS)
    {
      if (m_conf_limit_mbps > 0 || m_conf_part_limit_mbps > 0 || m_conf_white_part_limit_mbps > 0)
      {
        float mbps = 0;
        int limit_mbps = m_conf_limit_mbps;
        int part_limit_mbps = m_conf_part_limit_mbps;
        int white_part_limit_mbps = m_conf_white_part_limit_mbps;
        // int type = 0;
        int stats_speed_limit = 0;
        const int c_bytes_to_mpbs = (1000 * 1000 / 8);  

        if (0)
        {
        }
        else if (!isnan(p_qps->qps_last_3600))
        {
          mbps = p_qps->qps_last_3600 / c_bytes_to_mpbs;
          // type = 3600;
        }
        else if (!isnan(p_qps->qps_last_60))
        {
          mbps = p_qps->qps_last_60 / c_bytes_to_mpbs;
          // type = 60;
        }
        else if (!isnan(p_qps->qps_last_30))
        {
          mbps = p_qps->qps_last_30 / c_bytes_to_mpbs;
          // type = 30;
        }
        else if (!isnan(p_qps->qps_last_2))
        {
          mbps = p_qps->qps_last_2 / c_bytes_to_mpbs;
          // type = 2;
        }
        else
        {
        }

        if (limit_mbps > 0 && mbps > limit_mbps)
        {
          stats_speed_limit |= 1;
        }
        if (part_limit_mbps > 0 && mbps > part_limit_mbps)
        {
          stats_speed_limit |= 2;
        }
        if (white_part_limit_mbps > 0 && mbps > white_part_limit_mbps)
        {
          stats_speed_limit |= 4;
        }
        m_stats_speed_limit = stats_speed_limit;
      }
      else
      {
        m_stats_speed_limit = 0;
      }
    } 
  }

  m_qps_show_idx = qps_idx;

  m_qps_cur++;
}

int CGwStats::report_stat_run(void *arg_ptr)
{
  CGwStats *pThis = (CGwStats *)arg_ptr;
  ASSERT(pThis != NULL);

  return pThis->report_stat();
}

int CGwStats::report_stat()
{
  time_t started_time = m_comm->gw_time(); // 系统启动时间，作为运行时间的基准
  time_t last_time = m_comm->gw_time();
  int delta = 3;
  int i_ret = 0;

  while (!m_quit_signal)
  {
    // if (time(NULL) > GW_EXPIRE_TIME)
    // {
    //   // 当前时间达到了过期时间
    //   m_is_exprie_time = 1;
    // }
    if (unlikely(m_comm->get_verbose()) > 1)
    { 
      delta = 1;
    }
    else if (unlikely(m_comm->get_verbose()))
    {
      delta = 10;
    }
    else
    {
      delta = 10;
    }

    if (labs(last_time - m_comm->gw_time()) >= delta)
    {

      i_ret = collect_gw_parser_stats(delta); /* 收集状态信息 */
      if (i_ret != 0)
      {
        GWLOG_ERROR(m_comm, "collect gw parser stats failed (%d)\n", i_ret);
      }
      if (unlikely(m_comm->get_verbose()))
      {
        time_t now_gw_tm = m_comm->gw_time();
        time_t now_tm = time(NULL);
        struct tm tm_gm_tm = {0};
        now_tm  += 8 * 60 * 60;
        char buf[256] = {0};
        strftime(buf, sizeof(buf) - 1, "%Y-%m-%d %H:%M:%S CST", gmtime_r(&now_tm, &tm_gm_tm));
        printf("report stat time: %s %ld %ld %ld\n", buf, now_tm, now_gw_tm, now_gw_tm - started_time);
      }
      if (m_conf_show_stats)
      {
        gwp_print_stats();
      }
    
      last_time = m_comm->gw_time();
      if ((last_time - started_time) / SECONDS_OF_ONE_DAY > m_stats_run_days)
      {
        m_stats_run_days++;
        GWLOG_INFO(m_comm, "cut stats file, stats run days = %d\n", m_stats_run_days);
        bool is_del_stats_file = false;
        if (m_stats_run_days > m_stats_file_hold_days)
        {
          is_del_stats_file = true;
        }

        cut_stats_file(is_del_stats_file);
      }
    }

    sleep(1);
  }

  return 0;
}

int CGwStats::qps_stat_run(void *arg_ptr)
{
  CGwStats *pThis = (CGwStats *)arg_ptr;
  ASSERT(pThis != NULL);

  return pThis->qps_stat();
}

int CGwStats::qps_stat()
{

  m_qps_show_idx = 0;
  m_qps_cur = 0;
  m_gw_time_base = m_comm->gw_time();

  while (!m_quit_signal)
  {
    if (NULL != m_tcp_parser)
    {
      m_tcp_parser->stats_ip_fwd_collect();
    }

    update();
    sleep(1);
  }

  return 0;
}

void CGwStats::print_task_stats(void)
{
  char log_buf[LOG_BUF_LEN] = {0};
  get_queue_info(log_buf, LOG_BUF_LEN);
  get_task_info(log_buf, LOG_BUF_LEN);
  printf ("%s", log_buf);

  // if (unlikely(m_comm->get_verbose()))
  // {
  //   if (m_vec_task_name.size() > 0)
  //   {
  //     printf("\n%-30s %12s %12s %12s %12s %12s %12s\n", "stats queue ", "max_num", "num", "success", "failure", "full", "no_mem");
  //   }

  //   for (size_t i = 0; i < m_vec_task_name.size(); i++)
  //   {
  //     char buf[64] = {0};
  //     std::string &name = m_vec_task_name[i].second;
  //     CWorkerQueue *pwq = m_map_task_data[name];
  //     pwq->set_stats_task_data();
  //     const StatsTaskData *pstd = pwq->get_stats_task_data();
  //     //const StatsTaskData *pstd = m_map_task_data[name];

  //     snprintf(buf, COUNTOF(buf) - 1, "%s:", name.c_str());
  //     printf("%-30s %12" PRIu64 " %12" PRIu64 " %12" PRIu64 " %12" PRIu64 " %12" PRIu64 " %12" PRIu64 "\n",
  //            buf,
  //            pstd->q.cnt_q_max_num,
  //            pstd->q.cnt_q_num,
  //            pstd->q.cnt_q_succ,
  //            pstd->q.cnt_q_fail,
  //            pstd->q.cnt_q_full,
  //            pstd->q.cnt_q_no_mem);
  //   }
  // }
  // else
  // {
  //   if (m_vec_task_name.size() > 0)
  //   {
  //     printf("\n%-30s %12s %12s %12s %12s %12s\n", "stats queue ", "max_num", "num", "success", "failure", "full");
  //   }

  //   for (size_t i = 0; i < m_vec_task_name.size(); i++)
  //   {
  //     char buf[64] = {0};
  //     std::string &name = m_vec_task_name[i].second;
  //     //const StatsTaskData *pstd = m_map_task_data[name];
  //     CWorkerQueue *pwq = m_map_task_data[name];
  //     pwq->set_stats_task_data();
  //     const StatsTaskData *pstd = pwq->get_stats_task_data();

  //     snprintf(buf, COUNTOF(buf) - 1, "%s:", name.c_str());
  //     printf("%-30s %12" PRIu64 " %12" PRIu64 " %12" PRIu64 " %12" PRIu64 " %12" PRIu64 "\n",
  //            buf,
  //            pstd->q.cnt_q_max_num,
  //            pstd->q.cnt_q_num,
  //            pstd->q.cnt_q_succ,
  //            pstd->q.cnt_q_fail,
  //            pstd->q.cnt_q_full);
  //   }
  // }

  // if (m_vec_task_name.size() > 0)
  // {
  //   printf("\n%-30s %12s %12s %12s\n", "stats task ", "total", "success", "failure");
  // }

  // for (size_t i = 0; i < m_vec_task_name.size(); i++)
  // {
  //   char buf[64] = {0};
  //   std::string &name = m_vec_task_name[i].second;
  //   //const StatsTaskData *pstd = m_map_task_data[name];
  //   CWorkerQueue *pwq = m_map_task_data[name];
  //   const StatsTaskData *pstd = pwq->get_stats_task_data();

  //   snprintf(buf, COUNTOF(buf) - 1, "%s:", name.c_str());
  //   printf("%-30s %12" PRIu64 " %12" PRIu64 " %12" PRIu64 "\n",
  //          buf,
  //          pstd->cnt,
  //          pstd->cnt_succ,
  //          pstd->cnt_fail);
  // }

  // if (unlikely(m_comm->get_verbose()))
  // {
  //   if (m_vec_task_name.size() > 0)
  //   {
  //     printf("\n%-30s %12s %12s %12s\n", "stats task failure ", "failure2", "failure3", "failure4");
  //   }

  //   for (size_t i = 0; i < m_vec_task_name.size(); i++)
  //   {
  //     char buf[64] = {0};
  //     std::string &name = m_vec_task_name[i].second;
  //     //const StatsTaskData *pstd = m_map_task_data[name];
  //     CWorkerQueue *pwq = m_map_task_data[name];
  //     const StatsTaskData *pstd = pwq->get_stats_task_data();

  //     snprintf(buf, COUNTOF(buf) - 1, "%s:", name.c_str());
  //     printf("%-30s %12" PRIu64 " %12" PRIu64 " %12" PRIu64 "\n",
  //            buf,
  //            pstd->cnt_fail2,
  //            pstd->cnt_fail3,
  //            pstd->cnt_fail4);
  //   }
  // }
}

void CGwStats::get_queue_info (char* log_buf, size_t log_buf_len)
{
  if (unlikely(m_comm->get_verbose()))
  {
    if (m_vec_task_name.size() > 0)
    {
      snprintf(log_buf + strlen(log_buf), log_buf_len - 1 - strlen(log_buf), "\n%-30s %12s %12s %12s %12s %12s %12s\n", "stats queue "
                                                                                                                      , "max_num"
                                                                                                                      , "num"
                                                                                                                      , "success"
                                                                                                                      , "failure"
                                                                                                                      , "full"
                                                                                                                      , "no_mem");
    }

    for (size_t i = 0; i < m_vec_task_name.size(); i++)
    {
      char buf[64] = {0};
      std::string &name = m_vec_task_name[i].second;
      CWorkerQueue *pwq = m_map_task_data[name];
      pwq->set_stats_task_data();
      const StatsTaskData *pstd = pwq->get_stats_task_data();
      //const StatsTaskData *pstd = m_map_task_data[name];

      snprintf(buf, COUNTOF(buf) - 1, "%s:", name.c_str());
      snprintf(log_buf + strlen(log_buf), log_buf_len - 1 - strlen(log_buf), "%-30s %12" PRIu64 " %12" PRIu64 " %12" PRIu64 " %12" PRIu64 " %12" PRIu64 " %12" PRIu64 "\n", buf
                                                                                                                                                                          , pstd->q.cnt_q_max_num
                                                                                                                                                                          , pstd->q.cnt_q_num
                                                                                                                                                                          , pstd->q.cnt_q_succ
                                                                                                                                                                          , pstd->q.cnt_q_fail
                                                                                                                                                                          , pstd->q.cnt_q_full
                                                                                                                                                                          , pstd->q.cnt_q_no_mem);
    }
  }
  else
  {
    if (m_vec_task_name.size() > 0)
    {
      snprintf(log_buf + strlen(log_buf), log_buf_len - 1 - strlen(log_buf), "\n%-30s %12s %12s %12s %12s %12s\n", "stats queue "
                                                                                                                 , "max_num"
                                                                                                                 , "num", "success"
                                                                                                                 , "failure"
                                                                                                                 , "full");
    }

    for (size_t i = 0; i < m_vec_task_name.size(); i++)
    {
      char buf[64] = {0};
      std::string &name = m_vec_task_name[i].second;
      //const StatsTaskData *pstd = m_map_task_data[name];
      CWorkerQueue *pwq = m_map_task_data[name];
      pwq->set_stats_task_data();
      const StatsTaskData *pstd = pwq->get_stats_task_data();

      snprintf(buf, COUNTOF(buf) - 1, "%s:", name.c_str());
      snprintf(log_buf + strlen(log_buf), log_buf_len - 1 - strlen(log_buf), "%-30s %12" PRIu64 " %12" PRIu64 " %12" PRIu64 " %12" PRIu64 " %12" PRIu64 "\n", buf
                                                                                                                                                            , pstd->q.cnt_q_max_num
                                                                                                                                                            , pstd->q.cnt_q_num
                                                                                                                                                            , pstd->q.cnt_q_succ
                                                                                                                                                            , pstd->q.cnt_q_fail
                                                                                                                                                            , pstd->q.cnt_q_full);
    }
  }
}

void CGwStats::get_task_info(char* log_buf, size_t log_buf_len)
{
  if (m_vec_task_name.size() > 0)
  {
    snprintf(log_buf + strlen(log_buf), log_buf_len - 1 - strlen(log_buf), "\n%-30s %12s %12s %12s\n", "stats task"
                                                                                                     , "total"
                                                                                                     , "success"
                                                                                                     , "failure");
  }

  for (size_t i = 0; i < m_vec_task_name.size(); i++)
  {
    char buf[64] = {0};
    std::string &name = m_vec_task_name[i].second;
    CWorkerQueue *pwq = m_map_task_data[name];
    const StatsTaskData *pstd = pwq->get_stats_task_data();

    snprintf(buf, COUNTOF(buf) - 1, "%s:", name.c_str());
    snprintf(log_buf + strlen(log_buf), log_buf_len - 1 - strlen(log_buf), "%-30s %12" PRIu64 " %12" PRIu64 " %12" PRIu64 "\n", buf
                                                                                                                              , pstd->cnt
                                                                                                                              , pstd->cnt_succ
                                                                                                                              , pstd->cnt_fail);
  }

  if (unlikely(m_comm->get_verbose()))
  {
    if (m_vec_task_name.size() > 0)
    {
      printf(log_buf + strlen(log_buf), log_buf_len - 1 - strlen(log_buf), "\n%-30s %12s %12s %12s\n", "stats task failure "
                                                                                                    , "failure2"
                                                                                                    , "failure3"
                                                                                                    , "failure4");
    }

    for (size_t i = 0; i < m_vec_task_name.size(); i++)
    {
      char buf[64] = {0};
      std::string &name = m_vec_task_name[i].second;
      //const StatsTaskData *pstd = m_map_task_data[name];
      CWorkerQueue *pwq = m_map_task_data[name];
      const StatsTaskData *pstd = pwq->get_stats_task_data();

      snprintf(buf, COUNTOF(buf) - 1, "%s:", name.c_str());
      printf(log_buf + strlen(log_buf), log_buf_len - 1 - strlen(log_buf), "%-30s %12" PRIu64 " %12" PRIu64 " %12" PRIu64 "\n", buf
                                                                                                                              , pstd->cnt_fail2
                                                                                                                              , pstd->cnt_fail3
                                                                                                                              , pstd->cnt_fail4);
    }
  }
}

void CGwStats::print_qps_stats(void)
{
  char log_buf[LOG_BUF_LEN] = {0};
  get_qps_info(log_buf, LOG_BUF_LEN);
  printf ("%s", log_buf);
  // int qps_idx = m_qps_show_idx; //  % c_qps_data_max;

  // if (m_vec_qps_name.size() > 0)
  // {
  //   printf("\n%-24s %13s %13s %13s %13s\n", "stats qps ", "all", "2s", "1m", "1h");
  // }

  // for (size_t i = 0; i < m_vec_qps_name.size(); i++)
  // {
  //   char buf[64] = {0};
  //   std::string &name = m_vec_qps_name[i].second;
  //   const StatsQpsData *psqd = m_map_qps_data[name] + qps_idx;

  //   snprintf(buf, COUNTOF(buf) - 1, "%s:", name.c_str());
  //   printf("%-24s %13.2f %13.2f %13.2f %13.2f\n",
  //          buf,
  //          psqd->qps,
  //          psqd->qps_last_2,
  //          psqd->qps_last_60,
  //          psqd->qps_last_3600);
  // }

  // if (unlikely(m_comm->get_verbose()))
  // {
  //   if (m_vec_qps_name.size() > 0)
  //   {
  //     printf("\n%-24s %13s %13s %13s %13s\n", "stats qps2 ", "30s", "5m", "2h", "1d");
  //   }
  //   for (size_t i = 0; i < m_vec_qps_name.size(); i++)
  //   {
  //     char buf[64] = {0};
  //     std::string &name = m_vec_qps_name[i].second;
  //     const StatsQpsData *psqd = m_map_qps_data[name] + qps_idx;

  //     snprintf(buf, COUNTOF(buf) - 1, "%s:", name.c_str());
  //     printf("%-24s %13.2f %13.2f %13.2f %13.2f\n",
  //            buf,
  //            psqd->qps_last_30,
  //            psqd->qps_last_300,
  //            psqd->qps_last_7200,
  //            psqd->qps_last_86400);
  //   }
  // }
}

void CGwStats::get_qps_info(char* log_buf, size_t log_buf_len)
{
  int qps_idx = m_qps_show_idx; //  % c_qps_data_max;

  if (m_vec_qps_name.size() > 0)
  {
    snprintf(log_buf + strlen(log_buf), log_buf_len - 1 - strlen(log_buf), "\n%-24s %13s %13s %13s %13s\n", "stats qps "
                                                                                                          , "all"
                                                                                                          , "2s"
                                                                                                          , "1m"
                                                                                                          , "1h");
  }

  for (size_t i = 0; i < m_vec_qps_name.size(); i++)
  {
    char buf[64] = {0};
    std::string &name = m_vec_qps_name[i].second;
    const StatsQpsData *psqd = m_map_qps_data[name] + qps_idx;

    snprintf(buf, COUNTOF(buf) - 1, "%s:", name.c_str());
    snprintf(log_buf + strlen(log_buf), log_buf_len - 1 - strlen(log_buf), "%-24s %13.2f %13.2f %13.2f %13.2f\n", buf
                                                                                                                , psqd->qps
                                                                                                                , psqd->qps_last_2
                                                                                                                , psqd->qps_last_60
                                                                                                                , psqd->qps_last_3600);
  }

  if (unlikely(m_comm->get_verbose()))
  {
    if (m_vec_qps_name.size() > 0)
    {
      snprintf(log_buf + strlen(log_buf), log_buf_len - 1 - strlen(log_buf),"\n%-24s %13s %13s %13s %13s\n", "stats qps2 "
                                                                                                           , "30s"
                                                                                                           , "5m"
                                                                                                           , "2h"
                                                                                                           , "1d");
    }
    for (size_t i = 0; i < m_vec_qps_name.size(); i++)
    {
      char buf[64] = {0};
      std::string &name = m_vec_qps_name[i].second;
      const StatsQpsData *psqd = m_map_qps_data[name] + qps_idx;

      snprintf(buf, COUNTOF(buf) - 1, "%s:", name.c_str());
      snprintf(log_buf + strlen(log_buf), log_buf_len - 1 - strlen(log_buf), "%-24s %13.2f %13.2f %13.2f %13.2f\n", buf
                                                                                                                  , psqd->qps_last_30
                                                                                                                  , psqd->qps_last_300
                                                                                                                  , psqd->qps_last_7200
                                                                                                                  , psqd->qps_last_86400);
    }
  }
}

void CGwStats::print_mem_info(void)
{
  char log_buf[LOG_BUF_LEN] = {0};
  get_mem_info(log_buf, LOG_BUF_LEN);
  printf ("%s", log_buf);
}

void CGwStats::get_mem_info(char* log_buf, size_t log_buf_len)
{
    for (size_t i = 0; i < m_vec_mem_name.size(); i++)
    {
      char buf[64] = {0};
      std::string &name = m_vec_mem_name[i].second;
      const volatile uint64_t *p_mem = m_map_mem_data[name];
      const volatile uint64_t *p_max_mem = m_map_max_mem_data[name];

      snprintf(buf, COUNTOF(buf) - 1, "%s:", name.c_str());
      snprintf(log_buf + strlen(log_buf), log_buf_len - 1 - strlen(log_buf), "%s memory size: %" PRIu64 "M; max memory size: %" PRIu64 "M\n",
             buf,
             (uint64_t)(*p_mem / (1024ULL * 1024ULL)),
             (uint64_t)(*p_max_mem / (1024ULL * 1024ULL)));
    }
}

static inline void intIp2string(uint32_t addr, char*p_str) 
{
  uint8_t ip0 = (addr & 0x000000ff);
  uint8_t ip1 = (addr & 0x0000ff00) >> 8;
  uint8_t ip2 = (addr & 0x00ff0000) >> 16;
  uint8_t ip3 = (addr & 0xff000000) >> 24;
  sprintf(p_str, "%d.%d.%d.%d", ip3, ip2, ip1, ip0);
}

void CGwStats::gwp_print_stats(void)
{
  for (size_t i = 0; i < m_vec_custom_show_name.size(); i++)
  {
    custom_show_cb_t cb = m_map_custom_show_cb[m_vec_custom_show_name[i].second];
    (*cb.first)(cb.second);
  }

  print_task_stats();

  print_qps_stats();

  if (unlikely(m_comm->get_verbose()))
  {
    get_gene_file_cnt();
    print_mem_info();
  }

  if (m_conf_stream_debug) 
  {
    char src_ip_str[32] = {0};
    char dst_ip_str[32] = {0};
    printf("stream debug:\r\n");
    printf("%-8s%24s%24s%16s%16s%8s\r\n", "id", "src_ip", "dst_ip", "dst_port", "total_flow", "count");
    struct AddrPortCount *items = (struct AddrPortCount *)malloc(m_conf_addr_port_max_num * sizeof(struct AddrPortCount));
    int num = collect_top_addr_port_items(&items[0], m_conf_addr_port_max_num, m_conf_stream_order_field);
    for (int i = 0; i < num; ++ i) 
    {
      intIp2string(items[i].src_addr, &src_ip_str[0]);
      intIp2string(items[i].dst_addr, &dst_ip_str[0]);
      printf("%-8d%24s%24s%16hu%16lu%8lu\r\n", i, src_ip_str, dst_ip_str, items[i].dst_port, items[i].total, items[i].count);
    }

    printf("\r\n");
    free(items);
    items = NULL;
  }

}

/**
 * 加载配置参数（Json字符串，支持动态）。
 * @param const char *
 */
bool CGwStats::load_conf(const char *json_string)
{
  CGwConfig *pgwc = m_comm->get_gw_config();

  // 从配置文件中读取参数
  m_conf_show_stats = pgwc->read_conf_int("parser", "show_stats", 1);
  m_str_stats_path = pgwc->read_conf_string("parser", "stats_dir");
  if (m_str_stats_path.empty())
  {
    m_str_stats_path = "/opt/stats/";
  }
  m_str_stats_file = pgwc->read_conf_string("parser", "stats_file");
  if (m_str_stats_file.empty())
  {
    m_str_stats_file = "stats.file";
  }
  m_stats_data_interval = pgwc->read_conf_int("parser","stats_data_interval",600);

  m_conf_limit_mbps = pgwc->read_conf_int("parser", "limit_mbps", m_conf_limit_mbps);
  m_conf_part_limit_mbps = pgwc->read_conf_int("parser", "part_limit_mbps", m_conf_part_limit_mbps);
  m_conf_white_part_limit_mbps = pgwc->read_conf_int("parser", "white_part_limit_mbps", m_conf_white_part_limit_mbps);
  m_conf_stream_debug = pgwc->read_conf_int("parser", "stream_debug", 0);
  g_conf_single_stream_debug = pgwc->read_conf_int("parser", "single_stream_debug", 0);
  if ((g_conf_stream_debug == 1) && (m_conf_stream_debug == 0))
  {
    clear_addr_port_items();
  }
  
  g_conf_stream_debug = m_conf_stream_debug;

  std::string collect_flow_ip_white = pgwc->read_conf_string("parser", "collect_flow_ip_white");
  st_collect_flow_ip_white.set_ip_white(collect_flow_ip_white.c_str());
  //st_only_collect_ip_white = pgwc->read_conf_int("parser", "only_collect_ip_white", 0);

  std::string order_field = pgwc->read_conf_string("parser", "stream_order_field");
  if (strcmp(order_field.c_str(), "count") == 0)
  {
    m_conf_stream_order_field = STREAM_ORDER_BY_COUNT;
  }

  m_conf_addr_port_max_num = pgwc->read_conf_int("parser", "add_port_max_num", m_conf_addr_port_max_num);
  return true;
}

int CGwStats::pre_collect_gw_parser_stats(void)
{
  /* 判断g_stats_filename_path目录是否存在，存在则删除目录所有的状态文件，不存在在创建目录 */
  // int i_ret = 0;
  DIR *p_dir = NULL;
  char a_buf[PATH_MAX];
  memset(a_buf, 0, sizeof(a_buf));
  p_dir = opendir(m_str_stats_path.c_str());
  if (p_dir == NULL)
  {
    if (errno == ENOENT) /* 目录不存在 */
    {
      /* 创建目录 */
      sprintf(a_buf, "mkdir -p %s", m_str_stats_path.c_str());
      system(a_buf);
    }
    else
    {
      GWLOG_ERROR(m_comm, "open (%s) failed\n", m_str_stats_path.c_str());
      return -1;
    }
  }
  else
  {
    closedir(p_dir);
    remove_dir(m_str_stats_path.c_str());
  }

  /* 创建文件 */
  sprintf(a_buf, "%s%s", m_str_stats_path.c_str(), m_str_stats_file.c_str());
  m_stats_fp = fopen(a_buf, "w");
  if (m_stats_fp == NULL)
  {
    return -1;
  }

  return 0;
}

int CGwStats::collect_gw_parser_stats(int delta)
{
  int i_ret = 0;
  gw_parser_stats_t st_gw_parser_stats;
  memset(&st_gw_parser_stats, 0, sizeof(st_gw_parser_stats));

  pid_t pid = getpid();                 /* 进程号 */
  uint32_t u32_core_num = get_nprocs(); /* 获取可用核的个数 */

  for (auto pcsource : m_vec_source)
  {
    i_ret = pcsource->collect_info(&st_gw_parser_stats);
    if (i_ret != 0)
    {
      GWLOG_ERROR(m_comm, "collect %s info failed(%d)\n", pcsource->get_name(), i_ret);
      return i_ret;
    }
  }
  if (m_i_source_flag & SOURCE_FLAG_DPDK)
  {
    update_eth_info(st_gw_parser_stats);
  }

  /* 获取进程内部运行的数据内容 */
  std::map<std::string, StatsQpsData *>::iterator iter;
  iter =  m_map_qps_data.find(IP_BYTES_QPS);
  if (iter != m_map_qps_data.end())
  {
    const StatsQpsData *psqd = iter->second + m_qps_show_idx;
    st_gw_parser_stats.f_recv_bytes_speed = psqd->qps_last_2;            /* 网口接收数据的速率 */
  }
  else
  {
    st_gw_parser_stats.f_recv_bytes_speed = 0;
  }
  if (m_parser)
  {
    st_gw_parser_stats.u64_parse_http_cnt = m_parser->get_parser_http_cnt();
    st_gw_parser_stats.u64_succ_parse_http_cnt = m_parser->get_succ_parser_http_cnt();
  } else {
    st_gw_parser_stats.u64_parse_http_cnt = 0;
    st_gw_parser_stats.u64_succ_parse_http_cnt = 0;
  }

  iter = m_map_qps_data.find(HTTP_GZIP_QPS);
  if (iter != m_map_qps_data.end())
  {
    const StatsQpsData *psqd = iter->second + m_qps_show_idx;
    st_gw_parser_stats.f_http_gzip_speed = psqd->qps_last_2;
    st_gw_parser_stats.u64_http_gzip_num = psqd->cnt;
  }
  else
  {
    st_gw_parser_stats.f_http_gzip_speed = 0;
  }

  iter = m_map_qps_data.find(HTTP_SESSSION_QPS);
  if (iter != m_map_qps_data.end())
  {
    const StatsQpsData *psqd = iter->second + m_qps_show_idx;
    st_gw_parser_stats.f_parse_http_speed = psqd->qps_last_2;
  }
  else
  {
    st_gw_parser_stats.f_parse_http_speed = 0;
  }

    iter = m_map_qps_data.find(POSTGRE_SESSION_QPS);
    if (iter != m_map_qps_data.end())
    {
      const StatsQpsData *psqd = iter->second + m_qps_show_idx;
      st_gw_parser_stats.f_parse_postgre_speed = psqd->qps_last_2;
    }
    else
    {
      st_gw_parser_stats.f_parse_postgre_speed = 0;
    }

  iter = m_map_qps_data.find(UPLOAD_MSA_QPS);
  if (iter != m_map_qps_data.end())
  {
    const StatsQpsData *psqd = iter->second + m_qps_show_idx;
    st_gw_parser_stats.f_up_kafka_succ_speed = psqd->qps_last_2;
    st_gw_parser_stats.u64_up_kafka_cnt = psqd->cnt;
  }
  else
  {
    st_gw_parser_stats.f_up_kafka_succ_speed = 0;
    st_gw_parser_stats.u64_up_kafka_cnt = 0;
  }

  iter = m_map_qps_data.find(UPLOAD_FORWARD_BYTES_QPS);
  if (iter != m_map_qps_data.end())
  {
    const StatsQpsData *psqd = iter->second + m_qps_show_idx;
    st_gw_parser_stats.f_up_kafka_succ_bytes_speed = psqd->qps_last_2;
  }
  else
  {
    st_gw_parser_stats.f_up_kafka_succ_bytes_speed = 0;
  }

  auto it_work = m_map_task_data.find(UPLOAD_MSG);
  if (it_work != m_map_task_data.end())
  {
    st_gw_parser_stats.u64_up_kafak_succ_cnt = it_work->second->get_stats_task_data()->cnt_succ;
  } else
  {
     st_gw_parser_stats.u64_up_kafak_succ_cnt = 0;
  }
  

  //获取当前毫秒
  get_timeval(&st_gw_parser_stats.u64_time_val);
  get_proc_cpu_usage(u32_core_num, pid, &st_gw_parser_stats.f_cpu_usage);                                /* 当前CPU使用率 */
  get_proc_mem_usage(pid, &st_gw_parser_stats.f_mem_usage);
  collect_top_addr_port_items(&st_gw_parser_stats.addr_port_cnts[0], SHOW_ADDR_PORT_NUM, m_conf_stream_order_field);

  unsigned int cache_size = m_stats_data_interval/delta;
  for(auto it = m_map_stats_data.begin();it!=m_map_stats_data.end();++it){
    Stats_Data_t &data = it->second;
    uint64_t tmp = *(data.cur_value);
    data.cache_value.push_back(tmp);
    if (data.cache_value.size()>cache_size)
    {
      data.cache_value.erase(data.cache_value.begin(),data.cache_value.begin()+(data.cache_value.size()-cache_size));
    }
    
    for (auto bit=data.branches.begin(); bit != data.branches.end(); ++bit)
    {
      Stats_Data_t &bdata = bit->second;
      tmp = *(bdata.cur_value);
      bdata.cache_value.push_back(tmp);
      if (bdata.cache_value.size()>cache_size)
      {
        bdata.cache_value.erase(bdata.cache_value.begin(),bdata.cache_value.begin()+(bdata.cache_value.size()-cache_size));
      }
    }
    
  }

  /* 将结构体转换成Json格式 */
  cJSON *p_json_gw_parser_info = struct_to_json(&st_gw_parser_stats);
  if (p_json_gw_parser_info)
  {
    stats_data_to_json(p_json_gw_parser_info);
    char *p_json_buf = cJSON_PrintUnformattedWithBufferSize(p_json_gw_parser_info, 4096);
    if (m_stats_fp != NULL)
    {
      fprintf(m_stats_fp, "%s\n", p_json_buf);
    }
    // upload_msg(p_json_buf,msg_status_type);
    cJSON_free(p_json_buf);
    cJSON_Delete(p_json_gw_parser_info);
  }

  return 0;
}

void CGwStats::stats_data_to_json(cJSON* root)
{
  for(auto it = m_vec_stats_name.begin();it != m_vec_stats_name.end();++it)
  {
    std::string name = it->second;
    Stats_Data_t &data = m_map_stats_data[name];
    cJSON* dinfo = cJSON_CreateObject();
    if (dinfo == NULL)
    {
      return;
    }
    
    cJSON_AddNumberToObject(dinfo,"index",it->first);
    uint64_t drop_total = 0;
    cJSON* drops = cJSON_CreateObject();
    if (data.branches.size()>0)
    {
      for (auto bit = data.branches.begin(); bit != data.branches.end(); bit++)
      {
        const std::string &rname = bit->first;
        Stats_Data_t &rdata = bit->second;
        uint64_t drop_n = rdata.cache_value.back()-rdata.cache_value.front();
        drop_total += drop_n;
        cJSON_AddNumberToObject(drops,rname.c_str(), drop_n);
      }
    }
    cJSON_AddItemToObject(dinfo,"drop",drops);
    uint64_t total = data.cache_value.back()-data.cache_value.front();
    cJSON_AddNumberToObject(dinfo,"total", MAX(total, drop_total));
    cJSON_AddItemToObject(root,name.c_str(),dinfo);
  }

  for(auto it = m_vec_bytes_stats_name.begin();it != m_vec_bytes_stats_name.end();++it)
  {
    std::string name = it->second;
    Stats_Data_t &data = m_map_stats_data[name];
    cJSON* dinfo = cJSON_CreateObject();
    if (dinfo == NULL)
    {
      return;
    }
    
    cJSON_AddNumberToObject(dinfo,"index",it->first);
    cJSON_AddNumberToObject(dinfo,"total",data.cache_value.back()-data.cache_value.front());
    cJSON* drops = cJSON_CreateObject();
    if (data.branches.size()>0)
    {
      for (auto bit = data.branches.begin(); bit != data.branches.end(); bit++)
      {
        const std::string &rname = bit->first;
        Stats_Data_t &rdata = bit->second;
        cJSON_AddNumberToObject(drops,rname.c_str(),rdata.cache_value.back()-rdata.cache_value.front());
      }
    }
    cJSON_AddItemToObject(dinfo,"detail",drops);
    cJSON_AddItemToObject(root,name.c_str(),dinfo);
  }
}

cJSON *CGwStats::struct_to_json(const gw_parser_stats_t *p_st_gw_parser_info)
{
  /* 创建gw_parser_info JSON对象 */
  cJSON *p_json_gw_parser_info;

  uint32_t u32_index = 0;
  p_json_gw_parser_info = cJSON_CreateObject();
  if (p_json_gw_parser_info == NULL)
  {
    GWLOG_ERROR(m_comm, "create json object failed\n");
    return NULL;
  }
  cJSON_AddNumberToObject(p_json_gw_parser_info, "time_val", p_st_gw_parser_info->u64_time_val);                /* 时间戳 */
  cJSON_AddNumberToObject(p_json_gw_parser_info, "recv_bytes_speed", p_st_gw_parser_info->f_recv_bytes_speed);  /* 网口接收数据速率 */
  cJSON_AddNumberToObject(p_json_gw_parser_info, "parser_http_cnt", p_st_gw_parser_info->u64_parse_http_cnt);   /* 解析http数量 */
  cJSON_AddNumberToObject(p_json_gw_parser_info, "parser_http_speed", p_st_gw_parser_info->f_parse_http_speed); /* 解析http速率 */
  cJSON_AddNumberToObject(p_json_gw_parser_info, "parser_postgre_speed", p_st_gw_parser_info->f_parse_postgre_speed); /* 解析PostgreSQL速率 */
  cJSON_AddNumberToObject(p_json_gw_parser_info, "parser_http_zip_num", p_st_gw_parser_info->u64_http_gzip_num);
  cJSON_AddNumberToObject(p_json_gw_parser_info, "parser_http_zip_speed", p_st_gw_parser_info->f_http_gzip_speed);
  cJSON_AddNumberToObject(p_json_gw_parser_info, "parser_succ_http_cnt", p_st_gw_parser_info->u64_succ_parse_http_cnt);
  cJSON_AddNumberToObject(p_json_gw_parser_info, "up_kafka_cnt", p_st_gw_parser_info->u64_up_kafka_cnt);
  cJSON_AddNumberToObject(p_json_gw_parser_info, "up_succ_kafka_cnt", p_st_gw_parser_info->u64_up_kafak_succ_cnt);
  cJSON_AddNumberToObject(p_json_gw_parser_info, "up_succ_kafka_speed", p_st_gw_parser_info->f_up_kafka_succ_speed);
  cJSON_AddNumberToObject(p_json_gw_parser_info, "up_succ_kafka_bytes_speed", p_st_gw_parser_info->f_up_kafka_succ_bytes_speed);
  cJSON_AddNumberToObject(p_json_gw_parser_info, "cpu_usage", p_st_gw_parser_info->f_cpu_usage);
  cJSON_AddNumberToObject(p_json_gw_parser_info, "mem_usage", p_st_gw_parser_info->f_mem_usage);
  cJSON_AddNumberToObject(p_json_gw_parser_info, "source_flag", m_i_source_flag);
  if (m_i_source_flag & SOURCE_FLAG_DPDK)
  {
    cJSON *p_json_eth_info_array;
    cJSON *p_json_eth_info;
    cJSON_AddNumberToObject(p_json_gw_parser_info, "eth_num", p_st_gw_parser_info->u32_eth_num); /* 网口数量 */
    p_json_eth_info_array = cJSON_CreateArray();
    if (p_json_eth_info_array == NULL)
    {
      GWLOG_ERROR(m_comm, "create json array failed\n");
      cJSON_Delete(p_json_gw_parser_info);
      return NULL;
    }

    for (u32_index = 0; u32_index < p_st_gw_parser_info->u32_eth_num; ++u32_index)
    {
      p_json_eth_info = cJSON_CreateObject();
      if (p_json_eth_info == NULL)
      {
        cJSON_Delete(p_json_eth_info_array);
        cJSON_Delete(p_json_gw_parser_info);
        return NULL;
      }
      cJSON_AddNumberToObject(p_json_eth_info, "eth_speed", p_st_gw_parser_info->a_st_eth_info[u32_index].u64_eth_speed);
      cJSON_AddNumberToObject(p_json_eth_info, "total_packets", p_st_gw_parser_info->a_st_eth_info[u32_index].u64_in_total_packets);
      cJSON_AddNumberToObject(p_json_eth_info, "err_packets", p_st_gw_parser_info->a_st_eth_info[u32_index].u64_in_err_packets);
      cJSON_AddNumberToObject(p_json_eth_info, "err_bytes", p_st_gw_parser_info->a_st_eth_info[u32_index].u64_in_err_bytes);
      cJSON_AddNumberToObject(p_json_eth_info, "drop_packets", p_st_gw_parser_info->a_st_eth_info[u32_index].u64_in_drop_packets);
      cJSON_AddNumberToObject(p_json_eth_info, "drop_bytes", p_st_gw_parser_info->a_st_eth_info[u32_index].u64_in_drop_bytes);
      cJSON_AddNumberToObject(p_json_eth_info, "total_bytes", p_st_gw_parser_info->a_st_eth_info[u32_index].u64_in_total_bytes);
      cJSON_AddStringToObject(p_json_eth_info, "card_stat", p_st_gw_parser_info->a_st_eth_info[u32_index].a_net_card_stat);
      cJSON_AddItemToArray(p_json_eth_info_array, p_json_eth_info);
    }
    cJSON_AddItemToObject(p_json_gw_parser_info, "eth_info", p_json_eth_info_array);
  }
  if (m_i_source_flag & SOURCE_FLAG_AGENT)
  {
    cJSON *p_json_pcap_probe_info = NULL;
    p_json_pcap_probe_info = cJSON_CreateObject();
    if (p_json_pcap_probe_info == NULL)
    {
      GWLOG_ERROR(m_comm, "create pcap porbe josn obj failed\n");
      cJSON_Delete(p_json_gw_parser_info);
      return NULL;
    }
    cJSON_AddNumberToObject(p_json_pcap_probe_info, "pcap_file_num", p_st_gw_parser_info->st_pcap_probe_info.u64_file_num);
    cJSON_AddNumberToObject(p_json_pcap_probe_info, "pcap_succ_file_num", p_st_gw_parser_info->st_pcap_probe_info.u64_file_succ_num);
    cJSON_AddNumberToObject(p_json_pcap_probe_info, "pcap_fail_file_num", p_st_gw_parser_info->st_pcap_probe_info.u64_file_fail_num);
    cJSON_AddItemToObject(p_json_gw_parser_info, "pcap_probe_info", p_json_pcap_probe_info);
  }
  if ((m_i_source_flag & SOURCE_FLAG_NIC) || (m_i_source_flag & SOURCE_FLAG_PCAP))
  {
    cJSON *p_json_nic_info_array = cJSON_CreateArray();
    if (p_json_nic_info_array == NULL)
    {
      GWLOG_ERROR(m_comm, "create json array failed\n");
      cJSON_Delete(p_json_gw_parser_info);
      return NULL;
    }
    for (u32_index=0; u32_index < p_st_gw_parser_info->u32_nic_num; u32_index++)
    {
      cJSON *p_json_nic_info = cJSON_CreateObject();
      if (p_json_nic_info == NULL)
      {
        GWLOG_ERROR(m_comm, "create pcap porbe josn obj failed\n");
        cJSON_Delete(p_json_nic_info_array);
        cJSON_Delete(p_json_gw_parser_info);
        return NULL;
      }
      cJSON_AddStringToObject(p_json_nic_info, "device_name", p_st_gw_parser_info->a_st_nic_info[u32_index].ch_device_name);
      //镜像模式统一使用total_packets表示recv_packets
      cJSON_AddNumberToObject(p_json_nic_info, "total_packets",  p_st_gw_parser_info->a_st_nic_info[u32_index].u64_recv_packets);
      cJSON_AddNumberToObject(p_json_nic_info, "drop_packets",  p_st_gw_parser_info->a_st_nic_info[u32_index].u64_drop_packets);
      if (0 == p_st_gw_parser_info->a_st_nic_info[u32_index].u16_speed)
      {
        cJSON_AddStringToObject(p_json_nic_info, "eth_speed", "1000/10000");
      }
      else
      {
        cJSON_AddNumberToObject(p_json_nic_info, "eth_speed", p_st_gw_parser_info->a_st_nic_info[u32_index].u16_speed);
      }
      cJSON_AddStringToObject(p_json_nic_info, "card_stat", p_st_gw_parser_info->a_st_nic_info[u32_index].status);
      cJSON_AddItemToArray(p_json_nic_info_array, p_json_nic_info);
    }
    cJSON_AddItemToObject(p_json_gw_parser_info, "eth_info", p_json_nic_info_array);
  }



  if (m_conf_stream_debug) {
    cJSON *p_addr_port_cnt_arr = NULL;
    cJSON *p_addr_port_cnt = NULL;
    p_addr_port_cnt_arr = cJSON_CreateArray();
    if (p_addr_port_cnt_arr == NULL) {
        GWLOG_ERROR(m_comm, "create addr port josn array failed\n");
        cJSON_Delete(p_json_gw_parser_info);
        return NULL;
      }
    for (u32_index = 0; u32_index < SHOW_ADDR_PORT_NUM; ++ u32_index) 
    {
      p_addr_port_cnt = cJSON_CreateObject();
      if (p_addr_port_cnt == NULL) 
      {
        GWLOG_ERROR(m_comm, "create addr port josn obj failed\n");
        cJSON_Delete(p_addr_port_cnt_arr);
        cJSON_Delete(p_json_gw_parser_info);
        return NULL;
      }
      if (0 == p_st_gw_parser_info->addr_port_cnts[u32_index].total) 
      {
        cJSON_Delete(p_addr_port_cnt);
        break;
      }
      cJSON_AddNumberToObject(p_addr_port_cnt, "addr", p_st_gw_parser_info->addr_port_cnts[u32_index].dst_addr);
      cJSON_AddNumberToObject(p_addr_port_cnt, "port", p_st_gw_parser_info->addr_port_cnts[u32_index].dst_port);
      cJSON_AddNumberToObject(p_addr_port_cnt, "total", p_st_gw_parser_info->addr_port_cnts[u32_index].total);
      cJSON_AddNumberToObject(p_addr_port_cnt, "count", p_st_gw_parser_info->addr_port_cnts[u32_index].count);
      cJSON_AddItemToArray(p_addr_port_cnt_arr, p_addr_port_cnt);
    }
    cJSON_AddItemToObject(p_json_gw_parser_info, "addr_port_counts", p_addr_port_cnt_arr);
  }

  return p_json_gw_parser_info;
}

int CGwStats::get_timeval(uint64_t *p_u64_timaval)
{
  int i_ret = 0;
  struct timeval st_time;
  i_ret = gettimeofday(&st_time, NULL);
  if (i_ret != 0)
  {
    printf("get timeval failed(%d)\n", i_ret);
    return -1;
  }

  *p_u64_timaval = st_time.tv_sec * 1000 + st_time.tv_usec / 1000;
  return 0;
}

void CGwStats::cut_stats_file(bool is_del_stats_file)
{
  int i_ret = 0;
  if (m_stats_fp == NULL)
  {
    return ;
  }
  fclose(m_stats_fp);
  m_stats_fp = NULL;

  /* 将g_stats_filename重命名 */
  char a_old_path[PATH_MAX];
  char a_new_path[PATH_MAX];
  memset(a_old_path, 0, sizeof(a_old_path));
  memset(a_new_path, 0, sizeof(a_new_path));
  sprintf(a_old_path, "%s%s", m_str_stats_path.c_str(), m_str_stats_file.c_str());
  strncpy(a_new_path, a_old_path, strlen(a_old_path));
  struct tm *p_st_now_time = NULL;
  time_t new_time;
  new_time = time(NULL);
  p_st_now_time = localtime(&new_time);
  sprintf(a_new_path + strlen(a_new_path), "_%02d_%02d_%02d_%02d_%02d", p_st_now_time->tm_mon + 1, p_st_now_time->tm_mday, p_st_now_time->tm_hour, p_st_now_time->tm_min, p_st_now_time->tm_sec);

  i_ret = rename(a_old_path, a_new_path);
  if (i_ret != 0)
  {
    GWLOG_ERROR(m_comm, "rename failed(old path = %s, new path = %s, errno = %d)\n", a_old_path, a_new_path, errno);
    return ;
  }

  /* 创建新的文件 */
  m_stats_fp = fopen(a_old_path, "w");
  if (m_stats_fp == NULL)
  {
    GWLOG_ERROR(m_comm, "create file failed\n");
    return ;
  }

  /* 判断是否需要删除文件，超过两个星期就需要删除最旧的文件 */
  if (is_del_stats_file)
  {
    DIR *p_dir = NULL;
    struct dirent *p_dirent = NULL;
    char a_del_filename[PATH_MAX];
    memset(a_del_filename, 0, sizeof(a_del_filename));
    char a_dir_filename[PATH_MAX];
    memset(a_dir_filename, 0, sizeof(a_dir_filename));
    struct stat st_file_stat;
    memset(&st_file_stat, 0, sizeof(struct stat));
    uint64_t u64_file_oldest_time = ULLONG_MAX;
    p_dir = opendir(m_str_stats_path.c_str());
    if (p_dir == NULL)
    {
      GWLOG_ERROR(m_comm, "open dir failed(%s)\n", m_str_stats_path.c_str());
      return ;
    }

    while ((p_dirent = readdir(p_dir)) != NULL)
    {
      if (strcmp(p_dirent->d_name, ".") == 0 || strcmp(p_dirent->d_name, "..") == 0)
      {
        continue;
      }

      // if (p_dirent->d_type & DT_DIR) /* 如果是目录则continue */
      // {
      //   continue;
      // }
      sprintf(a_dir_filename, "%s%s", m_str_stats_path.c_str(), p_dirent->d_name);
      i_ret = stat(a_dir_filename, &st_file_stat);
      if (i_ret != 0)
      {
        GWLOG_ERROR(m_comm, "get (%s) stat failed (%d)\n", a_dir_filename, errno);
        closedir(p_dir);
        return ;
      }

      if (!S_ISREG(st_file_stat.st_mode))
      {
        continue;
      }
    
      if ((uint64_t)st_file_stat.st_mtim.tv_sec < u64_file_oldest_time)
      {
        u64_file_oldest_time = st_file_stat.st_ctim.tv_sec;
        memcpy(a_del_filename, a_dir_filename, sizeof(a_del_filename));
      }
    }
    closedir(p_dir);
    i_ret = unlink(a_del_filename);
    if (i_ret != 0)
    {
      GWLOG_ERROR(m_comm, "del file(%s) failed (%d, errno = %d)", a_del_filename, i_ret, errno);
      return ;
    }
  }

  return ;
}


void CGwStats::add_addr_port(void * psm, uint32_t dst_addr, uint16_t dst_port, uint32_t src_addr, uint16_t src_port, uint32_t flow, int first_link)
{
  CSessionMgt *p_session_mgt = (CSessionMgt*)psm;
  if (p_session_mgt == NULL) 
  {
    return;
  }

  p_session_mgt->add_addr_port(dst_addr, dst_port, src_addr, src_port, flow, first_link);

  // if (st_only_collect_ip_white) {
  //   if (st_collect_flow_ip_white.ip_white_hit(src_addr)) {
  //   p_session_mgt->add_addr_port(src_addr, src_port, flow, first_link);
  //   p_session_mgt->add_addr_port(dst_addr, dst_port, flow, first_link);
  //   }
  //   else if (st_collect_flow_ip_white.ip_white_hit(dst_addr)) {
  //     p_session_mgt->add_addr_port(dst_addr, dst_port, flow, first_link);
  //   }
  // }
  // else {
  //   p_session_mgt->add_addr_port(dst_addr, dst_port, flow, first_link);
  //   if (st_collect_flow_ip_white.ip_white_hit(src_addr)) {
  //     p_session_mgt->add_addr_port(src_addr, src_port, flow, first_link);
  //   }
  // }
  
}

void CGwStats::send_single_msg(void * psm, void * addr, int trigger_condition, int only_where, int reverse)
{
  CSessionMgt *p_session_mgt = (CSessionMgt*)psm;
  if (p_session_mgt == NULL) 
  {
    return;
  }
}

int CGwStats::collect_top_addr_port_items(struct AddrPortCount* rets, int num, int order_field)
{
  int session_mgt_num = m_comm->get_session_mgt_num();
  if (session_mgt_num <= 0) {
    return 0;
  }
  AddrPortCount *addr_port_cnts = (AddrPortCount *)malloc(session_mgt_num * m_conf_addr_port_max_num * sizeof(AddrPortCount));
  memset(addr_port_cnts, 0x00, session_mgt_num * m_conf_addr_port_max_num * sizeof(AddrPortCount));
  int collect_num = 0;
  int i = 0;
  for (i = 0; i < session_mgt_num; ++i) {
    CSessionMgt *psm = m_comm->get_session_mgt(i);
    int current_collect = psm->get_top_addr_port_items(&addr_port_cnts[collect_num], m_conf_addr_port_max_num, order_field);
    collect_num += current_collect;
  }

  sort(&addr_port_cnts[0], &addr_port_cnts[collect_num], [&order_field](const struct AddrPortCount &a, const struct AddrPortCount& b) { 
                                                                                    if (order_field == STREAM_ORDER_BY_COUNT) return a.count > b.count;
                                                                                    return a.total > b.total;});
  int num_new = num < collect_num ? num : collect_num;
  for (i = 0;i < num_new; i += 1) {
      rets[i] = addr_port_cnts[i];
        if (addr_port_cnts[i].total == 0) {
          i += 1;
          break;
      }
  }

  free(addr_port_cnts);
  return i; 

}

void CGwStats::clear_addr_port_items() 
{
  int session_mgt_num = m_comm->get_session_mgt_num();
  
  int i = 0;
  for (i = 0; i < session_mgt_num; ++i)
  {
    CSessionMgt *psm = m_comm->get_session_mgt(i);
    psm->clear_addr_port_items();
  }
}


void CGwStats::put_event_msg(bool is_request,const char* content,int content_len){
  CEventAnalyze::instance().put_event_msg(is_request,content,content_len);
}


void CGwStats::put_upload_msg(UploadMsg *p){
  CEventAnalyze::instance().put_upload_msg(p);
}


bool CGwStats::check_analyze_log(uint32_t source_ip,uint32_t dest_ip,uint16_t dest_port){
  return CEventAnalyze::instance().check_analyze_log(source_ip,dest_ip,dest_port);
}

uint32_t CGwStats::get_analyze_flag(){
  return CEventAnalyze::instance().get_analyze_flag();
}

void CGwStats::set_http_parser(CParser *cparser){
  assert(!m_parser);
  m_parser = cparser;
}

void CGwStats::set_tcp_parser(CTcpParser *tcp_parser)
{
  m_tcp_parser = tcp_parser;
}