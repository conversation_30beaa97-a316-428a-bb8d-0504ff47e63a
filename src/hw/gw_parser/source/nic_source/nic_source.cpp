/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#include <stdio.h>
#include <string.h>
#include <stdlib.h>

#include "nic_source.h"
#include "gw_logger.h"
#include "gw_common.h"
#include "gw_stats.h"
#include "display_stats_define.h"
#include "gw_config.h"
#include "gw_stats.h"
#include "display_stats_define.h"
#include "capture_data.h"
#include "utils.h"
#include "cpp_utils.h"
#include "tcp_parser.h"

CNicSource::CNicSource(void) : m_quit_signal(0)
                             , m_comm(NULL)
                             , m_tcp_parser(NULL)
{
    memset(m_name, 0, sizeof(m_name));
    snprintf(m_name, COUNTOF(m_name) - 1, "CNicSource-%" PRIu64 "", ((uint64_t)this) & 0xffff);
    memset(m_ca, 0, sizeof(m_ca));
}

CNicSource::~CNicSource(void)
{

}

/**
 * 模块初始时调用
 */
void CNicSource::init()
{
    ASSERT(m_comm != NULL);
    m_quit_signal = 0;
    g_quit_signal = 0;

    load_conf(NULL);

    if (m_comm->get_verbose())
    {
        for (auto iter = m_v_nic_name.begin(); iter != m_v_nic_name.end(); ++iter)
        {
            GWLOG_INFO(m_comm, "device name = %s\n", (*iter).c_str());
        }
        GWLOG_INFO(m_comm, "pcap direction = %d\n", g_conf_pcap_direction);
        GWLOG_INFO(m_comm, "pcap ip work queue num = %u\n", g_u32_ip_work_queue_num);
        GWLOG_INFO(m_comm, "pcap tcp work queue num = %u\n", g_u32_tcp_work_queue_num);
        GWLOG_INFO(m_comm, "zero copy = %u\n", g_pfring_zero_copy);
    }
    /* 设置nic网口信息 */
    m_comm->get_gw_stats()->set_stats_callback(STATS_CAP, print_cap_stats_callback, this);
}

void CNicSource::fini()
{
    ASSERT(m_comm != NULL);
}

void CNicSource::run()
{
    int err = 0;
    ASSERT(m_comm != NULL);

    if (0 != (err = pthread_create(&m_thread_worker_run, NULL, (void *(*)(void *))worker_run, this)))
    {
        GWLOG_ERROR(m_comm, "nic source thread create failed %s\n", strerror(err));
    }
    else
    {
        GWLOG_INFO(m_comm, "nic source thread created successfully\n");
    }
}

const char *CNicSource::get_name(void) const
{
    return m_name;
}

const char *CNicSource::get_version(void) const
{
  return NICSOURCE_VER;
}

void CNicSource::set_gw_common(CGwCommon *comm)
{
    ASSERT(comm != NULL);
    m_comm = comm;
}

bool CNicSource::load_conf(const char *)
{
    ASSERT(m_comm != NULL);
    CGwConfig *pgwc = m_comm->get_gw_config();
    ASSERT(pgwc != NULL);
    std::string nic_name_aggregation = pgwc->read_conf_string("parser", "nic_device_name");

    parser_network_device(nic_name_aggregation);

    g_conf_pcap_work_thread_num = pgwc->read_conf_int("parser", "nic_tcp_work_thread", 1);
    if (g_conf_pcap_work_thread_num > 8)
    {
      GWLOG_ERROR(m_comm, "nic_tcp_work_thread must be less than or equal to 8, set to 8\n");
      g_conf_pcap_work_thread_num = 8;
    }
    g_conf_pcap_direction = pgwc->read_conf_int("parser", "nic_pcap_direction", g_conf_pcap_direction);
    g_u32_ip_work_queue_num = pgwc->read_conf_int("parser", "nic_ip_work_queue_num", g_u32_ip_work_queue_num);
    g_u32_tcp_work_queue_num = pgwc->read_conf_int("parser", "nic_tcp_work_queue_num", g_u32_tcp_work_queue_num);
    g_pfring_zero_copy = pgwc->read_conf_int("parser", "nic_zero_copy", g_pfring_zero_copy);
    g_tcp_worker_timeout = pgwc->read_conf_int("parser", "tcp_worker_timeout", g_tcp_worker_timeout);

	std::string tunnel_type = pgwc->read_conf_string("parser", "nic_tunnel_type");
	if (tunnel_type == "vxlan")
	{
		g_u32_tunnel_type = NIC_TUNNEL_VXLAN;
	}
	if (tunnel_type == "erspan")
	{
		g_u32_tunnel_type = NIC_TUNNEL_ERSPAN;
	}

    g_pfring_open_fail_reload_driver = pgwc->read_conf_int("parser", "pfring_open_fail_reload_driver", g_pfring_open_fail_reload_driver);
    g_pfring_driver_check_fail_quit = pgwc->read_conf_int("parser", "pfring_driver_check_fail_quit", g_pfring_driver_check_fail_quit);

    // g_mtu = pgwc->read_conf_int("parser", "nic_mtu", g_mtu);
    std::string nic_mtu = pgwc->read_conf_string("parser", "nic_mtu");
    parser_nic_mtu(nic_mtu);

    return true;
}

void CNicSource::set_quit_signal(void)
{
    m_quit_signal = 1;
    g_quit_signal = 1;
}

void CNicSource::wait_for_stop(void)
{
    pthread_join(m_thread_worker_run, NULL);
}

int CNicSource::set_ip_callback(CALLBACK_PKT cb_ip, void *userdata_ip)
{
    m_ca->cb_ip = cb_ip;
    m_ca->userdata_ip = userdata_ip;

    return 0;
}

int CNicSource::set_tcp_callback(CALLBACK_PCAP cb_tcp, void *userdata_tcp)
{
    m_ca->cb_tcp = cb_tcp;
    m_ca->userdata_tcp = userdata_tcp;

    return 0;
}

void CNicSource::set_sample_callback(int mode, int rate, CALLBACK_SAMPLE cb_sample)
{
    set_sample_args(mode, rate, cb_sample);
    return;
}

void CNicSource::set_flow_callback(CALLBACK_FLOW cb_flow)
{
    m_ca->cb_flow = cb_flow;
}

int CNicSource::collect_eth_info(eth_info_t *p_st_eth_info, uint32_t *p_work_port_cnt)
{
    return 0;
}

int CNicSource::collect_pcap_probe_info(pcap_probe_info_t *p_st_probe_info)
{
    return 0;
}

int CNicSource::collect_info(gw_parser_stats_t *parser_stats)
{
  return collect_pcap_stats(parser_stats->a_st_nic_info, &parser_stats->u32_nic_num);
}

void CNicSource::set_tcp_parser(CTcpParser *tcp_parser)
{
    ASSERT(tcp_parser != NULL);
    m_tcp_parser = tcp_parser;
}

/**
 * 获取日志输出信息
 */
void CNicSource::get_log_buf(char *log_buf, size_t log_buf_len)
{
    get_nic_log_buf(log_buf, log_buf_len);
}


int CNicSource::source_status() const
{
    return nic_status();
}

int CNicSource::worker_run(void *arg_ptr)
{
    CNicSource *pThis = (CNicSource *)arg_ptr;
    ASSERT(pThis != NULL);

    GWLOG_DEBUG(pThis->m_comm, "enter %s()\n", __FUNCTION__);
    pcap_main(pThis->m_ca);
    GWLOG_DEBUG(pThis->m_comm, "leave %s()\n", __FUNCTION__);

    return 0;
}

void CNicSource::print_cap_stats_callback(void *arg_ptr)
{
    CNicSource *pThis = (CNicSource *)arg_ptr;
    ASSERT(pThis != NULL);
    pThis->print_cap_stats();
}

void CNicSource::print_cap_stats(void) const
{
    print_pcap_stats();
}

void CNicSource::parser_network_device(std::string nic_name_aggregation)
{
    m_v_nic_name = split_string(nic_name_aggregation, ",");

    if (m_v_nic_name.empty())
    {
        return;
    }

    g_network_device_num = MIN(m_v_nic_name.size(), COUNTOF(g_a_device_name));

    for (size_t i = 0; i < g_network_device_num; i++)
    {
        g_a_device_name[i] = m_v_nic_name[i].c_str();
    }
}

void CNicSource::parser_nic_mtu(std::string nic_mtu)
{
    std::vector<std::string> nic_mtu_array = split_string(nic_mtu, ",");

    if (nic_mtu_array.empty())
    {
        return;
    }

    memset(g_mtu, 0, sizeof(g_mtu));
    int nic_mtu_array_num = MIN(nic_mtu_array.size(), COUNTOF(g_mtu));

    int i = 0;
    for (; i < nic_mtu_array_num; i++)
    {
        g_mtu[i] = atoi(nic_mtu_array[i].c_str());
    }
}

void CNicSource::cache_clean()
{
    cache_ip_queue_clean();
    cache_tcp_queue_clean();
}
