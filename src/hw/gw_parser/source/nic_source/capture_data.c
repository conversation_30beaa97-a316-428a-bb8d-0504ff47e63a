#define _GNU_SOURCE 
#include <pfring.h>
#include <pcap.h>
#include <string.h>
#include <pthread.h>
#include <time.h>
#include <stdint.h>
#include <stdlib.h>
#include <unistd.h>
#include <netinet/in.h>
#include <netinet/ip.h>
#include <linux/udp.h>
#include <linux/if_ether.h>
#include <sched.h>

#include "capture_data.h"
#include "cap_data.h"
#include "pkt_queue.h"
#include "pcap_header.h"
#include "utils.h"

#define CAP_LEN (9728)
#define SCHE_TABLE_SIZE 256
#define MAX_WORKER_THREAD 64
#define FILTER_STR_LEN 256
#define SLEEP_MAX 500000
#define TCP_WORKER_DATA_WAIT 10 //us

typedef char uint24_t [3];

//const char *g_a_device_name = "";

int g_ip_cache_clean = 0;
int g_tcp_cache_clean = 0;

const char *g_a_device_name[MAX_NETWORK_DEVICE_NUM] = {NULL};
int g_quit_signal = 0;
int g_conf_pcap_work_thread_num = 1;
int g_conf_pcap_direction = 0;
uint32_t g_u32_ip_work_queue_num = 1000000;
uint32_t g_u32_tcp_work_queue_num = 1000000;

static CALLBACK_PKT g_cb_ip = NULL;
static CALLBACK_PCAP g_cb_tcp = NULL;
static CALLBACK_FLOW g_cb_flow = NULL;
static queue_root_t *g_p_ip_work_queue = NULL;
static queue_root_t *g_p_tcp_queue[MAX_WORKER_THREAD] = {0};

static pfring *g_p_pfring_handle[MAX_NETWORK_DEVICE_NUM] = {NULL};
uint32_t g_network_device_num = 0;
uint32_t g_pfring_zero_copy = 1;
uint32_t g_pfring_open_fail_reload_driver = 0;
uint32_t g_pfring_driver_check_fail_quit = 0;
uint16_t g_mtu[8] = {15854, 9728, 1518, 0};
uint16_t g_tcp_worker_timeout = 0;

uint32_t g_u32_tunnel_type = NIC_TUNNEL_NULL;
nic_info_t g_device_info[MAX_NETWORK_DEVICE_NUM];

typedef struct 
{
    pkt_info_t st_pkt_info;
    uint32_t u32_sec;
    uint32_t u32_usec;
} cap_pkt_info_t;

#ifdef ARM
cap_pkt_info_t** g_ip_worker_queue = NULL;
uint32_t g_ip_worker_queue_count = 0;
pthread_mutex_t g_ip_worker_queue_mutex;
#endif

typedef struct
{
    void *p_data;          /* pcap数据内容 */
    uint32_t u32_pcap_len; /* pcap数据内容长度 */
    uint32_t u32_seconds;
    uint32_t u32_microseconds;
    int i_work_idx; /* tcp处理线程索引 */
} queue_pcap_t;

#ifdef ARM
queue_pcap_t** g_tcp_worker_queue[MAX_WORKER_THREAD] = {NULL};
uint32_t g_tcp_worker_queue_count[MAX_WORKER_THREAD] = {0};
pthread_mutex_t g_tcp_worker_queue_mutex[MAX_WORKER_THREAD];
#endif

typedef struct 
{
    volatile uint64_t u64_rx_pkts;
    volatile uint64_t u64_enqueue_pkts;
    volatile uint64_t u64_enqueue_failed_pkts;
}recv_pkt_t;

typedef struct
{
    volatile uint64_t u64_dequeue_pkts;
    volatile uint64_t u64_enqueue_pkts;
    volatile uint64_t u64_enqueue_failed_pkts;
}ip_wrk_t;

typedef struct 
{
    volatile uint64_t u64_dequeue_pkts;
}tcp_wrk_t;

typedef struct 
{
    recv_pkt_t st_recv_pkt_stats;
    ip_wrk_t st_ip_wrk_stats;
    tcp_wrk_t st_tcp_wrk_stats;
}pcap_stats_t;

typedef struct 
{
    volatile uint8_t vxlan_flags;        /* vxlan校验位 */
    uint24_t vxlan_reserved1;            /* 保留区域1 */
    uint24_t vxlan_vni;                  /* vxlan网络标识，用于vxlan分组 */
    volatile uint8_t vxlan_reserved2;    /* 保留区域2 */
}vxlan_hdr;

#pragma pack(1) 
typedef struct
{
    struct ethhdr eth_hdr;         /* enthernet header 二 */
    struct ip ip_hdr;              /* ip头 */
    struct udphdr udp_hdr;         /* udp报文头 */
    vxlan_hdr vxlan_hdr0;    /* vxlan报文头 */
}vxlan_parser_hdr;
#pragma pack()

typedef struct
{
    unsigned  int checksum:1;
    unsigned  int routing:1;
    unsigned  int key:1;
    unsigned  int seq_num:1;
    unsigned  int strict_source_route:1;
    unsigned  int recursion:3;
    unsigned  int flags:5;
    unsigned  int version:3;
    unsigned  int protocol:16;
    unsigned  int sequence_num;
}gre_hdr;

typedef struct
{
    unsigned int vlan_1:4;
    unsigned int ver:4;
    unsigned int vlan_2:8;
    unsigned int cos:3;
    unsigned int en:2;
    unsigned int t:1;
    unsigned int session_id:10;
    unsigned int reserved:12;
    unsigned int index:20;
}erspan_hdr2;

typedef struct
{   
    unsigned int vlan_1:4;
    unsigned int ver:4;
    unsigned int vlan_2:8;
    unsigned int cos:3;
    unsigned int bos:2;
    unsigned int t:1;
    unsigned int session_id:10;
    unsigned int timestamp;
    unsigned int sgt:16;
    unsigned int p:1;
    unsigned int ft:5;
    unsigned int hw_id:6;
    unsigned int d:1;
    unsigned int gra:2;
    unsigned int o:1;
}erspan_hdr3;

#pragma pack(1)
typedef struct
{
    struct ethhdr eth_hdr;         /* enthernet header 二 */
    struct ip ip_hdr;              /* ip头 */
    gre_hdr gre_hdr;
    erspan_hdr2 erspan_hdr;
}erspan_parser_hdr2;
#pragma pack()

#pragma pack(1)
typedef struct
{
    struct ethhdr eth_hdr;         /* enthernet header 二 */
    struct ip ip_hdr;              /* ip头 */
    gre_hdr gre_hdr; 
    erspan_hdr3 erspan_hdr;
}erspan_parser_hdr3;
#pragma pack()

typedef struct 
{
    unsigned int crc;  /* data */
}erspan_crc;

pcap_stats_t g_st_pcap_stats;

static inline DEFINE_Q_DESTROY(queue_destroy_complete_pcap, queue_pcap_t);
static inline DEFINE_Q_FLUSH(queue_flush_complete_pcap, queue_pcap_t);

static int g_i_sample_mode = 0;
static int g_i_sample_rate = 0;
static CALLBACK_SAMPLE g_cb_sample = (CALLBACK_SAMPLE)NULL;
/* 设置数据包采样参数信息以及队列信息 */
void set_sample_args(int i_sample_mode, int i_sample_rate, CALLBACK_SAMPLE cb_sample)
{
  /* check param */
  if (cb_sample == NULL || i_sample_mode < 0 || i_sample_rate < 0)
  {
    return;
  }
//   RTE_LOG(NOTICE, GWHW, "sample_mode = %d, sample_rate = %d, \n", i_sample_mode, i_sample_rate);
  g_i_sample_mode = i_sample_mode;
  g_i_sample_rate = i_sample_rate;
  g_cb_sample = cb_sample;
  // if (g_i_sample_mode == 1)
  // {
  //   g_i_is_write_pcap_header = 1;
  // }
}


__attribute__((unused)) static void print_pcap_info(const struct pfring_pkthdr *p_pkthdr)
{
    printf("timestamp sec = %lu, usec = %lu\n", p_pkthdr->ts.tv_sec, p_pkthdr->ts.tv_usec);
    printf("capture packet len = %u\n", p_pkthdr->caplen);
    printf("wire packet len = %u\n", p_pkthdr->len);
}

static void free_queue_ip_wrk(void* p)
{
    cap_pkt_info_t *p_st_cap_pkt_info = (cap_pkt_info_t*)p;
    if (NULL == p_st_cap_pkt_info)
    {
        return;
    }

    if (g_pfring_zero_copy == 0)
    {
        if (p_st_cap_pkt_info->st_pkt_info.buf != NULL)
        {
            free(p_st_cap_pkt_info->st_pkt_info.buf);
        }
    }
    free(p_st_cap_pkt_info);
}

static void free_queue_pcap(queue_pcap_t *p_st_queue_pcap)
{
    if (p_st_queue_pcap == NULL)
    {
        return;
    }

    if (g_pfring_zero_copy == 0)
    {
        free(p_st_queue_pcap->p_data);
        p_st_queue_pcap->p_data = NULL;
    }
    free(p_st_queue_pcap);
    p_st_queue_pcap = NULL;

    return;
}

static void parser_vxlan(cap_pkt_info_t *p_st_cap_pkt_info){
    //判断长度是不是大于50 是不是UDP协议 端口是不是 4789 标识是不是1
    if (p_st_cap_pkt_info->st_pkt_info.size <= sizeof(vxlan_parser_hdr))
    {
        return ;
    } 

    vxlan_parser_hdr* info_vxlan_parser_hdr = (vxlan_parser_hdr*)p_st_cap_pkt_info->st_pkt_info.buf;
    if (0x11 != info_vxlan_parser_hdr->ip_hdr.ip_p) 
    {   
        return;
    }

    //因为大小端问题导致0x12b5 变为 0xb512
    if( 0xb512 !=  info_vxlan_parser_hdr->udp_hdr.dest )
    {
        return;
    }

    if ((0x08&info_vxlan_parser_hdr->vxlan_hdr0.vxlan_flags) != 0x08)
    {
        return;
    }

    char *newbuf = (char*)malloc(p_st_cap_pkt_info->st_pkt_info.size);

    if (NULL == newbuf) {
        fprintf(stderr, "malloc buffer failed\n");
        return ;
    }

    memcpy(newbuf, p_st_cap_pkt_info->st_pkt_info.buf+sizeof(vxlan_parser_hdr), p_st_cap_pkt_info->st_pkt_info.size-sizeof(vxlan_parser_hdr));
    memcpy(newbuf+p_st_cap_pkt_info->st_pkt_info.size-sizeof(vxlan_parser_hdr), p_st_cap_pkt_info->st_pkt_info.buf, sizeof(vxlan_parser_hdr));
    memcpy(p_st_cap_pkt_info->st_pkt_info.buf, newbuf, p_st_cap_pkt_info->st_pkt_info.size);
    p_st_cap_pkt_info->st_pkt_info.size -= sizeof(vxlan_parser_hdr);
    p_st_cap_pkt_info->st_pkt_info.pkt_size -= sizeof(vxlan_parser_hdr);
    free(newbuf);
    return;
}

static void parser_erspan(cap_pkt_info_t *p_st_cap_pkt_info){
    if (p_st_cap_pkt_info->st_pkt_info.size <= sizeof(erspan_parser_hdr2))
    {
        return;
    }

    erspan_parser_hdr2* erspan_parser_hdr = (erspan_parser_hdr2*)p_st_cap_pkt_info->st_pkt_info.buf;
    if (0x2f != erspan_parser_hdr->ip_hdr.ip_p)
    {
        return;
    }

    //下面这里 erspan二型的标志应为  88be 但是因为大小端的问题导致变成了 be88
    if ((0xbe88 == erspan_parser_hdr->gre_hdr.protocol) && (0x1 == erspan_parser_hdr->erspan_hdr.ver))
    {
        char *newbuf = (char*)malloc(p_st_cap_pkt_info->st_pkt_info.size);
        if (NULL == newbuf) {
            fprintf (stderr, "malloc buffer failed\n");
            return ;
        }
        memcpy(newbuf, p_st_cap_pkt_info->st_pkt_info.buf+sizeof(erspan_parser_hdr2), p_st_cap_pkt_info->st_pkt_info.size-sizeof(erspan_parser_hdr2));
        memcpy(newbuf+p_st_cap_pkt_info->st_pkt_info.size-sizeof(erspan_parser_hdr2), p_st_cap_pkt_info->st_pkt_info.buf, sizeof(erspan_parser_hdr2));
        memcpy(p_st_cap_pkt_info->st_pkt_info.buf, newbuf, p_st_cap_pkt_info->st_pkt_info.size);
        p_st_cap_pkt_info->st_pkt_info.size -= sizeof(erspan_parser_hdr2);
        p_st_cap_pkt_info->st_pkt_info.pkt_size -= sizeof(erspan_parser_hdr2);
        free(newbuf);
    }
    //下面这里 erspan三型的标志应为  22eb 但是因为大小端的问题导致变成了 eb22
    else if ((0xeb22 == erspan_parser_hdr->gre_hdr.protocol) && (0x2 == erspan_parser_hdr->erspan_hdr.ver))
    {
        char *newbuf = (char*)malloc(p_st_cap_pkt_info->st_pkt_info.size);
        if (NULL == newbuf) {
            fprintf (stderr, "malloc buffer failed\n");
            return ;
        }
        memcpy(newbuf, p_st_cap_pkt_info->st_pkt_info.buf+sizeof(erspan_parser_hdr3), p_st_cap_pkt_info->st_pkt_info.size-sizeof(erspan_parser_hdr3));
        memcpy(newbuf+p_st_cap_pkt_info->st_pkt_info.size-sizeof(erspan_parser_hdr3), p_st_cap_pkt_info->st_pkt_info.buf, sizeof(erspan_parser_hdr3));
        memcpy(p_st_cap_pkt_info->st_pkt_info.buf, newbuf, p_st_cap_pkt_info->st_pkt_info.size);
        p_st_cap_pkt_info->st_pkt_info.size -= sizeof(erspan_parser_hdr3);
        p_st_cap_pkt_info->st_pkt_info.pkt_size -= sizeof(erspan_parser_hdr3);
        free(newbuf);
    }
    return;
}

int pcap_ip_worker(void *p_arg_ptr)
{
    (void)p_arg_ptr;
    int i_ret = 0;
    int i_work_idx = 0;
    int a_sche_table[SCHE_TABLE_SIZE] = {0};
    int i = 0;
    for (i = 0; i < COUNTOF(a_sche_table); ++i)
    {
        a_sche_table[i] = i % g_conf_pcap_work_thread_num;
    }
    
    while (!g_quit_signal)
    {
        if (unlikely(g_ip_cache_clean)) 
        {
            queue_flush_complete(g_p_ip_work_queue, free_queue_ip_wrk);
        }
#ifdef ARM
        pthread_mutex_lock(&g_ip_worker_queue_mutex);
        cap_pkt_info_t** queue = g_ip_worker_queue;
        uint32_t count = g_ip_worker_queue_count;
        g_ip_worker_queue = (cap_pkt_info_t**)malloc(sizeof(cap_pkt_info_t*) * g_u32_ip_work_queue_num);
        g_ip_worker_queue_count = 0;
        pthread_mutex_unlock(&g_ip_worker_queue_mutex);

        cap_pkt_info_t *p_st_cap_pkt_info = NULL;
        uint32_t i = 0;
        for (; i < count; i++)
        {
            p_st_cap_pkt_info = queue[i];
#else
        cap_pkt_info_t *p_st_cap_pkt_info = NULL;
        if (QUEUE_OK == (i_ret = queue_get(g_p_ip_work_queue, (void**)&p_st_cap_pkt_info)) && p_st_cap_pkt_info != NULL)
        {
#endif
            g_st_pcap_stats.st_ip_wrk_stats.u64_dequeue_pkts += 1;
            if (g_cb_flow) {
                char* p = (char*)malloc(p_st_cap_pkt_info->st_pkt_info.size);
                memcpy(p, p_st_cap_pkt_info->st_pkt_info.buf, p_st_cap_pkt_info->st_pkt_info.size);
                g_cb_flow(p, p_st_cap_pkt_info->st_pkt_info.size);
            }

			//后续有无必要改造成插件模式
			if (g_u32_tunnel_type == NIC_TUNNEL_VXLAN)
			{
				parser_vxlan(p_st_cap_pkt_info);
			}
            
			if (g_u32_tunnel_type == NIC_TUNNEL_ERSPAN)
			{
				parser_erspan(p_st_cap_pkt_info);
			}

            g_cb_ip(&(p_st_cap_pkt_info->st_pkt_info), 1, NULL);

            if (p_st_cap_pkt_info->st_pkt_info.o_st == 0)
            {
                goto fail;
            }
            else if (p_st_cap_pkt_info->st_pkt_info.o_st > 0)
            {
                /* 数据入队列 */
                i_work_idx = a_sche_table[(p_st_cap_pkt_info->st_pkt_info.o_st - 1) & 0xff];

                queue_pcap_t *p_st_queue_pcap = NULL;
                p_st_queue_pcap = (queue_pcap_t *)malloc(sizeof(queue_pcap_t));
                if (p_st_queue_pcap == NULL)
                {
                    goto fail;
                }

                p_st_queue_pcap->i_work_idx = i_work_idx;
                p_st_queue_pcap->u32_pcap_len = p_st_cap_pkt_info->st_pkt_info.size;
                p_st_queue_pcap->u32_seconds = p_st_cap_pkt_info->u32_sec;
                p_st_queue_pcap->u32_microseconds = p_st_cap_pkt_info->u32_usec;
                p_st_queue_pcap->p_data = p_st_cap_pkt_info->st_pkt_info.buf;
#ifdef ARM
                pthread_mutex_lock(&g_tcp_worker_queue_mutex[i_work_idx]);
                if (g_tcp_worker_queue_count[i_work_idx] < g_u32_tcp_work_queue_num)
                {
                    g_tcp_worker_queue[i_work_idx][g_tcp_worker_queue_count[i_work_idx]] = p_st_queue_pcap;
                    g_tcp_worker_queue_count[i_work_idx]++;
                    i_ret = QUEUE_OK;
                }
                else
                {
                    i_ret = -1;
                }
                pthread_mutex_unlock(&g_tcp_worker_queue_mutex[i_work_idx]);
#else
                i_ret = queue_put(g_p_tcp_queue[i_work_idx], p_st_queue_pcap);
#endif
                if (i_ret != QUEUE_OK)
                {
                    free_queue_pcap(p_st_queue_pcap);
                    g_st_pcap_stats.st_ip_wrk_stats.u64_enqueue_failed_pkts += 1;
                    goto fail;
                }
                else
                {
                    g_st_pcap_stats.st_ip_wrk_stats.u64_enqueue_pkts += 1;
                }

                p_st_cap_pkt_info->st_pkt_info.buf = NULL;
            }
            else
            {
                goto fail;
            }
#ifdef ARM
            fail:
            free_queue_ip_wrk(p_st_cap_pkt_info);
        }
        free(queue);

        if (0 == count)
        {
            usleep(10);
        }
#else
        }
        else
        {
            usleep(10);
            continue;
        }

        fail:
            free_queue_ip_wrk(p_st_cap_pkt_info);
#endif
    }

    return 0;
}

static void put_pkt_to_queue(cap_pkt_info_t *p_st_cap_pkt_info)
{
    int i_ret = 0;
    int u32_sample_len = 0;
    unsigned char *p_queue_data = NULL;
    struct pcap_packet_header st_pkt_header;
    memset(&st_pkt_header, 0, sizeof(struct pcap_packet_header));

    st_pkt_header.timestamp = p_st_cap_pkt_info->u32_sec;
    st_pkt_header.microseconds = p_st_cap_pkt_info->u32_usec;
    st_pkt_header.packet_length = p_st_cap_pkt_info->st_pkt_info.pkt_size;
    st_pkt_header.packet_length_wire = p_st_cap_pkt_info->st_pkt_info.size;
    //RTE_LOG(NOTICE, GWHW, "packet_length = %u\n", u_cap_pktlen);
    u32_sample_len = sizeof(struct pcap_packet_header) + st_pkt_header.packet_length_wire;
    p_queue_data = (unsigned char *)malloc(u32_sample_len + sizeof(uint32_t));
    if (p_queue_data == NULL)
    {
      return;
    }
    memset(p_queue_data, 0, u32_sample_len + sizeof(uint32_t));
    /* 有可能会导致性能下降 */
    memcpy(p_queue_data, &u32_sample_len, sizeof(uint32_t));
    memcpy(p_queue_data + sizeof(uint32_t), &st_pkt_header, sizeof(struct pcap_packet_header));
    memcpy(p_queue_data + sizeof(uint32_t) + sizeof(struct pcap_packet_header), p_st_cap_pkt_info->st_pkt_info.buf, st_pkt_header.packet_length_wire);

    i_ret = g_cb_sample(p_queue_data);
    if (i_ret != 0)
    {
      free(p_queue_data);
      p_queue_data = NULL;
    }
  
}

void process_ip(u_char *p_arg, const struct pfring_pkthdr *p_pkthdr, u_char *p_packet)
{
    int i_ret = 0;
    __sync_fetch_and_add(&g_st_pcap_stats.st_recv_pkt_stats.u64_rx_pkts, 1);
    cap_pkt_info_t *p_st_cap_pkt_info = NULL;
    p_st_cap_pkt_info = (cap_pkt_info_t *)malloc(sizeof(cap_pkt_info_t));
    if (p_st_cap_pkt_info == NULL)
    {
        goto fail;
    }
    memset(p_st_cap_pkt_info, 0, sizeof(cap_pkt_info_t));

    p_st_cap_pkt_info->st_pkt_info.o_st = 0;
    p_st_cap_pkt_info->st_pkt_info.buf = p_packet;
    p_st_cap_pkt_info->st_pkt_info.size = p_pkthdr->caplen;
    p_st_cap_pkt_info->st_pkt_info.pkt_size = p_pkthdr->len;
    p_st_cap_pkt_info->u32_sec = p_pkthdr->ts.tv_sec;
    p_st_cap_pkt_info->u32_usec = p_pkthdr->ts.tv_usec;

    if (g_i_sample_mode)
    {
        put_pkt_to_queue(p_st_cap_pkt_info);
    }

#ifdef ARM
    pthread_mutex_lock(&g_ip_worker_queue_mutex);
    if (g_ip_worker_queue_count < g_u32_ip_work_queue_num)
    {
        g_ip_worker_queue[g_ip_worker_queue_count] = p_st_cap_pkt_info;
        g_ip_worker_queue_count++;
        i_ret = QUEUE_OK;
    }
    else
    {
        i_ret = -1;
    }
    pthread_mutex_unlock(&g_ip_worker_queue_mutex);
#else
    i_ret = queue_put(g_p_ip_work_queue, p_st_cap_pkt_info);
#endif

    if (i_ret != QUEUE_OK)
    {
        goto fail;
    }
    else
    {
        __sync_fetch_and_add(&g_st_pcap_stats.st_recv_pkt_stats.u64_enqueue_pkts, 1);
    }

    return;
fail:
    if (p_st_cap_pkt_info)
    {
        free(p_st_cap_pkt_info);
    }

    if (g_pfring_zero_copy == 0)
    {
        if (p_packet)
        {
            free(p_packet);
        }
    }

    __sync_fetch_and_add(&g_st_pcap_stats.st_recv_pkt_stats.u64_enqueue_failed_pkts, 1);
    return;
}

void check_pfring_driver(uint64_t* lasttime_pfring_recv_pkts, uint64_t* lasttime_device_recv_pkts, int index)
{
    // get pfring recv pkts
    pfring_stat stats;
    int ret = pfring_stats(g_p_pfring_handle[index], &stats);
    if (0 != ret)
    {
        fprintf(stderr, "check_pfring_driver get stats fail, ret=%d\n", ret);
        return;
    }

    // get device recv pkts
    FILE* fp = fopen("/proc/net/dev", "r");
    if (NULL == fp)
    {
        fprintf(stderr, "check_pfring_driver open file=/proc/net/dev fail, errno=%d, %s\n", errno, strerror(errno));
        return;
    }

    const char* device = g_a_device_name[index];
    char* sign = NULL;
    uint64_t device_recv_pkts = 0;
    char buf[1024] = {0};

    while (fgets(buf, sizeof(buf), fp) != NULL)
    {
        if (strstr(buf, device) != NULL && (sign = strchr(buf, ':')) != NULL)
        {
            *sign = 0;
            sign++;
            if (sscanf(sign, " %*d %lu", &device_recv_pkts) != 1)
            {
                fprintf(stderr, "check_pfring_driver get recv pkts fail, eth=%s, buf=%s\n", device, buf);
                device_recv_pkts = UINT64_MAX;
            }

            break;
        }
    }
    fclose(fp);

    // compare
    if (*lasttime_device_recv_pkts > 0 && *lasttime_pfring_recv_pkts > 0 &&
        device_recv_pkts > *lasttime_device_recv_pkts && stats.recv == *lasttime_pfring_recv_pkts)
    {
        fprintf(stderr, "check_pfring_driver pfring driver may be down, reload driver, "
                        "device recv pkts=%lu, "
                        "lasttime device recv pkts=%lu, "
                        "pfring recv pkts=%lu, "
                        "lasttime pfring recv pkts=%lu\n",
                device_recv_pkts,
                *lasttime_device_recv_pkts,
                stats.recv,
                *lasttime_pfring_recv_pkts);

        if (g_pfring_driver_check_fail_quit)
        {
            abort();
        }

        return;
    }

    *lasttime_device_recv_pkts = device_recv_pkts;
    *lasttime_pfring_recv_pkts = stats.recv;
}

static void zero_copy_recv(long index)
{
    int i_ret = 0;
    u_char *buffer_p = NULL;
    struct pfring_pkthdr hdr;

    uint64_t lasttime_pfring_recv_pkts = 0;
    uint64_t lasttime_device_recv_pkts = 0;
    uint64_t count = 0;

    do
    {   
        i_ret = pfring_recv(g_p_pfring_handle[index], &buffer_p, 0, &hdr, 0);
        if (i_ret > 0)
        {
            process_ip(NULL, &hdr, buffer_p);
        }
        else
        {
            usleep(1);
            count++;
        }

        if (count > SLEEP_MAX)
        {
            count = 0;
            check_pfring_driver(&lasttime_pfring_recv_pkts, &lasttime_device_recv_pkts, index);
        }

    } while (!g_quit_signal);

    return;
}

static void no_zero_copy_recv(long index)
{
    int i_ret = 1;
    u_char *buffer_p = NULL;
    struct pfring_pkthdr hdr;

    do 
    {
        if (i_ret > 0)
        {
            buffer_p = (u_char*)malloc(sizeof(u_char) * CAP_LEN);
            if (NULL == buffer_p)
            {
                fprintf (stderr, "malloc buffer failed\n");
                break;
            }
        }

        /* 捕获数据包，调用process_ip函数进行处理 */
        /* 最后一个参数设置成0,不阻塞，没有数据包直接返回 */
        i_ret = pfring_recv(g_p_pfring_handle[index], &buffer_p, CAP_LEN, &hdr, 0);
        if (i_ret > 0)
        {
            process_ip(NULL, &hdr, buffer_p);
        }
        else
        {
            usleep(1);
        }
    } while (!g_quit_signal);

    return;
}

static int pcap_data_worker(void *p_arg_ptr)
{
    long i = (long)(long*)p_arg_ptr;
    int cpu = 2 + (int)i;

    //网卡未绑定直接返回
    if (g_p_pfring_handle[i] == NULL)
    {
        return 0;
    }

    cpu_set_t mask;
    CPU_SET(cpu, &mask);
    pthread_setaffinity_np(pthread_self(), sizeof(mask), &mask);
    
    while (!g_quit_signal)
    {
        if (g_pfring_zero_copy == 1)
        {
            zero_copy_recv(i);
        }
        else
        {   
            no_zero_copy_recv(i);
        }
    }

    return 0;
}

static int process_tcp_worker(void *p_arg)
{
    long i_work_idx = (long)(long*)p_arg;

    int i_ret = 0;

    queue_pcap_t *p_st_pcap_info = NULL;

    uint64_t sleep_count = 0;
    const uint64_t sleep_count_max = (g_tcp_worker_timeout * 1000 * 1000) / TCP_WORKER_DATA_WAIT;

    while (!g_quit_signal)
    {
        if (unlikely(g_tcp_cache_clean)) 
        {
            queue_flush_complete_pcap(g_p_tcp_queue[i_work_idx], free_queue_pcap);
            usleep(10);
            continue;
        }
#ifdef ARM
        pthread_mutex_lock(&g_tcp_worker_queue_mutex[i_work_idx]);
        queue_pcap_t** queue = g_tcp_worker_queue[i_work_idx];
        uint32_t count = g_tcp_worker_queue_count[i_work_idx];
        g_tcp_worker_queue[i_work_idx] = (queue_pcap_t**)malloc(sizeof(queue_pcap_t*) * g_u32_tcp_work_queue_num);
        g_tcp_worker_queue_count[i_work_idx] = 0;
        i_ret = QUEUE_OK;
        pthread_mutex_unlock(&g_tcp_worker_queue_mutex[i_work_idx]);

        uint32_t i = 0;
        pcap_info_t st_pcap_info;
        for (; i < count; i++)
        {
            sleep_count = 0;

            __sync_fetch_and_add(&g_st_pcap_stats.st_tcp_wrk_stats.u64_dequeue_pkts, 1);
            p_st_pcap_info = queue[i];
            memset(&st_pcap_info, 0, sizeof(pcap_info_t));
            st_pcap_info.buf = p_st_pcap_info->p_data;
            st_pcap_info.timestamp = p_st_pcap_info->u32_seconds;
            st_pcap_info.microseconds = p_st_pcap_info->u32_microseconds;
            st_pcap_info.packet_length = p_st_pcap_info->u32_pcap_len;
            st_pcap_info.packet_length_wire = p_st_pcap_info->u32_pcap_len;

            g_cb_tcp(&st_pcap_info, 0, p_st_pcap_info->i_work_idx);

            free_queue_pcap(p_st_pcap_info);
        }

        if (0 == count)
        {
            usleep(TCP_WORKER_DATA_WAIT);

            sleep_count++;
            if (sleep_count > sleep_count_max)
            {
                sleep_count = 0;
                g_cb_tcp(NULL, &sleep_count, i_work_idx);
            }
        }

        free(queue);
#else
        i_ret = queue_get(g_p_tcp_queue[i_work_idx], (void **)&p_st_pcap_info);
        if (i_ret == QUEUE_OK)
        {
            __sync_fetch_and_add(&g_st_pcap_stats.st_tcp_wrk_stats.u64_dequeue_pkts, 1);
            if (p_st_pcap_info != NULL)
            {
                sleep_count = 0;

                pcap_info_t st_pcap_info;
                memset(&st_pcap_info, 0, sizeof(pcap_info_t));
                st_pcap_info.buf = p_st_pcap_info->p_data;
                st_pcap_info.timestamp = p_st_pcap_info->u32_seconds;
                st_pcap_info.microseconds = p_st_pcap_info->u32_microseconds;
                st_pcap_info.packet_length = p_st_pcap_info->u32_pcap_len;
                st_pcap_info.packet_length_wire = p_st_pcap_info->u32_pcap_len;

                g_cb_tcp(&st_pcap_info, 0, p_st_pcap_info->i_work_idx + 48); // +48偏移量，防止与其他source冲突，下同

                free_queue_pcap(p_st_pcap_info);
            }
        }
        else
        {
            usleep(TCP_WORKER_DATA_WAIT);

            sleep_count++;
            if (sleep_count > sleep_count_max)
            {
                sleep_count = 0;
                g_cb_tcp(NULL, &sleep_count, i_work_idx + 48); // 超时检查
            }
        }
#endif
    }

    return 0;
}

int create_tcp_work_queue()
{


    int i = 0;
    int tcp_work_queue_num = MIN(MAX_WORKER_THREAD, g_conf_pcap_work_thread_num);
    for (i = 0; i < tcp_work_queue_num; ++i)
    {
#ifdef ARM
        g_tcp_worker_queue[i] = (queue_pcap_t**)malloc(sizeof(queue_pcap_t*) * g_u32_tcp_work_queue_num);
        g_tcp_worker_queue_count[i] = 0;
        pthread_mutex_init(&g_tcp_worker_queue_mutex[i], NULL);
#endif
        /* 创建单生产者单消费者队列 */
        g_p_tcp_queue[i] = queue_create(g_u32_tcp_work_queue_num, QUEUE_SP_EQ | QUEUE_SC_DQ);
        if (g_p_tcp_queue[i] == NULL)
        {
            fprintf(stderr, "[pfring main] create tcp work queue failed\n");
            return -1;
        }
    }
    
    return 0;
}

int create_ip_work_queue()
{
#ifdef ARM
    g_ip_worker_queue = (cap_pkt_info_t**)malloc(sizeof(cap_pkt_info_t*) * g_u32_ip_work_queue_num);
    g_ip_worker_queue_count = 0;
    pthread_mutex_init(&g_ip_worker_queue_mutex, NULL);
#endif

    g_p_ip_work_queue = queue_create(g_u32_ip_work_queue_num,  QUEUE_SC_DQ);
    if (NULL == g_p_ip_work_queue)
    {
        fprintf (stderr, "[pfring main] create ip work queue failed\n");
        return -1;
    }

    return 0;
}

int create_tcp_handle_thread(pthread_t *thread_process_tcp)
{
    int i_ret = 0;
    int i = 0;
    int tcp_work_thread_num = MIN(MAX_WORKER_THREAD, g_conf_pcap_work_thread_num);
    for (i = 0; i < tcp_work_thread_num; ++i)
    {
        i_ret = pthread_create(&thread_process_tcp[i], NULL, (void *(*)(void *))process_tcp_worker, (void *)(long)(i));
        if (i_ret != 0)
        {
            fprintf(stderr, "[pfring main] create process tcp %d thread failed\n", i);
            break;
        }
        else
        {
            printf("[pfring main] create process tcp %d thread successfully\n", i);
        }
    }
    return i_ret;
}

int create_ip_hendle_thread(pthread_t *thread_ip_worker)
{
    int i_ret = 0;
    /* 创建处理IP数据线程 */
    i_ret = pthread_create(thread_ip_worker, NULL, (void *(*)(void *))pcap_ip_worker, NULL);
    if (i_ret != 0)
    {
        fprintf (stderr, "[pfring main] create pcap ip worker thread failed\n");
    }
    else
    {
        printf ("[pfring main] create pcap ip worker thread successfully\n");
    }

    return i_ret;
}

int create_pcap_thread(pthread_t *thread_pcap_data)
{
    int i_ret = 0;
    int i = 0;

    for (i = 0; i < g_network_device_num; i++)
    {
        /* 创建捕获数据包线程 */
        i_ret = pthread_create(thread_pcap_data, NULL, (void *(*)(void *))pcap_data_worker, (void*)(long)i);
        if (i_ret != 0)
        {
            fprintf(stderr, "[pfring main] create pcap data thread failed\n");
        }
        else
        {
            printf("[pfring main] create pcap data successfully\n");
        }
    }

    return i_ret;
}

void reload_driver()
{
    uint32_t index = 0;

    for (index = 0; index < g_network_device_num; ++index)
    {
        if (NULL != g_p_pfring_handle[index])
        {
            pfring_close(g_p_pfring_handle[index]);
        }
    }

    // 如果不加这个，后续执行load_driver.sh会导致其他进程继承23端口的文件描述符
    // 这只是临时解决方案，还需查明原因，具体解决
    long max_fd = sysconf(_SC_OPEN_MAX);  // 获取最大文件描述符数量
    if (max_fd == -1) {
        max_fd = 1024;  // 如果获取失败，假设最大文件描述符数为 1024
    }

    long fd = 3;
    for (; fd < max_fd; fd++) {  // 从 3 开始，保留 0, 1, 2
        close(fd);  // 关闭文件描述符
    }

    system("sh /opt/apigw/gwhw/tools/load_driver.sh &");
}

void open_pfring(const char *device_name, pfring **pfring_handle)
{
    int try_num = COUNTOF(g_mtu);
    int i = 0;
    for (i = 0; i < try_num; i++)
    {
        if (0 == g_mtu[i]) {break;}
        *pfring_handle = pfring_open(device_name, g_mtu[i], PF_RING_PROMISC | PF_RING_DO_NOT_PARSE);
        if (NULL == *pfring_handle)
        {
            fprintf(stderr, "[pfring main] open device(%s) failed, mtu(%d)\n", device_name, g_mtu[i]);
            continue;
        }

        break;
    }

    if (*pfring_handle == NULL)
    {
        if (g_pfring_open_fail_reload_driver)
        {
            reload_driver();
        }

        return; 
    }
    fprintf(stderr, "[pfring main] open device(%s) success, mtu(%d)\n", device_name, g_mtu[i]);

	return;
}

int set_pcap_direction(pfring *pfring_handle)
{
    int i_ret = 0;
    /* 设置只抓取从网卡进入的流量 */
    //typedef enum {PCAP_D_INOUT = 0, PCAP_D_IN, PCAP_D_OUT} pcap_direction_t;
    if (g_conf_pcap_direction != 0 && g_conf_pcap_direction != 1 && g_conf_pcap_direction != 2)
    {
        fprintf(stderr, "pcap direction param invaild (%d)\n", g_conf_pcap_direction);
    }

    i_ret = pfring_set_direction(pfring_handle, g_conf_pcap_direction);
    if (i_ret < 0)
    {
        fprintf (stderr, "[pfring main] set direction %d failed\n", g_conf_pcap_direction);
    }
    else
    {
        fprintf (stdout, "[pfring main] set direction %d successfully\n", g_conf_pcap_direction);
    }

    return i_ret;
}

int enable_pfring(pfring *pfring_handle)
{
    int i_ret = 0;
    i_ret = pfring_enable_ring(pfring_handle);
    if (i_ret != 0)
    {
        fprintf (stderr, "enable pfring handle failed\n");
    }

    return i_ret;
}

int handle_pfring()
{
    uint32_t index = 0;
    int i_ret = 0;

    for (index = 0; index < g_network_device_num; ++index)
    {
        open_pfring(g_a_device_name[index], &g_p_pfring_handle[index]);
        if (g_p_pfring_handle[index] ==  NULL)
        {
            fprintf(stderr, "[handle_pfring] open_pfring device open failed device name: %s\n",g_a_device_name[index]);
            continue;
        }

        i_ret = set_pcap_direction(g_p_pfring_handle[index]);
        if (i_ret < 0)
        {
            g_p_pfring_handle[index] = NULL;
            fprintf(stderr, "[handle_pfring] set_pcap_direction device open failed device name: %s\n",g_a_device_name[index]);
            continue;
        }

        i_ret = enable_pfring(g_p_pfring_handle[index]);
        if (i_ret != 0)
        {
            g_p_pfring_handle[index] = NULL;
            fprintf(stderr, "[handle_pfring] enable_pfring device open failed device name: %s\n",g_a_device_name[index]);
            continue;
        }

        g_device_info[index].u16_speed = pfring_get_interface_speed(g_p_pfring_handle[index]);
    }

    //判断是否有网口正常打开，如果有则返回
    for (index = 0; index < g_network_device_num; ++index)
    {
        if (g_p_pfring_handle[index] ==  NULL)
        {
            continue;
        }
        return i_ret;
    }
    //没有配置正确的网卡则配置lo网卡
    g_a_device_name[0] = "lo";
    open_pfring(g_a_device_name[0], &g_p_pfring_handle[0]);
    if (g_p_pfring_handle[0] ==  NULL)
    {
        fprintf(stderr, "[handle_pfring] open_pfring device open failed device name: %s\n",g_a_device_name[0]);
        return -1;
    }

    i_ret = set_pcap_direction(g_p_pfring_handle[0]);
    if (i_ret < 0)
    {
        g_p_pfring_handle[0] = NULL;
        fprintf(stderr, "[handle_pfring] set_pcap_direction device open failed device name: %s\n",g_a_device_name[0]);
        return i_ret;
    }

    i_ret = enable_pfring(g_p_pfring_handle[0]);
    if (i_ret != 0)
    {
        g_p_pfring_handle[0] = NULL;
        fprintf(stderr, "[handle_pfring] enable_pfring device open failed device name: %s\n",g_a_device_name[0]);
        return i_ret;
    }

    return i_ret;
}

void pcap_main(capd_args_t *p_pca)
{
    int i = 0;
    int i_ret = 0;
    pthread_t thread_pcap_data;
    pthread_t thread_ip_worker;
    pthread_t thread_process_tcp[MAX_WORKER_THREAD];

    if (p_pca == NULL)
    {
        fprintf(stderr, "[pfring main] args error\n");
        return;
    }
    memset(&g_st_pcap_stats, 0, sizeof(g_st_pcap_stats));

    g_cb_ip = p_pca->cb_ip;
    g_cb_tcp = p_pca->cb_tcp;
    g_cb_flow = p_pca->cb_flow;

    i_ret = handle_pfring();
    if (i_ret != 0)
    {
        goto end;
    }

    /* 创建tcp处理队列 */
    i_ret = create_tcp_work_queue();
    if (i_ret < 0)
    {
        goto end;
    }

    /* 创建ip 处理队列 */
    i_ret = create_ip_work_queue();
    if (i_ret < 0)
    {
        goto end;
    }

    /* 创建tcp处理线程 */
    i_ret = create_tcp_handle_thread(thread_process_tcp);
    if (i_ret < 0)
    {
        goto end;
    }

    /* 创建ip处理线程 */
    i_ret = create_ip_hendle_thread(&thread_ip_worker);
    if (i_ret < 0)
    {
        goto end;
    }

    /* 创建pcap线程 */
    i_ret = create_pcap_thread(&thread_pcap_data);
    if (i_ret < 0)
    {
        goto end;
    }


    pthread_join(thread_pcap_data, NULL);
    pthread_join(thread_ip_worker, NULL);

    for (i = 0; i < g_conf_pcap_work_thread_num; ++i)
    {
        pthread_join(thread_process_tcp[i], NULL);
    }

    queue_destroy_complete(g_p_ip_work_queue, free_queue_ip_wrk);

    for (i = 0; i < g_conf_pcap_work_thread_num; ++i)
    {
        queue_destroy_complete_pcap(g_p_tcp_queue[i], free_queue_pcap);
    }

end:
    close_pcap();

    return;
}

int collect_pcap_stats(nic_info_t *info, uint32_t *network_device_num)
{
    *network_device_num = g_network_device_num;
    int i = 0;
    for( ; i < g_network_device_num; i++)
    {
        if (g_p_pfring_handle[i] == NULL)
        {
            continue;
        } 
        pfring_stat st_pcap_stat = {0, 0, 0};
        int ret = 0;
        if ((ret = pfring_stats(g_p_pfring_handle[i], &st_pcap_stat)))
        {
            *network_device_num = i;
            fprintf(stderr, "[pcap stats] get pcap stats failed\n");
            return ret;
        }
        strncpy(info[i].ch_device_name, g_a_device_name[i], NIC_DEVICE_NAME_LEN);
        info[i].u64_recv_packets = st_pcap_stat.recv;
        info[i].u64_drop_packets = st_pcap_stat.drop;
        info[i].u16_speed = g_device_info[i].u16_speed;
        snprintf(info[i].status, sizeof(info[i].status) - 1, "%s", pfring_get_link_status(g_p_pfring_handle[i]) ? "UP" : "DOWN");
    }
    return 0;
}

void get_nic_log_buf(char *log_buf, size_t log_buf_len)
{
    int i_ret = 0;
    int i = 0;

    char show_data[5][512];
    memset(show_data, 0, sizeof(show_data));
    char buf[128];
    char buf1[128];
    uint32_t speed = 0;
    for (i = 0; i < g_network_device_num; i++)
    {
        if (g_p_pfring_handle[i] == NULL)
        {
            continue;
        }

        snprintf(buf1, COUNTOF(buf1) - 1, "port %s", g_a_device_name[i]);
        snprintf(buf, COUNTOF(buf) - 1, " %20s", buf1);
        strcat(show_data[0], buf);

        pfring_stat st_pcap_stat;
        memset(&st_pcap_stat, 0, sizeof(struct pcap_stat));
        i_ret = pfring_stats(g_p_pfring_handle[i], &st_pcap_stat);
        if (i_ret != 0)
        {
            fprintf(stderr, "[pcap stats] get pcap stats failed\n");
            return;
        }

        snprintf(buf, COUNTOF(buf) - 1, "%20" PRIu64 "", st_pcap_stat.recv);
        strcat(show_data[1], buf);
        snprintf(buf, COUNTOF(buf) - 1, "%20" PRIu64 "", st_pcap_stat.drop);
        strcat(show_data[2], buf);

        speed = g_device_info[i].u16_speed;
        snprintf(buf, COUNTOF(buf) - 1, "%20u", speed);
        strcat(show_data[3], buf);

        snprintf(buf, COUNTOF(buf) - 1,  "%20s", pfring_get_link_status(g_p_pfring_handle[i]) ? "UP" : "DOWN");
        strcat(show_data[4], buf);
    }

    sprintf (log_buf + strlen(log_buf), "\nstats:\n");
    sprintf (log_buf + strlen(log_buf), "              %s\n", show_data[0]);
    sprintf (log_buf + strlen(log_buf), " - Pkts in:   %s\n", show_data[1]);
    sprintf (log_buf + strlen(log_buf), " - Pkts drop: %s\n", show_data[2]);
    sprintf (log_buf + strlen(log_buf), " - Speed:     %s\n", show_data[3]);
    sprintf (log_buf + strlen(log_buf), " - Status:    %s\n", show_data[4]);
    sprintf (log_buf + strlen(log_buf), "\nthread stats:\n");
    sprintf (log_buf + strlen(log_buf), "                                %16s %16s %16s\n", "RX", "IP Worker", "TCP Worker");
    sprintf (log_buf + strlen(log_buf), " - Pkts deqd from upstream:     %16" PRIu64 " %16" PRIu64 " %16" PRIu64 "\n", g_st_pcap_stats.st_recv_pkt_stats.u64_rx_pkts
                                                                                                                     , g_st_pcap_stats.st_ip_wrk_stats.u64_dequeue_pkts
                                                                                                                     , g_st_pcap_stats.st_tcp_wrk_stats.u64_dequeue_pkts);
    sprintf (log_buf + strlen(log_buf), " - Pkts enqd to workers ring:   %16" PRIu64 " %16" PRIu64  "\n", g_st_pcap_stats.st_recv_pkt_stats.u64_enqueue_pkts
                                                                                                        , g_st_pcap_stats.st_ip_wrk_stats.u64_enqueue_pkts);
    sprintf (log_buf + strlen(log_buf), " - Pkts enq to workers failed:  %16" PRIu64 " %16" PRIu64  "\n", g_st_pcap_stats.st_recv_pkt_stats.u64_enqueue_failed_pkts
                                                                                                        , g_st_pcap_stats.st_ip_wrk_stats.u64_enqueue_failed_pkts);
    return;
}

void print_pcap_stats()
{
    char log_buf[LOG_BUF_LEN] = {0};
    get_nic_log_buf(log_buf, LOG_BUF_LEN);

    printf("%s", log_buf);
    // int i_ret = 0;
    // int i = 0;

    // char show_data[4][512];
    // memset(show_data, 0, sizeof(show_data));
    // char buf[128];
    // char buf1[128];
    // for (i = 0; i < g_network_device_num; i++)
    // {
    //     if (g_p_pfring_handle[i] == NULL)
    //     {
    //         continue;
    //     }

    //     snprintf(buf1, COUNTOF(buf1) - 1, "port %s", g_a_device_name[i]);
    //     snprintf(buf, COUNTOF(buf) - 1, " %20s", buf1);
    //     strcat(show_data[0], buf);

    //     pfring_stat st_pcap_stat;
    //     memset(&st_pcap_stat, 0, sizeof(struct pcap_stat));
    //     i_ret = pfring_stats(g_p_pfring_handle[i], &st_pcap_stat);
    //     if (i_ret != 0)
    //     {
    //         fprintf(stderr, "[pcap stats] get pcap stats failed\n");
    //         return;
    //     }

    //     snprintf(buf, COUNTOF(buf) - 1, "%20" PRIu64 "", st_pcap_stat.recv);
    //     strcat(show_data[1], buf);
    //     snprintf(buf, COUNTOF(buf) - 1, "%20" PRIu64 "", st_pcap_stat.drop);
    //     strcat(show_data[2], buf);
    // }

    // printf("\nstats:\n");
    // printf("              %s\n", show_data[0]);

    // printf(" - Pkts in:   %s\n", show_data[1]);
    // printf(" - Pkts drop: %s\n", show_data[2]);
    

    // printf("\nthread stats:\n");

    // printf("                                %16s %16s %16s\n", "RX", "IP Worker", "TCP Worker");
    // printf(" - Pkts deqd from upstream:     %16" PRIu64 " %16" PRIu64 " %16" PRIu64 "\n", g_st_pcap_stats.st_recv_pkt_stats.u64_rx_pkts, g_st_pcap_stats.st_ip_wrk_stats.u64_dequeue_pkts, g_st_pcap_stats.st_tcp_wrk_stats.u64_dequeue_pkts);
    // printf(" - Pkts enqd to workers ring:   %16" PRIu64 " %16" PRIu64  "\n", g_st_pcap_stats.st_recv_pkt_stats.u64_enqueue_pkts, g_st_pcap_stats.st_ip_wrk_stats.u64_enqueue_pkts);
    // printf(" - Pkts enq to workers failed:  %16" PRIu64 " %16" PRIu64  "\n", g_st_pcap_stats.st_recv_pkt_stats.u64_enqueue_failed_pkts, g_st_pcap_stats.st_ip_wrk_stats.u64_enqueue_failed_pkts);
    return;
}

void close_pcap(void)
{
    uint32_t index = 0;

    for (index = 0; index < g_network_device_num; index++)
    {
        if (g_p_pfring_handle[index] == NULL)
        {
            continue;
        }

        pfring_close(g_p_pfring_handle[index]);
        g_p_pfring_handle[index] = NULL;
    }

    return;
}

void cache_ip_queue_clean() 
{
    g_ip_cache_clean = 1;
    while (queue_elements(g_p_ip_work_queue)) 
        ;

    g_ip_cache_clean = 0;
    return;
}

void cache_tcp_queue_clean() 
{
    int i = 0;
    g_tcp_cache_clean = 1;
    for (i = 0; i < MAX_WORKER_THREAD && g_p_tcp_queue[i]; i++) 
    {
        if (queue_elements(g_p_tcp_queue[i]))
        {
            i--;
        }
    }
    g_tcp_cache_clean = 0;
    return;
}

int nic_status()
{
    if (queue_elements(g_p_ip_work_queue))
    {
        return 1;
    }

    int i = 0;
    for (i = 0; i < MAX_WORKER_THREAD && g_p_tcp_queue[i]; i++) 
    {
        if (queue_elements(g_p_tcp_queue[i]))
        {
            return 1;
        }
    }

    return 0;
}
