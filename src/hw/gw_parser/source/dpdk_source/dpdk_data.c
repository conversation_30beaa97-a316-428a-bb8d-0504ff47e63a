
// cap_data.c

#include <signal.h>
#include <getopt.h>

#define __USE_GNU
#include <pthread.h>
#undef __USE_GNU

#include <unistd.h>
#include <sys/time.h>
#include <sys/sysinfo.h>

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/types.h>
#include <dirent.h>
#include <sys/stat.h>

#include <rte_eal.h>
#include <rte_common.h>
#include <rte_errno.h>
#include <rte_ethdev.h>
#include <rte_lcore.h>
#include <rte_malloc.h>
#include <rte_mbuf.h>
#include <rte_mempool.h>
#include <rte_ring.h>
#include <rte_reorder.h>
#include <rte_log.h>
#include <rte_cycles.h>
#include <rte_config.h>

#include "cap_data.h"
#include "dpdk_data.h"

#include "pcap_header.h"
#include "utils_core.h"
#include "utils.h"

static CALLBACK_FLOW g_cb_flow = NULL;

void ring_flush(struct rte_ring *r)
{
    struct rte_mbuf *o;
    while (!rte_ring_dequeue(r, (void **)&o)) rte_pktmbuf_free(o);
}

/* parse a sysfs (or other) file containing one integer value */
static inline int eal_parse_sysfs_value(const char *filename, unsigned long *val)
{
    FILE *f;
    char buf[BUFSIZ];
    char *end = NULL;

    if ((f = fopen(filename, "r")) == NULL) {
        RTE_LOG(ERR, EAL, "%s(): cannot open sysfs value %s\n", __func__, filename);
        return -1;
    }

    if (fgets(buf, sizeof(buf), f) == NULL) {
        RTE_LOG(ERR, EAL, "%s(): cannot read sysfs value %s\n", __func__, filename);
        fclose(f);
        return -1;
    }
    *val = strtoul(buf, &end, 0);
    if ((buf[0] == '\0') || (end == NULL) || (*end != '\n')) {
        RTE_LOG(ERR, EAL, "%s(): cannot parse sysfs value %s\n", __func__, filename);
        fclose(f);
        return -1;
    }
    fclose(f);
    return 0;
}

#define SYS_CPU_DIR "/sys/devices/system/cpu/cpu%u"
#define CORE_ID_FILE "topology/core_id"
#define NUMA_NODE_PATH "/sys/devices/system/node"

/* Check if a cpu is present by the presence of the cpu information for it */
static inline int eal_cpu_detected(unsigned lcore_id)
{
    char path[PATH_MAX];
    int len = snprintf(path, sizeof(path), SYS_CPU_DIR "/" CORE_ID_FILE, lcore_id);
    if (len <= 0 || (unsigned)len >= sizeof(path)) return 0;
    if (access(path, F_OK) != 0) return 0;

    return 1;
}

/*
 * Get CPU socket id (NUMA node) for a logical core.
 *
 * This searches each nodeX directories in /sys for the symlink for the given
 * lcore_id and returns the numa node where the lcore is found. If lcore is not
 * found on any numa node, returns zero.
 */
static unsigned eal_cpu_socket_id(unsigned lcore_id)
{
    unsigned socket;

    for (socket = 0; socket < RTE_MAX_NUMA_NODES; socket++) {
        char path[PATH_MAX];

        snprintf(path, sizeof(path), "%s/node%u/cpu%u", NUMA_NODE_PATH, socket, lcore_id);
        if (access(path, F_OK) == 0) return socket;
    }
    return 0;
}

/* Get the cpu core id value from the /sys/.../cpuX core_id value */
static inline unsigned eal_cpu_core_id(unsigned lcore_id)
{
    char path[PATH_MAX];
    unsigned long id;

    int len = snprintf(path, sizeof(path), SYS_CPU_DIR "/%s", lcore_id, CORE_ID_FILE);
    if (len <= 0 || (unsigned)len >= sizeof(path)) goto err;
    if (eal_parse_sysfs_value(path, &id) != 0) goto err;
    return (unsigned)id;

err:
    RTE_LOG(ERR, EAL,
            "Error reading core id value from %s "
            "for lcore %u - assuming core 0\n",
            SYS_CPU_DIR, lcore_id);
    return 0;
}

// CPU  逻辑核数
#define CPU_LCORE_MAX_NUM (256)

//
// #define MBUF_DATA_SIZE (RTE_MBUF_DEFAULT_DATAROOM)
#define MBUF_DATA_SIZE (RTE_ETHER_MAX_JUMBO_FRAME_LEN)
// #define MBUF_DATA_SIZE (65536 - 1 - RTE_PKTMBUF_HEADROOM)
// #define MBUF_DATA_SIZE (4096)

// tcp工作线程，最多支持的数量
#define TCP_RING_OUT_MAX_SIZE 64
// #define TCP_RING_OUT_MAX_SIZE 32

// IP抓包以及线程，最多支持数量
#define RX_IP_RING_OUT_MAX_SIZE 16

#define RX_DESC_PER_QUEUE 128
// #define RX_DESC_PER_QUEUE 768
#define TX_DESC_PER_QUEUE 512

// #define MAX_PKTS_BURST 32
// #define MAX_PKTS_BURST 1024
#define MAX_PKTS_BURST 4096
// #define REORDER_BUFFER_SIZE 8192
// #define MBUF_PER_POOL 65535
#define MBUF_PER_POOL 8191
// #define MBUF_PER_POOL 2047
// #define MBUF_PER_POOL 127
// #define MBUF_POOL_CACHE_SIZE 250
#define MBUF_POOL_CACHE_SIZE 500
// #define MBUF_POOL_CACHE_SIZE 10

// #define RING_SIZE 16384
// #define RING_WORKER_SIZE 1048576
#define RING_WORKER_SIZE 524288
// #define RING_RX_SIZE 1048576
// #define RING_RX_SIZE 16384
#define RING_RX_SIZE 65536

/* Macros for printing using RTE_LOG */
#define RTE_LOGTYPE_GWHW RTE_LOGTYPE_USER1

#define CONF_OUTPUT_FILE_TEMPLATE "gwhw-\%COREID-\%FCOUNT.pcap"

#define CONF_SNAPLEN 65536
// 至少为100个字节
#define GWHW_OUTPUT_FILENAME_LENGTH 200
// worker线程最大数量
#define WORKER_MAX_NUM 1

// ip worker 线程默认数量
#define DEFAULT_IP_WORKER_NUM 1

// rx worker 线程默认数量
#define DEFAULT_RX_WORKER_NUM 1

// 一个rx线程处理端口的最大数量
#define MAX_DEAL_PORT_PER_RX_THREAD 4

#define MAX_PORT_NUM RX_IP_RING_OUT_MAX_SIZE

// 数据包最大长度
#define PKT_MAX_SIZE (128 * 1024L)

/* 数据包采样模式(1: 采样  0: 不采样) */
#define SAMPLE_MODE 0
/* 数据包采样速率(单位：秒) */
#define SAMPLE_RATE 5

// 空闲时计数值 1万 或 10万 不于等于0时表示不释放CPU资源
#define IDLE_MAX_CNT (1 * 10000L)
// #define IDLE_MAX_CNT (0)
//  默认值 为负数时则不释放CPU资源
// #define IDLE_DEFAULT (-1)
#define IDLE_DEFAULT (0)
// 休眠 1ms
#define IDLE_USLEEP (1 * 1000L)
// 休眠函数
// #define IDLE_USLEEP_FUNC rte_delay_us
#define IDLE_USLEEP_FUNC usleep

#define IDLE_F_COUNTER(idle_cnt, idle_deep_cnt, IDLE_DEEP_MAX_CNT_, IDLE_DEEP_SLEEP_)    \
    do {                                                                                 \
        if (likely(g_conf_idle_max_count > 0 && idle_cnt >= 0)) {                        \
            idle_cnt++;                                                                  \
            if (idle_cnt >= g_conf_idle_max_count) {                                     \
                if (idle_deep_cnt < IDLE_DEEP_MAX_CNT_) {                                \
                    idle_deep_cnt++;                                                     \
                    IDLE_USLEEP_FUNC(IDLE_USLEEP); /* sleep 1ms */                       \
                } else {                                                                 \
                    IDLE_USLEEP_FUNC(IDLE_USLEEP *IDLE_DEEP_SLEEP_); /* sleep x * 1ms */ \
                }                                                                        \
                idle_cnt = 0;                                                            \
            }                                                                            \
        }                                                                                \
    } while (0)

#define IDLE_F_RESET(idle_cnt, idle_deep_cnt) \
    do {                                      \
        if (unlikely(idle_cnt > 0)) {         \
            idle_cnt = 0;                     \
            idle_deep_cnt = 0;                \
        }                                     \
    } while (0)

#define MAX_LCORE_PARAMS 1024
#define MAX_RX_QUEUE_PER_PORT 128
#define MAX_RX_QUEUE_PER_LCORE 16
#define NB_SOCKETS 8

#define SOCKET_NUM_MAX 8
#define CORE_NUM_MAX 64

// 全局配置参数
int g_conf_pcap_timestamp = 0;      /* 是否使用数据包的时间戳,默认不使用 */
volatile eth_data_info_t eth_infos; /* 统计网卡接收信息的数据丢失情况 */
static struct timeval g_pcap_tv;
// 工作线程最大数量
static int g_conf_mbuf_data_size = MBUF_DATA_SIZE;  // RTE_MBUF_DEFAULT_DATAROOM; // ETHER_MAX_JUMBO_FRAME_LEN
static int g_conf_work_thread = WORKER_MAX_NUM;
static int g_conf_ip_work_thread = DEFAULT_IP_WORKER_NUM;
static int g_conf_rx_desc_per_queue = RX_DESC_PER_QUEUE;
static int g_conf_tx_desc_per_queue = TX_DESC_PER_QUEUE;
static int g_conf_max_pkts_burst = MAX_PKTS_BURST;
static int g_conf_mbuf_per_pool = MBUF_PER_POOL;
static int g_conf_mbuf_pool_cache_size = MBUF_POOL_CACHE_SIZE;
static int g_conf_ring_worker_size = RING_WORKER_SIZE;
static int g_conf_ring_rx_size = RING_RX_SIZE;
static int g_conf_ring_tcp_size = RING_RX_SIZE;
static const char *g_conf_output_file_template = CONF_OUTPUT_FILE_TEMPLATE;
static int g_conf_snaplen = CONF_SNAPLEN;
static int g_conf_pkt_max_size = PKT_MAX_SIZE;
static int g_i_sample_mode = SAMPLE_MODE;
static int g_i_sample_rate = SAMPLE_RATE;
static uint32_t g_u32_eth_num = 0;
// static int g_i_is_write_pcap_header = 0;
CALLBACK_SAMPLE g_cb_sample = NULL;

static uint32_t g_conf_speed = RTE_ETH_SPEED_NUM_NONE;  // ETH_SPEED_NUM_100M; // RTE_ETH_SPEED_NUM_NONE;
static int g_conf_duplex = RTE_ETH_LINK_FULL_DUPLEX;    // ETH_LINK_HALF_DUPLEX; // RTE_ETH_LINK_FULL_DUPLEX;
static uint32_t g_conf_speed_fixed =
    RTE_ETH_LINK_SPEED_AUTONEG;  // RTE_ETH_LINK_SPEED_FIXED; // RTE_ETH_LINK_SPEED_AUTONEG;
static int g_conf_idle_max_count = IDLE_MAX_CNT;
static int g_conf_converge_pkts = 1;                      /* 默认聚合数据包入队列 */
static int g_conf_ip_work_deq_num = 2048;                 /* IP worker 线程出队列的个数 */
static int g_conf_rx_work_thread = DEFAULT_RX_WORKER_NUM; /* rx线程数目 */
static int g_conf_rx_deq_num = 1024;                      /* 接受数据包默认聚合个数 */
static int g_conf_rx_converge_pkts = 0;                   /* 接受数据线程聚合数据包标示  */
static unsigned int portmask;
// static unsigned int disable_reorder;
static volatile uint8_t quit_signal = 0;
// 是否写入到文件
static volatile uint8_t g_pcap_write = 0;
// 显示详细内容
static volatile uint8_t g_verbose_mode = 0;

/* IP worker 空闲时计数，防止数据包个数小于聚合数一直等待 */
static int g_conf_converge_idel_cnt = 100000;
int g_risk_flag = 1;

#define MAX_RING_NUM 128

static struct rte_ring *g_ip_ring[MAX_RING_NUM] = {NULL};
static struct rte_ring *g_tcp_ring[MAX_RING_NUM] = {NULL};

static int g_ip_cache_clean = 0;
static int g_tcp_cache_clean = 0;

static time_t g_lasttime_timestamp = 0;
static uint64_t g_lasttime_ipackets[8] = {0};
static uint64_t g_lasttime_ibytes[8] = {0};

uint8_t g_rx_lcore_id[RTE_MAX_LCORE];
uint8_t g_ip_lcore_id[RTE_MAX_LCORE];
uint8_t g_tcp_lcore_id[RTE_MAX_LCORE];
uint8_t g_cpu_layout[SOCKET_NUM_MAX][CORE_NUM_MAX][2];

uint8_t g_app_stats_count = 0;
app_stats_t *g_app_stats_array[CORE_NUM_MAX];
pthread_mutex_t g_app_stats_mutex = PTHREAD_MUTEX_INITIALIZER;

uint8_t g_rss_enable = 0;

volatile app_stats_t app_stats;

struct worker_thread_args {
    struct rte_ring *ring_in;
    struct rte_ring *ring_out;
    void(fastcall *callback)(void *, unsigned int, void *);
    void *userdata;
};

struct rx_tcp_thread_args {
    CALLBACK_PKT callback;
    void *userdata;
    uint8_t port_id[MAX_DEAL_PORT_PER_RX_THREAD];
    int port_id_cnt;
    struct rte_ring *ring_out[TCP_RING_OUT_MAX_SIZE];  // 数组形式 最多为8个
};

struct ip_worker_thread_args {
    struct rte_ring *ring_in[RX_IP_RING_OUT_MAX_SIZE];
    struct rte_ring *ring_out[TCP_RING_OUT_MAX_SIZE];  // 数组形式 最多为8个
    uint16_t u16_ip_ring_cnt;
    CALLBACK_PKT callback;
    void *userdata;
};

struct tcp_worker_thread_args {
    struct rte_ring *ring_in;
    struct rte_ring *ring_out;
    CALLBACK_PCAP callback;
    void *userdata;
};

struct rx_thread_args {
    uint8_t port_id[MAX_DEAL_PORT_PER_RX_THREAD];
    int port_id_cnt;
    struct rte_ring *ring_out;
};

struct lcore_params {
    uint8_t port_id;
    uint8_t queue_id;
    uint8_t lcore_id;
} __rte_cache_aligned;

static uint16_t nb_lcore_params = 0;
static struct lcore_params lcore_params[MAX_LCORE_PARAMS];

struct lcore_rx_queue {
    uint8_t port_id;
    uint8_t queue_id;
} __rte_cache_aligned;

struct lcore_conf {
    uint16_t n_rx_queue;
    struct lcore_rx_queue rx_queue_list[MAX_RX_QUEUE_PER_LCORE];
} __rte_cache_aligned;

struct lcore_conf lcore_conf[RTE_MAX_LCORE];
static struct rte_mempool *pktmbuf_pool[NB_SOCKETS];

/*
 *
 * IRQ smp_affinity
 *
 */

#define IRQ_SMP_AFFINITY_BUF_SIZE (1024)
#define IRQ_DIR "/proc/irq/"
#define PCI_DEVICES_DIR "/sys/bus/pci/devices/"

// 读取irq的CPU亲和性默认值
static inline size_t read_irq_default_smp_affinity(char *buf, size_t count)
{
    FILE *fp;
    size_t res;

    fp = fopen("/proc/irq/default_smp_affinity", "r");
    if (fp == NULL) {
        return 0;
    }
    res = fread(buf, sizeof(buf[0]), count, fp);
    fclose(fp);
    printf("read default_smp_affinity=%s\n", buf);
    return res;
}

struct _tag_irq_smp_t {
    int irq;
    char *cpu_affinity;
    struct _tag_irq_smp_t *next;
};
typedef struct _tag_irq_smp_t irq_smp_t;

// 读取所有irq的CPU亲和性值
static irq_smp_t *read_irq_all_smp_affinity(void)
{
    FILE *fp;
    DIR *d;
    struct dirent *file;
    struct stat sb;
    char path[PATH_MAX] = {0};
    irq_smp_t *irq_smp_root = NULL;
    irq_smp_t *irq_smp = NULL;
    char smp_affinity[IRQ_SMP_AFFINITY_BUF_SIZE] = {0};
    int cnt = 0;
    int cnt_succ = 0;
    size_t n;

    // 遍历目录 直接写文件
    d = opendir(IRQ_DIR);
    if (d == NULL) {
        return NULL;
    }

    while ((file = readdir(d)) != NULL) {
        if (strncmp(file->d_name, ".", 1) == 0) {
            continue;
        }
        if (g_verbose_mode) {
            printf("read_irq_all_smp_affinity: Search dir filename = \"%s\"\n", file->d_name);
        }

        snprintf(path, COUNTOF(path) - 1, "%s%s", IRQ_DIR, file->d_name);
        if (!(stat(path, &sb) >= 0 && S_ISDIR(sb.st_mode))) {
            continue;
        }
        cnt++;

        // smp_affinity path
        snprintf(path, COUNTOF(path) - 1, "%s%s/smp_affinity", IRQ_DIR, file->d_name);
        if (!(access(path, F_OK) == 0)) {
            continue;
        }

        fp = fopen(path, "r");
        if (fp == NULL) {
            printf("read_irq_all_smp_affinity: read file \"%s\" failed\n", path);
            continue;
        }
        if (g_verbose_mode) {
            printf("read_irq_all_smp_affinity: read file \"%s\" successed\n", path);
        }

        memset(smp_affinity, 0, sizeof(smp_affinity));
        n = fread(smp_affinity, sizeof(smp_affinity[0]), COUNTOF(smp_affinity) - 1, fp);
        fclose(fp);
        if ((int)n <= 0) {
            continue;
        }

        if (irq_smp_root != NULL) {
            if (irq_smp == NULL) {
                continue;
            }
            irq_smp->next = (irq_smp_t *)malloc(sizeof(irq_smp_t));
            irq_smp = irq_smp->next;
        } else {
            irq_smp_root = irq_smp = (irq_smp_t *)malloc(sizeof(irq_smp_t));
        }
        if (irq_smp == NULL) {
            continue;
        }

        irq_smp->next = NULL;
        irq_smp->irq = atoi(file->d_name);
        irq_smp->cpu_affinity = (char *)malloc(sizeof(irq_smp->cpu_affinity[0]) * (strlen(smp_affinity) + 1));
        if (irq_smp->cpu_affinity == NULL) {
            continue;
        }
        strcpy(irq_smp->cpu_affinity, smp_affinity);
        cnt_succ++;
    }

    closedir(d);

    printf("read_irq_all_smp_affinity: read file num = %d; succ = %d\n", cnt, cnt_succ);
    return irq_smp_root;
}

// 写入所有irq的CPU亲和性值
static void write_irq_all_smp_affinity(const irq_smp_t *irq_smp_root)
{
    FILE *fp;
    char path[PATH_MAX] = {0};
    const irq_smp_t *irq_smp;
    size_t n;
    int cnt = 0;
    int cnt_succ = 0;

    for (irq_smp = irq_smp_root; irq_smp != NULL; irq_smp = irq_smp->next) {
        if (irq_smp->cpu_affinity == NULL) {
            continue;
        }

        cnt++;
        snprintf(path, COUNTOF(path) - 1, "%s%d/smp_affinity", IRQ_DIR, irq_smp->irq);
        if (!(access(path, F_OK) == 0)) {
            continue;
        }

        fp = fopen(path, "w");
        if (fp == NULL) {
            printf("write_irq_all_smp_affinity: write file \"%s\" failed\n", path);
            continue;
        }
        if (g_verbose_mode) {
            printf("write_irq_all_smp_affinity: write file \"%s\" successed\n", path);
        }
        n = fwrite(irq_smp->cpu_affinity, sizeof(irq_smp->cpu_affinity[0]), strlen(irq_smp->cpu_affinity), fp);
        if (n == strlen(irq_smp->cpu_affinity)) {
            cnt_succ++;
        }
        fclose(fp);
    }

    printf("write_irq_all_smp_affinity: write file num = %d; succ = %d\n", cnt, cnt_succ);
}

static inline void free_all_irq_smp(irq_smp_t *irq_smp_root)
{
    irq_smp_t *irq_smp = irq_smp_root;

    while (irq_smp != NULL) {
        if (irq_smp->cpu_affinity != NULL) {
            free(irq_smp->cpu_affinity);
        }
        irq_smp_root = irq_smp;
        irq_smp = irq_smp->next;
        free(irq_smp_root);
    }

    return;
}

// 更新irq的CPU亲和性
static void update_irq(const char *default_smp_affinity)
{
    FILE *fp;
    DIR *d;
    struct dirent *file;
    struct stat sb;
    char path[PATH_MAX] = {0};
    int cnt = 0;
    int cnt_succ = 0;
    size_t n;

    if (default_smp_affinity == NULL || strlen(default_smp_affinity) == 0) {
        return;
    }

    printf("write default_smp_affinity=%s\n", default_smp_affinity);
    fp = fopen("/proc/irq/default_smp_affinity", "w");
    if (fp == NULL) {
        return;
    }
    n = fwrite(default_smp_affinity, sizeof(default_smp_affinity[0]), strlen(default_smp_affinity), fp);
    if (n == strlen(default_smp_affinity)) {
    }
    fclose(fp);

    // 遍历目录 直接写文件
    d = opendir(IRQ_DIR);
    if (d == NULL) {
        return;
    }

    while ((file = readdir(d)) != NULL) {
        if (strncmp(file->d_name, ".", 1) == 0) {
            continue;
        }
        if (g_verbose_mode) {
            printf("update_irq: Search dir filename = \"%s\"\n", file->d_name);
        }

        snprintf(path, COUNTOF(path) - 1, "%s%s", IRQ_DIR, file->d_name);
        if (!(stat(path, &sb) >= 0 && S_ISDIR(sb.st_mode))) {
            continue;
        }
        cnt++;

        // smp_affinity path
        snprintf(path, COUNTOF(path) - 1, "%s%s/smp_affinity", IRQ_DIR, file->d_name);
        if (!(access(path, F_OK) == 0)) {
            continue;
        }
        if (g_verbose_mode) {
            printf("update_irq: write file \"%s\"\n", path);
        }

        fp = fopen(path, "w");
        if (fp == NULL) {
            printf("update_irq: update file \"%s\" failed\n", path);
            continue;
        }
        if (g_verbose_mode) {
            printf("update_irq: update file \"%s\" successed\n", path);
        }
        n = fwrite(default_smp_affinity, sizeof(default_smp_affinity[0]), strlen(default_smp_affinity), fp);
        if (n == strlen(default_smp_affinity)) {
            cnt_succ++;
        }
        fclose(fp);
    }

    closedir(d);

    printf("update_irq: update file num = %d; succ = %d\n", cnt, cnt_succ);
}

// 调用者需要free
static inline char *get_irq_smp_affinity(int *lcore_flags, unsigned int lcore_num)
{
    char *p = NULL;

    int cnt = 0;
    char buf[IRQ_SMP_AFFINITY_BUF_SIZE] = {0};
    size_t buf_idx = 0;
    int buf_cnt = 0;
    int t = 0;
    int m = 1;
    size_t i, k;
    unsigned int lcore_id;

    for (lcore_id = 0; lcore_id < lcore_num; lcore_id++) {
        if (buf_idx > COUNTOF(buf) - 1 - 1) {
            t = 0;
            break;
        }

        if (lcore_flags[lcore_id] == 0) {
            cnt++;
            t |= m;
        }

        if (!((lcore_id & (4 - 1)) == (4 - 1))) {
            m <<= 1;
            continue;
        }
        if (buf_cnt && (buf_cnt & (8 - 1)) == 0) {
            buf[buf_idx++] = ',';
        }

        buf_cnt++;
        buf[buf_idx++] = (t > 9 ? 'a' + t - 10 : '0' + t);
        t = 0;
        m = 1;
    }

    if (buf_idx < COUNTOF(buf) - 1 - 1) {
        if (t) {
            if (buf_cnt && (buf_cnt & (8 - 1)) == 0) {
                buf[buf_idx++] = ',';
            }
            buf[buf_idx++] = (t > 9 ? 'a' + t - 10 : '0' + t);
        }
    }

    printf("lcore_flags: core_num=%d, cnt=%d buf=%s\n", lcore_num, cnt, buf);

    // 字符串求反
    for (i = 0, k = strlen(buf) - 1; i < strlen(buf) / 2; i++, k--) {
        t = buf[i];
        buf[i] = buf[k];
        buf[k] = t;
    }

    if (cnt > 0) {
        p = (char *)malloc(sizeof(buf[0]) * (strlen(buf) + 1));
        if (p != NULL) {
            strcpy(p, buf);
        }
    }

    return p;
}

static inline int get_pci_irq_read_file(const char *path, char *buf, int size)
{
    FILE *fp;
    size_t n;

    if (!(access(path, F_OK) == 0)) {
        return -1;
    }

    if (g_verbose_mode) {
        printf("get_pci_irq_read_file: read file \"%s\"\n", path);
    }

    fp = fopen(path, "r");
    if (fp == NULL) {
        printf("get_pci_irq_read_file: read file \"%s\" failed\n", path);
        return -1;
    }
    if (g_verbose_mode) {
        printf("get_pci_irq_read_file: read file \"%s\" successed\n", path);
    }
    n = fread(buf, sizeof(buf[0]), size, fp);
    fclose(fp);

    return (int)n;
}

// 调用者需要free
static char *get_pci_irq(int pci_class)
{
    // 遍历目录 直接读指定PCI类别设备的IRQ
    DIR *d;
    struct dirent *file;
    struct stat sb;
    char path[PATH_MAX] = {0};
    int cnt = 0;
    int cnt_succ = 0;
    char buf[1024] = {0};
    int n;
    int r_irq;
    char r_class[32];
    char irq_list[1024] = {0};
    char pci_class_str[32] = {0};
    char *p;

    snprintf(pci_class_str, COUNTOF(pci_class_str), (pci_class >= 0x10000 ? "0x%.6x" : "0x%.4x"), pci_class);

    d = opendir(PCI_DEVICES_DIR);
    if (d != NULL) {
        while ((file = readdir(d)) != NULL) {
            if (strncmp(file->d_name, ".", 1) == 0) {
                continue;
            }
            if (g_verbose_mode) {
                printf("get_pci_irq: Search dir filename = \"%s\"\n", file->d_name);
            }

            snprintf(path, COUNTOF(path) - 1, "%s%s", PCI_DEVICES_DIR, file->d_name);
            if (!(stat(path, &sb) >= 0 && S_ISDIR(sb.st_mode))) {
                continue;
            }

            cnt++;
            // class path
            snprintf(path, COUNTOF(path) - 1, "%s%s/class", PCI_DEVICES_DIR, file->d_name);
            n = get_pci_irq_read_file(path, buf, sizeof(buf));
            memset(r_class, 0, sizeof(r_class));
            if (n > 0) {
                memcpy(r_class, buf, MIN((size_t)n, COUNTOF(r_class) - 1));
            }

            // irq path
            snprintf(path, COUNTOF(path) - 1, "%s%s/irq", PCI_DEVICES_DIR, file->d_name);
            n = get_pci_irq_read_file(path, buf, sizeof(buf));
            r_irq = -1;
            if (n > 0) {
                //
                buf[MIN((size_t)n, COUNTOF(buf) - 1)] = '\0';
                r_irq = atoi(buf);
            }

            if (r_irq >= 0 && strlen(r_class) > 0) {
                // printf("r_class=%u %s\n", strlen(r_class), r_class);
                // printf("pci_class_str=%s\n", pci_class_str);
                if (0 == memcmp(r_class, pci_class_str, strlen(pci_class_str))) {
                    size_t length = strlen(irq_list);
                    snprintf(irq_list + length, COUNTOF(irq_list) - 1 - length, (length == 0 ? "%d" : ",%d"), r_irq);
                    cnt_succ++;
                }
            }
        }

        closedir(d);
    }

    printf("get_pci_irq: read file num = %d; succ = %d; irq_list=%s\n", cnt, cnt_succ, irq_list);
    if (strlen(irq_list) == 0) {
        return NULL;
    }
    p = (char *)malloc(strlen(irq_list) + 1);
    if (p == NULL) {
        return p;
    }

    return strcpy(p, irq_list);
}

/**
 * Get the last enabled lcore ID
 *
 * @return
 *   The last enabled lcore ID.
 */
static unsigned int get_last_lcore_id(void)
{
    int i;

    for (i = RTE_MAX_LCORE - 1; i >= 0; i--)
        if (rte_lcore_is_enabled(i)) return i;
    return 0;
}

static time_t cap_time(void)
{
    struct timespec tp;
    clock_gettime(CLOCK_MONOTONIC_RAW, &tp);
    return tp.tv_sec;
}

static void put_pkt_to_queue(struct rte_mbuf **pp_mbuf_table, uint16_t u16_pkt_cnt)
{
    // RTE_LOG(NOTICE, GWHW, "pkt cnt = %hu\n", u16_pkt_cnt);
    int i_ret = 0;
    uint16_t i = 0;
    for (i = 0; i < u16_pkt_cnt; ++i) {
        struct rte_mbuf *p_mbuf_ptr = pp_mbuf_table[i];
        struct pcap_packet_header st_pkt_header;
        memset(&st_pkt_header, 0, sizeof(struct pcap_packet_header));

        void *p_data = NULL;
        unsigned char *p_queue_data = NULL;
        unsigned u_mbuf_data_len = 0;
        struct timeval tv;
        unsigned u_cap_pktlen = 0;
        unsigned u_wire_pkt_len = 0;
        u_wire_pkt_len = rte_pktmbuf_pkt_len(p_mbuf_ptr);
        u_cap_pktlen = MIN((unsigned)g_conf_snaplen, u_wire_pkt_len);
        uint32_t u32_sample_len = 0;

        gettimeofday(&tv, NULL);
        st_pkt_header.timestamp = (uint32_t)tv.tv_sec;
        st_pkt_header.microseconds = (uint32_t)tv.tv_usec;
        st_pkt_header.packet_length = u_cap_pktlen;
        st_pkt_header.packet_length_wire = u_wire_pkt_len;
        // RTE_LOG(NOTICE, GWHW, "packet_length = %u\n", u_cap_pktlen);
        u32_sample_len = sizeof(struct pcap_packet_header) + u_cap_pktlen;
        p_queue_data = (unsigned char *)malloc(u32_sample_len + sizeof(uint32_t));
        if (p_queue_data == NULL) {
            return;
        }
        memset(p_queue_data, 0, u32_sample_len + sizeof(uint32_t));
        /* 有可能会导致性能下降 */
        memcpy(p_queue_data, &u32_sample_len, sizeof(uint32_t));
        memcpy(p_queue_data + sizeof(uint32_t), &st_pkt_header, sizeof(struct pcap_packet_header));
        unsigned u_enqeue_len = 0;
        while (p_mbuf_ptr != NULL) {
            p_data = rte_pktmbuf_mtod(p_mbuf_ptr, void *);
            u_mbuf_data_len = rte_pktmbuf_data_len(p_mbuf_ptr);
            if (u_enqeue_len + u_mbuf_data_len > u_cap_pktlen) {
                u_mbuf_data_len = u_cap_pktlen - u_enqeue_len;
            }
            memcpy(p_queue_data + sizeof(uint32_t) + sizeof(struct pcap_packet_header) + u_enqeue_len, p_data,
                   u_mbuf_data_len);
            u_enqeue_len += u_mbuf_data_len;
            p_mbuf_ptr = p_mbuf_ptr->next;
        }
        // rte_pktmbuf_free(pp_mbuf_table[i]);

        // RTE_LOG(NOTICE, GWHW, "pkt len = %u, pkt wire len = %u, sample len = %u\n", st_pkt_header.packet_length,
        // st_pkt_header.packet_length_wire, u32_sample_len);
        /* 调用回调函数 */
        i_ret = g_cb_sample(p_queue_data);
        if (i_ret != 0) {
            free(p_queue_data);
            p_queue_data = NULL;
        }
    }
}

static void put_pkt_to_flow_queue(struct rte_mbuf **pp_mbuf_table, uint16_t u16_pkt_cnt)
{
    // RTE_LOG(NOTICE, GWHW, "pkt cnt = %hu\n", u16_pkt_cnt);
    //  int i_ret = 0;
    uint16_t i = 0;
    for (i = 0; i < u16_pkt_cnt; ++i) {
        struct rte_mbuf *p_mbuf_ptr = pp_mbuf_table[i];
        struct pcap_packet_header st_pkt_header;
        memset(&st_pkt_header, 0, sizeof(struct pcap_packet_header));

        void *p_data = NULL;
        unsigned char *p_queue_data = NULL;
        unsigned u_mbuf_data_len = 0;
        unsigned u_cap_pktlen = 0;
        unsigned u_wire_pkt_len = 0;
        u_wire_pkt_len = rte_pktmbuf_pkt_len(p_mbuf_ptr);
        u_cap_pktlen = MIN((unsigned)g_conf_snaplen, u_wire_pkt_len);

        p_queue_data = (unsigned char *)malloc(u_cap_pktlen);
        if (p_queue_data == NULL) {
            return;
        }
        memset(p_queue_data, 0, u_cap_pktlen);
        unsigned u_enqeue_len = 0;
        while (p_mbuf_ptr != NULL) {
            p_data = rte_pktmbuf_mtod(p_mbuf_ptr, void *);
            u_mbuf_data_len = rte_pktmbuf_data_len(p_mbuf_ptr);
            if (u_enqeue_len + u_mbuf_data_len > u_cap_pktlen) {
                u_mbuf_data_len = u_cap_pktlen - u_enqeue_len;
            }
            memcpy(p_queue_data + u_enqeue_len, p_data, u_mbuf_data_len);
            u_enqeue_len += u_mbuf_data_len;
            p_mbuf_ptr = p_mbuf_ptr->next;
        }
        // rte_pktmbuf_free(pp_mbuf_table[i]);

        // RTE_LOG(NOTICE, GWHW, "pkt len = %u, pkt wire len = %u, sample len = %u\n", st_pkt_header.packet_length,
        // st_pkt_header.packet_length_wire, u32_sample_len);
        /* 调用回调函数 */
        g_cb_flow((char *)p_queue_data, u_cap_pktlen);
    }
}

static void update_irq_only(unsigned int lcore_irq_only, const char *p_irq_sata)
{
    const char *p = p_irq_sata;
    char *p2;
    int cnt = 0;
    int cnt_succ = 0;
    char path[PATH_MAX] = {0};
    char irq_name[16] = {0};
    int lcore_flags[RTE_MAX_LCORE] = {0};
    unsigned int last_lcore_id = get_last_lcore_id();
    FILE *fp;
    char *buf;
    size_t k;
    size_t n;

    if (p == NULL) {
        return;
    }
    if ((signed)lcore_irq_only < 0 || lcore_irq_only >= COUNTOF(lcore_flags)) {
        return;
    }

    lcore_flags[lcore_irq_only] = 1;
    for (k = 0; k < COUNTOF(lcore_flags); k++) {
        lcore_flags[k] = (lcore_flags[k] ? 0 : 1);
    }
    buf = get_irq_smp_affinity(lcore_flags, last_lcore_id + 1);
    if (buf == NULL) {
        return;
    }

    while (p != NULL && *p != '\0') {
        cnt++;

        memset(irq_name, 0, sizeof(irq_name));
        p2 = strchr(p, ',');
        if (p2 == NULL) {
            strncpy(irq_name, p, COUNTOF(irq_name) - 1);
            p = NULL;
        } else {
            strncpy(irq_name, p, MIN(COUNTOF(irq_name) - 1, (size_t)(p2 - p)));
            p = p2 + 1;
        }

        // smp_affinity path
        snprintf(path, COUNTOF(path) - 1, "%s%s/smp_affinity", IRQ_DIR, irq_name);
        // printf("smp affinity path = %s\n", path);
        if (!(access(path, F_OK) == 0)) {
            continue;
        }
        if (g_verbose_mode) {
            printf("update_irq_only: write file \"%s\"\n", path);
        }

        fp = fopen(path, "w");
        if (fp == NULL) {
            printf("update_irq_only: update file \"%s\" failed\n", path);
            continue;
        }
        if (g_verbose_mode) {
            printf("update_irq_only: update file \"%s\" successed\n", path);
        }
        n = fwrite(buf, sizeof(buf[0]), strlen(buf), fp);
        if (n == strlen(buf)) {
            cnt_succ++;
        }
        fclose(fp);
    }

    free(buf);

    printf("update_irq_only: update file num = %d; succ = %d\n", cnt, cnt_succ);
}

/**
 * Get the previous enabled lcore ID
 * @param id
 *  The current lcore ID
 * @return
 *   The previous enabled lcore ID or the current lcore
 *   ID if it is the first available core.
 */
// static unsigned int
// get_previous_lcore_id(unsigned int id)
// {
//   int i;

//   for (i = id - 1; i >= 0; i--)
//     if (rte_lcore_is_enabled(i))
//       return i;
//   return id;
// }

static void pktmbuf_free_bulk(struct rte_mbuf *mbuf_table[], unsigned n)
{
    unsigned int i;

    for (i = 0; i < n; i++) rte_pktmbuf_free(mbuf_table[i]);
}

/* display usage */
static void print_usage(const char *prgname)
{
    printf(
        "%s [EAL options] -- -p PORTMASK\n"
        "  -p PORTMASK: hexadecimal bitmask of ports to configure\n"
        "  -v, --verbose : verbose mode\n"
        "  -w : write pcap file\n",
        prgname);
}

static int parse_portmask(const char *portmask)
{
    unsigned long pm;
    char *end = NULL;
    const char *p = portmask;

    /* parse hexadecimal string */
    if (*p == '=' || *p == ' ') {
        p++;
    }
    pm = strtoul(p, &end, 16);
    if (g_verbose_mode) {
        printf("portmask:%s\n", p);
    }
    if ((portmask[0] == '\0') || (end == NULL) || (*end != '\0')) return -1;

    if (pm == 0) return -1;

    return pm;
}

void app_stats_register(app_stats_t *app_stats)
{
    pthread_mutex_lock(&g_app_stats_mutex);
    g_app_stats_array[g_app_stats_count] = app_stats;
    g_app_stats_count++;
    pthread_mutex_unlock(&g_app_stats_mutex);
}

app_stats_t app_stats_collect()
{
    app_stats_t app_stats;
    memset(&app_stats, 0, sizeof(app_stats));

    uint8_t i = 0;
    for (i = 0; i < g_app_stats_count; i++) {
        app_stats.rx.rx_pkts += g_app_stats_array[i]->rx.rx_pkts;
        app_stats.rx.enqueue_pkts += g_app_stats_array[i]->rx.enqueue_pkts;
        app_stats.rx.enqueue_failed_pkts += g_app_stats_array[i]->rx.enqueue_failed_pkts;

        app_stats.wkr.dequeue_pkts += g_app_stats_array[i]->wkr.dequeue_pkts;
        app_stats.wkr.enqueue_pkts += g_app_stats_array[i]->wkr.enqueue_pkts;
        app_stats.wkr.enqueue_failed_pkts += g_app_stats_array[i]->wkr.enqueue_failed_pkts;

        app_stats.ip_wkr.dequeue_pkts += g_app_stats_array[i]->ip_wkr.dequeue_pkts;
        app_stats.ip_wkr.enqueue_pkts += g_app_stats_array[i]->ip_wkr.enqueue_pkts;
        app_stats.ip_wkr.enqueue_failed_pkts += g_app_stats_array[i]->ip_wkr.enqueue_failed_pkts;

        app_stats.tx.dequeue_pkts += g_app_stats_array[i]->tx.dequeue_pkts;
        app_stats.tx.early_pkts_txtd_woro += g_app_stats_array[i]->tx.early_pkts_txtd_woro;
        app_stats.tx.early_pkts_tx_failed_woro += g_app_stats_array[i]->tx.early_pkts_tx_failed_woro;
        app_stats.tx.ro_tx_pkts += g_app_stats_array[i]->tx.ro_tx_pkts;
        app_stats.tx.ro_tx_failed_pkts += g_app_stats_array[i]->tx.ro_tx_failed_pkts;
    }

    return app_stats;
}

static int parse_config(const char *q_arg)
{
    char s[256];
    const char *p, *p0 = q_arg;
    char *end;
    enum fieldnames { FLD_PORT = 0, FLD_QUEUE, FLD_LCORE, _NUM_FLD };
    unsigned long int_fld[_NUM_FLD];
    char *str_fld[_NUM_FLD];
    int i;
    unsigned size;

    nb_lcore_params = 0;

    printf("parse config=%s\n", q_arg);

    while ((p = strchr(p0, '(')) != NULL) {
        ++p;
        if ((p0 = strchr(p, ')')) == NULL) return -1;

        size = p0 - p;
        if (size >= sizeof(s)) return -1;

        snprintf(s, sizeof(s), "%.*s", size, p);
        if (rte_strsplit(s, sizeof(s), str_fld, _NUM_FLD, ',') != _NUM_FLD) return -1;

        for (i = 0; i < _NUM_FLD; i++) {
            errno = 0;
            int_fld[i] = strtoul(str_fld[i], &end, 0);
            if (errno != 0 || end == str_fld[i] || int_fld[i] > 255) return -1;
        }

        if (nb_lcore_params >= MAX_LCORE_PARAMS) {
            printf("exceeded max number of lcore params: %hu\n", nb_lcore_params);
            return -1;
        }

        lcore_params[nb_lcore_params].port_id = (uint8_t)int_fld[FLD_PORT];
        lcore_params[nb_lcore_params].queue_id = (uint8_t)int_fld[FLD_QUEUE];
        lcore_params[nb_lcore_params].lcore_id = (uint8_t)int_fld[FLD_LCORE];

        printf("get config result=%ld, %ld, %ld\n", int_fld[FLD_PORT], int_fld[FLD_QUEUE], int_fld[FLD_LCORE]);

        ++nb_lcore_params;
    }
    return 0;
}

static int check_lcore_params(void)
{
    uint8_t queue, lcore;
    uint16_t i;

    for (i = 0; i < nb_lcore_params; ++i) {
        queue = lcore_params[i].queue_id;
        if (queue >= MAX_RX_QUEUE_PER_PORT) {
            printf("invalid queue number: %hhu\n", queue);
            return -1;
        }
        lcore = lcore_params[i].lcore_id;
        if (!rte_lcore_is_enabled(lcore)) {
            printf("error: lcore %hhu is not enabled in lcore mask\n", lcore);
            return -1;
        }
    }
    return 0;
}

static int init_lcore_rx_queues(void)
{
    uint16_t i, nb_rx_queue;
    uint8_t lcore;

    memset(lcore_conf, 0, sizeof(lcore_conf));

    for (i = 0; i < nb_lcore_params; ++i) {
        lcore = lcore_params[i].lcore_id;
        nb_rx_queue = lcore_conf[lcore].n_rx_queue;
        if (nb_rx_queue >= MAX_RX_QUEUE_PER_LCORE) {
            printf("error: too many queues (%u) for lcore: %u\n", (unsigned)nb_rx_queue + 1, (unsigned)lcore);
            return -1;
        } else {
            lcore_conf[lcore].rx_queue_list[nb_rx_queue].port_id = lcore_params[i].port_id;
            lcore_conf[lcore].rx_queue_list[nb_rx_queue].queue_id = lcore_params[i].queue_id;

            printf("lcore id=%u, port id=%u, queue id=%u, n_rx_queue=%u\n", lcore,
                   lcore_conf[lcore].rx_queue_list[nb_rx_queue].port_id,
                   lcore_conf[lcore].rx_queue_list[nb_rx_queue].queue_id, nb_rx_queue);

            lcore_conf[lcore].n_rx_queue++;
            g_rx_lcore_id[lcore] = 1;
        }
    }

    return 0;
}

static uint8_t get_port_n_rx_queues(const uint8_t port)
{
    int queue = -1;
    uint16_t i;

    for (i = 0; i < nb_lcore_params; ++i) {
        if (lcore_params[i].port_id == port) {
            if (lcore_params[i].queue_id == queue + 1)
                queue = lcore_params[i].queue_id;
            else
                rte_exit(EXIT_FAILURE, "queue ids of the port %d must be in sequence and must start with 0\n",
                         lcore_params[i].port_id);
        }
    }
    return (uint8_t)(++queue);
}

static int init_mem(unsigned portid)
{
    int socketid;
    char s[64];

    socketid = rte_eth_dev_socket_id(portid);

    if (socketid >= NB_SOCKETS) {
        rte_exit(EXIT_FAILURE, "Socket %d of port %u is out of range %d\n", socketid, portid, NB_SOCKETS);
    }

    if (pktmbuf_pool[socketid] == NULL) {
        snprintf(s, sizeof(s), "mbuf_pool_%d", socketid);
        pktmbuf_pool[socketid] = rte_pktmbuf_pool_create(s, g_conf_mbuf_per_pool, g_conf_mbuf_pool_cache_size, 0,
                                                         (RTE_PKTMBUF_HEADROOM + g_conf_mbuf_data_size), socketid);
        if (pktmbuf_pool[socketid] == NULL) {
            rte_exit(EXIT_FAILURE, "Cannot init mbuf pool on socket %d, errno: %d\n", socketid, rte_errno);
        } else
            printf("Allocated mbuf pool on socket %d\n", socketid);
    }

    return 0;
}

/* Parse the argument given in the command line of the application */
static int parse_args(int argc, char **argv)
{
    int opt, ret = 0;
    int option_index;
    char **argvopt;
    char *prgname = argv[0];
    static struct option lgopts[] = {// optional_argument
                                     // {"disable-reorder", no_argument, 0, 0},
                                     {"verbose", no_argument, 0, 0},
                                     {"work-thread", required_argument, 0, 0},
                                     {"ip-work-thread", required_argument, 0, 0},
                                     {"rx-desc-per-queue", required_argument, 0, 0},
                                     {"tx-desc-per-queue", required_argument, 0, 0},
                                     {"max-pkts-burst", required_argument, 0, 0},
                                     {"mbuf-data-size", required_argument, 0, 0},
                                     {"mbuf-per-pool", required_argument, 0, 0},
                                     {"mbuf-pool-cache-size", required_argument, 0, 0},
                                     //{"ring-worker-size", required_argument, 0, 0},
                                     {"ring-rx-size", required_argument, 0, 0},
                                     {"ring-tcp-size", required_argument, 0, 0},
                                     {"output-file-template", required_argument, 0, 0},
                                     {"snaplen", required_argument, 0, 0},
                                     {"pkt-max-size", required_argument, 0, 0},
                                     {"speed", required_argument, 0, 0},
                                     {"duplex", required_argument, 0, 0},
                                     {"speed-fixed", required_argument, 0, 0},
                                     {"idle-max-count", required_argument, 0, 0},
                                     {"converge-pkts", required_argument, 0, 0},
                                     {"ip-work-deq-num", required_argument, 0, 0},
                                     {"rx-work-thread", required_argument, 0, 0},
                                     {"rx-deq-num", required_argument, 0, 0},
                                     {"rx-converge-pkts", required_argument, 0, 0},
                                     {"config", required_argument, 0, 0},
                                     {"rss-enable", required_argument, 0, 0},
                                     {NULL, 0, 0, 0}};

    const int old_optind = optind;
    const int old_optopt = optopt;
    char *const old_optarg = optarg;

    argvopt = argv;
    optind = 1;

    while ((opt = getopt_long(argc, argvopt, ":p:w", lgopts, &option_index)) != EOF) {
        switch (opt) {
            /* portmask */
            case 'p':
                portmask = parse_portmask(optarg);
                if (portmask == 0) {
                    printf("invalid portmask\n");
                    print_usage(prgname);
                    ret = -1;
                    goto out;
                }
                break;

            /* write pcap */
            case 'w':
                g_pcap_write = 1;
                break;

            /* verbose mode */
            case 'v':
                g_verbose_mode = 1;
                break;

            /* long options */
            case 0:
#define LONG_OPTIONS_ARG_FUNC(nm, var, func)             \
    else if (0 == strcmp(nm, lgopts[option_index].name)) \
    {                                                    \
        var = func(optarg);                              \
    }
#define LONG_OPTIONS_ARG(nm, var)                        \
    else if (0 == strcmp(nm, lgopts[option_index].name)) \
    {                                                    \
        var = (optarg);                                  \
    }

                if (0) {
                    // printf("reorder disabled\n");
                    // disable_reorder = 1;
                } else if (0 == strcmp("verbose", lgopts[option_index].name)) {
                    // printf("verbose:%s\n", optarg);
                    g_verbose_mode = 1;
                    // printf("verbose:%d\n", g_verbose_mode);
                } else if (0 == strcmp("config", lgopts[option_index].name)) {
                    ret = parse_config(optarg);
                    if (ret) {
                        goto out;
                    }
                }
                // else if (0 == strcmp("work-thread", lgopts[option_index].name))
                // {
                //   //printf("work-thread:%s\n", optarg);
                //   g_conf_work_thread = atoi(optarg);
                //   //printf("g_conf_work_thread:%d\n", g_conf_work_thread);
                // }
                LONG_OPTIONS_ARG_FUNC("work-thread", g_conf_work_thread, atoi)
                LONG_OPTIONS_ARG_FUNC("ip-work-thread", g_conf_ip_work_thread, atoi)
                LONG_OPTIONS_ARG_FUNC("rx-desc-per-queue", g_conf_rx_desc_per_queue, atoi)
                LONG_OPTIONS_ARG_FUNC("tx-desc-per-queue", g_conf_tx_desc_per_queue, atoi)
                LONG_OPTIONS_ARG_FUNC("max-pkts-burst", g_conf_max_pkts_burst, atoi)
                LONG_OPTIONS_ARG_FUNC("mbuf-data-size", g_conf_mbuf_data_size, atoi)
                LONG_OPTIONS_ARG_FUNC("mbuf-per-pool", g_conf_mbuf_per_pool, atoi)
                LONG_OPTIONS_ARG_FUNC("mbuf-pool-cache-size", g_conf_mbuf_pool_cache_size, atoi)
                LONG_OPTIONS_ARG_FUNC("ring-worker-size", g_conf_ring_worker_size, atoi)
                LONG_OPTIONS_ARG_FUNC("ring-rx-size", g_conf_ring_rx_size, atoi)
                LONG_OPTIONS_ARG_FUNC("ring-tcp-size", g_conf_ring_tcp_size, atoi)
                LONG_OPTIONS_ARG_FUNC("snaplen", g_conf_snaplen, atoi)
                LONG_OPTIONS_ARG_FUNC("pkt-max-size", g_conf_pkt_max_size, atoi)
                LONG_OPTIONS_ARG("output-file-template", g_conf_output_file_template)

                LONG_OPTIONS_ARG_FUNC("duplex", g_conf_duplex, atoi)
                LONG_OPTIONS_ARG_FUNC("speed", g_conf_speed, atoi)
                LONG_OPTIONS_ARG_FUNC("speed-fixed", g_conf_speed_fixed, atoi)
                LONG_OPTIONS_ARG_FUNC("idle-max-count", g_conf_idle_max_count, atoi)
                LONG_OPTIONS_ARG_FUNC("converge-pkts", g_conf_converge_pkts, atoi)
                LONG_OPTIONS_ARG_FUNC("ip-work-deq-num", g_conf_ip_work_deq_num, atoi)
                LONG_OPTIONS_ARG_FUNC("rx-work-thread", g_conf_rx_work_thread, atoi)
                LONG_OPTIONS_ARG_FUNC("rx-deq-num", g_conf_rx_deq_num, atoi)
                LONG_OPTIONS_ARG_FUNC("rx-converge-pkts", g_conf_rx_converge_pkts, atoi)
                LONG_OPTIONS_ARG_FUNC("rss-enable", g_rss_enable, atoi)
                break;
            case '?':
                break;
            default:
                print_usage(prgname);
                ret = -1;
                goto out;
        }
    }
    if (optind <= 1) {
        print_usage(prgname);
        ret = -1;
        goto out;
    }

    argv[optind - 1] = prgname;
    optind = 1; /* reset getopt lib */

    if (g_conf_work_thread > WORKER_PARAMS_MAX_NUM) {
        printf("tcp worker thread cnt(%d) greater than WORKER_PARAMS_MAX_NUM(%d)\n", g_conf_work_thread,
               WORKER_PARAMS_MAX_NUM);
        ret = -1;
    }

out:
    /* restore getopt lib */
    optind = old_optind;
    optopt = old_optopt;
    optarg = old_optarg;

    return ret;
}

// /*
//  * Tx buffer error callback
//  */
// static void
// flush_tx_error_callback(struct rte_mbuf **unsent, uint16_t count,
//     void *userdata __rte_unused) {
//
//   /* free the mbufs which failed from transmit */
//   app_stats.tx.ro_tx_failed_pkts += count;
// //  RTE_LOG(DEBUG, GWHW, "%s:Packet loss with tx_burst\n", __func__);
//   pktmbuf_free_bulk(unsent, count);
//
// }

// static inline int
// free_tx_buffers(struct rte_eth_dev_tx_buffer *tx_buffer[]) {
//   const uint8_t nb_ports = rte_eth_dev_count_avail();
//   unsigned port_id;

//   /* initialize buffers for all ports */
//   for (port_id = 0; port_id < nb_ports; port_id++) {
//     /* skip ports that are not enabled */
//     if ((portmask & (1 << port_id)) == 0)
//       continue;

//     rte_free(tx_buffer[port_id]);
//   }
//   return 0;
// }

// static inline int
// configure_tx_buffers(struct rte_eth_dev_tx_buffer *tx_buffer[])
// {
//   const uint8_t nb_ports = rte_eth_dev_count_avail();
//   unsigned port_id;
//   int ret;

//   /* initialize buffers for all ports */
//   for (port_id = 0; port_id < nb_ports; port_id++) {
//     /* skip ports that are not enabled */
//     if ((portmask & (1 << port_id)) == 0)
//       continue;

//     /* Initialize TX buffers */
//     tx_buffer[port_id] = rte_zmalloc_socket("tx_buffer",
//         RTE_ETH_TX_BUFFER_SIZE(g_conf_max_pkts_burst), 0,
//         rte_eth_dev_socket_id(port_id));
//     if (tx_buffer[port_id] == NULL)
//       rte_exit(EXIT_FAILURE, "Cannot allocate buffer for tx on port %u\n",
//           (unsigned) port_id);

//     rte_eth_tx_buffer_init(tx_buffer[port_id], g_conf_max_pkts_burst);

//     ret = rte_eth_tx_buffer_set_err_callback(tx_buffer[port_id],
//         flush_tx_error_callback, NULL);
//     if (ret < 0)
//       rte_exit(EXIT_FAILURE, "Cannot set error callback for "
//           "tx buffer on port %u\n", (unsigned) port_id);
//   }
//   return 0;
// }

static int configure_eth_port(uint8_t port_id)
{
    struct rte_ether_addr addr;
    uint16_t rxRings = 1, txRings = 1;
    // const uint16_t txRings = 1;
    // uint16_t rxRings = 0;
    const uint8_t nb_ports = rte_eth_dev_count_avail();
    struct rte_eth_dev_info dev_info;
    int ret;
    uint16_t q;
    int stat;
    uint16_t mtu;
    uint16_t new_mtu;
    char name[256] = {0};
    struct rte_eth_conf port_conf_default = {0};
    uint32_t link_speeds = RTE_ETH_LINK_SPEED_AUTONEG;  // RTE_ETH_LINK_SPEED_FIXED | x
    uint32_t speed = g_conf_speed;                      // ETH_SPEED_NUM_100M; // RTE_ETH_SPEED_NUM_NONE;
    int duplex = g_conf_duplex;                         // RTE_ETH_LINK_FULL_DUPLEX; // ETH_LINK_HALF_DUPLEX;
    uint32_t speed_fixed = g_conf_speed_fixed;          // RTE_ETH_LINK_SPEED_FIXED; // RTE_ETH_LINK_SPEED_AUTONEG;

    if (port_id > nb_ports) return -1;

    if (g_verbose_mode) {
        ret = rte_eth_dev_is_valid_port(port_id);
        printf("rte_eth_dev_is_valid_port(%d)=%d\n", port_id, ret);
    }

    if (g_verbose_mode) {
        ret = rte_eth_dev_get_name_by_port(port_id, name);
        printf("port %d name: %s ret=%d\n", port_id, name, ret);
    }

    /* Get the device info */
    rte_eth_dev_info_get(port_id, &dev_info);
    if (g_verbose_mode) {
        printf("get device info:\n");
        printf("  rx_desc_lim.nb_min=%d\n", dev_info.rx_desc_lim.nb_min);
        printf("  rx_desc_lim.nb_max=%d\n", dev_info.rx_desc_lim.nb_max);
        printf("  rx_desc_lim.nb_align=%d\n", dev_info.rx_desc_lim.nb_align);
        printf("  max_rx_pktlen=%d\n", dev_info.max_rx_pktlen);
        printf("  min_rx_bufsize=%d\n", dev_info.min_rx_bufsize);
        printf("  max_rx_queues=%d\n", dev_info.max_rx_queues);
        printf("  max_tx_queues=%d\n", dev_info.max_tx_queues);
        printf("  max_vfs=%d \n", dev_info.max_vfs);
    }
    // rxRings = dev_info.max_rx_queues;

    static uint8_t rss_sym_key[52] = {0x5A, 0x6D, 0x5A, 0x6D, 0x5A, 0x6D, 0x5A, 0x6D, 0x5A, 0x6D, 0x5A, 0x6D, 0x5A,
                                      0x6D, 0x5A, 0x6D, 0x5A, 0x6D, 0x5A, 0x6D, 0x5A, 0x6D, 0x5A, 0x6D, 0x5A, 0x6D,
                                      0x5A, 0x6D, 0x5A, 0x6D, 0x5A, 0x6D, 0x5A, 0x6D, 0x5A, 0x6D, 0x5A, 0x6D, 0x5A,
                                      0x6D, 0x5A, 0x6D, 0x5A, 0x6D, 0x5A, 0x6D, 0x5A, 0x6D, 0x5A, 0x6D, 0x5A, 0x6D};

    // port_conf_default.rxmode.jumbo_frame = 1; 23.11.3版本移除,改用offloads
    // port_conf_default.rxmode.max_lro_pkt_size = dev_info.max_rx_pktlen;
    // port_conf_default.rxmode.enable_lro = 0; 23.11.3版本移除,改用offloads
    // port_conf_default.rxmode.hw_ip_checksum = 0; 23.11.3版本移除,改用offloads
    // port_conf_default.rxmode.hw_vlan_strip = 0; 23.11.3版本移除,改用offloads
    // port_conf_default.rxmode.hw_strip_crc = 0; 23.11.3版本移除,改用offloads
    // port_conf_default.rxmode.enable_scatter = 1; 23.11.3版本移除,改用offloads

    // 保留CRC，关闭LRO
    port_conf_default.rxmode.offloads = RTE_ETH_RX_OFFLOAD_SCATTER      /* Scatter Mode */
                                        | RTE_ETH_RX_OFFLOAD_CHECKSUM   /* 硬件 IP/TCP/UDP 校验和 */
                                        | RTE_ETH_RX_OFFLOAD_VLAN_STRIP /* VLAN Strip */
                                        | RTE_ETH_RX_OFFLOAD_KEEP_CRC;  /* 保留 CRC */

    port_conf_default.rxmode.mq_mode = RTE_ETH_MQ_RX_NONE;

    if (1 == g_rss_enable) {
        port_conf_default.rxmode.mq_mode = RTE_ETH_MQ_RX_RSS;
        port_conf_default.rx_adv_conf.rss_conf.rss_key = rss_sym_key;
        port_conf_default.rx_adv_conf.rss_conf.rss_key_len = 40;
        port_conf_default.rx_adv_conf.rss_conf.rss_hf = RTE_ETH_RSS_IPV4 | RTE_ETH_RSS_IPV6 | RTE_ETH_RSS_TCP | RTE_ETH_RSS_UDP;
        port_conf_default.rxmode.offloads = RTE_ETH_RX_OFFLOAD_SCATTER      /* Scatter Mode */
                                                | RTE_ETH_RX_OFFLOAD_CHECKSUM   /* 硬件 IP/TCP/UDP 校验和 */
                                                | RTE_ETH_RX_OFFLOAD_VLAN_STRIP /* VLAN Strip */
                                                | RTE_ETH_RX_OFFLOAD_KEEP_CRC   /* 保留 CRC */
                                                | RTE_ETH_RX_OFFLOAD_RSS_HASH;
    }

    // ETH_LINK_[HALF/FULL]_DUPLEX
    // #define ETH_LINK_HALF_DUPLEX    0 /**< Half-duplex connection. */
    // #define RTE_ETH_LINK_FULL_DUPLEX    1 /**< Full-duplex connection. */
    // #define RTE_ETH_SPEED_NUM_NONE         0 /**< Not defined */
    // #define ETH_SPEED_NUM_100M       100 /**< 100 Mbps */
    // #define ETH_SPEED_NUM_1G        1000 /**<   1 Gbps */
    // #define ETH_SPEED_NUM_2_5G      2500 /**< 2.5 Gbps */
    // #define ETH_SPEED_NUM_5G        5000 /**<   5 Gbps */
    // #define ETH_SPEED_NUM_10G      10000 /**<  10 Gbps */
    // uint32_t rte_eth_speed_bitflag(uint32_t speed, int duplex);
    // #define RTE_ETH_LINK_SPEED_AUTONEG  (0 <<  0)  /**< Autonegotiate (all speeds) */
    // #define RTE_ETH_LINK_SPEED_FIXED    (1 <<  0)  /**< Disable autoneg (fixed speed) */

    link_speeds = rte_eth_speed_bitflag(speed, duplex);
    if (link_speeds) {
        if (speed_fixed) {
            port_conf_default.link_speeds = (RTE_ETH_LINK_SPEED_FIXED | link_speeds);
        } else {
            port_conf_default.link_speeds = (RTE_ETH_LINK_SPEED_AUTONEG | link_speeds);
        }
        if (g_verbose_mode) {
            printf(" link_speeds: 0x%X speeds: %d duplex: %d speed_fixed: %d\n", link_speeds, speed, duplex,
                   speed_fixed);
        }
    }

    rxRings = get_port_n_rx_queues(port_id);

    ret = rte_eth_dev_configure(port_id, rxRings, txRings, &port_conf_default);
    if (ret != 0) return ret;

    if (g_verbose_mode) {
        printf("port conf:\n");
        printf("  link_speeds           = 0x%X\n", port_conf_default.link_speeds);
        printf("  rxmode.mq_mode        = 0x%.8X\n", port_conf_default.rxmode.mq_mode);
        printf("  rxmode.max_lro_pkt_size = %d\n", port_conf_default.rxmode.max_lro_pkt_size);
        // printf("  rxmode.split_hdr_size = %d\n", port_conf_default.rxmode.split_hdr_size);
        // printf("  rxmode.header_split   = %d\n", port_conf_default.rxmode.header_split);
        // printf("  rxmode.hw_ip_checksum = %d\n", port_conf_default.rxmode.hw_ip_checksum);
        // printf("  rxmode.hw_vlan_filter = %d\n", port_conf_default.rxmode.hw_vlan_filter);
        // printf("  rxmode.hw_vlan_strip  = %d\n", port_conf_default.rxmode.hw_vlan_strip);
        // printf("  rxmode.hw_vlan_extend = %d\n", port_conf_default.rxmode.hw_vlan_extend);
        // printf("  rxmode.jumbo_frame    = %d\n", port_conf_default.rxmode.jumbo_frame);
        // printf("  rxmode.hw_strip_crc   = %d\n", port_conf_default.rxmode.hw_strip_crc);
        // printf("  rxmode.enable_scatter = %d\n", port_conf_default.rxmode.enable_scatter);
        // printf("  rxmode.enable_lro     = %d\n", port_conf_default.rxmode.enable_lro);
    }

#if 0
  if (g_conf_rx_desc_per_queue < dev_info.rx_desc_lim.nb_max)
  {
    g_conf_rx_desc_per_queue = dev_info.rx_desc_lim.nb_max;
  }
#endif

    uint32_t lcore_id;
    for (lcore_id = 0; lcore_id < RTE_MAX_LCORE; lcore_id++) {
        if (rte_lcore_is_enabled(lcore_id) == 0) continue;
        struct lcore_conf *qconf = &lcore_conf[lcore_id];
        printf("Initializing rx queues on lcore %u ... \n", lcore_id);
        fflush(stdout);
        /* init RX queues */
        uint8_t queue, portid, queueid, socketid;
        for (queue = 0; queue < qconf->n_rx_queue; ++queue) {
            portid = qconf->rx_queue_list[queue].port_id;
            queueid = qconf->rx_queue_list[queue].queue_id;

            if (portid != port_id) {
                continue;
            }

            socketid = (uint8_t)rte_eth_dev_socket_id(port_id);

            printf("rxq=%d,%d,%d\n", portid, queueid, socketid);
            fflush(stdout);

            ret = rte_eth_rx_queue_setup(portid, queueid, g_conf_rx_desc_per_queue, socketid, NULL,
                                         pktmbuf_pool[socketid]);
            if (ret < 0) rte_exit(EXIT_FAILURE, "rte_eth_rx_queue_setup: err=%d, port=%d\n", ret, portid);
        }
    }

    for (q = 0; q < txRings; q++) {
        ret = rte_eth_tx_queue_setup(port_id, q,
                                     g_conf_tx_desc_per_queue,  // dev_info.tx_desc_lim.nb_min, // TX_DESC_PER_QUEUE,
                                     rte_eth_dev_socket_id(port_id), NULL);
        if (ret < 0) return ret;
    }

    // led on
    stat = rte_eth_led_on(port_id);
    if (stat && rte_errno) {
        RTE_LOG(NOTICE, GWHW, "rte_eth_led_on() %d %s\n", stat, rte_strerror(rte_errno));
    }

    // // setting mtu
    {
        size_t i;
        int mtu_delta[] = {
            (0),
            (RTE_ETHER_HDR_LEN),
            (RTE_ETHER_HDR_LEN + RTE_ETHER_CRC_LEN),
            (RTE_ETHER_HDR_LEN + RTE_ETHER_CRC_LEN + 4),
            (RTE_ETHER_HDR_LEN + RTE_ETHER_CRC_LEN + 4 * 2),
            (RTE_ETHER_HDR_LEN + RTE_ETHER_CRC_LEN + 4 * 3),
            (RTE_ETHER_HDR_LEN + RTE_ETHER_CRC_LEN + 4 * 4),
            (RTE_PKTMBUF_HEADROOM + 0),
            (RTE_PKTMBUF_HEADROOM + (RTE_ETHER_HDR_LEN)),
            (RTE_PKTMBUF_HEADROOM + (RTE_ETHER_HDR_LEN + RTE_ETHER_CRC_LEN)),
            (RTE_PKTMBUF_HEADROOM + (RTE_ETHER_HDR_LEN + RTE_ETHER_CRC_LEN + 4)),
            (RTE_PKTMBUF_HEADROOM + (RTE_ETHER_HDR_LEN + RTE_ETHER_CRC_LEN + 4 * 2)),
            (RTE_PKTMBUF_HEADROOM + (RTE_ETHER_HDR_LEN + RTE_ETHER_CRC_LEN + 4 * 3)),
            (RTE_PKTMBUF_HEADROOM + (RTE_ETHER_HDR_LEN + RTE_ETHER_CRC_LEN + 4 * 4)),
        };
        int mtu_base;
        int mtu_set = 0;
        for (mtu_base = 0x10000; mtu_base > 0; mtu_base -= 0x100) {
            for (i = 0; i < COUNTOF(mtu_delta); i++) {
                new_mtu = mtu_base - mtu_delta[i];
                if (new_mtu > port_conf_default.rxmode.max_lro_pkt_size) {
                    continue;
                }
                if (new_mtu < RTE_ETHER_MIN_LEN) {
                    continue;
                }
                if (g_verbose_mode) {
                    printf("try new mtu=%d\n", new_mtu);
                }
                stat = rte_eth_dev_set_mtu(port_id, new_mtu);
                if (stat == 0) {
                    mtu_set = 1;  // 成功设置
                    break;
                }
            }
            if (new_mtu < RTE_ETHER_MIN_LEN) {
                continue;
            }
            if (mtu_set) {
                break;
            }
        }
    }

    // new_mtu = 65535;
    // //new_mtu = ETHER_MAX_JUMBO_FRAME_LEN;
    // //new_mtu = 9198;
    // //new_mtu = 9216;
    // new_mtu = 9710;
    // //new_mtu = 16100;
    // new_mtu = 15000;
    // //new_mtu = 10000;
    // new_mtu = port_conf_default.rxmode.max_lro_pkt_size - (RTE_ETHER_HDR_LEN + RTE_ETHER_CRC_LEN);
    // //
    // //new_mtu = 2026;
    // //new_mtu = 2022;
    // stat = rte_eth_dev_set_mtu(port_id, new_mtu);
    if (g_verbose_mode) {
        if (stat == 0) {
            printf("Port %i: MTU set to %i\n", port_id, new_mtu);
        } else if (stat == -ENOTSUP) {
            printf("Port %i: Operation not supported\n", port_id);
        } else if (stat == -ENODEV) {
            printf("Port %i: port_id invalid\n", port_id);
        } else if (stat == -EINVAL) {
            printf("Port %i: mtu invalid\n", port_id);
        } else if (stat == -EBUSY) {
            printf("Port %i: operation is not allowed when the port is running\n", port_id);
        } else {
            printf("Port %i: Error setting MTU stat=%d\n", port_id, stat);
        }
    }

    // 尝试设置mtu为数据包的最大值
#if 0
  //rte_eth_dev_set_mtu(port_id, 65535);
  rte_eth_dev_set_mtu(port_id, 65000);
  rte_eth_dev_set_mtu(port_id, 65400);
  //rte_eth_dev_set_mtu(port_id, 65535 - (RTE_ETHER_HDR_LEN + RTE_ETHER_CRC_LEN));
#endif

    ret = rte_eth_dev_start(port_id);
    if (ret < 0) return ret;

    // show mac addr
    rte_eth_macaddr_get(port_id, &addr);
    RTE_LOG(INFO, GWHW, "Port %u MAC: %02" PRIx8 " %02" PRIx8 " %02" PRIx8 " %02" PRIx8 " %02" PRIx8 " %02" PRIx8 "\n",
            (unsigned)port_id, addr.addr_bytes[0], addr.addr_bytes[1], addr.addr_bytes[2], addr.addr_bytes[3],
            addr.addr_bytes[4], addr.addr_bytes[5]);

    // set promisc
    rte_eth_promiscuous_enable(port_id);
    RTE_LOG(NOTICE, GWHW, "promiscuous = %d\n", rte_eth_promiscuous_get(port_id));

    // get mtu
    rte_eth_dev_get_mtu(port_id, &mtu);
    RTE_LOG(NOTICE, GWHW, "mtu = %d\n", mtu);

    // link up
    stat = rte_eth_dev_set_link_up(port_id);
    if (stat && rte_errno) {
        RTE_LOG(NOTICE, GWHW, "rte_eth_dev_set_link_up() %d %s\n", stat, rte_strerror(rte_errno));
    }

    return 0;
}

static void print_stats(void)
{
    /*
  thread stats:
                                            RX    IP Worker   TCP Worker
   - Pkts deqd from upstream:              598          598          125
   - Pkts enqd to workers ring:            598          125            0
   - Pkts enq to workers failed:             0            0            0

  stats:
                       Port 0
   - Pkts in:             598
   - Pkts out:              0
   - In Errs:               0
   - Out Errs:              0
   - Mbuf Errs:             0

  link:
                       Port 0
   - Speed:             10000
   - Duplex:             FULL
   - Autoneg:           FIXED
   - Staus:                UP
     */

    // const int c_nstats_len = 5 + 3;
    // const int c_link_len = 4;
    // const uint8_t nb_ports = rte_eth_dev_count_avail();
    // unsigned i;
    // struct rte_eth_stats eth_stats[8];
    // struct rte_eth_link eth_link[8];
    // char buf[64] = {0};
    // char buf1[64] = {0};
    // char show_data[1 + c_nstats_len + c_link_len][512];
    // const int c_stats_off = 1;
    // const int c_link_off = 1 + c_nstats_len;
    // //struct rte_eth_rxq_info rxq_info;

    // memset(show_data, 0, sizeof(show_data));

    // printf("\nthread stats:\n");
    // printf("                                %16s %16s %16s\n", "RX", "IP Worker", "TCP Worker");
    // printf(" - Pkts deqd from upstream:     %16" PRIu64 " %16" PRIu64 " %16" PRIu64 "\n",
    //        app_stats.rx.rx_pkts, app_stats.ip_wkr.dequeue_pkts, app_stats.wkr.dequeue_pkts);
    // printf(" - Pkts enqd to workers ring:   %16" PRIu64 " %16" PRIu64 " %16" PRIu64 "\n",
    //        app_stats.rx.enqueue_pkts, app_stats.ip_wkr.enqueue_pkts, app_stats.wkr.enqueue_pkts);
    // printf(" - Pkts enq to workers failed:  %16" PRIu64 " %16" PRIu64 " %16" PRIu64 "\n",
    //        app_stats.rx.enqueue_failed_pkts, app_stats.ip_wkr.enqueue_failed_pkts,
    //        app_stats.wkr.enqueue_failed_pkts);

    // memset((void*)&eth_infos,0,sizeof(eth_infos));
    // for (i = 0; i < nb_ports; i++)
    // {
    //   /* skip ports that are not enabled */
    //   if ((portmask & (1 << i)) == 0)
    //   {
    //     continue;
    //   }
    //   snprintf(buf1, COUNTOF(buf1) - 1, "Port %u", i);
    //   snprintf(buf, COUNTOF(buf) - 1, " %16s", buf1);
    //   strcat(show_data[0], buf);

    //   if (i < COUNTOF(eth_stats))
    //   {
    //     rte_eth_stats_get(i, &eth_stats[i]);
    //     snprintf(buf, COUNTOF(buf) - 1, " %16" PRIu64 "", eth_stats[i].ipackets);
    //     eth_infos.u64_in_total_packets += eth_stats[i].ipackets;
    //     strcat(show_data[c_stats_off + 0], buf);
    //     snprintf(buf, COUNTOF(buf) - 1, " %16" PRIu64 "", eth_stats[i].opackets);
    //     strcat(show_data[c_stats_off + 1], buf);
    //     snprintf(buf, COUNTOF(buf) - 1, " %16" PRIu64 "", eth_stats[i].ierrors);
    //     eth_infos.u64_in_err_packets += eth_stats[i].ierrors;
    //     strcat(show_data[c_stats_off + 2], buf);
    //     snprintf(buf, COUNTOF(buf) - 1, " %16" PRIu64 "", eth_stats[i].oerrors);
    //     strcat(show_data[c_stats_off + 3], buf);
    //     snprintf(buf, COUNTOF(buf) - 1, " %16" PRIu64 "", eth_stats[i].rx_nombuf);
    //     strcat(show_data[c_stats_off + 4], buf);
    //     snprintf(buf, COUNTOF(buf) - 1, " %16" PRIu64 "", eth_stats[i].ibytes);
    //     strcat(show_data[c_stats_off + 5], buf);
    //     snprintf(buf, COUNTOF(buf) - 1, " %16" PRIu64 "", eth_stats[i].obytes);
    //     strcat(show_data[c_stats_off + 6], buf);
    //     snprintf(buf, COUNTOF(buf) - 1, " %16" PRIu64 "", eth_stats[i].imissed);
    //     eth_infos.u64_in_miss_packets += eth_stats[i].imissed;
    //     strcat(show_data[c_stats_off + 7], buf);
    //   }
    //   if (i < COUNTOF(eth_link))
    //   {
    //     //rte_eth_link_get(i, &eth_link[i]);
    //     rte_eth_link_get_nowait(i, &eth_link[i]);
    //     snprintf(buf, COUNTOF(buf) - 1, " %16u", eth_link[i].link_speed);
    //     strcat(show_data[c_link_off + 0], buf);
    //     snprintf(buf, COUNTOF(buf) - 1, " %16s", eth_link[i].link_duplex ? "FULL" : "HALF");
    //     strcat(show_data[c_link_off + 1], buf);
    //     snprintf(buf, COUNTOF(buf) - 1, " %16s", eth_link[i].link_autoneg ? "AUTONEG" : "FIXED");
    //     strcat(show_data[c_link_off + 2], buf);
    //     snprintf(buf, COUNTOF(buf) - 1, " %16s", eth_link[i].link_status ? "UP" : "DOWN");
    //     strcat(show_data[c_link_off + 3], buf);
    //   }
    // }

    // printf("\nstats:\n");
    // printf("              %s\n", show_data[0]);

    // printf(" - Pkts in:   %s\n", show_data[c_stats_off + 0]);
    // printf(" - Pkts out:  %s\n", show_data[c_stats_off + 1]);
    // printf(" - In Errs:   %s\n", show_data[c_stats_off + 2]);
    // printf(" - Out Errs:  %s\n", show_data[c_stats_off + 3]);
    // printf(" - Mbuf Errs: %s\n", show_data[c_stats_off + 4]);
    // printf(" - In Bytes:  %s\n", show_data[c_stats_off + 5]);
    // printf(" - Out Bytes: %s\n", show_data[c_stats_off + 6]);
    // printf(" - In Missed: %s\n", show_data[c_stats_off + 7]);

    // printf("\nlink:\n");
    // printf("              %s\n", show_data[0]);
    // printf(" - Speed:     %s\n", show_data[c_link_off + 0]);
    // printf(" - Duplex:    %s\n", show_data[c_link_off + 1]);
    // printf(" - Autoneg:   %s\n", show_data[c_link_off + 2]);
    // printf(" - Staus:     %s\n", show_data[c_link_off + 3]);

    char log_buf[LOG_BUF_LEN] = {0};
    get_dpdk_log_buf(log_buf, LOG_BUF_LEN);

    printf("%s", log_buf);
}

static void adj_thread_pri_ni_inner(int policy, int nice_value)
{
    struct sched_param param;
    int ret;

    // 设置调度策略
    param.sched_priority = 0;  // SCHED_OTHER 策略下优先级必须为0
    ret = pthread_setschedparam(pthread_self(), policy, &param);
    if (ret != 0) {
        printf("Failed to set thread scheduling policy: %s\n", strerror(errno));
    }

    // 设置 nice 值
    ret = nice(nice_value);
    if (ret == -1) {
        printf("Failed to set nice value: %s\n", strerror(errno));
    }
}

static void adj_thread_pri_ni(void)
{
    int nice_value = -20;        // 普通优先级
    int policy = SCHED_OTHER;  // 时间片轮转调度
    adj_thread_pri_ni_inner(policy, nice_value);
}

/**
 * This thread receives mbufs from the port and affects them an internal
 * sequence number to keep track of their order of arrival through an
 * mbuf structure.
 * The mbufs are then passed to the worker threads via the rx_to_workers
 * ring.
 * 接收数据包线程，多线程版本 */
static int rx_thread(void *args_ptr)
{
    struct rx_thread_args *p_rx_thread_args = (struct rx_thread_args *)args_ptr;
    struct rte_ring *ring_out = p_rx_thread_args->ring_out;
    uint16_t ret = 0;
    uint16_t nb_rx_pkts = 0;
    struct rte_mbuf *pkts[g_conf_max_pkts_burst];
    int idle_cnt = IDLE_DEFAULT;
    int idle_deep_cnt = 0;
    int i = 0;
    uint8_t port_id;
    uint8_t queue_id;
    uint8_t lcore_id = rte_lcore_id();
    struct lcore_conf *qconf = &lcore_conf[lcore_id];
    app_stats_t app_stats;
    memset(&app_stats, 0, sizeof(app_stats));

    app_stats_register(&app_stats);
    adj_thread_pri_ni();
    RTE_LOG(INFO, GWHW, "%s() started on lcore %u\n", __func__, lcore_id);
    while (!quit_signal) {
        if (g_risk_flag == 0) {
            usleep(10);
            continue;
        }

        for (i = 0; i < qconf->n_rx_queue; i++) {
            /* receive packets */
            port_id = qconf->rx_queue_list[i].port_id;
            queue_id = qconf->rx_queue_list[i].queue_id;

            nb_rx_pkts = rte_eth_rx_burst(port_id, queue_id, pkts, g_conf_max_pkts_burst);

            if (unlikely(nb_rx_pkts == 0)) {
                if (unlikely(g_verbose_mode)) {
                    RTE_LOG(DEBUG, GWHW, "%s(): port id(%d) recevied zero packets\n", __func__, port_id);
                }
                IDLE_F_COUNTER(idle_cnt, idle_deep_cnt, 10000, 10);
                continue;
            }
            IDLE_F_RESET(idle_cnt, idle_deep_cnt);

            app_stats.rx.rx_pkts += nb_rx_pkts;

            /* enqueue to rx_to_workers ring */
            ret = rte_ring_enqueue_burst(ring_out, (void *)pkts, nb_rx_pkts, NULL);

            app_stats.rx.enqueue_pkts += ret;

            if (ret < nb_rx_pkts) {
                app_stats.rx.enqueue_failed_pkts += (int)nb_rx_pkts - ret;
                pktmbuf_free_bulk(&pkts[ret], nb_rx_pkts - ret);
            }
        }
    }

    return 0;
}

/* rx_tcp_thread 多线程实现 */
static int rx_tcp_thread(void *args_ptr)
{
    struct rx_tcp_thread_args *args = (struct rx_tcp_thread_args *)args_ptr;
    struct rte_ring **ring_out = args->ring_out;
    CALLBACK_PKT callback = args->callback;
    void *userdata = args->userdata;
    uint8_t *port_id = args->port_id;
    int port_id_cnt = args->port_id_cnt;

    uint16_t ret = 0;
    register uint i = 0;
    uint16_t burst_size = 0;
    struct rte_mbuf **burst_buffer = NULL;

    pkt_info_t *pkti = NULL;
    int st_off = 0;
    int st_num = 0;
    int st_table[256] = {0};
    unsigned k = 0;
    int idle_cnt = IDLE_DEFAULT;
    int idle_deep_cnt = 0;

    adj_thread_pri_ni();
    RTE_LOG(INFO, GWHW, "%s() started on lcore %u\n", __func__, rte_lcore_id());
    pkti = rte_malloc(NULL, sizeof(*pkti) * g_conf_max_pkts_burst, 0);
    if (pkti == NULL) {
        rte_exit(EXIT_FAILURE, "%s\n", rte_strerror(rte_errno));
        return 1;
    }

    burst_buffer = rte_malloc(NULL, sizeof(*burst_buffer) * g_conf_max_pkts_burst, 0);
    if (NULL == burst_buffer) {
        rte_exit(EXIT_FAILURE, "%s\n", rte_strerror(rte_errno));
        return 2;
    }
    memset(burst_buffer, 0, sizeof(*burst_buffer) * g_conf_max_pkts_burst);

    for (i = 0; i < COUNTOF(args->ring_out); ++i) {
        if (ring_out[i] == NULL) {
            break;
        }
        st_num++;
    }
    if (st_num == 0) {
        rte_exit(EXIT_FAILURE, "ip worker ring out is empty\n");
        return 4;
    }
    for (i = 0; i < COUNTOF(st_table); i++) {
        st_table[i] = i % st_num;
    }

    if (NULL == callback) {
        while (!quit_signal) {
            if (g_risk_flag == 0) {
                usleep(10);
                continue;
            }
            for (i = 0; i < port_id_cnt; ++i) {
                /* receive pkts */
                burst_size = rte_eth_rx_burst(port_id[i], 0, burst_buffer, g_conf_max_pkts_burst);
                if (unlikely(burst_size == 0)) {
                    if (unlikely(g_verbose_mode)) {
                        RTE_LOG(DEBUG, GWHW, "%s():Received zero packets\n", __func__);
                    }

                    IDLE_F_COUNTER(idle_cnt, idle_deep_cnt, 10000, 10);
                    continue;
                }
                IDLE_F_RESET(idle_cnt, idle_deep_cnt);

                __sync_fetch_and_add(&app_stats.rx.rx_pkts, burst_size);

                {
                    st_off = st_table[(k - 0) & 0xff];

                    burst_buffer[i]->dynfield1[0] = st_off;
                    ret = rte_ring_enqueue_burst(ring_out[st_off], (void *)&burst_buffer[i], burst_size, NULL);
                    app_stats.rx.enqueue_pkts += ret;
                    //__sync_fetch_and_add(&app_stats.rx.enqueue_pkts, ret);
                    if (unlikely(ret < burst_size)) {
                        /* Return the mbufs to their respective pool, dropping packets */
                        app_stats.rx.enqueue_failed_pkts += (int)burst_size - ret;
                        // __sync_fetch_and_add(&app_stats.rx.enqueue_failed_pkts,
                        //     (int)burst_size - ret);
                        pktmbuf_free_bulk(&burst_buffer[0], burst_size);
                    }
                }
            }
        }
    } else {
        while (!quit_signal) {
            if (g_risk_flag == 0) {
                usleep(10);
                continue;
            }
            for (i = 0; i < port_id_cnt; ++i) {
                /* receive pkts */
                burst_size = rte_eth_rx_burst(port_id[i], 0, burst_buffer, g_conf_max_pkts_burst);
                if (unlikely(burst_size == 0)) {
                    if (unlikely(g_verbose_mode)) {
                        RTE_LOG(DEBUG, GWHW, "%s():Received zero packets\n", __func__);
                    }

                    IDLE_F_COUNTER(idle_cnt, idle_deep_cnt, 10000, 10);
                    continue;
                }
                IDLE_F_RESET(idle_cnt, idle_deep_cnt);

                __sync_fetch_and_add(&app_stats.rx.rx_pkts, burst_size);

                {
                    for (i = 0; i < burst_size; ++i) {
                        pkti[i].buf = rte_pktmbuf_mtod(burst_buffer[i], void *);
                        pkti[i].size = rte_pktmbuf_data_len(burst_buffer[i]);
                        pkti[i].pkt_size = rte_pktmbuf_pkt_len(burst_buffer[i]);
                        pkti[i].o_st = 0;
                    }

                    callback(pkti, burst_size, userdata);
                    if (unlikely(quit_signal)) {
                        break;
                    } else {
                        for (i = 0; i < burst_size; ++i) {
                            if (pkti[i].o_st == 0) {
                                rte_pktmbuf_free(burst_buffer[i]);
                            } else if (pkti[i].o_st > 0) {
                                st_off = st_table[(pkti[i].o_st - 1) & 0xff];

                                burst_buffer[i]->dynfield1[0] = st_off;
                                ret = rte_ring_enqueue(ring_out[st_off], (void *)burst_buffer[i]);

                                __sync_fetch_and_add(&app_stats.rx.enqueue_pkts, ret);
                                if (unlikely(ret != 0)) {
                                    /* Return the mbufs to their respective pool, dropping packets */
                                    __sync_fetch_and_add(&app_stats.rx.enqueue_failed_pkts, 1);
                                    rte_pktmbuf_free(burst_buffer[i]);
                                }
                            } else {
                                __sync_fetch_and_add(&app_stats.rx.enqueue_failed_pkts, 1);
                                rte_pktmbuf_free(burst_buffer[i]);
                            }
                        }
                    }
                }
            }
        }
    }
    if (pkti) {
        rte_free(pkti);
    }

    if (burst_buffer) {
        rte_free(burst_buffer);
    }

    return 0;
}

static int ip_worker_thread(void *args_ptr)
{
    register uint i;
    uint16_t ret = 0;
    uint16_t burst_size = 0;
    // register uint burst_size = 0;
    struct ip_worker_thread_args *args;
    struct rte_mbuf **burst_buffer;
    // struct rte_mbuf *bufptr;
    struct rte_ring **ring_in, **ring_out;
    uint16_t u16_ring_in_cnt = 0;
    CALLBACK_PKT callback;
    void *userdata;
    // uint cap_size, size;
    // void *data;
    // struct timeval tv;
    pkt_info_t *pkti;
    int st_off;
    int st_num = 0;
    int st_table[256] = {0};
    unsigned int k = 0;
    int idle_cnt = IDLE_DEFAULT;
    int idle_deep_cnt = 0;
    time_t t_base_time = cap_time();
    time_t t_last_time = t_base_time;
    app_stats_t app_stats;
    memset(&app_stats, 0, sizeof(app_stats));

    app_stats_register(&app_stats);
    adj_thread_pri_ni();

    RTE_LOG(INFO, GWHW, "%s() started on lcore %u\n", __func__, rte_lcore_id());

    // pkti = rte_malloc(NULL, sizeof(*pkti) * g_conf_max_pkts_burst, 0); // malloc(g_conf_pkt_max_size); //
    pkti = rte_malloc(NULL, sizeof(*pkti) * g_conf_ip_work_deq_num, 0);  // malloc(g_conf_ip_work_deq_num); //
    if (pkti == NULL) {
        // RTE_LOG(ERR, GWHW, "rte_malloc error\n");
        rte_exit(EXIT_FAILURE, "%s\n", rte_strerror(rte_errno));
        return 1;
    }
    burst_buffer = rte_malloc(NULL, sizeof(*burst_buffer) * g_conf_ip_work_deq_num, 0);
    if (burst_buffer == NULL) {
        // RTE_LOG(ERR, GWHW, "rte_malloc error\n");
        rte_exit(EXIT_FAILURE, "%s\n", rte_strerror(rte_errno));
        return 2;
    }
    memset(burst_buffer, 0, sizeof(*burst_buffer) * g_conf_ip_work_deq_num);

    args = (struct ip_worker_thread_args *)args_ptr;
    ring_in = args->ring_in;
    ring_out = args->ring_out;
    userdata = args->userdata;
    callback = args->callback;
    u16_ring_in_cnt = args->u16_ip_ring_cnt;

    for (i = 0; i < COUNTOF(args->ring_out); ++i) {
        if (ring_out[i] == NULL) {
            break;
        }
        st_num++;
    }

    if (st_num == 0) {
        rte_exit(EXIT_FAILURE, "ip worker ring out is empty\n");
        return 4;
    }
    for (i = 0; i < COUNTOF(st_table); i++) {
        st_table[i] = i % st_num;
    }

    struct rte_mbuf ***tcp_mbuf = rte_malloc(NULL, sizeof(**tcp_mbuf) * st_num, 0);
    if (NULL == tcp_mbuf) {
        rte_exit(EXIT_FAILURE, "%s\n", rte_strerror(rte_errno));
        return 5;
    }
    memset(tcp_mbuf, 0, sizeof(**tcp_mbuf) * st_num);

    struct rte_mbuf **pp_mbuf = rte_malloc(NULL, sizeof(*pp_mbuf) * g_conf_ip_work_deq_num * st_num, 0);
    if (NULL == pp_mbuf) {
        rte_exit(EXIT_FAILURE, "%s\n", rte_strerror(rte_errno));
        return 6;
    }
    memset(pp_mbuf, 0, sizeof(*pp_mbuf) * g_conf_ip_work_deq_num * st_num);

    for (i = 0; i < st_num; ++i) {
        tcp_mbuf[i] = pp_mbuf + i * g_conf_ip_work_deq_num;
    }

    uint16_t *mbuf_index = rte_malloc(NULL, sizeof(uint16_t) * st_num, 0);
    if (NULL == mbuf_index) {
        rte_exit(EXIT_FAILURE, "%s\n", rte_strerror(rte_errno));
        return 7;
    }
    memset(mbuf_index, 0, sizeof(uint16_t) * st_num);

    int *p_idle_cnt = rte_malloc(NULL, sizeof(int) * st_num, 0);
    if (NULL == p_idle_cnt) {
        rte_exit(EXIT_FAILURE, "%s\n", rte_strerror(rte_errno));
        return 8;
    }
    memset(p_idle_cnt, 0, sizeof(int) * st_num);

// if (callback == NULL)
// {
//   RTE_LOG(ERR, GWHW, "no callback\n");
//   return 3;
// }
#if 0
  int i_ret = 0;
  pthread_info_t st_ip_worker_thread_info;
  memset(&st_ip_worker_thread_info, 0, sizeof(pthread_info_t));
  st_ip_worker_thread_info.pid = pthread_self();
  st_ip_worker_thread_info.u32_is_important = 1;
  uint32_t u32_index;
  i_ret = watch_module_conf_pthread_info(&st_ip_worker_thread_info, &u32_index);
  if (i_ret != 0)
  {
    RTE_LOG(ERR, GWHW, "pthread ip worker pthread info failed(%d)\n", i_ret);
    u32_index = -1;
  }
  RTE_LOG(INFO, GWHW, "ip worker pthread index = %u\n", u32_index);
#endif

    uint32_t ring_deq_threhold = g_conf_ip_work_deq_num;
    uint32_t ring_cnt = 0;
    int need_sleep = 0;
    while (!quit_signal) {
        if (!(need_sleep ^ ((1 << u16_ring_in_cnt) - 1))) {
            usleep(10);
        }
        // watch_modole_report_pthread_info(u32_index);
        /* dequeue the mbufs from rx_to_workers ring */
        need_sleep = 0;
        for (i = 0; i < u16_ring_in_cnt; ++i) {
            if (likely(g_conf_converge_pkts == 1)) /* 判断是否进行聚合出队列 */
            {
                ring_cnt = rte_ring_count(ring_in[i]);
                if (unlikely(quit_signal)) {
                    break;
                }
                if (ring_cnt < ring_deq_threhold) {
                    need_sleep |= (1 << i);
                    *(p_idle_cnt + i) += 1;
                    if (*(p_idle_cnt + i) <= g_conf_converge_idel_cnt) {
                        continue;
                    }
                }
            }

            if (unlikely(g_ip_cache_clean)) {
                ring_flush(ring_in[i]);
                usleep(10);
                continue;
            }
            *(p_idle_cnt + i) = 0;
            burst_size = rte_ring_dequeue_burst(ring_in[i], (void *)burst_buffer, g_conf_ip_work_deq_num, NULL);
            if (unlikely(burst_size == 0)) {
                IDLE_F_COUNTER(idle_cnt, idle_deep_cnt, 10000, 10);
                continue;
            }
            IDLE_F_RESET(idle_cnt, idle_deep_cnt);

            app_stats.ip_wkr.dequeue_pkts += burst_size;
            // rte_pktmbuf_clone(burst_buffer[i], mbuf_pool) ?
            // printf ("ip worker dequeue size = %hu\n", burst_size);

            if (unlikely(g_i_sample_mode == 1)) {
                if (unlikely(g_i_sample_rate > 0)) {
                    t_last_time = cap_time();
                    if (t_base_time + g_i_sample_rate < t_last_time) {
                        put_pkt_to_queue(burst_buffer, burst_size);
                        t_base_time = t_last_time;
                    }
                } else {
                    put_pkt_to_queue(burst_buffer, burst_size);
                }
            }

            if (g_cb_flow) {
                put_pkt_to_flow_queue(burst_buffer, burst_size);
            }

            if (callback) {
                for (i = 0; i < burst_size; ++i) {
                    pkti[i].buf = rte_pktmbuf_mtod(burst_buffer[i], void *);
                    pkti[i].size = rte_pktmbuf_data_len(burst_buffer[i]);
                    pkti[i].pkt_size = rte_pktmbuf_pkt_len(burst_buffer[i]);
                    pkti[i].o_st = 0;
                }
                callback(pkti, burst_size, userdata);
                if (unlikely(quit_signal)) {
                    break;
                }
                for (i = 0; i < burst_size; ++i) {
                    if (likely(pkti[i].o_st > 0)) {
                        st_off = st_table[(pkti[i].o_st - 1) & 0xff];
                        burst_buffer[i]->dynfield1[0] = st_off;
                        tcp_mbuf[st_off][mbuf_index[st_off]] = burst_buffer[i];
                        mbuf_index[st_off]++;
                    } else if (pkti[i].o_st == 0) {
                        rte_pktmbuf_free(burst_buffer[i]);
                    } else {
                        app_stats.ip_wkr.enqueue_failed_pkts++;
                        rte_pktmbuf_free(burst_buffer[i]);
                    }
                }

                for (i = 0; i < st_num; ++i) {
                    if (mbuf_index[i] == 0) {
                        continue;
                    }

                    ret = rte_ring_enqueue_burst(ring_out[i], (void *)tcp_mbuf[i], mbuf_index[i], NULL);

                    app_stats.ip_wkr.enqueue_pkts += ret;

                    if (ret < mbuf_index[i]) {
                        app_stats.ip_wkr.enqueue_failed_pkts += (int)mbuf_index[i] - ret;
                        pktmbuf_free_bulk(&tcp_mbuf[i][ret], mbuf_index[i] - ret);
                    }
                    mbuf_index[i] = 0;
                }

                // pktmbuf_free_bulk(&burst_buffer[0], burst_size);
            } else {
                burst_buffer[i]->dynfield1[0] = (k - 0) % st_num;
                ret = rte_ring_enqueue_burst(ring_out[(k++) % st_num], (void *)&burst_buffer[i], burst_size, NULL);
                /* 单线程不使用原子操作(性能优化) */
                if (likely(g_conf_ip_work_thread == 1)) {
                    app_stats.ip_wkr.enqueue_pkts += ret;
                } else {
                    __sync_fetch_and_add(&app_stats.ip_wkr.enqueue_pkts, ret);
                }
                if (unlikely(ret < burst_size)) {
                    /* Return the mbufs to their respective pool, dropping packets */
                    /* 单线程不使用原子操作(性能优化) */

                    if (likely(g_conf_ip_work_thread == 1)) {
                        app_stats.ip_wkr.enqueue_failed_pkts += ((int)burst_size - ret);
                    } else {
                        __sync_fetch_and_add(&app_stats.ip_wkr.enqueue_failed_pkts, (int)burst_size - ret);
                    }
                    pktmbuf_free_bulk(&burst_buffer[0], burst_size);
                }
            }
        }
    }

    if (pkti) {
        rte_free(pkti);
    }

    if (burst_buffer) {
        rte_free(burst_buffer);
    }

    if (tcp_mbuf) {
        rte_free(tcp_mbuf);
    }

    if (pp_mbuf) {
        rte_free(pp_mbuf);
    }

    if (mbuf_index) {
        rte_free(mbuf_index);
    }
    return 0;
}

int creat_timestamp(void *args)
{
    memset(&g_pcap_tv, 0, sizeof(g_pcap_tv));
    struct timeval tv;
    memset(&tv, 0, sizeof(tv));
    while (!quit_signal) {
        if (likely(g_conf_pcap_timestamp == 0)) {
            g_pcap_tv.tv_sec = time(NULL);
            sleep(1);
        } else {
            gettimeofday(&tv, NULL);
            g_pcap_tv.tv_sec = tv.tv_sec;
            g_pcap_tv.tv_usec = tv.tv_usec;
            usleep(1);
        }
    }
    return 0;
}

static int tcp_worker_thread(void *args_ptr)
{
    // const uint8_t nb_ports = rte_eth_dev_count_avail();
    uint i;
    // register uint i;
    // uint16_t ret = 0;
    uint16_t burst_size = 0;
    // register uint burst_size = 0;
    struct tcp_worker_thread_args *args;
    struct rte_mbuf **burst_buffer;
    struct rte_mbuf *bufptr;
    struct rte_ring *ring_in, *ring_out;
    CALLBACK_PCAP callback;
    void *userdata;
    char *cap_buf;
    uint cap_size, size;
    // void *data;
    // struct timeval tv;
    pcap_info_t pi = {0};
    int idle_cnt = IDLE_DEFAULT;
    int idle_deep_cnt = 0;
    app_stats_t app_stats;
    memset(&app_stats, 0, sizeof(app_stats));

    app_stats_register(&app_stats);
    adj_thread_pri_ni();

    RTE_LOG(INFO, GWHW, "%s() started on lcore %u\n", __func__, rte_lcore_id());

    cap_buf = rte_malloc(NULL, g_conf_pkt_max_size, 0);  // malloc(g_conf_pkt_max_size); //
    if (cap_buf == NULL) {
        // RTE_LOG(ERR, GWHW, "rte_malloc error\n");
        rte_exit(EXIT_FAILURE, "%s\n", rte_strerror(rte_errno));
        return 1;
    }
    burst_buffer = rte_malloc(NULL, sizeof(*burst_buffer) * g_conf_ip_work_deq_num, 0);
    if (burst_buffer == NULL) {
        // RTE_LOG(ERR, GWHW, "rte_malloc error\n");
        rte_exit(EXIT_FAILURE, "%s\n", rte_strerror(rte_errno));
        return 2;
    }
    memset(burst_buffer, 0, sizeof(*burst_buffer) * g_conf_ip_work_deq_num);

    args = (struct tcp_worker_thread_args *)args_ptr;
    ring_in = args->ring_in;
    ring_out = args->ring_out;
    userdata = args->userdata;
    callback = args->callback;
    (void)ring_out; /* 避免编译警告 */

    if (callback == NULL) {
        RTE_LOG(ERR, GWHW, "no callback\n");
        return 2;
    }

#if 0
  int i_ret = 0;
  pthread_info_t st_tcp_worker_thread_info;
  memset(&st_tcp_worker_thread_info, 0, sizeof(pthread_info_t));
  st_tcp_worker_thread_info.pid = pthread_self();
  st_tcp_worker_thread_info.u32_is_important = 1;
  uint32_t u32_index;
  i_ret = watch_module_conf_pthread_info(&st_tcp_worker_thread_info, &u32_index);
  if (i_ret != 0)
  {
    RTE_LOG(ERR, GWHW, "pthread tcp worker pthread info failed(%d)\n", i_ret);
    u32_index = -1;
  }
  RTE_LOG(INFO, GWHW, "tcp worker pthread index = %u\n", u32_index);
#endif

    while (!quit_signal) {
        if (unlikely(g_tcp_cache_clean)) {
            ring_flush(ring_in);
            usleep(10);
            continue;
        }
        // watch_modole_report_pthread_info(u32_index);

        /* dequeue the mbufs from rx_to_workers ring */
        burst_size = rte_ring_dequeue_burst(ring_in, (void *)burst_buffer, g_conf_ip_work_deq_num, NULL);
        if (unlikely(burst_size == 0)) {
            IDLE_F_COUNTER(idle_cnt, idle_deep_cnt, 10000, 10);
            continue;
        }
        IDLE_F_RESET(idle_cnt, idle_deep_cnt);
        // printf ("rte_lcore_id = %u, tcp worker dequeue = %d\n", rte_lcore_id(), burst_size);

        app_stats.wkr.dequeue_pkts += burst_size;
        // rte_pktmbuf_clone(burst_buffer[i], mbuf_pool) ?

        if (1) {
            for (i = 0; i < burst_size; ++i) {
                bufptr = burst_buffer[i];
                // Write content
                cap_size = 0;

                /* bufptr->next != NULL, 不进行拷贝操作(性能优化) */
                if (bufptr->next != NULL) {
                    do {
                        // data = rte_pktmbuf_mtod(bufptr, void*);
                        size = rte_pktmbuf_data_len(bufptr);
                        rte_memcpy(cap_buf + cap_size, rte_pktmbuf_mtod(bufptr, void *), size);
                        cap_size += size;
                        bufptr = bufptr->next;
                    } while (unlikely(bufptr != NULL));
                    pi.buf = cap_buf;
                } else {
                    pi.buf = rte_pktmbuf_mtod(bufptr, void *);
                    cap_size = rte_pktmbuf_data_len(bufptr);
                }

                // Get time
                // gettimeofday(&tv, NULL);
                // pi.buf = cap_buf;
                /* 获取时间有单独的线程获取 */
                pi.timestamp = g_pcap_tv.tv_sec;
                pi.microseconds = g_pcap_tv.tv_usec;
                pi.packet_length = cap_size;
                pi.packet_length_wire = cap_size;

                callback(&pi, userdata, burst_buffer[i]->dynfield1[0]);
                if (unlikely(quit_signal)) {
                    break;
                }
                // rte_pktmbuf_free(burst_buffer[i]);
            }

            // pktmbuf_free_bulk(&burst_buffer[0], burst_size);
        }

        // if (unlikely(g_i_sample_mode == 1))
        // {
        //   if (unlikely(g_i_sample_rate > 0))
        //   {
        //     t_last_time = cap_time();
        //     if (t_base_time + g_i_sample_rate < t_last_time)
        //     {
        //       put_pkt_to_queue(burst_buffer, burst_size);
        //       t_base_time = t_last_time;
        //     }
        //   }
        //   else
        //   {
        //     put_pkt_to_queue(burst_buffer, burst_size);
        //   }
        // }
        // else
        // {
        pktmbuf_free_bulk(&burst_buffer[0], burst_size);
        // }
    }

    if (cap_buf) {
        rte_free(cap_buf);
    }

    if (burst_buffer) {
        rte_free(burst_buffer);
    }

    return 0;
}

void capd_set_quit_signal(void)
{
    quit_signal = 1;
}

void capd_print_stats(void)
{
    print_stats();
}

/* 收集网卡当前的状态 */
int collect_eth_info_inner(eth_info_t *p_st_eth_info, uint32_t *p_work_port_cnt)
{
    if (p_st_eth_info == NULL || p_work_port_cnt == NULL) {
        printf("param error\n");
        return -1;
    }
    int i_ret = 0;
    const uint32_t nb_ports = rte_eth_dev_count_avail();
    uint32_t i = 0;
    struct rte_eth_stats st_eth_stats[8];
    struct rte_eth_link st_eth_link[8];

    for (i = 0; i < nb_ports; i++) {
        /* skip ports that are not enabled */
        if ((portmask & (1 << i)) == 0) {
            continue;
        }
        if (i < COUNTOF(st_eth_stats)) {
            i_ret = rte_eth_stats_get(i, &st_eth_stats[i]);
            if (i_ret != 0) {
                printf("get eth stats failed (port index = %u)\n", i);
                return i_ret;
            }
            p_st_eth_info[i].u64_in_total_packets = st_eth_stats[i].ipackets; /* 进入网口的包的数量 */
            p_st_eth_info[i].u64_in_err_packets = st_eth_stats[i].ierrors;    /* 进入网口出现错误包的数量 */
            p_st_eth_info[i].u64_in_drop_packets = st_eth_stats[i].imissed;   /* 网口丢弃的数据包 */
            p_st_eth_info[i].u64_in_total_bytes = st_eth_stats[i].ibytes;     /* 进入网口字节数的总量 */
        }

        if (i < COUNTOF(st_eth_link)) {
            rte_eth_link_get_nowait(i, &st_eth_link[i]);
            if (st_eth_link[i].link_status) /* 网口的状态是“UP”状态 */
            {
                p_st_eth_info[i].u64_eth_speed = st_eth_link[i].link_speed;
                strncpy(p_st_eth_info[i].a_net_card_stat, "UP", strlen("UP"));
            } else {
                strncpy(p_st_eth_info[i].a_net_card_stat, "DOWN", strlen("DOWN"));
            }
        }
    }

    *p_work_port_cnt = nb_ports;
    return 0;
}

/* 设置数据包采样参数信息以及队列信息 */
void set_sample_args(int i_sample_mode, int i_sample_rate, CALLBACK_SAMPLE cb_sample)
{
    /* check param */
    if (cb_sample == NULL || i_sample_mode < 0 || i_sample_rate < 0) {
        return;
    }
    RTE_LOG(NOTICE, GWHW, "sample_mode = %d, sample_rate = %d, \n", i_sample_mode, i_sample_rate);
    g_i_sample_mode = i_sample_mode;
    g_i_sample_rate = i_sample_rate;
    g_cb_sample = cb_sample;
    // if (g_i_sample_mode == 1)
    // {
    //   g_i_is_write_pcap_header = 1;
    // }
}

/* 设置限制的网口数量 */
void set_limit_eth_num(uint32_t u32_eth_num)
{
    RTE_LOG(NOTICE, GWHW, "limit eth num = %u\n", u32_eth_num);
    g_u32_eth_num = u32_eth_num;
}

void get_dpdk_log_buf(char *log_buf, size_t log_buf_len)
{
    const int c_nstats_len = 5 + 3 + 3;
    const int c_link_len = 4;
    const uint8_t nb_ports = rte_eth_dev_count_avail();
    unsigned i;
    struct rte_eth_stats eth_stats[8];
    struct rte_eth_link eth_link[8];
    char buf[64] = {0};
    char buf1[64] = {0};
    char show_data[1 + c_nstats_len + c_link_len][512];
    const int c_stats_off = 1;
    const int c_link_off = 1 + c_nstats_len;

    memset(show_data, 0, sizeof(show_data));
    memset((void *)&eth_infos, 0, sizeof(eth_infos));
    app_stats_t app_stats = app_stats_collect();

    sprintf(log_buf + strlen(log_buf), "\nthread stats:\n");
    sprintf(log_buf + strlen(log_buf), "                                %16s %16s %16s\n", "RX", "IP Worker",
            "TCP Worker");
    sprintf(log_buf + strlen(log_buf), " - Pkts deqd from upstream:     %16" PRIu64 " %16" PRIu64 " %16" PRIu64 "\n",
            app_stats.rx.rx_pkts, app_stats.ip_wkr.dequeue_pkts, app_stats.wkr.dequeue_pkts);
    sprintf(log_buf + strlen(log_buf), " - Pkts enqd to workers ring:   %16" PRIu64 " %16" PRIu64 " %16" PRIu64 "\n",
            app_stats.rx.enqueue_pkts, app_stats.ip_wkr.enqueue_pkts, app_stats.wkr.enqueue_pkts);
    sprintf(log_buf + strlen(log_buf), " - Pkts enq to workers failed:  %16" PRIu64 " %16" PRIu64 " %16" PRIu64 "\n",
            app_stats.rx.enqueue_failed_pkts, app_stats.ip_wkr.enqueue_failed_pkts, app_stats.wkr.enqueue_failed_pkts);

    time_t current_time = time(NULL);
    uint64_t delta = current_time - g_lasttime_timestamp;
    g_lasttime_timestamp = current_time;

    for (i = 0; i < nb_ports; i++) {
        /* skip ports that are not enabled */
        if ((portmask & (1 << i)) == 0) {
            continue;
        }
        snprintf(buf1, COUNTOF(buf1) - 1, "Port %u", i);
        snprintf(buf, COUNTOF(buf) - 1, " %16s", buf1);
        strcat(show_data[0], buf);

        if (i < COUNTOF(eth_stats)) {
            rte_eth_stats_get(i, &eth_stats[i]);
            snprintf(buf, COUNTOF(buf) - 1, " %16" PRIu64 "", eth_stats[i].ipackets);
            eth_infos.u64_in_total_packets += eth_stats[i].ipackets;
            strcat(show_data[c_stats_off + 0], buf);
            snprintf(buf, COUNTOF(buf) - 1, " %16" PRIu64 "", eth_stats[i].opackets);
            strcat(show_data[c_stats_off + 1], buf);
            snprintf(buf, COUNTOF(buf) - 1, " %16" PRIu64 "", eth_stats[i].ierrors);
            eth_infos.u64_in_err_packets += eth_stats[i].ierrors;
            strcat(show_data[c_stats_off + 2], buf);
            snprintf(buf, COUNTOF(buf) - 1, " %16" PRIu64 "", eth_stats[i].oerrors);
            strcat(show_data[c_stats_off + 3], buf);
            snprintf(buf, COUNTOF(buf) - 1, " %16" PRIu64 "", eth_stats[i].rx_nombuf);
            strcat(show_data[c_stats_off + 4], buf);
            snprintf(buf, COUNTOF(buf) - 1, " %16" PRIu64 "", eth_stats[i].ibytes);
            strcat(show_data[c_stats_off + 5], buf);
            snprintf(buf, COUNTOF(buf) - 1, " %16" PRIu64 "", eth_stats[i].obytes);
            strcat(show_data[c_stats_off + 6], buf);
            snprintf(buf, COUNTOF(buf) - 1, " %16" PRIu64 "", eth_stats[i].imissed);
            eth_infos.u64_in_miss_packets += eth_stats[i].imissed;
            strcat(show_data[c_stats_off + 7], buf);

            snprintf(buf, COUNTOF(buf) - 1, " %16.5lf",
                     (double)eth_stats[i].imissed / (eth_stats[i].ipackets + eth_stats[i].imissed) * 100);
            strcat(show_data[c_stats_off + 8], buf);

            snprintf(buf, COUNTOF(buf) - 1, " %16lu", (eth_stats[i].ibytes - g_lasttime_ibytes[i]) * 8 / delta);
            strcat(show_data[c_stats_off + 9], buf);
            g_lasttime_ibytes[i] = eth_stats[i].ibytes;

            snprintf(buf, COUNTOF(buf) - 1, " %16lu", (eth_stats[i].ipackets - g_lasttime_ipackets[i]) / delta);
            strcat(show_data[c_stats_off + 10], buf);
            g_lasttime_ipackets[i] = eth_stats[i].ipackets;
        }
        if (i < COUNTOF(eth_link)) {
            // rte_eth_link_get(i, &eth_link[i]);
            rte_eth_link_get_nowait(i, &eth_link[i]);
            snprintf(buf, COUNTOF(buf) - 1, " %16u", eth_link[i].link_speed);
            strcat(show_data[c_link_off + 0], buf);
            snprintf(buf, COUNTOF(buf) - 1, " %16s", eth_link[i].link_duplex ? "FULL" : "HALF");
            strcat(show_data[c_link_off + 1], buf);
            snprintf(buf, COUNTOF(buf) - 1, " %16s", eth_link[i].link_autoneg ? "AUTONEG" : "FIXED");
            strcat(show_data[c_link_off + 2], buf);
            snprintf(buf, COUNTOF(buf) - 1, " %16s", eth_link[i].link_status ? "UP" : "DOWN");
            strcat(show_data[c_link_off + 3], buf);
        }
    }

    sprintf(log_buf + strlen(log_buf), "\nstats:\n");
    sprintf(log_buf + strlen(log_buf), "              %s\n", show_data[0]);
    sprintf(log_buf + strlen(log_buf), " - Pkts in:   %s\n", show_data[c_stats_off + 0]);
    sprintf(log_buf + strlen(log_buf), " - Pkts out:  %s\n", show_data[c_stats_off + 1]);
    sprintf(log_buf + strlen(log_buf), " - In Errs:   %s\n", show_data[c_stats_off + 2]);
    sprintf(log_buf + strlen(log_buf), " - Out Errs:  %s\n", show_data[c_stats_off + 3]);
    sprintf(log_buf + strlen(log_buf), " - Mbuf Errs: %s\n", show_data[c_stats_off + 4]);
    sprintf(log_buf + strlen(log_buf), " - In Bytes:  %s\n", show_data[c_stats_off + 5]);
    sprintf(log_buf + strlen(log_buf), " - Out Bytes: %s\n", show_data[c_stats_off + 6]);
    sprintf(log_buf + strlen(log_buf), " - In Missed: %s\n", show_data[c_stats_off + 7]);
    sprintf(log_buf + strlen(log_buf), " - Drop(%%):   %s\n", show_data[c_stats_off + 8]);
    sprintf(log_buf + strlen(log_buf), " - Rate(bps): %s\n", show_data[c_stats_off + 9]);
    sprintf(log_buf + strlen(log_buf), " - Pps:       %s\n", show_data[c_stats_off + 10]);

    sprintf(log_buf + strlen(log_buf), "\nlink:\n");
    sprintf(log_buf + strlen(log_buf), "              %s\n", show_data[0]);
    sprintf(log_buf + strlen(log_buf), " - Speed:     %s\n", show_data[c_link_off + 0]);
    sprintf(log_buf + strlen(log_buf), " - Duplex:    %s\n", show_data[c_link_off + 1]);
    sprintf(log_buf + strlen(log_buf), " - Autoneg:   %s\n", show_data[c_link_off + 2]);
    sprintf(log_buf + strlen(log_buf), " - Staus:     %s\n", show_data[c_link_off + 3]);

    return;
}

static void show_conf(void)
{
    printf("cap conf params:\n");
    printf("  verbose_mode=%d\n", g_verbose_mode);
    printf("  portmask=%X\n", portmask);
    printf("  pcap_write=%d\n", g_pcap_write);
    printf("  mbuf_data_size=%d\n", g_conf_mbuf_data_size);
    printf("  work_thread=%d\n", g_conf_work_thread);
    printf("  ip_work_thread=%d\n", g_conf_ip_work_thread);
    printf("  rx_work_thread=%d\n", g_conf_rx_work_thread);
    printf("  rx_desc_per_queue=%d\n", g_conf_rx_desc_per_queue);
    printf("  tx_desc_per_queue=%d\n", g_conf_tx_desc_per_queue);
    printf("  max_pkts_burst=%d\n", g_conf_max_pkts_burst);
    printf("  mbuf_per_pool=%d\n", g_conf_mbuf_per_pool);
    printf("  pool_cache_size=%d\n", g_conf_mbuf_pool_cache_size);
    printf("  ring_worker_size=%d\n", g_conf_ring_worker_size);
    printf("  ring_rx_size=%d\n", g_conf_ring_rx_size);
    printf("  ring_tcp_size=%d\n", g_conf_ring_tcp_size);
    printf("  snaplen=%d\n", g_conf_snaplen);
    printf("  pkt_max_size=%d\n", g_conf_pkt_max_size);
    printf("  output_file_template=%s\n", g_conf_output_file_template);
    printf("  speed=%d\n", g_conf_speed);
    printf("  duplex=%d\n", g_conf_duplex);
    printf("  speed_fixed=%d\n", g_conf_speed_fixed);
    printf("  idle_max_count=%d\n", g_conf_idle_max_count);
    printf("  converge_pkts = %d\n", g_conf_converge_pkts);
    printf("  ip_work_deq_num = %d\n", g_conf_ip_work_deq_num);
    printf("  rx de num = %d\n", g_conf_rx_deq_num);
    printf("  rx converge pkts = %d\n", g_conf_rx_converge_pkts);
    printf("  g_conf_pcap_timestamp = %d\n", g_conf_pcap_timestamp);
}

int capd_main_new(int argc, char **argv, capd_args_t *pca)
{
    int ret;
    unsigned nb_ports;
    unsigned int lcore_id;
    unsigned int last_lcore_id;
    unsigned int master_lcore_id;
    unsigned int master_socket_id;
#if 0
  unsigned int rx_thread_lcore_id, ip_thread_lcore_id, pcap_write_thread_lcore_id;
#else
    unsigned pcap_write_thread_lcore_id;
#endif

    unsigned int port_socket_id;
    uint8_t port_id;
    uint8_t nb_ports_available = 0;
    uint8_t available_port_id[MAX_PORT_NUM] = {0};
    struct ip_worker_thread_args ip_worker_args[RX_IP_RING_OUT_MAX_SIZE] = {{{0}, {0}, 0, 0, 0}};
    struct tcp_worker_thread_args tcp_worker_args[TCP_RING_OUT_MAX_SIZE] = {{0}};
    struct rx_tcp_thread_args rx_tcp_args[RX_IP_RING_OUT_MAX_SIZE] = {{0}};
    struct rte_ring *rx_to_workers = NULL;
    struct rte_ring *ip_workers_to_tcp_workers = NULL;
    struct rte_ring *workers_to_pcap = NULL;
    unsigned int i, k, rx_thread_cnt = 0;
    pthread_t timestamp_thread;
    size_t default_smp_affinity_size = 0;
    char default_smp_affinity[1024] = {0};  // irq 亲和性
    irq_smp_t *irq_smp_root = NULL;
    int lcore_flags[RTE_MAX_LCORE];

    struct rx_thread_args rx_args[RX_IP_RING_OUT_MAX_SIZE];
    memset(rx_args, 0, sizeof(struct rx_thread_args) * RX_IP_RING_OUT_MAX_SIZE);
    memset(lcore_flags, 0, sizeof(lcore_flags) * sizeof(int));

    char str_buf[32] = {0};
    g_cb_flow = pca->cb_flow;

    if (pca == NULL || pca->size != sizeof(capd_args_t)) {
        return 1;
    }
    memset(tcp_worker_args, 0, sizeof(tcp_worker_args));

    /* Initialize EAL */
    ret = rte_eal_init(argc, argv);
    if (ret < 0) return -1;

    argc -= ret;
    argv += ret;

    /* Parse the application specific arguments */
    ret = parse_args(argc, argv);
    if (ret < 0) return -1;

    if (g_verbose_mode) {
        show_conf();
    }

    /* Check if we have enought cores */
    if (rte_lcore_count() < 2) {
        rte_exit(EXIT_FAILURE,
                 "Error, This application needs at "
                 "least 2 logical cores to run:\n"
                 "1 lcore for packet RX\n"
                 "and at least 1 lcore for worker threads\n");
    }

    nb_ports = rte_eth_dev_count_total();
    printf("rte_eth_dev_count_total:%d\n", nb_ports);
    nb_ports = rte_eth_dev_count_avail();
    printf("rte_eth_dev_count_avail:%d\n", nb_ports);
    if (nb_ports == 0) {
        rte_exit(EXIT_FAILURE, "Error: no ethernet ports detected\n");
    }
    if (nb_ports != 1 && (nb_ports & 1)) {
        rte_exit(EXIT_FAILURE, "Error: number of ports must be even, except when using a single port\n");
    }

    /* license 限制网口个数 */
    if (g_u32_eth_num != 0 && g_u32_eth_num < nb_ports) {
        nb_ports = g_u32_eth_num;
    }

    last_lcore_id = get_last_lcore_id();
    master_lcore_id = rte_get_main_lcore(); /* 管理lcore ID */
    master_socket_id = rte_lcore_to_socket_id(master_lcore_id);
    pcap_write_thread_lcore_id = -1;
    port_socket_id = rte_socket_id();
    (void)pcap_write_thread_lcore_id;

    nb_ports_available = nb_ports;
    for (port_id = 0; port_id < nb_ports; port_id++) {
        /* skip ports that are not enabled */
        if ((portmask & (1 << port_id)) == 0) {
            printf("\nSkipping disabled port %d\n", port_id);
            nb_ports_available--;
            continue;
        }
        if (rte_eth_dev_socket_id(port_id) >= 0) {
            port_socket_id = rte_eth_dev_socket_id(port_id);
        }
        if (g_verbose_mode) {
            printf("rte_eth_dev_socket_id(%d)=%d\n", port_id, rte_eth_dev_socket_id(port_id));
        }
    }

    if (!nb_ports_available) {
        rte_exit(EXIT_FAILURE, "All available ports are disabled. Please set portmask.\n");
    }

    if (check_lcore_params() < 0) rte_exit(EXIT_FAILURE, "check_lcore_params failed\n");

    if (init_lcore_rx_queues() < 0) rte_exit(EXIT_FAILURE, "init_lcore_rx_queues failed\n");

    uint8_t portid;
    /* initialize all ports */
    for (portid = 0; portid < nb_ports; portid++) {
        /* skip ports that are not enabled */
        if ((portmask & (1 << portid)) == 0) {
            printf("\nSkipping disabled port %d\n", portid);
            continue;
        }

        /* init port */
        printf("Initializing port %d ... \n", portid);
        fflush(stdout);

        /* init memory */
        ret = init_mem(portid);
        if (ret < 0) rte_exit(EXIT_FAILURE, "init_mem failed\n");

        configure_eth_port(portid);

        printf("\n");
    }

    // get cpu layout
    uint8_t core_id = 0;
    uint8_t socket_id = 0;
    uint8_t nb_cores = 0;
    uint8_t nb_sockets = 0;
    uint8_t tmp_lcore_id = 0;
    uint8_t nb_lcores = rte_lcore_count();
    for (lcore_id = 0; lcore_id < nb_lcores; lcore_id++) {
        core_id = eal_cpu_core_id(lcore_id);
        socket_id = eal_cpu_socket_id(lcore_id);

        if (core_id >= CORE_NUM_MAX) {
            rte_exit(EXIT_FAILURE, "core id=%u too large\n", core_id);
        }

        if (socket_id >= SOCKET_NUM_MAX) {
            rte_exit(EXIT_FAILURE, "socket id=%u too large\n", socket_id);
        }

        tmp_lcore_id = g_cpu_layout[socket_id][core_id][0];
        if (tmp_lcore_id > 0) {
            g_cpu_layout[socket_id][core_id][1] = lcore_id;
        } else {
            g_cpu_layout[socket_id][core_id][0] = lcore_id;
        }

        if (core_id > nb_cores) nb_cores = core_id;
        if (socket_id > nb_sockets) nb_sockets = socket_id;

        // 检查g_rx_lcore_id的socket_id是否和port_socketid一样
        if (1 == g_rx_lcore_id[lcore_id]) {
            if (socket_id != port_socket_id) {
                rte_exit(EXIT_FAILURE, "rx lcore id=%u socket id=%u, port socket id=%u, socket id not same\n", lcore_id,
                         socket_id, port_socket_id);
            }

            rx_thread_cnt++;
        }
    }

    nb_cores++;
    nb_sockets++;
    uint8_t nb_lcores_per_core = 1;
    if (g_cpu_layout[0][0][0] > 0) {
        g_cpu_layout[0][0][1] = g_cpu_layout[0][0][0];
        g_cpu_layout[0][0][0] = 0;

        nb_lcores_per_core = 2;
    }

    printf("nb_cores=%u, nb_sockets=%u, nb_lcores_per_core=%u\n", nb_cores, nb_sockets, nb_lcores_per_core);

    uint8_t ip_thread_cnt = 0;
    uint8_t tcp_thread_cnt = 0;
    for (socket_id = 0; socket_id < nb_sockets; socket_id++) {
        for (core_id = 0; core_id < nb_cores; core_id++) {
            for (k = 0; k < nb_lcores_per_core; k++) {
                lcore_id = g_cpu_layout[socket_id][core_id][k];

                if (0 == lcore_id || 1 == g_rx_lcore_id[lcore_id]) continue;

                if (ip_thread_cnt < g_conf_ip_work_thread && socket_id == port_socket_id) {
                    printf("get ip thread lcore id=%u\n", lcore_id);
                    g_ip_lcore_id[lcore_id] = 1;
                    ip_thread_cnt++;
                    continue;
                }

                if (tcp_thread_cnt < g_conf_work_thread) {
                    printf("get tcp thread lcore id=%u\n", lcore_id);
                    g_tcp_lcore_id[lcore_id] = 1;
                    tcp_thread_cnt++;
                }
            }
        }
    }

    if (ip_thread_cnt < g_conf_ip_work_thread) {
        rte_exit(EXIT_FAILURE, "need ip work thread=%d, but only get %u\n", g_conf_ip_work_thread, ip_thread_cnt);
    }

    if (tcp_thread_cnt < g_conf_work_thread) {
        rte_exit(EXIT_FAILURE, "need tcp work thread=%d, but only get %u\n", g_conf_work_thread, tcp_thread_cnt);
    }

    /* 创建用于生成时间戳的线程 */
    int err = 0;
    if (0 != (err = pthread_create(&timestamp_thread, NULL, (void *(*)(void *))creat_timestamp, (void *)NULL))) {
        printf("create timestamp thread failed %s\n", strerror(err));
        return err;
    } else {
        if (g_verbose_mode) {
            printf("create upload hive event data thread successfully\n");
        }
    }

    /* Start tcp_worker_thread() on all the available slave cores but the last 1 */
    // get_previous_lcore_id(last_lcore_id)
    for (k = 0, lcore_id = 0; lcore_id < nb_lcores; lcore_id++) {
        if (0 == g_tcp_lcore_id[lcore_id]) continue;

        // if (ip_worker_args.ring_out[k] == NULL)
        if (ip_worker_args[0].ring_out[k] == NULL) {
            snprintf(str_buf, COUNTOF(str_buf) - 1, "ip_to_tcp_workers_%03d", k);
            // ip_workers_to_tcp_workers = rte_ring_create(str_buf, g_conf_ring_tcp_size,
            // rte_lcore_to_socket_id(lcore_id), RING_F_SP_ENQ | RING_F_SC_DEQ);
            if (g_conf_ip_work_thread == 1) {
                ip_workers_to_tcp_workers = rte_ring_create(
                    str_buf, g_conf_ring_tcp_size, rte_lcore_to_socket_id(lcore_id), RING_F_SP_ENQ | RING_F_SC_DEQ);
                // ip_workers_to_tcp_workers = rte_ring_create(str_buf, g_conf_ring_tcp_size, master_socket_id,
                // RING_F_SP_ENQ | RING_F_SC_DEQ);
            } else {
                ip_workers_to_tcp_workers =
                    rte_ring_create(str_buf, g_conf_ring_tcp_size, rte_lcore_to_socket_id(lcore_id), RING_F_SC_DEQ);
                // ip_workers_to_tcp_workers = rte_ring_create(str_buf, g_conf_ring_tcp_size, master_socket_id,
                // RING_F_SC_DEQ);
            }
            if (ip_workers_to_tcp_workers == NULL) rte_exit(EXIT_FAILURE, "%s\n", rte_strerror(rte_errno));
        }
        unsigned int u_index = 0;
        for (; u_index < g_conf_ip_work_thread; ++u_index) {
            ip_worker_args[u_index].ring_out[k] = ip_workers_to_tcp_workers;
        }

        for (u_index = 0; u_index < g_conf_rx_work_thread; ++u_index) {
            rx_tcp_args[u_index].ring_out[k] = ip_workers_to_tcp_workers;
        }
        g_tcp_ring[k] = ip_workers_to_tcp_workers;
        // ip_worker_args.ring_out[k] = ip_workers_to_tcp_workers;
        tcp_worker_args[k].ring_in = ip_workers_to_tcp_workers;
        tcp_worker_args[k].ring_out = workers_to_pcap;
        tcp_worker_args[k].callback = pca->cb_tcp;
        tcp_worker_args[k].userdata = pca->userdata_tcp;
        ip_workers_to_tcp_workers = NULL;

        if (rte_eal_remote_launch(tcp_worker_thread, (void *)&tcp_worker_args[k], lcore_id)) {
            printf("%s\n", rte_strerror(rte_errno));
        } else {
            k++;
            lcore_flags[lcore_id] = 1;
        }
    }

    if (ip_thread_cnt > rx_thread_cnt) {
        for (i = 0; i < rx_thread_cnt; ++i) {
            snprintf(str_buf, 30, "rx_to_workers_%03d", i);
            rx_args[i].ring_out = rte_ring_create(str_buf, g_conf_ring_rx_size, port_socket_id, RING_F_SP_ENQ);

            for (k = 0; k < ip_thread_cnt; k++) {
                uint16_t ip_work_ring_index = ip_worker_args[k].u16_ip_ring_cnt;
                ip_worker_args[k].ring_in[ip_work_ring_index++] = rx_args[i].ring_out;
                ip_worker_args[k].u16_ip_ring_cnt = ip_work_ring_index;
                if (g_verbose_mode) {
                    printf("rx thread index = %d, ip work thread index = %d, ip work thread ring in cnt = %hu\n", i, k,
                           ip_work_ring_index);
                }
            }
        }
    } else {
        for (i = 0; i < rx_thread_cnt; ++i) {
            snprintf(str_buf, 30, "rx_to_workers_%03d", i);
            rx_args[i].ring_out =
                rte_ring_create(str_buf, g_conf_ring_rx_size, port_socket_id, RING_F_SP_ENQ | RING_F_SC_DEQ);

            int ip_work_index = i % g_conf_ip_work_thread;
            uint16_t ip_work_ring_index = ip_worker_args[ip_work_index].u16_ip_ring_cnt;
            ip_worker_args[ip_work_index].ring_in[ip_work_ring_index++] = rx_args[i].ring_out;
            ip_worker_args[ip_work_index].u16_ip_ring_cnt = ip_work_ring_index;
            if (g_verbose_mode) {
                printf("rx thread index = %d, ip work thread index = %d, ip work thread ring in cnt = %hu\n", i,
                       ip_work_index, ip_work_ring_index);
            }
        }
    }

    for (i = 0, lcore_id = 0; lcore_id < nb_lcores; lcore_id++) {
        if (0 == g_ip_lcore_id[lcore_id]) continue;

        ip_worker_args[i].callback = pca->cb_ip;
        ip_worker_args[i].userdata = pca->userdata_ip;
        if (rte_eal_remote_launch(ip_worker_thread, (void *)&ip_worker_args[i], lcore_id)) {
            printf("%s\n", rte_strerror(rte_errno));
        } else {
            lcore_flags[lcore_id] = 1;
            i++;
        }
    }

    adj_thread_pri_ni();

    /* 判断rx_thread 是否使用master_lcore_id */
    int rx_use_master_lcore = 0;

    for (lcore_id = 0; lcore_id < nb_lcores; lcore_id++) {
        if (0 == g_rx_lcore_id[lcore_id]) continue;

        if (lcore_id == master_lcore_id) {
            rx_use_master_lcore = 1;
            break;
        }

        lcore_flags[lcore_id] = 1;
    }

    if (rx_use_master_lcore)
    // if (rx_thread_lcore_id == master_lcore_id)
    {
        fprintf(stderr, "Warning: rx thread is master!\n");
    } else {
        // 调整IRQ亲和性
        char *buf = NULL;
        char *p_irq_usb;
        char *p_irq_sata;
        char *p_irq_raid;
        unsigned int lcore_irq_only = RTE_MAX_LCORE;
        // lcore_flags[rx_thread_lcore_id] = 1;

        for (lcore_id = 0; lcore_id < nb_lcores; lcore_id++) {
            if (1 == lcore_flags[lcore_id]) continue;

            lcore_irq_only = lcore_id;
            lcore_flags[lcore_id] = 1;
            break;
        }

        if (g_verbose_mode) {
            unsigned int i;
            char buf[1024] = {0};

            for (i = 0; i <= last_lcore_id; i++) {
                snprintf(buf + strlen(buf), COUNTOF(buf) - 1 - strlen(buf), (i ? " %d" : "%d"), lcore_flags[i]);
            }
            printf("lcore flags data=|%s|\n", buf);
            printf("lcore_irq_only=%d\n", lcore_irq_only);
        }

        buf = get_irq_smp_affinity(lcore_flags, last_lcore_id + 1);

        p_irq_usb = get_pci_irq(0x0c03);   // USB
        p_irq_sata = get_pci_irq(0x0106);  // SATA
        p_irq_raid = get_pci_irq(0x0104);  // RAID

        if (buf != NULL) {
            irq_smp_root = read_irq_all_smp_affinity();
            default_smp_affinity_size =
                read_irq_default_smp_affinity(default_smp_affinity, COUNTOF(default_smp_affinity));
            update_irq(buf);
            free(buf);
        }

        if (p_irq_sata != NULL) {
            update_irq_only(lcore_irq_only, p_irq_sata);
            free(p_irq_sata);
        }
        if (p_irq_usb != NULL) {
            update_irq_only(lcore_irq_only, p_irq_usb);
            free(p_irq_usb);
        }
        if (p_irq_raid != NULL) {
            update_irq_only(lcore_irq_only, p_irq_raid);
            free(p_irq_raid);
        }
    }

    // printf("master_lcore_id=%d, rx_thread_lcore_id=%d, ip_thread_lcore_id=%d\n", master_lcore_id, rx_thread_lcore_id,
    // ip_thread_lcore_id);
    //  运行捕包线程
    for (k = 0, lcore_id = 0; lcore_id < nb_lcores; lcore_id++) {
        if (0 == g_rx_lcore_id[lcore_id]) continue;

        if (rte_eal_remote_launch(rx_thread, (void *)&rx_args[k], lcore_id)) {
            printf("%s\n", rte_strerror(rte_errno));
        } else {
            k++;
        }
    }

#if 0
  if (!(rx_thread_lcore_id == master_lcore_id))
  {
    sleep(1);
    print_stats();

    while (!quit_signal)
    {
      usleep(100 * 1000L); // sleep 10ms
      // sleep(1);
    }
  }
#else
    sleep(1);
    print_stats();
    while (!quit_signal) {
        usleep(100 * 1000L);
    }

#endif
    // 等待所有线程结束
    rte_eal_mp_wait_lcore();
    // RTE_LCORE_FOREACH_SLAVE(lcore_id) {
    //   if (rte_eal_wait_lcore(lcore_id) < 0)
    //     return -1;
    // }

    if (timestamp_thread) {
        pthread_join(timestamp_thread, NULL);
    }

    for (port_id = 0; port_id < nb_ports; port_id++) {
        /* skip ports that are not enabled */
        if ((portmask & (1 << port_id)) == 0) {
            // printf("\nSkipping disabled port %d\n", port_id);
            continue;
        }

        /* stop & close port */
        rte_eth_dev_stop(port_id);
        rte_eth_dev_close(port_id);
    }

    if (rx_to_workers != NULL) {
        rte_ring_free(rx_to_workers);
    }
    if (ip_workers_to_tcp_workers != NULL) {
        rte_ring_free(ip_workers_to_tcp_workers);
    }
    for (k = 0; k < g_conf_rx_work_thread; ++k) {
        if (rx_args[k].ring_out != NULL) {
            rte_ring_free(rx_args[k].ring_out);
        }
    }

    for (k = 0; k < g_conf_ip_work_thread; ++k) {
        if (tcp_worker_args[k].ring_out != NULL) {
            rte_ring_free(tcp_worker_args[k].ring_out);
        }
    }

    print_stats();

    // 还原IRQ亲和性
    if (default_smp_affinity_size > 0) {
        update_irq(default_smp_affinity);
    }

    write_irq_all_smp_affinity(irq_smp_root);
    free_all_irq_smp(irq_smp_root);

    return 0;
}

void ip_cache_clean()
{
    g_ip_cache_clean = 1;
    int i = 0;
    for (; i < MAX_RING_NUM && g_ip_ring[i]; i++) {
        if (!rte_ring_empty(g_ip_ring[i])) {
            i--;
        }
    }
    g_ip_cache_clean = 0;
    return;
}

void tcp_cache_clean()
{
    g_tcp_cache_clean = 1;
    int i = 0;
    for (; i < MAX_RING_NUM && g_tcp_ring[i]; i++) {
        if (!rte_ring_empty(g_tcp_ring[i])) {
            i--;
        }
    }
    g_tcp_cache_clean = 0;
    return;
}

int dpdk_status()
{
    int i = 0;
    for (; i < MAX_RING_NUM && g_ip_ring[i]; i++) {
        if (!rte_ring_empty(g_ip_ring[i])) {
            return 1;
        }
    }

    i = 0;
    for (; i < MAX_RING_NUM && g_tcp_ring[i]; i++) {
        if (!rte_ring_empty(g_tcp_ring[i])) {
            return 1;
        }
    }

    return 0;
}
