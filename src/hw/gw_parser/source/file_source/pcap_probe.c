/*
 */

/* system header */
#include <string.h>
#include <dirent.h>
#include <limits.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <unistd.h>
#include <stdlib.h>
#include <errno.h>

/* 3rd project header */
#include <pcap.h>

/* my project header */
#include "utils.h"
//#include "queue.h"
#include "pkt_queue.h"
#include "cap_data.h"
#include "pcap_probe.h"

// 空闲时计数值 1万 或 10万 不等于0时表示不释放CPU资源
#define IDLE_MAX_CNT (1 * 10000L)
#define IDLE_DEFAULT (0)
// 休眠 1ms
#define IDLE_USLEEP (1 * 10000L)
// 休眠函数
//#define IDLE_USLEEP_FUNC rte_delay_us
#define IDLE_USLEEP_FUNC usleep

#define PCAPNG_FILE_HEAD 0x0A0D0D0A

#define PCAP_FILE_HEAD 0xA1B2C3D4

#define TCP_WORKER_DATA_WAIT 10 //us

static int g_conf_idle_max_count = IDLE_MAX_CNT;

typedef struct 
{
  uint64_t u64_queue_success;
  uint64_t u64_queue_failed;
}pcap_queue_stats;


// TODO 从取配置文件加载参数
volatile int g_quit_signal = 0;
int g_conf_pcap_work_thread_num = 1; // 2
const char *g_conf_pcapdir_path = ""; // "/opt/pcap/task";
const char *g_pcap_sample_path = NULL;
int g_conf_pcapfile_mode = 0;
int g_conf_pcap_reserve = 0;
int g_conf_pcap_timestamp = 0;
uint16_t g_tcp_worker_timeout = 0;
//uint64_t g_conf_file_source_parser_max_bps = 100 * 1024ULL * 1024ULL;
uint64_t g_conf_file_source_queue_max_elements = 100000;

//static uint64_t g_current_parsed_total_bytes = 0;
//static uint64_t g_last_parse_ts_ms = 0;
//static uint64_t g_current_parse_ts_ms = 0;

#define IDLE_F_COUNTER(idle_cnt, idle_deep_cnt, IDLE_DEEP_MAX_CNT_, IDLE_DEEP_SLEEP_) \
  do                                                                                  \
  {                                                                                   \
    if (likely(g_conf_idle_max_count > 0 && idle_cnt >= 0))                           \
    {                                                                                 \
      idle_cnt++;                                                                     \
      if (idle_cnt >= g_conf_idle_max_count)                                          \
      {                                                                               \
        if (idle_deep_cnt < IDLE_DEEP_MAX_CNT_)                                       \
        {                                                                             \
          idle_deep_cnt++;                                                            \
          IDLE_USLEEP_FUNC(IDLE_USLEEP); /* sleep 1ms */                              \
        }                                                                             \
        else                                                                          \
        {                                                                             \
          IDLE_USLEEP_FUNC(IDLE_USLEEP *IDLE_DEEP_SLEEP_); /* sleep x * 1ms */        \
        }                                                                             \
        idle_cnt = 0;                                                                 \
      }                                                                               \
    }                                                                                 \
  } while (0)

#define IDLE_F_RESET(idle_cnt, idle_deep_cnt) \
  do                                          \
  {                                           \
    if (unlikely(idle_cnt > 0))               \
    {                                         \
      idle_cnt = 0;                           \
      idle_deep_cnt = 0;                      \
    }                                         \
  } while (0)

#define ERR_SUCCESS 0
#define ERR_FAILED 1
#define ERR_INVALID_PARAM 2
#define ERR_NOMEM 3
#define ERR_SYS_FAILED 4
#define ERR_UNSUPPORT_LINKTYPE 5

#define MAX_WORKER_THREAD 64
#define SCHE_TABLE_SIZE 256

#define DIR_PATH_LEN 256
#define FULL_PATH_LEN 256
#define DIR_NAME_LEN 256
#define FILE_NAME_LEN 256
#define FILE_LIST_MAX_NUM 1024
#define TASK_DIR_MAX_NUM 64

#define FINISH_FILE "finish"
#define CMD_MAX_LEN 256
#define PS_RESULT_MAX_LEN 512

#define TASK_CHECK_INTERVAL 0
#define TASK_DIR_DEL_INTERVAL 5
#define FILE_MONITOR_NAME "file_monitor.sh"
#define TASK_MAX_NUM 1024

#define WAIT_TO_PARSE_TAG "newtask_"

volatile uint64_t g_u64_file_num = 0;      /* 已经解析文件的数量 */
volatile uint64_t g_u64_succ_file_num = 0; /* 解析成功文件的数量 */
volatile uint64_t g_u64_fail_file_num = 0; /* 解析失败文件的数量 */

//TODO
//文件夹递归功能增加
//统计功能
//性能优化
//.87设备pcap是否可以解析

/**
* @Struct: tagFiletype
* @Author: guojianchuan
* @Date: 2018-02-22
* @Description: 文件类型
*/
enum tagFiletype
{
  UNKNOWN_TYPE = 0,
  DIR_TYPE = 4,
  FILE_TYPE = 8,
  LINK_TYPE = 10
};

/**
* @Struct: tagLinktype
* @Author: guojianchuan
* @Date: 2018-02-22
* @Description: pcap linktype枚举
*/
enum tagLinktype
{
    ETHERNET = 1
  , LINUX_SLL = 113
};

/**
* @Struct: tag_queue_task
* @Author: guojianchuan
* @Date: 2018-02-22
* @Description: 任务队列, 用户会把多个.pcap文件打包成1个.zip文件，1个.zip文件被当成1个任务
*/
typedef struct tag_queue_task
{
  /* 1个任务中的文件数量(pcap文件) */
  int filenum;

  /* 文件目录 */
  char dirname[DIR_NAME_LEN];

  /* 目录下的文件列表 */
  char **filelist;
} queue_task_t;

/**
* @Struct:tag_queue_pcap
* @Author: guojianchuan
* @Date: 2018-02-22
* @Description: pcap包队列
*/
typedef struct tag_queue_pcap
{
  /* pcap包的长度 */
  size_t length;

  /* pcap包的内容(链路层头部开始的数据) */
  void *s;

  /* timestamp */
  uint32_t timestamp;

  /* 微秒 1秒=1000*1000微秒 */
  uint32_t microseconds;

  /* 附加数据 */
  int udata;

  char filename[FILE_NAME_LEN];
} queue_pcap_t;

/**
* @Struct:tag_ondisk_pkt_hdr
* @Author: guojianchuan
* @Date: 2018-02-22
* @Description: 保存在文件中的pcap包结构的头 (和实时抓取的情况结构长度不一样)
*/
typedef struct tag_ondisk_pkt_hdr
{
  /* 秒 */
  uint32_t timestamp;

  /* 微秒 1秒=1000*1000微秒 */
  uint32_t microseconds;

  /* 包抓取长度, (caplen <= len) */
  uint32_t caplen;

  /* 包实际长度 */
  uint32_t len;
} ondisk_pkt_hdr;

/**
* @Struct:tag_pcap_ts
* @Author: guojianchuan
* @Date: 2018-02-22
* @Description: pcap文件的时间戳结构体
*/
typedef struct tag_pcap_ts
{
  char filename[FILE_NAME_LEN];
  long timestamp;
} pcap_ts;

/* tcp处理队列 */
//static queue_t *g_q_pcap_tcp[MAX_WORKER_THREAD] = {0};
static queue_root_t *g_q_pcap_tcp[MAX_WORKER_THREAD] = {0};
static pcap_queue_stats st_pcap_stats[8] = {{0}};
static inline DEFINE_Q_GET(queue_get_pcap, queue_pcap_t);
static inline DEFINE_Q_DESTROY(queue_destroy_complete_pcap, queue_pcap_t);
static inline DEFINE_Q_FLUSH(queue_flush_complete_pcap, queue_pcap_t);

static int g_tcp_cache_clean = 0;

static CALLBACK_PKT g_pcap_cb_ip = NULL;
static CALLBACK_PCAP g_pcap_cb_tcp = NULL;
static CALLBACK_FLOW g_pcap_cb_flow = NULL;
static int g_sample_flag = 0;

/**
* @Function:free_queue_pcap
* @Author: guojianchuan
* @Date: 2018-02-22
* @Description: 释放pcap队列
*/
static inline void free_queue_pcap(queue_pcap_t *packet)
{
  if (NULL == packet)
  {
    return;
  }

  free(packet->s);
  free(packet);

  return;
}

static int copy_file(const char *filename_path, const char* filename)
{
  char a_to_file_path[1024] = {0};
  char a_read_buf[1024] = {0};
  const char *ptr = NULL;
  snprintf(a_to_file_path, sizeof(a_to_file_path), "%s/%s", g_pcap_sample_path, filename);
  FILE *fp_from = NULL;
  FILE *fp_to = NULL;
  size_t read_bytes = 0;
  size_t write_bytes = 0;

  fp_from = fopen(filename_path, "r");
  fp_to = fopen(a_to_file_path, "w");

  if (fp_from == NULL || fp_to == NULL)
  {
    fprintf(stderr, "open pcap file failed,%s\n", strerror(errno));
    return -1;
  }

  while ((read_bytes = fread(a_read_buf, 1, 1024, fp_from)))
  {
    if (read_bytes <= 0)
    {
      break;
    }
    else 
    {
      ptr = a_read_buf;
      while ((write_bytes = fwrite(ptr, 1, read_bytes, fp_to)))
      {
        if (write_bytes <= 0)
        {
          break;
        }
        else if (write_bytes == read_bytes)
        {
          break;
        }
        else if (write_bytes < read_bytes)
        {
          ptr += write_bytes;
          read_bytes -= write_bytes;
        } 
      }

      if (write_bytes <= 0)
      {
        break;
      }
    }
  }

  fclose(fp_from);
  fclose(fp_to);

  return 0;
}

static inline uint64_t gw_time_ms(void) 
{
  struct timespec tp;
  clock_gettime(CLOCK_REALTIME, &tp);
  // clock_gettime(CLOCK_REALTIME, &tp);

  return (uint64_t)tp.tv_sec * 1000 + tp.tv_nsec / (1000LL * 1000LL);
  // return (uint64_t)time(NULL) * 1000L;
}

/**
* @Function:parse_pcap_file
* @Author: guojianchuan Fanzihang
* @Date: 2018-02-22 2022-06-20
* @Description: 处理pcap,pcapng文件
*/
static int parse_pcap_file(char *filepath, const char *filename)
{
  int ret = ERR_SUCCESS;

  if (NULL == filepath || NULL == filename)
  {
    return ERR_INVALID_PARAM;
  }

  int worker_idx = 0;
  int sche_table[SCHE_TABLE_SIZE] = {0};

  int i = 0;
  for (i = 0; i < COUNTOF(sche_table); i++)
  {
    sche_table[i] = i % g_conf_pcap_work_thread_num;
  }

  char *data = NULL;
  pcap_t * pcap_handle = NULL;
  const unsigned char *pkt_data = NULL;		// 保存接收到的数据包的起始地址
  struct pcap_pkthdr *pkt_header;
  char errbuf[PCAP_ERRBUF_SIZE];

  pcap_handle = pcap_open_offline(filepath, errbuf);
  if(pcap_handle == NULL)
  {
    fprintf(stderr, "%s\n",errbuf);
    return ERR_SYS_FAILED;
  }

  int retry_cnt = 0;
  uint32_t caplen_real = 0;
  int next_ret = 0;
  int pcap_link_type = 0;
  while (!g_quit_signal)
  {
    next_ret = pcap_next_ex(pcap_handle,&pkt_header,&pkt_data);
    if(next_ret != 1)
    {
      switch (next_ret)
      {
        case 0:
          ret = ERR_SYS_FAILED; //timeout
          break;
        case PCAP_ERROR:
          ret = ERR_SYS_FAILED;
          break;
        case PCAP_ERROR_BREAK://all be readn
          break;
      }
      break;
    }
    pcap_link_type = pcap_datalink(pcap_handle);
    if (ETHERNET != pcap_link_type && LINUX_SLL != pcap_link_type )
    {
      fprintf(stderr, "unsupport linktype[%d][%s]\n", pcap_link_type, filepath);
      pcap_close(pcap_handle);
      return ERR_UNSUPPORT_LINKTYPE;
    }

    caplen_real = pkt_header->caplen;
    data = (char *)malloc(caplen_real);
    if (NULL == data)
    {
      fprintf(stderr, "no memory\n");
      ret = ERR_NOMEM;
      break;
    }

    memcpy(data, pkt_data, caplen_real);

    queue_pcap_t *qe = (queue_pcap_t *)malloc(sizeof(queue_pcap_t));
    if (NULL == qe)
    {
      fprintf(stderr, "no memory\n");
      free(data);
      ret = ERR_NOMEM;
      break;
    }
    memset(qe, 0, sizeof(queue_pcap_t));

    qe->length = caplen_real;
    qe->s = data;
    qe->udata = 0;
    qe->timestamp = pkt_header->ts.tv_sec;
    qe->microseconds = pkt_header->ts.tv_usec;
    memcpy(qe->filename, filename, MIN(COUNTOF(qe->filename) - 1, strlen(filename)));

    pkt_info_t pkti = {0};
    pkti.o_st = 0;
    pkti.buf = (void *)data;
    pkti.size = caplen_real;
    pkti.pkt_size = caplen_real;

    retry_cnt = 0;
    if (g_pcap_cb_flow) {
      char* p = (char*)malloc(caplen_real);
      memcpy(p, data, caplen_real);
      g_pcap_cb_flow(p, caplen_real);
    }
    g_pcap_cb_ip(&pkti, 1, NULL);
    
    if (0 == pkti.o_st)
    {
      free_queue_pcap(qe);
    }
    else if (pkti.o_st > 0)
    {
      //g_current_parsed_total_bytes += pkt_hdr.caplen;
      // if (g_current_parsed_total_bytes >= g_conf_file_source_parser_max_bps && g_conf_file_source_parser_max_bps > 0) {
      //   g_current_parse_ts_ms = gw_time_ms();
      //   if (g_last_parse_ts_ms + 1000 > g_current_parse_ts_ms) {
      //     uint64_t diff = g_last_parse_ts_ms + 1000 - g_current_parse_ts_ms;
      //     usleep(diff*1000);
      //   }
      //   g_current_parsed_total_bytes -= g_conf_file_source_parser_max_bps;
      //   g_last_parse_ts_ms = g_current_parse_ts_ms;
      // }
      worker_idx = sche_table[(pkti.o_st - 1) & 0xff];
      qe->udata = worker_idx;
      do
      {
        /* 写入队列 */
        int ret = queue_put(g_q_pcap_tcp[worker_idx], qe);
        if (QUEUE_OK != ret)
        {
          if (QUEUE_FULL == ret)
          {
            retry_cnt += 1;
            if (retry_cnt < 1000) 
            {
              usleep(1000);
              continue;
            }
          }
          st_pcap_stats[worker_idx].u64_queue_failed++;
          free_queue_pcap(qe);
          fprintf(stderr, "pcap content put to queue failed!len=%d\r\n", caplen_real);
        }
        else
        {
          st_pcap_stats[worker_idx].u64_queue_success++;
        }
        break;
      } while (!g_quit_signal);
    }
    else
    {
      free_queue_pcap(qe);
    }

  }

  pcap_close(pcap_handle);

  return ret;
}
/**
* @Function:get_pcap_dir
* @Author: guojianchuan
* @Date: 2018-02-22
* @Description: 获取pcap目录(默认是在/opt/pcap/task)
*/
static const char *get_pcap_dir(void)
{
  return g_conf_pcapdir_path;
}

/**
* @Function:pcap_worker_tcp
* @Author: guojianchuan
* @Date: 2018-02-22
* @Description: tcp处理线程
*/
static int pcap_worker_tcp(void *arg_ptr)
{
  int8_t q_st = 0;
  queue_pcap_t *q_e = NULL;
  long idx = (long)(long *)arg_ptr;
  
  uint64_t sleep_count = 0;
  const uint64_t sleep_count_max = (g_tcp_worker_timeout * 1000 * 1000) / TCP_WORKER_DATA_WAIT;

  while (!g_quit_signal)
  {
    if (unlikely(g_tcp_cache_clean)) 
    {
      queue_flush_complete_pcap(g_q_pcap_tcp[idx], free_queue_pcap);
      usleep(10);
      continue;
    }
    q_e = NULL;
    if (likely(QUEUE_OK == (q_st = queue_get_pcap(g_q_pcap_tcp[idx], &q_e)) && q_e != NULL))
    {
      sleep_count = 0;
      
      pcap_info_t pi = {0};
      pi.buf = q_e->s;
      pi.filename = q_e->filename;
      pi.timestamp = q_e->timestamp;
      pi.microseconds = q_e->microseconds;
      pi.packet_length = q_e->length;
      pi.packet_length_wire = q_e->length;

      g_pcap_cb_tcp(&pi, 0, q_e->udata + 40); // +40偏移量，防止与其他source冲突，下同
      free_queue_pcap(q_e);
    }
    else
    {
      usleep(TCP_WORKER_DATA_WAIT);
      
      sleep_count++;
      if (sleep_count > sleep_count_max)
      {
        sleep_count = 0;
        g_pcap_cb_tcp(NULL, &sleep_count, idx + 40); // 超时检查
      }
    }
  }

  return 0;
}

static int pcap_filter(const struct dirent *dir_ent)
{
  if (FILE_TYPE == dir_ent->d_type || UNKNOWN_TYPE == dir_ent->d_type)
  {
    if (0 == strncmp(dir_ent->d_name, WAIT_TO_PARSE_TAG, strlen(WAIT_TO_PARSE_TAG)))
    {
      return 1;
    }
  }
  else
  {
    return 0;
  }

  return 0;
}

static int pcap_compare(const struct dirent **a, const struct dirent **b)
{
  int i_ret = 0;
  char a_fullpath_a[PATH_MAX] = {0};
  char a_fullpath_b[PATH_MAX] = {0};

  snprintf(a_fullpath_a, sizeof(a_fullpath_a) - 1, "%s/%s", get_pcap_dir(), (*a)->d_name);
  snprintf(a_fullpath_b, sizeof(a_fullpath_a) - 1, "%s/%s", get_pcap_dir(), (*b)->d_name);

  struct stat buf_a;
  struct stat buf_b;
  i_ret = stat(a_fullpath_a, &buf_a);
  if (i_ret != 0)
  {
    printf("stat %s file failed,(err = %s)\n", a_fullpath_a, strerror(errno));
    return 0;
  }

  i_ret = stat(a_fullpath_b, &buf_b);
  if (i_ret != 0)
  {
    printf("stat %s file failed, (err = %s)\n", a_fullpath_b, strerror(errno));
    return 0;
  }

  if (buf_a.st_mtime < buf_b.st_mtime)
  {
    return 1;
  }
  else if (buf_a.st_mtime > buf_b.st_mtime)
  {
    return -1;
  }

  if (buf_a.st_mtim.tv_nsec < buf_b.st_mtim.tv_nsec) {
    return 1;
  }
  else {
    return -1;
  }
  return 0;
}

/**
* @Function:pcap_worker_ip
* @Author: guojianchuan
* @Date: 2018-02-22
* @Description: ip包处理线程

* @Return:
*/
static int pcap_worker_ip(void *arg_ptr)
{
  (void)arg_ptr;
  int ret = ERR_SUCCESS;
  int idle_cnt = IDLE_DEFAULT;
  int idle_deep_cnt = 0;
  int n = 0;
  //g_last_parse_ts_ms = gw_time_ms();
  const char *pcap_dir = get_pcap_dir();
  while (!g_quit_signal)
  {
    sleep(TASK_CHECK_INTERVAL);
    struct dirent **name_list;
    n = scandir(pcap_dir, &name_list, pcap_filter, pcap_compare);
    if (n <= 0)
    {
      IDLE_F_COUNTER(idle_cnt, idle_deep_cnt, 10000, 100);
      continue;
    }

    IDLE_F_RESET(idle_cnt, idle_deep_cnt);
    while (n--)
    {
      //printf("name list name = %s\n", name_list[n]->d_name);
      char fullpath[FULL_PATH_LEN] = {0};
      snprintf(fullpath, sizeof(fullpath) - 1, "%s/%s", pcap_dir, name_list[n]->d_name);
      if (g_sample_flag == 1)
      {
        /* 拷贝文件 */
        copy_file(fullpath, name_list[n]->d_name);
      }

      /* 解析文件 */
      ret = parse_pcap_file (fullpath, name_list[n]->d_name);
      if (ERR_SUCCESS != ret)
      {
          fprintf (stderr, "parse_pcap_file  failed\n");
          g_u64_fail_file_num++;
      } else {
          g_u64_succ_file_num++;
      }
      
      g_u64_file_num++;

      /* 删除文件 */
      int ret = unlink (fullpath);
      if (0 != ret)
      {
          perror ("rename");
      }
      free(name_list[n]);
    }
    
    free(name_list);
  }

  return 0;
}

/**
* @Function:pcap_probe_main
* @Author: guojianchuan
* @Date: 2018-02-22
* @Description: 主进程
* @Return:
*/
void pcap_probe_main(capd_args_t *pca)
{
  if (NULL == pca)
  {
    return;
  }

  g_pcap_cb_ip = pca->cb_ip;
  g_pcap_cb_tcp = pca->cb_tcp;
  g_pcap_cb_flow = pca->cb_flow;
  //pcap_queue_stats[8] = {0};

  size_t i;
  pthread_t thread_ip;
  pthread_t thread_tcp[MAX_WORKER_THREAD];

  for (i = 0; i < g_conf_pcap_work_thread_num; i++)
  {
    g_q_pcap_tcp[i] = queue_create(g_conf_file_source_queue_max_elements, QUEUE_SP_EQ | QUEUE_SC_DQ);
  }

  /* Create tcp process thread */
  pthread_create(&thread_ip, NULL, (void *(*)(void *))pcap_worker_ip, (void *)NULL);
  for (i = 0; i < g_conf_pcap_work_thread_num; i++)
  {
    pthread_create(&thread_tcp[i], NULL, (void *(*)(void *))pcap_worker_tcp, (void *)i);
  }

  // 解析等待结束。

  pthread_join(thread_ip, NULL);
  for (i = 0; i < g_conf_pcap_work_thread_num; i++)
  {
    pthread_join(thread_tcp[i], NULL);
  }

  for (i = 0; i < g_conf_pcap_work_thread_num; i++)
  {
    queue_destroy_complete_pcap(g_q_pcap_tcp[i], free_queue_pcap);
  }
}

int pcap_probe_info(pcap_probe_info_t *p_pcap_probe_info)
{
  if (NULL == p_pcap_probe_info)
  {
    return ERR_INVALID_PARAM;
  }

  p_pcap_probe_info->u64_file_num = g_u64_file_num;
  p_pcap_probe_info->u64_file_succ_num = g_u64_succ_file_num;
  p_pcap_probe_info->u64_file_fail_num = g_u64_fail_file_num;

  return ERR_SUCCESS;
}

void set_sample_flag(int i_sample_mode)
{
  if (i_sample_mode < 0)
  {
    return ;
  }

  g_sample_flag = i_sample_mode;
}

void get_pcap_log_buf(char *log_buf, size_t log_buf_len)
{
  int i = 0; 
  for (i = 0; i < g_conf_pcap_work_thread_num; ++i)
  {
    if (g_q_pcap_tcp[i])
    {
      snprintf(log_buf + strlen(log_buf), log_buf_len - strlen(log_buf) - 1, "\npcap tcp queue index %d status\n", i);
      snprintf(log_buf + strlen(log_buf), log_buf_len - strlen(log_buf) - 1, "\t %12s %12s %12s\n", "num", "success", "failed");
      snprintf(log_buf + strlen(log_buf), log_buf_len - strlen(log_buf) - 1, "\t %12" PRIu64 " %12" PRIu64 " %12" PRIu64 "\n", (uint64_t)queue_elements(g_q_pcap_tcp[i]), st_pcap_stats[i].u64_queue_success, st_pcap_stats[i].u64_queue_failed);
    }
  }
}

void print_pcap_stats() 
{
  // int i = 0;
  // for (i = 0; i < g_conf_pcap_work_thread_num; ++i)  
  // {
  //   if (g_q_pcap_tcp[i])
  //   {
  //     printf ("\npcap tcp queue index %d status\n", i);
  //     printf ("\t %12s %12s %12s\n", "num", "success", "failed");
  //     printf ("\t %12" PRIu64 " %12" PRIu64 " %12" PRIu64 "\n", (uint64_t)queue_elements(g_q_pcap_tcp[i]), st_pcap_stats[i].u64_queue_success, st_pcap_stats[i].u64_queue_failed);
  //   }
  // }
  char log_buf[LOG_BUF_LEN] = {0};
  get_pcap_log_buf(log_buf, LOG_BUF_LEN);
  printf ("%s", log_buf);
}

void cache_tcp_queue_clean() 
{
  g_tcp_cache_clean = 1;
  int i=0;
  for (; i < MAX_WORKER_THREAD && g_q_pcap_tcp[i]; i++) 
  {
    if (queue_elements(g_q_pcap_tcp[i]))
    {
      i--;
    }
  }
  g_tcp_cache_clean = 0;
  return;
}


int file_status()
{
  int i = 0;
  for (; i < MAX_WORKER_THREAD && g_q_pcap_tcp[i]; i++)
  {
    if (queue_elements(g_q_pcap_tcp[i]))
    {
      return 1;
    }
  }

  return 0;
}