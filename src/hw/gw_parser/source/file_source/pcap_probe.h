#ifndef _PCAP_PROBE_H_
#define _PCAP_PROBE_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include "cap_data.h"

void pcap_probe_main(capd_args_t *pca);

int pcap_probe_info(pcap_probe_info_t *p_pcap_probe_info);

void set_sample_flag(int i_sample_mode);

void print_pcap_stats();

void get_pcap_log_buf(char *log_buf, size_t log_buf_len);

void cache_tcp_queue_clean();

int file_status();

extern volatile int g_quit_signal;
extern int g_conf_pcap_work_thread_num;
extern const char *g_conf_pcapdir_path;
extern const char *g_pcap_sample_path;

extern int g_conf_pcapfile_mode;
extern int g_conf_pcap_reserve;
extern int g_conf_pcap_timestamp;

extern uint64_t g_conf_file_source_parser_max_bps ;
extern uint64_t g_conf_file_source_queue_max_elements;

#define PCAP_FILE_NUMS "pcap file"
#define PCAP_FILE_FAILE "failed"
extern volatile uint64_t g_u64_file_num;      /* 已经解析文件的数量 */
// extern volatile uint64_t g_u64_succ_file_num = 0; /* 解析成功文件的数量 */
extern volatile uint64_t g_u64_fail_file_num; /* 解析失败文件的数量 */
extern uint16_t g_tcp_worker_timeout;

#ifdef __cplusplus
}
#endif

#endif