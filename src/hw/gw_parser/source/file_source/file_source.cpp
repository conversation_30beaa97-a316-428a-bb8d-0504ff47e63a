/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <memory.h>
#include <inttypes.h>

#include "file_source.h"
#include "display_stats_define.h"

#include "utils.h"
#include "gw_logger.h"
#include "gw_common.h"
#include "gw_config.h"
#include "gw_license.h"
#include "tcp_parser.h"
#include "gw_stats.h"

#include "pcap_probe.h"

/**
 * CFileSource implementation
 *
 * PCAP文件数据源（Agent模式）
 */

CFileSource::CFileSource(void)
{
  m_quit_signal = 0;
  m_comm = NULL;
  memset(m_name, 0, sizeof(m_name));
  snprintf(m_name, COUNTOF(m_name) - 1, "CFileSource-%" PRIu64 "", ((uint64_t)this) & 0xffff);

  memset(m_ca, 0, sizeof(m_ca));
  m_tcp_parser = NULL;
}

CFileSource::~CFileSource(void)
{
}

/**
 * 模块初始时调用
 */
void CFileSource::init()
{
  ASSERT(m_comm != NULL);
  ASSERT(m_tcp_parser != NULL);
  m_quit_signal = 0;

  load_conf(NULL);

  if (m_comm->get_verbose())
  {
    GWLOG_INFO(m_comm, "file tcp work thread = %d\n", g_conf_pcap_work_thread_num);
  }
  m_comm->get_gw_stats()->set_stats_callback(STATS_AGENT, print_agent_stats_callback, this);
  m_comm->get_gw_stats()->set_stats(PCAP_FILE_NUMS,NULL,&g_u64_file_num);
  m_comm->get_gw_stats()->set_stats(PCAP_FILE_NUMS,PCAP_FILE_FAILE,&g_u64_fail_file_num);
}

/**
 * 模块结束时调用
 */
void CFileSource::fini()
{
  ASSERT(m_comm != NULL);
}

/**
 * 运行调用
 */
void CFileSource::run()
{
  int err;
  ASSERT(m_comm != NULL);

  if (0 != (err = pthread_create(&m_thread_worker_run, NULL, (void *(*)(void *))worker_run, this)))
  {
    GWLOG_ERROR(m_comm, "file source thread create failed %s\n", strerror(err));
  }
  else
  {
    GWLOG_INFO(m_comm, "file source thread created successfully\n");
  }
}

/**
 * 获取对象名。以-为分隔符，前半部分为类名，后半部分为实例地址尾部分。
 */
const char *CFileSource::get_name(void) const
{
  return m_name;
}

/**
 * 获取版本号。
 */
const char *CFileSource::get_version(void) const
{
  return FILESOURCE_VER;
}

/**
 * 设置全局公共类对象实例。
 * @param CGwCommon *comm
 */
void CFileSource::set_gw_common(CGwCommon *comm)
{
  m_comm = comm;
}

/**
 * 加载配置参数（Json字符串，支持动态）。
 * @param const char *
 */
bool CFileSource::load_conf(const char *json_string)
{
  CGwConfig *pgwc = m_comm->get_gw_config();

  // if (json_string != NULL)
  // {
  //   // TODO 动态加载配置参数
  //   pgwc->load_string(json_string);
  // }

  std::string ss = pgwc->read_conf_string("parser", "pcap_dir");
  if (ss.size() > 0)
  {
    m_pcapdir_path = ss;

    g_conf_pcapdir_path = m_pcapdir_path.c_str();
  }
  else
  {
    g_conf_pcapdir_path = "/opt/pcap/";
  }

  ss = pgwc->read_conf_string("parser", "pcap_sample_dir");
  if (ss.size() > 0)
  {
    m_sample_path = ss;
    g_pcap_sample_path =m_sample_path.c_str();
  }
  else
  {
    g_pcap_sample_path = "/opt/sample/";
  }

  g_conf_pcapfile_mode = pgwc->read_conf_int("parser", "pcapfile_mode", 0);
  g_conf_pcap_work_thread_num = pgwc->read_conf_int("parser", "pcap_tcp_work_thread", 1);
  if (g_conf_pcap_work_thread_num > 8)
  {
    GWLOG_ERROR(m_comm, "pcap_tcp_work_thread must be less than or equal to 8, set to 8\n");
    g_conf_pcap_work_thread_num = 8;
  }
  g_conf_pcap_reserve = pgwc->read_conf_int("parser", "pcap_reserve", 0);
  g_conf_pcap_timestamp = pgwc->read_conf_int("parser", "pcap_timestamp", 0);
  g_tcp_worker_timeout = pgwc->read_conf_int("parser", "tcp_worker_timeout", g_tcp_worker_timeout);

  //g_conf_file_source_parser_max_bps = pgwc->read_conf_int("parser", "file_source_parser_max_bps", g_conf_file_source_parser_max_bps / (1024ULL * 1024ULL)) * (1024ULL * 1024ULL);
  g_conf_file_source_queue_max_elements = pgwc->read_conf_int("parser", "file_source_queue_max_elements", g_conf_file_source_queue_max_elements );

  return true;
}
/**
 * 触发退出信号时处理
 */
void CFileSource::set_quit_signal(void)
{
  m_quit_signal = 1;
  g_quit_signal = 1;
}

/**
 * 等待运行结束
 */
void CFileSource::wait_for_stop(void)
{
  pthread_join(m_thread_worker_run, NULL);
}

/**
 * 设置IP层回调函数（通常为单线程）
 * @param CALLBACK_PKT cb_ip
 * @param void *userdata_ip
 */
int CFileSource::set_ip_callback(CALLBACK_PKT cb_ip, void *userdata_ip)
{
  m_ca->cb_ip = cb_ip;
  m_ca->userdata_ip = userdata_ip;

  return 0;
}

/**
 * 设置TCP层回调函数（一般为多线程）
 * @param CALLBACK_PCAP cb_tcp
 * @param void *userdata_tcp
 */
int CFileSource::set_tcp_callback(CALLBACK_PCAP cb_tcp, void *userdata_tcp)
{
  m_ca->cb_tcp = cb_tcp;
  m_ca->userdata_tcp = userdata_tcp;

  return 0;
}

/**
 * 设置采样参数（仅针对DPDK）
 * @param int mode
 * @param int rate
 * @param CALLBACK_SAMPLE cb_sample
 */
void CFileSource::set_sample_callback(int mode, int rate, CALLBACK_SAMPLE cb_sample)
{
  set_sample_flag(mode);
}

/*
* 设置转发回调函数
*
*/
void CFileSource::set_flow_callback(CALLBACK_FLOW cb_flow) 
{
  m_ca->cb_flow = cb_flow;
}

/**
 * 收集网口信息（针对DPDK）
 * @param eth_info_t *p_st_eth_info
 * @param uint32_t *p_work_port_cnt
 */
int CFileSource::collect_eth_info(eth_info_t *p_st_eth_info, uint32_t *p_work_port_cnt)
{
  // TODO
  return 0;
}

/**
 *  收集解析pcap文件信息(针对pcap) 
 *  @param pcap_probe_info_t *p_st_probe_info
 */
int CFileSource::collect_pcap_probe_info(pcap_probe_info_t *p_st_probe_info)
{
  //TODO
  return pcap_probe_info(p_st_probe_info);
}

int CFileSource::collect_info(gw_parser_stats_t *parser_stats)
{
  return collect_pcap_probe_info(&parser_stats->st_pcap_probe_info);
}


void CFileSource::set_tcp_parser(CTcpParser *tcp_parser)
{
  ASSERT(tcp_parser != NULL);
  m_tcp_parser = tcp_parser;
}

void CFileSource::get_log_buf(char *log_buf, size_t log_buf_len)
{
  get_pcap_log_buf(log_buf, log_buf_len);
}

int CFileSource::source_status() const
{
  return file_status(); 
}

int CFileSource::worker_run(void *arg_ptr)
{
  CFileSource *pThis = (CFileSource *)arg_ptr;
  ASSERT(pThis != NULL);

  GWLOG_DEBUG(pThis->m_comm, "enter %s()\n", __FUNCTION__);
  //#if !_DISABLE_LICENSE_
  if (pThis->m_comm->get_gw_license()->verify_pcap_func() == 1)
  {
    pcap_probe_main(pThis->m_ca);
  }
  else
  {
    GWLOG_ERROR(pThis->m_comm, "pcap func not enable\n");
    /* 程序退出 */
    pThis->m_comm->set_gwparser_exit();
  }
  //#else
  //pcap_probe_main(pThis->m_ca);
  //#endif

  GWLOG_DEBUG(pThis->m_comm, "leave %s()\n", __FUNCTION__);

  return 0;
}

void CFileSource::print_agent_stats_callback(void *args)
{
  CFileSource *pThis = (CFileSource*)args;
  ASSERT(pThis != NULL);

  pThis->print_agent_stats();
}

void CFileSource::print_agent_stats()
{
  print_pcap_stats();
}

void CFileSource::cache_clean() 
{
  cache_tcp_queue_clean();
}