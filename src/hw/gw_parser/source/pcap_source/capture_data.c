#define _GNU_SOURCE
#include <pcap.h>
#include <string.h>
#include <pthread.h>
#include <time.h>
#include <stdint.h>
#include <stdlib.h>
#include <unistd.h>
#include <netinet/in.h>
#include <netinet/ip.h>
#include <linux/if_ether.h>
#include <sched.h>
#include <dirent.h>

#include "capture_data.h"
#include "cap_data.h"
#include "pkt_queue.h"
#include "utils.h"

#define CAP_LEN (9728)
#define SCHE_TABLE_SIZE 256
#define MAX_WORKER_THREAD 64
#define FILTER_STR_LEN 256
#define TCP_WORKER_DATA_WAIT 10 //us

typedef char uint24_t[3];

// const char *g_a_device_name = "";

int g_ip_cache_clean = 0;
int g_tcp_cache_clean = 0;

const char *g_a_device_name[MAX_NETWORK_DEVICE_NUM] = {NULL};
int g_quit_signal = 0;
int g_conf_pcap_work_thread_num = 1;
int g_conf_pcap_direction = 0;
uint32_t g_u32_ip_work_queue_num = 1000000;
uint32_t g_u32_tcp_work_queue_num = 1000000;
uint32_t g_u32_pcap_mtu = 9728;
uint16_t g_tcp_worker_timeout = 0;

static CALLBACK_PKT g_cb_ip = NULL;
static CALLBACK_PCAP g_cb_tcp = NULL;
static queue_root_t *g_p_ip_work_queue = NULL;
static queue_root_t *g_p_tcp_queue[MAX_WORKER_THREAD] = {0};

static pcap_t *g_p_pcap_handle[MAX_NETWORK_DEVICE_NUM] = {NULL};
uint32_t g_network_device_num = 0;
uint32_t cpu_list[256] = {0};
uint32_t cpu_count = 0;

char *bpf_rules = NULL;

typedef struct
{
    pkt_info_t st_pkt_info;
    uint32_t u32_sec;
    uint32_t u32_usec;
} cap_pkt_info_t;

typedef struct {
    void *p_data;          /* pcap数据内容 */
    uint32_t u32_pcap_len; /* pcap数据内容长度 */
    uint32_t u32_seconds;
    uint32_t u32_microseconds;
    int i_work_idx; /* tcp处理线程索引 */
} queue_pcap_t;

typedef struct
{
    volatile uint64_t u64_rx_pkts;
    volatile uint64_t u64_enqueue_pkts;
    volatile uint64_t u64_enqueue_failed_pkts;
} recv_pkt_t;

typedef struct
{
    volatile uint64_t u64_dequeue_pkts;
    volatile uint64_t u64_enqueue_pkts;
    volatile uint64_t u64_enqueue_failed_pkts;
} ip_wrk_t;

typedef struct
{
    volatile uint64_t u64_dequeue_pkts;
} tcp_wrk_t;

typedef struct
{
    recv_pkt_t st_recv_pkt_stats;
    ip_wrk_t st_ip_wrk_stats;
    tcp_wrk_t st_tcp_wrk_stats;
} pcap_stats_t;

pcap_stats_t g_st_pcap_stats;

static inline DEFINE_Q_DESTROY(queue_destroy_complete_pcap, queue_pcap_t);
static inline DEFINE_Q_FLUSH(queue_flush_complete_pcap, queue_pcap_t);

__attribute__((unused)) static void print_pcap_info(const struct pcap_pkthdr *p_pkthdr)
{
    printf("timestamp sec = %lu, usec = %lu\n", p_pkthdr->ts.tv_sec, p_pkthdr->ts.tv_usec);
    printf("capture packet len = %u\n", p_pkthdr->caplen);
    printf("wire packet len = %u\n", p_pkthdr->len);
}

static int get_interface_numa_node(const char *ifname)
{
    char path[128];
    snprintf(path, sizeof(path), "/sys/class/net/%s/device/numa_node", ifname);
    FILE *f = fopen(path, "r");
    if (!f)
    {
        fprintf(stderr, "numa节点不存在\n");
        return -1;
    }
    int node = -1;
    if (fscanf(f, "%d", &node) != 1)
    {
        fclose(f);
        fprintf(stderr, "Failed to read NUMA node\n");
        return -1;
    }
    fclose(f);
    return node;
}

static int get_cpus_from_numa_node_sysfs(int node)
{
    char path[256];
    snprintf(path, sizeof(path), "/sys/devices/system/node/node%d", node);

    DIR *dir = opendir(path);
    if (!dir)
    {
        perror("opendir");
        return -1;
    }

    struct dirent *entry;
    while ((entry = readdir(dir)) != NULL)
    {
        if (strncmp(entry->d_name, "cpu", 3) == 0)
        {
            int cpu;
            if (sscanf(entry->d_name + 3, "%d", &cpu) == 1 && cpu_count < 256)
            {
                cpu_list[cpu_count++] = cpu;
            }
        }
    }

    closedir(dir);
    return cpu_count;
}

static void free_queue_ip_wrk(void *p)
{
    cap_pkt_info_t *p_st_cap_pkt_info = (cap_pkt_info_t *)p;
    if (NULL == p_st_cap_pkt_info)
    {
        return;
    }

    if (p_st_cap_pkt_info->st_pkt_info.buf != NULL) {
        free(p_st_cap_pkt_info->st_pkt_info.buf);
    }

    free(p_st_cap_pkt_info);
}

static void free_queue_pcap(queue_pcap_t *p_st_queue_pcap)
{
    if (p_st_queue_pcap == NULL) {
        return;
    }

    free(p_st_queue_pcap->p_data);
    p_st_queue_pcap->p_data = NULL;

    free(p_st_queue_pcap);
    p_st_queue_pcap = NULL;

    return;
}

int pcap_ip_worker(void *p_arg_ptr)
{
    (void)p_arg_ptr;
    int i_ret = 0;
    int i_work_idx = 0;
    int a_sche_table[SCHE_TABLE_SIZE] = {0};
    int i = 0;
    for (i = 0; i < COUNTOF(a_sche_table); ++i) {
        a_sche_table[i] = i % g_conf_pcap_work_thread_num;
    }

    if (cpu_count >= 4)
    {
        int cpu = cpu_list[cpu_count - 2];
        cpu_set_t mask;
        CPU_SET(cpu, &mask);
        pthread_setaffinity_np(pthread_self(), sizeof(mask), &mask);
    }

    pthread_setname_np(pthread_self(), "p_ip_worker");

    while (!g_quit_signal)
    {
        if (unlikely(g_ip_cache_clean))
        {
            queue_flush_complete(g_p_ip_work_queue, free_queue_ip_wrk);
        }
        cap_pkt_info_t *p_st_cap_pkt_info = NULL;
        if (QUEUE_OK == (i_ret = queue_get(g_p_ip_work_queue, (void **)&p_st_cap_pkt_info)) &&
            p_st_cap_pkt_info != NULL)
        {
            g_st_pcap_stats.st_ip_wrk_stats.u64_dequeue_pkts += 1;

            g_cb_ip(&(p_st_cap_pkt_info->st_pkt_info), 1, NULL);

            if (p_st_cap_pkt_info->st_pkt_info.o_st == 0) {
                goto fail;
            } else if (p_st_cap_pkt_info->st_pkt_info.o_st > 0) {
                /* 数据入队列 */
                i_work_idx = a_sche_table[(p_st_cap_pkt_info->st_pkt_info.o_st - 1) & 0xff];

                queue_pcap_t *p_st_queue_pcap = NULL;
                p_st_queue_pcap = (queue_pcap_t *)malloc(sizeof(queue_pcap_t));
                if (p_st_queue_pcap == NULL) {
                    goto fail;
                }

                p_st_queue_pcap->i_work_idx = i_work_idx;
                p_st_queue_pcap->u32_pcap_len = p_st_cap_pkt_info->st_pkt_info.size;
                p_st_queue_pcap->u32_seconds = p_st_cap_pkt_info->u32_sec;
                p_st_queue_pcap->u32_microseconds = p_st_cap_pkt_info->u32_usec;
                p_st_queue_pcap->p_data = p_st_cap_pkt_info->st_pkt_info.buf;

                i_ret = queue_put(g_p_tcp_queue[i_work_idx], p_st_queue_pcap);
                if (i_ret != QUEUE_OK) {
                    free_queue_pcap(p_st_queue_pcap);
                    p_st_cap_pkt_info->st_pkt_info.buf = NULL;
                    g_st_pcap_stats.st_ip_wrk_stats.u64_enqueue_failed_pkts += 1;
                    goto fail;
                } else {
                    g_st_pcap_stats.st_ip_wrk_stats.u64_enqueue_pkts += 1;
                }

                p_st_cap_pkt_info->st_pkt_info.buf = NULL;
            } else {
                goto fail;
            }
        } else {
            usleep(10);
            continue;
        }

    fail:
        free_queue_ip_wrk(p_st_cap_pkt_info);
    }

    return 0;
}

void process_ip(u_char *p_arg, const struct pcap_pkthdr *p_pkthdr, u_char *p_packet)
{
    int i_ret = 0;
    __sync_fetch_and_add(&g_st_pcap_stats.st_recv_pkt_stats.u64_rx_pkts, 1);
    cap_pkt_info_t *p_st_cap_pkt_info = NULL;
    p_st_cap_pkt_info = (cap_pkt_info_t *)malloc(sizeof(cap_pkt_info_t));
    if (p_st_cap_pkt_info == NULL) {
        goto fail;
    }
    memset(p_st_cap_pkt_info, 0, sizeof(cap_pkt_info_t));

    p_st_cap_pkt_info->st_pkt_info.o_st = 0;
    p_st_cap_pkt_info->st_pkt_info.buf = p_packet;
    p_st_cap_pkt_info->st_pkt_info.size = p_pkthdr->caplen;
    p_st_cap_pkt_info->st_pkt_info.pkt_size = p_pkthdr->len;
    p_st_cap_pkt_info->u32_sec = p_pkthdr->ts.tv_sec;
    p_st_cap_pkt_info->u32_usec = p_pkthdr->ts.tv_usec;
    i_ret = queue_put(g_p_ip_work_queue, p_st_cap_pkt_info);
    if (i_ret != QUEUE_OK) {
        goto fail;
    } else {
        __sync_fetch_and_add(&g_st_pcap_stats.st_recv_pkt_stats.u64_enqueue_pkts, 1);
    }

    return;
fail:
    if (p_st_cap_pkt_info) {
        free(p_st_cap_pkt_info);
    }

    if (p_packet) {
        free(p_packet);
    }

    __sync_fetch_and_add(&g_st_pcap_stats.st_recv_pkt_stats.u64_enqueue_failed_pkts, 1);
    return;
}

// static void no_zero_copy_recv(long index)
// {
//     int i_ret = 0;
//     const u_char *buffer_p = NULL;
//     struct pcap_pkthdr *hdr = NULL;
//     do {
//         i_ret = pcap_next_ex(g_p_pcap_handle[index], &hdr, &buffer_p);
//         if (i_ret == 1) {
//             u_char *data = NULL;
//             data = (u_char *)malloc(hdr->caplen);
//             if (data == NULL) {
//                 fprintf(stderr, "malloc buffer failed\n");
//                 continue;
//             }
//             memcpy(data, buffer_p, hdr->caplen);

//             process_ip(NULL, hdr, data);
//         } else {
//             usleep(10);
//         }
//     } while (!g_quit_signal);

//     return;
// }

// 批量处理数据包的回调函数
static void pcap_dispatch_callback(u_char *user, const struct pcap_pkthdr *hdr, const u_char *packet)
{
    u_char *data = (u_char *)malloc(hdr->caplen);
    if (data == NULL)
    {
        fprintf(stderr, "malloc buffer failed\n");
        return;
    }
    memcpy(data, packet, hdr->caplen);
    process_ip(NULL, hdr, data);
}

static int pcap_data_worker(void *p_arg_ptr)
{
    int i = (long)(long *)p_arg_ptr;
    int i_ret = 0;

    // 满足node中至少有4个核
    if (cpu_count >= 4 && i < cpu_count)
    {
        int cpu = cpu_list[i];
        cpu_set_t mask;
        CPU_SET(cpu, &mask);
        pthread_setaffinity_np(pthread_self(), sizeof(mask), &mask);
    }

    // 网卡未绑定直接返回
    if (g_p_pcap_handle[i] == NULL)
    {
        return 0;
    }

    pthread_setname_np(pthread_self(), "p_rx_worker");

    // 设置更大的缓冲区大小,减少丢包,使用pcap_open_live方式无效,需要pcap_create的方式，但是也受系统参数影响,暂时不需要修改
    // if (pcap_set_buffer_size(g_p_pcap_handle[i], 256 * 1024 * 1024) < 0) {
    //     fprintf(stderr, "Failed to set buffer size for device %s\n", g_a_device_name[i]);
    // }

    // 设置非阻塞模式，避免在pcap_dispatch时阻塞
    if (pcap_setnonblock(g_p_pcap_handle[i], 1, NULL) < 0)
    {
        fprintf(stderr, "Failed to set nonblock mode for device %s\n", g_a_device_name[i]);
    }

    while (!g_quit_signal)
    {
        // 使用pcap_dispatch批量处理数据包，每次处理最多1000个包
        i_ret = pcap_dispatch(g_p_pcap_handle[i], 5000, pcap_dispatch_callback, NULL);

        if (i_ret < 0)
        {
            fprintf(stderr, "Error reading packets: %s\n", pcap_geterr(g_p_pcap_handle[i]));
            usleep(100);  // 发生错误时短暂休眠
        }
        else if (i_ret == 0)
        {
            // 没有数据包时短暂休眠，避免CPU空转
            usleep(10);
        }
    }

    return 0;
}

static int process_tcp_worker(void *p_arg)
{
    long i_work_idx = (long)(long *)p_arg;

    int i_ret = 0;

    queue_pcap_t *p_st_pcap_info = NULL;

    uint64_t sleep_count = 0;
    const uint64_t sleep_count_max = (g_tcp_worker_timeout * 1000 * 1000) / TCP_WORKER_DATA_WAIT;

    while (!g_quit_signal)
    {
        if (unlikely(g_tcp_cache_clean))
        {
            queue_flush_complete_pcap(g_p_tcp_queue[i_work_idx], free_queue_pcap);
            usleep(10);
            continue;
        }
        i_ret = queue_get(g_p_tcp_queue[i_work_idx], (void **)&p_st_pcap_info);
        if (i_ret == QUEUE_OK) {
            __sync_fetch_and_add(&g_st_pcap_stats.st_tcp_wrk_stats.u64_dequeue_pkts, 1);
            if (p_st_pcap_info != NULL)
            {
                sleep_count = 0;

                pcap_info_t st_pcap_info;
                memset(&st_pcap_info, 0, sizeof(pcap_info_t));
                st_pcap_info.buf = p_st_pcap_info->p_data;
                st_pcap_info.timestamp = p_st_pcap_info->u32_seconds;
                st_pcap_info.microseconds = p_st_pcap_info->u32_microseconds;
                st_pcap_info.packet_length = p_st_pcap_info->u32_pcap_len;
                st_pcap_info.packet_length_wire = p_st_pcap_info->u32_pcap_len;

                g_cb_tcp(&st_pcap_info, 0, p_st_pcap_info->i_work_idx + 32); // +32偏移量，防止与其他source冲突，下同

                free_queue_pcap(p_st_pcap_info);
            }
        }
        else
        {
            usleep(TCP_WORKER_DATA_WAIT);

            sleep_count++;
            if (sleep_count > sleep_count_max)
            {
                sleep_count = 0;
                g_cb_tcp(NULL, &sleep_count, i_work_idx + 32); // 超时检查
            }
        }
    }

    return 0;
}

int create_tcp_work_queue()
{
    int i = 0;
    int tcp_work_queue_num = MIN(MAX_WORKER_THREAD, g_conf_pcap_work_thread_num);
    for (i = 0; i < tcp_work_queue_num; ++i) {
        /* 创建单生产者单消费者队列 */
        g_p_tcp_queue[i] = queue_create(g_u32_tcp_work_queue_num, QUEUE_SP_EQ | QUEUE_SC_DQ);
        if (g_p_tcp_queue[i] == NULL) {
            fprintf(stderr, "[pcap main] create tcp work queue failed\n");
            return -1;
        }
    }

    return 0;
}

int create_ip_work_queue()
{
    g_p_ip_work_queue = queue_create(g_u32_ip_work_queue_num, QUEUE_SC_DQ);
    if (NULL == g_p_ip_work_queue)
    {
        fprintf(stderr, "[pcap main] create ip work queue failed\n");
        return -1;
    }

    return 0;
}

int create_tcp_handle_thread(pthread_t *thread_process_tcp)
{
    int i_ret = 0;
    int i = 0;
    int tcp_work_thread_num = MIN(MAX_WORKER_THREAD, g_conf_pcap_work_thread_num);
    for (i = 0; i < tcp_work_thread_num; ++i) {
        i_ret = pthread_create(&thread_process_tcp[i], NULL, (void *(*)(void *))process_tcp_worker, (void *)(long)(i));
        if (i_ret != 0) {
            fprintf(stderr, "[pcap main] create process tcp %d thread failed\n", i);
            break;
        } else {
            printf("[pcap main] create process tcp %d thread successfully\n", i);
        }
        char thread_name[16];
        snprintf(thread_name, sizeof(thread_name), "p_tcp_worker_%d", i);
        pthread_setname_np(thread_process_tcp[i], thread_name);
    }
    return i_ret;
}

int create_ip_hendle_thread(pthread_t *thread_ip_worker)
{
    int i_ret = 0;
    /* 创建处理IP数据线程 */
    i_ret = pthread_create(thread_ip_worker, NULL, (void *(*)(void *))pcap_ip_worker, NULL);
    if (i_ret != 0)
    {
        fprintf(stderr, "[pcap main] create pcap ip worker thread failed\n");
    }
    else
    {
        printf("[pcap main] create pcap ip worker thread successfully\n");
    }

    return i_ret;
}

int create_pcap_thread(pthread_t *thread_pcap_data)
{
    int i_ret = 0;
    int i = 0;

    for (i = 0; i < g_network_device_num; i++) {
        /* 创建捕获数据包线程 */
        i_ret = pthread_create(thread_pcap_data, NULL, (void *(*)(void *))pcap_data_worker, (void *)(long)i);
        if (i_ret != 0)
        {
            fprintf(stderr, "[pcap main] create pcap data thread failed\n");
        } else {
            printf("[pcap main] create pcap data successfully\n");
        }
    }

    return i_ret;
}

void open_pcap(const char *device_name, pcap_t **pcap_handle)
{
    unsigned int mtu = g_u32_pcap_mtu;
    char errbuf[PCAP_ERRBUF_SIZE];
    // 设置捕获缓存区大小（例如设置为 64MB）
    *pcap_handle = pcap_open_live(device_name, mtu, 1, -1, errbuf);

    // mtu 小于1400不再处理
    while (*pcap_handle == NULL && mtu > 0x578) {
        mtu -= 0x100;
        *pcap_handle = pcap_open_live(device_name, mtu, 1, -1, errbuf);
    }
    if (*pcap_handle == NULL) {
        fprintf(stderr, "[pcap main] open device(%s) failed\n", device_name);
        return;
    }
    fprintf(stderr, "[pcap main] open device(%s) success, mtu(%d)\n", device_name, mtu);

    return;
}

int set_pcap_direction(pcap_t *pcap_handle)
{
    int i_ret = 0;
    /* 设置只抓取从网卡进入的流量 */
    // typedef enum {PCAP_D_INOUT = 0, PCAP_D_IN, PCAP_D_OUT} pcap_direction_t;
    if (g_conf_pcap_direction != 0 && g_conf_pcap_direction != 1 && g_conf_pcap_direction != 2)
    {
        fprintf(stderr, "pcap direction param invaild (%d)\n", g_conf_pcap_direction);
    }

    i_ret = pcap_setdirection(pcap_handle, g_conf_pcap_direction);
    if (i_ret < 0)
    {
        fprintf(stderr, "[pcap main] set direction %d failed\n", g_conf_pcap_direction);
    }
    else
    {
        fprintf(stdout, "[pcap main] set direction %d successfully\n", g_conf_pcap_direction);
    }

    return i_ret;
}

int handle_pcap()
{
    uint32_t index = 0;
    int i_ret = 0;

    if (strcmp(g_a_device_name[0], "lo") != 0)
    {
        int node = get_interface_numa_node(g_a_device_name[0]);
        if (node != -1)
        {
            get_cpus_from_numa_node_sysfs(node);
        }
    }
    else
    {
        if (g_network_device_num >= 2)
        {
            int node = get_interface_numa_node(g_a_device_name[1]);
            if (node != -1)
            {
                get_cpus_from_numa_node_sysfs(node);
            }
        }
        else
        {
            // 只有lo口也要进行线程核绑定
            get_cpus_from_numa_node_sysfs(0);
        }
    }

    for (index = 0; index < g_network_device_num; ++index)
    {
        if (index != 0 && strcmp(g_a_device_name[index],"lo") == 0)
            continue;

        open_pcap(g_a_device_name[index], &g_p_pcap_handle[index]);
        if (g_p_pcap_handle[index] == NULL)
        {
            fprintf(stderr, "[handle_pcap] open_pcap device open failed device name: %s\n", g_a_device_name[index]);
            continue;
        }

        i_ret = set_pcap_direction(g_p_pcap_handle[index]);
        if (i_ret < 0) {
            g_p_pcap_handle[index] = NULL;
            fprintf(stderr, "[handle_pcap] set_pcap_direction device open failed device name: %s\n",
                    g_a_device_name[index]);
            continue;
        }

        struct bpf_program program_bpf;
        fprintf(stderr, "[handle_pcap] pcap_compile bpf_rules: %s\n", bpf_rules);
        i_ret = pcap_compile(g_p_pcap_handle[index], &program_bpf, bpf_rules, 1, 0);
        if (i_ret != 0)
        {
            fprintf(stderr, "[handle_pcap] pcap_compile error: %s\n", pcap_geterr(g_p_pcap_handle[index]));
            continue;
        }

        i_ret = pcap_setfilter(g_p_pcap_handle[index], &program_bpf);
        if (i_ret != 0)
        {
            fprintf(stderr, "[handle_pcap] pcap_setfilter error: %s\n", pcap_geterr(g_p_pcap_handle[index]));
            continue;
        }
    }

    free(bpf_rules);
    // 判断是否有网口正常打开，如果有则返回
    for (index = 0; index < g_network_device_num; ++index)
    {
        if (g_p_pcap_handle[index] == NULL)
        {
            continue;
        }
        return i_ret;
    }

    return -1;
}

void pcap_main(capd_args_t *p_pca)
{
    int i = 0;
    int i_ret = 0;
    pthread_t thread_pcap_data;
    pthread_t thread_ip_worker;
    pthread_t thread_process_tcp[MAX_WORKER_THREAD];

    if (p_pca == NULL) {
        fprintf(stderr, "[pcap main] args error\n");
        return;
    }
    memset(&g_st_pcap_stats, 0, sizeof(g_st_pcap_stats));

    g_cb_ip = p_pca->cb_ip;
    g_cb_tcp = p_pca->cb_tcp;

    i_ret = handle_pcap();
    if (i_ret != 0) {
        goto end;
    }

    /* 创建tcp处理队列 */
    i_ret = create_tcp_work_queue();
    if (i_ret < 0) {
        goto end;
    }

    /* 创建ip 处理队列 */
    i_ret = create_ip_work_queue();
    if (i_ret < 0) {
        goto end;
    }

    /* 创建tcp处理线程 */
    i_ret = create_tcp_handle_thread(thread_process_tcp);
    if (i_ret < 0) {
        goto end;
    }

    /* 创建ip处理线程 */
    i_ret = create_ip_hendle_thread(&thread_ip_worker);
    if (i_ret < 0) {
        goto end;
    }

    /* 创建pcap线程 */
    i_ret = create_pcap_thread(&thread_pcap_data);
    if (i_ret < 0) {
        goto end;
    }

    pthread_join(thread_pcap_data, NULL);
    pthread_join(thread_ip_worker, NULL);

    for (i = 0; i < g_conf_pcap_work_thread_num; ++i) {
        pthread_join(thread_process_tcp[i], NULL);
    }

    queue_destroy_complete(g_p_ip_work_queue, free_queue_ip_wrk);

    for (i = 0; i < g_conf_pcap_work_thread_num; ++i) {
        queue_destroy_complete_pcap(g_p_tcp_queue[i], free_queue_pcap);
    }

end:
    close_pcap();

    return;
}

int collect_pcap_stats(nic_info_t *info, uint32_t *network_device_num)
{
    *network_device_num = g_network_device_num;
    int i = 0;
    for (; i < g_network_device_num; i++)
    {
        if (g_p_pcap_handle[i] == NULL)
        {
            continue;
        }
        struct pcap_stat st_pcap_stat = {0, 0, 0};
        int ret = 0;
        if ((ret = pcap_stats(g_p_pcap_handle[i], &st_pcap_stat))) {
            *network_device_num = i;
            fprintf(stderr, "[pcap stats] get pcap stats failed\n");
            return ret;
        }
        strncpy(info[i].ch_device_name, g_a_device_name[i], NIC_DEVICE_NAME_LEN);
        info[i].u64_recv_packets = st_pcap_stat.ps_recv;
        info[i].u64_drop_packets = st_pcap_stat.ps_drop;
    }
    return 0;
}

void get_nic_log_buf(char *log_buf, size_t log_buf_len)
{
    int i_ret = 0;
    int i = 0;

    char show_data[4][512];
    memset(show_data, 0, sizeof(show_data));
    char buf[128];
    char buf1[128];
    for (i = 0; i < g_network_device_num; i++) {
        if (g_p_pcap_handle[i] == NULL) {
            continue;
        }

        snprintf(buf1, COUNTOF(buf1) - 1, "port %s", g_a_device_name[i]);
        snprintf(buf, COUNTOF(buf) - 1, " %20s", buf1);
        strcat(show_data[0], buf);

        struct pcap_stat st_pcap_stat;
        memset(&st_pcap_stat, 0, sizeof(struct pcap_stat));
        i_ret = pcap_stats(g_p_pcap_handle[i], &st_pcap_stat);
        if (i_ret != 0) {
            fprintf(stderr, "[pcap stats] get pcap stats failed\n");
            return;
        }

        snprintf(buf, COUNTOF(buf) - 1, "%20" PRIu32 "", st_pcap_stat.ps_recv);
        strcat(show_data[1], buf);
        snprintf(buf, COUNTOF(buf) - 1, "%20" PRIu32 "", st_pcap_stat.ps_drop);
        strcat(show_data[2], buf);
    }

    sprintf(log_buf + strlen(log_buf), "\nstats:\n");
    sprintf(log_buf + strlen(log_buf), "              %s\n", show_data[0]);
    sprintf(log_buf + strlen(log_buf), " - Pkts in:   %s\n", show_data[1]);
    sprintf(log_buf + strlen(log_buf), " - Pkts drop: %s\n", show_data[2]);
    sprintf(log_buf + strlen(log_buf), "\nthread stats:\n");
    sprintf(log_buf + strlen(log_buf), "                                %16s %16s %16s\n", "RX", "IP Worker",
            "TCP Worker");
    sprintf(log_buf + strlen(log_buf), " - Pkts deqd from upstream:     %16" PRIu64 " %16" PRIu64 " %16" PRIu64 "\n",
            g_st_pcap_stats.st_recv_pkt_stats.u64_rx_pkts, g_st_pcap_stats.st_ip_wrk_stats.u64_dequeue_pkts,
            g_st_pcap_stats.st_tcp_wrk_stats.u64_dequeue_pkts);
    sprintf(log_buf + strlen(log_buf), " - Pkts enqd to workers ring:   %16" PRIu64 " %16" PRIu64 "\n",
            g_st_pcap_stats.st_recv_pkt_stats.u64_enqueue_pkts, g_st_pcap_stats.st_ip_wrk_stats.u64_enqueue_pkts);
    sprintf(log_buf + strlen(log_buf), " - Pkts enq to workers failed:  %16" PRIu64 " %16" PRIu64 "\n",
            g_st_pcap_stats.st_recv_pkt_stats.u64_enqueue_failed_pkts,
            g_st_pcap_stats.st_ip_wrk_stats.u64_enqueue_failed_pkts);
    return;
}

void print_pcap_stats()
{
    char log_buf[LOG_BUF_LEN] = {0};
    get_nic_log_buf(log_buf, LOG_BUF_LEN);

    printf("%s", log_buf);

    return;
}

void close_pcap(void)
{
    uint32_t index = 0;

    for (index = 0; index < g_network_device_num; index++) {
        if (g_p_pcap_handle[index] == NULL) {
            continue;
        }
        pcap_close(g_p_pcap_handle[index]);
        g_p_pcap_handle[index] = NULL;
    }

    return;
}

void cache_ip_queue_clean()
{
    g_ip_cache_clean = 1;
    while (queue_elements(g_p_ip_work_queue));

    g_ip_cache_clean = 0;
    return;
}

void cache_tcp_queue_clean()
{
    int i = 0;
    g_tcp_cache_clean = 1;
    for (i = 0; i < MAX_WORKER_THREAD && g_p_tcp_queue[i]; i++)
    {
        if (queue_elements(g_p_tcp_queue[i]))
        {
            i--;
        }
    }
    g_tcp_cache_clean = 0;
    return;
}

int nic_status()
{
    if (queue_elements(g_p_ip_work_queue)) {
        return 1;
    }

    int i = 0;
    for (i = 0; i < MAX_WORKER_THREAD && g_p_tcp_queue[i]; i++)
    {
        if (queue_elements(g_p_tcp_queue[i]))
        {
            return 1;
        }
    }

    return 0;
}
