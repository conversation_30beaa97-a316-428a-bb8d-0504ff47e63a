#ifndef _CAPTURE_DATA_H_
#define _CAPTURE_DATA_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include "cap_data.h"

#define MAX_NETWORK_DEVICE_NUM 8

extern const char *g_a_device_name[MAX_NETWORK_DEVICE_NUM]; // 0留给lo
extern int g_quit_signal;
extern int g_conf_pcap_work_thread_num;
extern int g_conf_pcap_direction;
extern uint32_t g_u32_ip_work_queue_num;
extern uint32_t g_u32_tcp_work_queue_num;
extern uint32_t g_u32_pcap_mtu;
extern uint32_t g_network_device_num;
extern uint32_t cpu_list[256];
extern uint32_t cpu_count;
extern char *bpf_rules;
extern uint16_t g_tcp_worker_timeout;

int collect_pcap_stats(nic_info_t *info, uint32_t *network_device_num);

void pcap_main(capd_args_t *p_pca);

void get_nic_log_buf(char *log_buf, size_t log_buf_len);

void print_pcap_stats();

void close_pcap(void);

void cache_ip_queue_clean();

void cache_tcp_queue_clean();

int nic_status();

#ifdef __cplusplus
}
#endif

#endif
