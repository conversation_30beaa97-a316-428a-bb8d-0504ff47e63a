/**
 * Project gw-hw
 * <AUTHOR> @version 1.9.0
 */

#include <stdio.h>
#include <string.h>
#include <stdlib.h>

#include "pcap_source.h"
#include "gw_logger.h"
#include "gw_common.h"
#include "gw_stats.h"
#include "display_stats_define.h"
#include "gw_config.h"
#include "gw_stats.h"
#include "display_stats_define.h"
#include "capture_data.h"
#include "utils.h"
#include "cpp_utils.h"
#include "tcp_parser.h"

CPcapSource::CPcapSource(void) : m_quit_signal(0), m_comm(NULL), m_tcp_parser(NULL)
{
    memset(m_name, 0, sizeof(m_name));
    snprintf(m_name, COUNTOF(m_name) - 1, "CPcapSource-%" PRIu64 "", ((uint64_t)this) & 0xffff);
    memset(m_ca, 0, sizeof(m_ca));
}

CPcapSource::~CPcapSource(void)
{
}

/**
 * 模块初始时调用
 */
void CPcapSource::init()
{
    ASSERT(m_comm != NULL);
    m_quit_signal = 0;
    g_quit_signal = 0;

    load_conf(NULL);

    if (m_comm->get_verbose()) {
        for (auto iter = m_v_nic_name.begin(); iter != m_v_nic_name.end(); ++iter) {
            GWLOG_INFO(m_comm, "device name = %s\n", (*iter).c_str());
        }
        GWLOG_INFO(m_comm, "pcap direction = %d\n", g_conf_pcap_direction);
        GWLOG_INFO(m_comm, "pcap ip work queue num = %u\n", g_u32_ip_work_queue_num);
        GWLOG_INFO(m_comm, "pcap tcp work queue num = %u\n", g_u32_tcp_work_queue_num);
    }
    /* 设置nic网口信息 */
    m_comm->get_gw_stats()->set_stats_callback(STATS_CAP, print_cap_stats_callback, this);
}

void CPcapSource::fini()
{
    ASSERT(m_comm != NULL);
}

void CPcapSource::run()
{
    int err = 0;
    ASSERT(m_comm != NULL);

    if (0 != (err = pthread_create(&m_thread_worker_run, NULL, (void *(*)(void *))worker_run, this))) {
        GWLOG_ERROR(m_comm, "pcap source thread create failed %s\n", strerror(err));
    } else {
        GWLOG_INFO(m_comm, "pcap source thread created successfully\n");
    }
}

const char *CPcapSource::get_name(void) const
{
    return m_name;
}

const char *CPcapSource::get_version(void) const
{
    return NICSOURCE_VER;
}

void CPcapSource::set_gw_common(CGwCommon *comm)
{
    ASSERT(comm != NULL);
    m_comm = comm;
}

bool CPcapSource::load_conf(const char *)
{
    ASSERT(m_comm != NULL);
    CGwConfig *pgwc = m_comm->get_gw_config();
    ASSERT(pgwc != NULL);
    std::string nic_name_aggregation = pgwc->read_conf_string("parser", "nic_device_name");

    parser_network_device(nic_name_aggregation);

    g_conf_pcap_work_thread_num = pgwc->read_conf_int("parser", "pcap_tcp_work_thread", 1);
    if (g_conf_pcap_work_thread_num > 8)
    {
        GWLOG_ERROR(m_comm, "pcap_tcp_work_thread must be less than or equal to 8, set to 8\n");
        g_conf_pcap_work_thread_num = 8;
    }
    g_conf_pcap_direction = pgwc->read_conf_int("parser", "nic_pcap_direction", g_conf_pcap_direction);
    g_u32_ip_work_queue_num = pgwc->read_conf_int("parser", "pcap_ip_work_queue_num", g_u32_ip_work_queue_num);
    g_u32_tcp_work_queue_num = pgwc->read_conf_int("parser", "pcap_tcp_work_queue_num", g_u32_tcp_work_queue_num);
    g_u32_pcap_mtu = pgwc->read_conf_int("parser", "pcap_mtu", g_u32_pcap_mtu);
    g_tcp_worker_timeout = pgwc->read_conf_int("parser", "tcp_worker_timeout", g_tcp_worker_timeout);

    std::string pcap_protocol = "";
    std::string port_filter = "";
    std::string port_white = "";
    std::string ip_filter = "";
    std::string ip_white = "";

    std::string bpf_rules_string = "";

    pcap_protocol = pgwc->read_conf_string("parser", "pcap_protocol");
    port_filter = pgwc->read_conf_string("parser", "pcap_port_filter");
    port_white = pgwc->read_conf_string("parser", "pcap_port_white");
    ip_filter = pgwc->read_conf_string("parser", "pcap_ip_filter");
    ip_white = pgwc->read_conf_string("parser", "pcap_ip_white");

    if (pcap_protocol == "")
    {
        bpf_rules = NULL;
        return true;
    }

    bpf_rules_string = pcap_protocol;

    if (port_filter != "")
    {
        bpf_rules_string += " and ";
        bpf_rules_string = bpf_rules_string + "not port " + port_filter;
    }

    if (ip_filter != "")
    {
        bpf_rules_string += " and ";
        bpf_rules_string = bpf_rules_string + "not host " + ip_filter;
    }

    if (port_white != "")
    {
        bpf_rules_string += " and ";
        bpf_rules_string = bpf_rules_string + "port " + port_white;
    }

    if (ip_white != "")
    {
        bpf_rules_string += " and ";
        bpf_rules_string = bpf_rules_string + "host " + ip_white;
    }

    bpf_rules = (char *)malloc(sizeof(char) * bpf_rules_string.size() + 1);
    memset(bpf_rules, 0, sizeof(char) * bpf_rules_string.size() + 1);
    if (bpf_rules == NULL)
    {
        GWLOG_INFO(m_comm, "malloc failed: \n");
        return false;
    }
    strcpy(bpf_rules, bpf_rules_string.c_str());

    GWLOG_INFO(m_comm, "bpf_rules: %s\n", bpf_rules);

    return true;
}

void CPcapSource::set_quit_signal(void)
{
    m_quit_signal = 1;
    g_quit_signal = 1;
}

void CPcapSource::wait_for_stop(void)
{
    pthread_join(m_thread_worker_run, NULL);
}

int CPcapSource::set_ip_callback(CALLBACK_PKT cb_ip, void *userdata_ip)
{
    m_ca->cb_ip = cb_ip;
    m_ca->userdata_ip = userdata_ip;

    return 0;
}

int CPcapSource::set_tcp_callback(CALLBACK_PCAP cb_tcp, void *userdata_tcp)
{
    m_ca->cb_tcp = cb_tcp;
    m_ca->userdata_tcp = userdata_tcp;

    return 0;
}

int CPcapSource::collect_eth_info(eth_info_t *p_st_eth_info, uint32_t *p_work_port_cnt)
{
    return 0;
}

int CPcapSource::collect_pcap_probe_info(pcap_probe_info_t *p_st_probe_info)
{
    return 0;
}

int CPcapSource::collect_info(gw_parser_stats_t *parser_stats)
{
    return collect_pcap_stats(parser_stats->a_st_nic_info, &parser_stats->u32_nic_num);
}

void CPcapSource::set_tcp_parser(CTcpParser *tcp_parser)
{
    ASSERT(tcp_parser != NULL);
    m_tcp_parser = tcp_parser;
}

/**
 * 获取日志输出信息
 */
void CPcapSource::get_log_buf(char *log_buf, size_t log_buf_len)
{
    get_nic_log_buf(log_buf, log_buf_len);
}

int CPcapSource::source_status() const
{
    return nic_status();
}

int CPcapSource::worker_run(void *arg_ptr)
{
    CPcapSource *pThis = (CPcapSource *)arg_ptr;
    ASSERT(pThis != NULL);

    GWLOG_DEBUG(pThis->m_comm, "enter %s()\n", __FUNCTION__);
    pcap_main(pThis->m_ca);
    GWLOG_DEBUG(pThis->m_comm, "leave %s()\n", __FUNCTION__);

    return 0;
}

void CPcapSource::print_cap_stats_callback(void *arg_ptr)
{
    CPcapSource *pThis = (CPcapSource *)arg_ptr;
    ASSERT(pThis != NULL);
    pThis->print_cap_stats();
}

void CPcapSource::print_cap_stats(void) const
{
    print_pcap_stats();
}

void CPcapSource::parser_network_device(std::string nic_name_aggregation)
{
    m_v_nic_name = split_string(nic_name_aggregation, ",");

    if (m_v_nic_name.empty())
    {
        return;
    }

    g_network_device_num = MIN(m_v_nic_name.size(), COUNTOF(g_a_device_name) - 1) + 1;

    g_a_device_name[0] = "lo";
    for (size_t i = 1; i < g_network_device_num; i++) {
        g_a_device_name[i] = m_v_nic_name[i - 1].c_str();
    }
}

void CPcapSource::cache_clean()
{
    cache_ip_queue_clean();
    cache_tcp_queue_clean();
}
