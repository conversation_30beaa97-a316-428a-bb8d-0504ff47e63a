/*
 * Oracle高级数据类型处理器实现
 * 处理Oracle的复杂数据类型：XMLType, Object Types, Collections等
 * <AUTHOR> @date 2025
 */

#include "oracle_advanced_types.h"
#include "oracle_parser_common.h"
#include "gw_logger.h"
#include <string.h>
#include <stdlib.h>
#include <ctype.h>

// 简化的日志宏定义
#define ADV_LOG_DEBUG(fmt, ...) printf("[ADV-DEBUG] " fmt "\n", ##__VA_ARGS__)
#define ADV_LOG_INFO(fmt, ...)  printf("[ADV-INFO] " fmt "\n", ##__VA_ARGS__)
#define ADV_LOG_WARN(fmt, ...)  printf("[ADV-WARN] " fmt "\n", ##__VA_ARGS__)
#define ADV_LOG_ERROR(fmt, ...) printf("[ADV-ERROR] " fmt "\n", ##__VA_ARGS__)

// 大端序读取函数
static uint16_t read_uint16_be(const char *data) {
    return (((uint8_t)data[0]) << 8) | ((uint8_t)data[1]);
}

static uint32_t read_uint32_be(const char *data) {
    return (((uint8_t)data[0]) << 24) | (((uint8_t)data[1]) << 16) | 
           (((uint8_t)data[2]) << 8) | ((uint8_t)data[3]);
}

OracleAdvancedTypes::OracleAdvancedTypes()
    : m_debug_enabled(false)
    , m_max_type_cache_size(1000)
    , m_max_object_depth(10)
    , m_total_xmltypes_parsed(0)
    , m_total_objects_parsed(0)
    , m_total_collections_parsed(0)
    , m_total_refs_parsed(0)
    , m_parse_errors(0)
{
    ADV_LOG_INFO("Oracle Advanced Types Handler initialized");
}

OracleAdvancedTypes::~OracleAdvancedTypes()
{
    // 清理类型缓存
    for (auto& pair : m_type_cache) {
        free_type_descriptor(pair.second);
    }
    m_type_cache.clear();
    
    ADV_LOG_INFO("Oracle Advanced Types Handler destroyed, parsed %lu XMLTypes, %lu objects",
                 (unsigned long)m_total_xmltypes_parsed, (unsigned long)m_total_objects_parsed);
}

int OracleAdvancedTypes::parse_xmltype(const char *data, size_t data_len,
                                     oracle_status_t *status, oracle_parsed_data_t *result)
{
    if (!data || data_len < 8 || !status || !result) {
        return ADVANCED_TYPE_PARSE_ERROR;
    }

    oracle_xmltype_t *xmltype = (oracle_xmltype_t*)calloc(1, sizeof(oracle_xmltype_t));
    if (!xmltype) {
        return ADVANCED_TYPE_PARSE_ERROR;
    }

    size_t offset = 0;
    
    // 解析XMLType头部
    int ret = parse_xmltype_header(data, data_len, &offset, xmltype);
    if (ret != ADVANCED_TYPE_PARSE_SUCCESS) {
        free_xmltype(xmltype);
        return ret;
    }

    // 解析XML内容
    ret = parse_xml_content(data, data_len, &offset, xmltype);
    if (ret != ADVANCED_TYPE_PARSE_SUCCESS) {
        free_xmltype(xmltype);
        return ret;
    }

    // 设置结果
    result->xmltype = xmltype;
    result->data_type = ORACLE_DATA_TYPE_XMLTYPE;
    
    m_total_xmltypes_parsed++;
    
    ADV_LOG_DEBUG("XMLType parsed, size: %u bytes", xmltype->xml_size);
    
    return ADVANCED_TYPE_PARSE_SUCCESS;
}

int OracleAdvancedTypes::parse_object_type(const char *data, size_t data_len,
                                         oracle_status_t *status, oracle_parsed_data_t *result)
{
    if (!data || data_len < 8 || !status || !result) {
        return ADVANCED_TYPE_PARSE_ERROR;
    }

    oracle_object_t *object = (oracle_object_t*)calloc(1, sizeof(oracle_object_t));
    if (!object) {
        return ADVANCED_TYPE_PARSE_ERROR;
    }

    size_t offset = 0;
    
    // 解析对象头部
    int ret = parse_object_header(data, data_len, &offset, object);
    if (ret != ADVANCED_TYPE_PARSE_SUCCESS) {
        free_object(object);
        return ret;
    }

    // 解析对象属性
    ret = parse_object_attributes(data, data_len, &offset, object);
    if (ret != ADVANCED_TYPE_PARSE_SUCCESS) {
        free_object(object);
        return ret;
    }

    // 设置结果
    result->object = object;
    result->data_type = ORACLE_DATA_TYPE_OBJECT;
    
    m_total_objects_parsed++;
    
    ADV_LOG_DEBUG("Object parsed: type=%s, attributes=%u", 
                 object->type_name, object->attribute_count);
    
    return ADVANCED_TYPE_PARSE_SUCCESS;
}

int OracleAdvancedTypes::parse_collection_type(const char *data, size_t data_len,
                                             oracle_status_t *status, oracle_parsed_data_t *result)
{
    if (!data || data_len < 8 || !status || !result) {
        return ADVANCED_TYPE_PARSE_ERROR;
    }

    oracle_collection_t *collection = (oracle_collection_t*)calloc(1, sizeof(oracle_collection_t));
    if (!collection) {
        return ADVANCED_TYPE_PARSE_ERROR;
    }

    size_t offset = 0;
    
    // 解析集合头部
    int ret = parse_collection_header(data, data_len, &offset, collection);
    if (ret != ADVANCED_TYPE_PARSE_SUCCESS) {
        free_collection(collection);
        return ret;
    }

    // 解析集合元素
    ret = parse_collection_elements(data, data_len, &offset, collection);
    if (ret != ADVANCED_TYPE_PARSE_SUCCESS) {
        free_collection(collection);
        return ret;
    }

    // 设置结果
    result->collection = collection;
    result->data_type = ORACLE_DATA_TYPE_COLLECTION;
    
    m_total_collections_parsed++;
    
    ADV_LOG_DEBUG("Collection parsed: type=%u, elements=%u", 
                 collection->collection_type, collection->element_count);
    
    return ADVANCED_TYPE_PARSE_SUCCESS;
}

// 私有方法的简化实现
int OracleAdvancedTypes::parse_xmltype_header(const char *data, size_t data_len, size_t *offset, oracle_xmltype_t *xmltype)
{
    if (*offset + 8 > data_len) {
        return ADVANCED_TYPE_PARSE_NEED_MORE_DATA;
    }

    xmltype->xml_id = read_uint32_be(data + *offset);
    *offset += 4;
    
    xmltype->xml_size = read_uint32_be(data + *offset);
    *offset += 4;
    
    return ADVANCED_TYPE_PARSE_SUCCESS;
}

int OracleAdvancedTypes::parse_xml_content(const char *data, size_t data_len, size_t *offset, oracle_xmltype_t *xmltype)
{
    if (*offset + xmltype->xml_size > data_len) {
        return ADVANCED_TYPE_PARSE_NEED_MORE_DATA;
    }

    // 简化实现：只记录XML大小，不解析实际内容
    *offset += xmltype->xml_size;
    
    return ADVANCED_TYPE_PARSE_SUCCESS;
}

int OracleAdvancedTypes::parse_object_header(const char *data, size_t data_len, size_t *offset, oracle_object_t *object)
{
    if (*offset + 8 > data_len) {
        return ADVANCED_TYPE_PARSE_NEED_MORE_DATA;
    }

    object->object_id = read_uint32_be(data + *offset);
    *offset += 4;
    
    uint16_t type_name_len = read_uint16_be(data + *offset);
    *offset += 2;
    
    if (*offset + type_name_len > data_len || type_name_len >= sizeof(object->type_name)) {
        return ADVANCED_TYPE_PARSE_ERROR;
    }
    
    memcpy(object->type_name, data + *offset, type_name_len);
    object->type_name[type_name_len] = '\0';
    *offset += type_name_len;
    
    object->attribute_count = read_uint16_be(data + *offset);
    *offset += 2;
    
    return ADVANCED_TYPE_PARSE_SUCCESS;
}

int OracleAdvancedTypes::parse_object_attributes(const char *data, size_t data_len, size_t *offset, oracle_object_t *object)
{
    // 简化实现：只跳过属性数据
    for (uint16_t i = 0; i < object->attribute_count; i++) {
        if (*offset + 4 > data_len) {
            return ADVANCED_TYPE_PARSE_NEED_MORE_DATA;
        }
        
        uint16_t attr_name_len = read_uint16_be(data + *offset);
        *offset += 2;
        
        uint16_t attr_value_len = read_uint16_be(data + *offset);
        *offset += 2;
        
        if (*offset + attr_name_len + attr_value_len > data_len) {
            return ADVANCED_TYPE_PARSE_NEED_MORE_DATA;
        }
        
        *offset += attr_name_len + attr_value_len;
    }
    
    return ADVANCED_TYPE_PARSE_SUCCESS;
}

int OracleAdvancedTypes::parse_collection_header(const char *data, size_t data_len, size_t *offset, oracle_collection_t *collection)
{
    if (*offset + 8 > data_len) {
        return ADVANCED_TYPE_PARSE_NEED_MORE_DATA;
    }

    collection->collection_id = read_uint32_be(data + *offset);
    *offset += 4;
    
    collection->collection_type = data[*offset];
    *offset += 1;
    
    collection->element_count = read_uint16_be(data + *offset);
    *offset += 2;
    
    collection->element_type = data[*offset];
    *offset += 1;
    
    return ADVANCED_TYPE_PARSE_SUCCESS;
}

int OracleAdvancedTypes::parse_collection_elements(const char *data, size_t data_len, size_t *offset, oracle_collection_t *collection)
{
    // 简化实现：只跳过元素数据
    for (uint16_t i = 0; i < collection->element_count; i++) {
        if (*offset + 2 > data_len) {
            return ADVANCED_TYPE_PARSE_NEED_MORE_DATA;
        }
        
        uint16_t element_len = read_uint16_be(data + *offset);
        *offset += 2;
        
        if (*offset + element_len > data_len) {
            return ADVANCED_TYPE_PARSE_NEED_MORE_DATA;
        }
        
        *offset += element_len;
    }
    
    return ADVANCED_TYPE_PARSE_SUCCESS;
}

void OracleAdvancedTypes::free_xmltype(oracle_xmltype_t *xmltype)
{
    if (xmltype) {
        free(xmltype);
    }
}

void OracleAdvancedTypes::free_object(oracle_object_t *object)
{
    if (object) {
        free(object);
    }
}

void OracleAdvancedTypes::free_collection(oracle_collection_t *collection)
{
    if (collection) {
        free(collection);
    }
}

void OracleAdvancedTypes::free_type_descriptor(oracle_type_descriptor_t *type_desc)
{
    if (type_desc) {
        free(type_desc);
    }
}
