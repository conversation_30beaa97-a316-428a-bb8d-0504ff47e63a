---
description: 
globs: 
alwaysApply: true
---
---
name: Code Style
globs:
  - "**/*.c"
  - "**/*.cpp"
---
- Use consistent naming conventions following existing HTTP and FTP plugins within the framework.
- Prefer camelCase for variable names and PascalCase for functions and classes, reflecting the style in current implementations.
- Maintain a consistent indentation style with spaces/tabs as defined by existing code.
- Use block formatting for all `if`, `for`, and `while` statements even if they contain a single line.
- For all `if` statements, abnormal situations are always handled first, followed by normal situation judgments