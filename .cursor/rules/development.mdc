---
description: 
globs: 
alwaysApply: true
---
---
name: Iterative Development
globs:
  - "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/postgre_parser/*.h"
  - "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/postgre_parser/*.c"
  - "/Users/<USER>/gw-hw/src/hw/gw_parser/parser/postgre_parser/*.cpp"
---
- 逐步实现功能，使用小提交以确保更改易于跟踪和调试。 
- 用清晰的提交信息记录每个变更，解释更新的目的和影响。

## PostgreSQL协议解析插件架构概览

### 核心组件文件结构
- **主解析器类**: [postgre_parser.cpp](mdc:src/hw/gw_parser/parser/postgre_parser/postgre_parser.cpp) - 插件主类，实现CParser接口
- **协议解析核心**: [postgre_parser_deal_parser.cpp](mdc:src/hw/gw_parser/parser/postgre_parser/postgre_parser_deal_parser.cpp) - PostgreSQL协议解析的核心逻辑(3766行)
- **消息解析器**: [postgre_parser_parser_msg.cpp](mdc:src/hw/gw_parser/parser/postgre_parser/postgre_parser_parser_msg.cpp) - 处理各种PostgreSQL消息类型(838行)
- **探测逻辑**: [postgre_parser_deal_probe.cpp](mdc:src/hw/gw_parser/parser/postgre_parser/postgre_parser_deal_probe.cpp) - 流量识别和协议探测
- **二进制处理**: [postgre_parser_binary_handlers.cpp](mdc:src/hw/gw_parser/parser/postgre_parser/postgre_parser_binary_handlers.cpp) - 二进制数据类型转换处理
- **公共定义**: [postgre_parser_common.h](mdc:src/hw/gw_parser/parser/postgre_parser/postgre_parser_common.h) - 数据结构、常量、枚举定义(317行)
- **主头文件**: [postgre_parser.h](mdc:src/hw/gw_parser/parser/postgre_parser/postgre_parser.h) - 主类定义和接口声明(322行)

### 核心功能特性
1. **协议支持**: 完整支持PostgreSQL Protocol Version 3.0
2. **消息类型**: 支持所有标准前端/后端消息类型(Query、Parse、Bind、Execute等)
3. **认证机制**: 支持多种认证方式(明文、MD5、SASL、GSS等)
4. **扩展查询**: 完整支持Prepared Statement和Portal机制
5. **数据类型**: 支持30+种PostgreSQL数据类型的解析和转换
6. **二进制格式**: 支持二进制数据格式的解析和文本转换
7. **COPY操作**: 支持COPY IN/OUT/BOTH操作的解析
8. **SSL/TLS**: 支持SSL连接请求的识别
9. **事务跟踪**: 跟踪事务状态和连接状态

### 数据结构设计
- **postgre_stream_t**: 连接流状态管理，包含客户端/服务端半流
- **postgre_parsed_data_t**: 解析后的数据容器(SQL、结果集、错误信息等)
- **result_set_t**: 结果集数据结构，支持多行多列数据
- **column_def_t**: 列定义信息(名称、类型、OID等)
- **prepared_statement_t**: Prepared Statement跟踪
- **portal_t**: Portal对象跟踪
- **pg_type_handler_t**: 二进制类型处理器链表

### 配置参数
| 参数名称 | 说明 | 默认值 | 用途 |
|---------|------|-------|------|
| postgre_upload_results_num | 结果集上传最大行数 | 100 | 防止大结果集占用过多内存 |
| postgre_upload_field_max_size | 单字段最大字节数 | 8192 | 限制大字段(BLOB/TEXT)的内存占用 |
| postgre_upload_queue_num | 上传队列最大数量 | 1000 | 控制上传队列大小 |
| postgre_upload_queue_memory_size | 上传队列内存大小 | 100MB | 内存使用限制 |
| postgre_upload_thread_num | 上传线程数 | 1 | 并发上传控制 |
| postgre_use_binary_extended | 二进制扩展处理 | 1 | 启用高级二进制数据处理 |
| pcap_timestamp | PCAP时间戳使用 | 1 | 时间戳来源选择 |

### 支持的PostgreSQL数据类型
- **基础类型**: BOOL, CHAR, INT2/4/8, FLOAT4/8, TEXT, VARCHAR
- **时间类型**: DATE, TIME, TIMESTAMP, TIMESTAMPTZ, INTERVAL
- **数值类型**: NUMERIC(任意精度)
- **二进制类型**: BYTEA
- **JSON类型**: JSON, JSONB
- **空间类型**: POINT, LSEG, PATH, BOX, POLYGON
- **网络地址类型**: INET, CIDR, MACADDR, MACADDR8
- **数组类型**: 支持所有基础类型的数组版本（如INT4[], TEXT[], BOOL[]等）
- **枚举类型**: 用户定义的枚举类型（ENUM）
- **其他类型**: UUID, XML, OID, NAME

### 性能优化特性
- **内存管理**: 智能的结果集大小限制，防止内存溢出
- **字段截断**: 大字段自动截断，保持系统稳定性
- **队列控制**: 可配置的上传队列，平衡性能和资源使用
- **二进制优化**: 高效的二进制数据转换处理
- **连接复用**: 支持连接池和长连接的状态跟踪

### 事件上传格式
插件将解析的数据封装为JSON格式上传，包含：
- **请求信息**: 用户名、数据库名、SQL语句、操作类型
- **响应信息**: 状态码、执行时间、影响行数、结果集数据
- **元数据**: 时间戳、服务器版本、应用名称
- **网络信息**: IP地址、端口、流量来源
- **唯一标识**: 用于事件关联和去重

### 开发注意事项
- 遵循现有的camelCase变量命名和PascalCase函数命名规范
- 异常情况优先处理，正常情况判断在后
- 保持与现有HTTP/FTP插件的一致性
- 优先考虑代码重构为模块化函数，而非直接修改逻辑
- 注重内存使用效率，特别是解析过程中的资源分配和释放 