# PostgreSQL流复制协议调整后实现方案

## 1. 方案概述

基于用户要求，重新设计PostgreSQL流复制协议解析方案，重点关注：
- 在`parse_by_message_signature`函数中集成流复制命令处理
- 采用旁路处理方式，避免创建新的解析节点
- 专注于从XLogData消息中提取DML操作内容
- 将解析结果存储到现有的`postgre_parsed_data_t`结构中

## 2. 问题1解决方案：parse_replication_command函数集成

### 2.1 当前状态分析

**现有集成点**：
```cpp
// 在parse_by_message_signature函数中已有检查
if (pgs->replication_conn) {
    return parse_replication_message(pgs, data, len, is_client, pcon, p_session);
}
```

**问题**：
1. 函数签名不匹配：调用时传入`is_client`，但定义需要`direction`
2. 缺少对流复制命令的具体处理逻辑
3. 未将流复制命令添加到`sql_list`中

### 2.2 具体实现方案

**步骤1：修正函数签名**
```cpp
// 修改parse_replication_message函数签名以匹配调用
int CPostgreParser::parse_replication_message(postgre_stream_t *pgs,
                                             const char *data, int len,
                                             bool is_client, const struct conn *pcon, CSession *p_session) {
    // 转换is_client为direction
    int direction = is_client ? PGSQL_REQUEST : PGSQL_RESPONSE;
    
    // 优先检查流复制命令（客户端请求）
    if (is_client && is_replication_command(data, len)) {
        return handle_replication_command_with_sql_recording(pgs, data, len, pcon, p_session);
    }
    
    // 继续现有的流复制消息处理逻辑
    // ...
}
```

**步骤2：实现流复制命令处理并记录SQL**
```cpp
int CPostgreParser::handle_replication_command_with_sql_recording(postgre_stream_t *pgs,
                                                                 const char *data, int len,
                                                                 const struct conn *pcon, CSession *p_session) {
    // 解析流复制命令并更新状态
    int parse_result = parse_replication_command(pgs, data, len);
    
    // 创建新的解析数据节点用于记录SQL命令
    postgre_half_stream_t *p_new_stream = new postgre_half_stream_t();
    if (!p_new_stream) {
        return PARSER_STATUS_DROP_DATA;
    }
    
    memset(p_new_stream, 0, sizeof(postgre_half_stream_t));
    p_new_stream->next = NULL;
    p_new_stream->prev = NULL;
    
    // 提取SQL命令文本
    if (data[0] == POSTGRE_MSG_QUERY && len >= 5) {
        uint32_t msg_len = GET_MSG_LEN(data + 1);
        const char *query = data + 5;
        size_t query_len = msg_len - 4;
        
        // 添加SQL语句到链表
        add_sql_statement(&p_new_stream->data, query, query_len);
        
        // 设置时间戳
        p_new_stream->data.start_time = get_time_ts_ms(p_session, m_conf_pcap_timestamp);
        p_new_stream->data.pcap_ts = p_new_stream->data.start_time;
    }
    
    // 插入到请求链表
    insert_into_parser_header(pgs, PGSQL_REQUEST, p_new_stream);
    
    return parse_result;
}
```

## 3. 问题2解决方案：WAL数据解析调整

### 3.1 解析目标明确

**重点关注**：
- 从XLogData消息中提取logical-replication消息
- 解析DML操作内容（INSERT、UPDATE、DELETE）
- 将DML操作转换为结果集格式存储到`rs_list`中

**不关注**：
- 完整的WAL记录解析
- 物理WAL数据的详细内容
- 复杂的WAL解码器实现

### 3.2 数据结构调整

**现有结构保持不变**：
```cpp
typedef struct wal_data_record {
    uint64_t start_lsn;
    uint64_t end_lsn;
    uint64_t timestamp;
    size_t data_len;
    uint32_t record_count;
    struct wal_data_record *next;
} wal_data_record_t;
```

**新增DML操作结果集结构**：
```cpp
// 用于存储解析出的DML操作
typedef struct dml_operation_result {
    char *operation_type;    // "INSERT", "UPDATE", "DELETE"
    char *table_name;        // 表名
    char *schema_name;       // 模式名
    result_set_t *result_set; // 操作数据的结果集表示
} dml_operation_result_t;
```

### 3.3 实现方案

**步骤1：增强XLogData消息解析**
```cpp
int CPostgreParser::parse_xlog_data_msg(postgre_stream_t *pgs,
                                       const char *data, int len) {
    // 现有的LSN和时间戳解析保持不变
    const char *pos = data + 1;
    uint64_t start_lsn = read_uint64_be(pos); pos += 8;
    uint64_t end_lsn = read_uint64_be(pos); pos += 8;
    uint64_t timestamp = read_uint64_be(pos); pos += 8;
    size_t wal_data_len = len - 25;
    
    // 新增：检查是否包含logical-replication消息
    if (pgs->replication_conn->conn_type == PGSQL_CONN_LOGICAL_REP) {
        // 解析logical-replication消息并提取DML操作
        extract_dml_operations_from_wal(pgs, pos, wal_data_len);
    }
    
    // 保持现有的简化WAL记录处理
    wal_data_record_t *wal_record = new wal_data_record_t();
    if (wal_record) {
        wal_record->start_lsn = start_lsn;
        wal_record->end_lsn = end_lsn;
        wal_record->timestamp = timestamp;
        wal_record->data_len = wal_data_len;
        wal_record->record_count = estimate_wal_record_count(pos, wal_data_len);
        wal_record->next = NULL;
        
        add_wal_record_to_stream(pgs, wal_record);
    }
    
    return PARSER_STATUS_FINISH;
}
```

**步骤2：实现DML操作提取**
```cpp
void CPostgreParser::extract_dml_operations_from_wal(postgre_stream_t *pgs,
                                                    const char *wal_data, size_t data_len) {
    const char *pos = wal_data;
    size_t remaining = data_len;
    
    while (remaining > 0) {
        if (remaining < 1) break;
        
        char msg_type = *pos;
        pos += 1;
        remaining -= 1;
        
        switch (msg_type) {
            case LOGICAL_MSG_INSERT:
                extract_insert_operation(pgs, pos, remaining);
                break;
            case LOGICAL_MSG_UPDATE:
                extract_update_operation(pgs, pos, remaining);
                break;
            case LOGICAL_MSG_DELETE:
                extract_delete_operation(pgs, pos, remaining);
                break;
            case LOGICAL_MSG_RELATION:
                // 缓存关系定义，用于后续DML操作解析
                cache_relation_definition(pgs, pos, remaining);
                break;
            default:
                // 跳过其他消息类型
                break;
        }
        
        // 简化处理：假设每个消息占用固定长度或跳过到下一个消息
        // 实际实现需要根据消息格式正确计算长度
        break;
    }
}
```

**步骤3：DML操作转换为结果集**
```cpp
void CPostgreParser::extract_insert_operation(postgre_stream_t *pgs,
                                             const char *data, size_t len) {
    if (len < 5) return;
    
    uint32_t relation_id = read_uint32_be(data);
    
    // 查找关系定义
    logical_relation_t *relation = find_logical_relation(pgs, relation_id);
    if (!relation) return;
    
    // 创建结果集表示INSERT操作
    result_set_t *rs = new result_set_t();
    if (!rs) return;
    
    memset(rs, 0, sizeof(result_set_t));
    
    // 创建列定义：operation_type, table_name, affected_rows
    create_dml_operation_columns(rs);
    
    // 创建行数据
    postgre_row_data_t *row = new postgre_row_data_t();
    if (row) {
        row->row = new b_string_t*[3];
        row->field_count = 3;
        
        // operation_type
        row->row[0] = new b_string_t();
        row->row[0]->s = strdup("INSERT");
        row->row[0]->len = 6;
        
        // table_name
        row->row[1] = new b_string_t();
        row->row[1]->s = strdup(relation->relation_name);
        row->row[1]->len = strlen(relation->relation_name);
        
        // affected_rows
        row->row[2] = new b_string_t();
        row->row[2]->s = strdup("1");
        row->row[2]->len = 1;
        
        // 添加到结果集
        rs->rows = new postgre_row_data_t*[1];
        rs->rows[0] = row;
        rs->row_cnt = 1;
        rs->row_capacity = 1;
    }
    
    // 添加到当前解析数据的结果集链表
    add_result_set_to_current_stream(pgs, rs);
}
```

## 4. 集成策略

### 4.1 与现有架构的兼容性

**保持不变的部分**：
- 现有的`postgre_parsed_data_t`结构
- 现有的事件上传机制
- 现有的内存管理策略

**新增的部分**：
- 流复制命令的SQL记录
- DML操作的结果集表示
- 简化的WAL数据处理

### 4.2 事件上传集成

**流复制命令事件**：
- 通过`sql_list`链表记录流复制SQL命令
- 使用现有的`handle_access_event`函数上传

**DML操作事件**：
- 通过`rs_list`链表记录DML操作结果集
- 与普通查询结果使用相同的上传机制

## 5. 实施步骤

### 5.1 第一阶段：修正函数集成
1. 修正`parse_replication_message`函数签名
2. 实现流复制命令的SQL记录功能
3. 测试流复制命令识别和记录

### 5.2 第二阶段：实现DML操作提取
1. 实现`extract_dml_operations_from_wal`函数
2. 实现基础的INSERT/UPDATE/DELETE操作提取
3. 将DML操作转换为结果集格式

### 5.3 第三阶段：集成测试和优化
1. 端到端测试流复制协议解析
2. 验证事件上传的正确性
3. 性能优化和内存管理

## 6. 预期效果

**功能完整性**：
- 流复制命令能够被正确识别和记录
- DML操作能够从WAL数据中提取并转换为结果集
- 与现有事件上传机制无缝集成

**业务价值**：
- 提供PostgreSQL流复制的完整审计能力
- 支持逻辑复制的数据变更监控
- 增强数据库操作的可追溯性

**技术优势**：
- 最小化对现有架构的影响
- 复用现有的数据结构和处理机制
- 保持高性能和低内存占用

这个调整后的方案更加注重实用性和业务价值，避免了复杂的WAL解码实现，同时确保了与现有系统的良好集成。
