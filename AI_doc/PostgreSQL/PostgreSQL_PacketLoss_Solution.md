# PostgreSQL协议解析器丢包异常处理技术方案

## 1. 问题背景

### 1.1 问题描述
在large_query部分数据丢失场景下，PostgreSQL协议解析器出现请求响应匹配失败问题，导致统计数据异常：

```
stats postgre                total      success      failure
postgre session:                   0
postgre parser:                    4            3            0
postgre request parser:            2            1            0
postgre reponse parser:            2            2            0
postgre match:                     3            0            3  ← 全部匹配失败
postgre request match:             1            0            1  ← 请求匹配失败
postgre reponse match:             2            0            2  ← 响应匹配失败
```

### 1.2 根本原因分析
1. **Query消息不完整**：large_query丢包导致SQL内容部分丢失
2. **匹配条件过严**：要求请求必须有完整SQL语句才能匹配
3. **Error响应无法匹配**：不完整的Query请求无法与Error响应配对

## 2. 丢包场景全面分析

### 2.1 请求端丢包影响
- **Simple Query丢包**：SQL语句截断，`sql_list`为空或`sql_count`为0
- **Extended Query丢包**：Parse/Bind/Execute消息参数丢失
- **认证消息丢包**：Startup/Password消息参数缺失

### 2.2 响应端丢包影响
- **Error Response丢包**：错误消息字段解析失败
- **数据响应丢包**：Row Description/Data Row信息缺失
- **状态响应丢包**：Ready for Query/Parameter Status丢失

### 2.3 双向丢包综合影响
- **TCP序列号错位**：影响请求响应匹配算法
- **消息边界混乱**：无法正确识别消息完整性
- **协议状态错乱**：状态机跟踪失效

## 3. 统一解决方案设计

### 3.1 核心设计原则
- **分层处理**：检测→标记→匹配→上传的清晰流程
- **容错平衡**：严格模式（无丢包）+ 容错模式（有丢包）
- **向后兼容**：不影响现有正常场景的处理逻辑

### 3.2 架构设计
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   TCP层丢包检测   │───▶│  协议层丢包标记   │───▶│   匹配层容错处理  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ half_stream信息  │    │ 消息完整性标记   │    │  事件上传保证    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 3.3 多级匹配策略
1. **精确匹配**：TCP序列号完全匹配（现有逻辑）
2. **容错匹配**：考虑丢包偏移的序列号匹配
3. **时间窗口匹配**：在时间窗口内的请求响应配对
4. **类型匹配**：基于消息类型的启发式匹配

## 4. 技术实现方案

### 4.1 数据结构扩展

#### 丢包上下文结构
```cpp
typedef struct packet_loss_context {
    bool has_request_loss;      // 请求端丢包
    bool has_response_loss;     // 响应端丢包
    int lost_bytes;             // 丢失字节数
    char loss_reason[64];       // 丢包原因描述
} packet_loss_context_t;
```

#### 解析数据扩展
```cpp
typedef struct postgre_parsed_data {
    // ... 现有字段 ...
    packet_loss_context_t loss_ctx;  // 丢包上下文
    bool is_incomplete;               // 数据是否不完整
} postgre_parsed_data_t;
```

### 4.2 核心函数接口

#### 丢包检测函数
```cpp
bool detect_packet_loss_from_tcp(half_stream *hlf, packet_loss_context_t *ctx);
bool detect_message_incompleteness(const char *data, int len, char msg_type, packet_loss_context_t *ctx);
void mark_incomplete_message(postgre_parsed_data_t *data, const char *reason);
```

#### 容错匹配函数
```cpp
bool execute_match_logic_with_packet_loss(postgre_stream_t *p_stream, 
                                         const struct conn *pcon,
                                         postgre_half_stream_t *p_req, 
                                         postgre_half_stream_t *p_resp);
```

## 5. 实施计划

### 5.1 第一阶段：基础设施建设（高优先级）
**目标**：建立丢包检测和标记机制
- [ ] 扩展数据结构（`packet_loss_context_t`）
- [ ] 实现TCP层丢包检测函数
- [ ] 实现协议层消息完整性检查
- [ ] 添加丢包标记机制

### 5.2 第二阶段：匹配逻辑优化（最高优先级）
**目标**：直接解决当前统计异常问题
- [ ] 修改`execute_match_logic`函数，添加丢包容错处理
- [ ] 实现多级匹配策略
- [ ] 放宽匹配条件（允许无SQL的请求与Error响应匹配）
- [ ] 添加匹配置信度评估

### 5.3 第三阶段：解析逻辑增强（中优先级）
**目标**：提升整体容错能力
- [ ] 增强Query消息解析（为不完整消息创建占位符）
- [ ] 增强Error响应解析（提高错误字段解析容错性）
- [ ] 优化消息边界检测
- [ ] 添加消息类型特定的丢包处理

### 5.4 第四阶段：完善功能（中优先级）
**目标**：确保功能完整性和可监控性
- [ ] 完善事件上传逻辑（确保丢包场景下的事件上传）
- [ ] 添加丢包相关统计指标
- [ ] 实现性能监控和调试功能
- [ ] 完善文档和测试用例

## 6. 预期效果

### 6.1 统计数据改善
```
修改前：
postgre match:                     3            0            3
postgre request match:             1            0            1  
postgre reponse match:             2            0            2

修改后预期：
postgre match:                     3            2            1
postgre request match:             1            1            0
postgre reponse match:             2            1            1
```

### 6.2 功能改善
- ✅ large_query丢包场景下正常匹配
- ✅ Error响应与不完整请求正确配对
- ✅ 访问事件正常上传
- ✅ 统计数据准确反映实际情况

## 7. 风险控制

### 7.1 技术风险
- **误匹配风险**：通过匹配置信度评估控制
- **性能影响**：优先精确匹配，按需启用容错模式
- **内存开销**：及时清理丢包上下文信息

### 7.2 实施风险
- **回归风险**：保留原有逻辑作为回滚选项
- **测试风险**：制定完整的测试验证策略
- **维护风险**：完善文档和代码注释

## 8. 验证策略

### 8.1 测试场景
- [ ] large_query完整场景测试
- [ ] large_query部分丢包场景测试
- [ ] Error响应丢包场景测试
- [ ] 双向丢包场景测试
- [ ] 性能回归测试

### 8.2 成功标准
- 匹配成功率提升至80%以上
- 正常场景性能无明显下降
- 事件上传完整性达到95%以上

## 9. 关键代码修改点

### 9.1 匹配条件放宽（核心修改）

#### 当前代码（问题所在）
```cpp
// 位置：postgre_parser_deal_parser.cpp:5839
if (!p_req->data.sql_list || p_req->data.sql_count == 0) {
    free_postgre_matched_data(p_stream, p_req, p_resp);
    return false;  // 严格要求SQL存在，导致丢包场景匹配失败
}
```

#### 修改后代码
```cpp
// 检查是否存在丢包情况
bool has_packet_loss = detect_packet_loss_in_request_response(p_req, p_resp);

if (!has_packet_loss && (!p_req->data.sql_list || p_req->data.sql_count == 0)) {
    // 无丢包情况下保持严格要求
    free_postgre_matched_data(p_stream, p_req, p_resp);
    return false;
}

if (has_packet_loss && (!p_req->data.sql_list || p_req->data.sql_count == 0)) {
    // 丢包场景下允许匹配，创建占位符SQL
    add_sql_statement(&p_req->data, "[INCOMPLETE_QUERY_DUE_TO_PACKET_LOSS]",
                     strlen("[INCOMPLETE_QUERY_DUE_TO_PACKET_LOSS]"));
    GWLOG_DEBUG(m_comm, "[PostgreSQL][PacketLoss] Allowing match despite missing SQL\n");
}
```

### 9.2 Query消息解析增强

#### 当前代码（问题所在）
```cpp
// 位置：postgre_parser_deal_parser.cpp:1388-1390
uint32_t msg_len = GET_MSG_LEN(data + offset + 1);
if (msg_len < POSTGRE_MSG_HEADER_LEN || msg_len + 1 > (uint32_t)(len - offset)) {
    return PARSER_STATUS_CONTINUE;  // 等待更多数据，但丢包时永远等不到
}
```

#### 修改后代码
```cpp
uint32_t msg_len = GET_MSG_LEN(data + offset + 1);
if (msg_len < POSTGRE_MSG_HEADER_LEN || msg_len + 1 > (uint32_t)(len - offset)) {
    // 检查是否为large_query丢包场景
    if (msg_len > (uint32_t)(len - offset) && msg_len > 8192) {
        char msg_type = data[offset];
        if (msg_type == POSTGRE_MSG_QUERY) {
            // 为不完整的Query创建占位符
            postgre_half_stream_t *phs = pgs->p_postgre_client;
            if (phs) {
                add_sql_statement(&phs->data, "[LARGE_QUERY_INCOMPLETE_DUE_TO_PACKET_LOSS]",
                                strlen("[LARGE_QUERY_INCOMPLETE_DUE_TO_PACKET_LOSS]"));
                return PARSER_STATUS_FINISH;  // 允许后续匹配
            }
        }
    }
    return PARSER_STATUS_CONTINUE;
}
```

### 9.3 丢包检测函数实现

```cpp
bool CPostgreParser::detect_packet_loss_in_request_response(postgre_half_stream_t *p_req,
                                                           postgre_half_stream_t *p_resp) {
    if (!p_req || !p_resp) return false;

    bool req_has_loss = false;
    bool resp_has_loss = false;

    // 1. 检查请求SQL缺失但有TCP数据传输
    if ((!p_req->data.sql_list || p_req->data.sql_count == 0) &&
        p_req->data.tcp_seq != p_req->data.tcp_ack) {
        req_has_loss = true;
    }

    // 2. 检查SQL中是否有丢包标记
    if (p_req->data.sql_list && p_req->data.sql_list->sql.s) {
        if (strstr(p_req->data.sql_list->sql.s, "INCOMPLETE_DUE_TO_PACKET_LOSS")) {
            req_has_loss = true;
        }
    }

    // 3. 检查响应是否有错误（通常表明请求有问题）
    if (p_resp->data.err_msg && p_resp->data.err_code > 0) {
        resp_has_loss = true;
    }

    // 4. 检查TCP序列号异常跳跃
    uint32_t seq_diff = p_req->data.tcp_ack - p_req->data.tcp_seq;
    if (seq_diff > 8192) {  // 大于8KB可能表明大量数据丢失
        req_has_loss = true;
    }

    return req_has_loss || resp_has_loss;
}
```

## 10. 测试验证计划

### 10.1 单元测试用例

#### 测试用例1：large_query完整场景
```cpp
// 测试数据：完整的大SQL查询
char large_query[] = "SELECT * FROM large_table WHERE condition = 'very_long_condition_string'...";
// 预期：正常解析，正常匹配，统计正确
```

#### 测试用例2：large_query丢包场景
```cpp
// 测试数据：截断的大SQL查询
char truncated_query[] = "SELECT * FROM large_table WHERE condi";  // 后续数据丢失
// 预期：创建占位符SQL，允许与Error响应匹配
```

#### 测试用例3：Error响应匹配
```cpp
// 测试数据：Error响应消息
char error_response[] = "EERROR\x00\x00\x00\x2ASEVERITY\x00ERROR\x00CODE\x00...";
// 预期：与不完整请求正确匹配
```

### 10.2 集成测试场景

#### 场景1：完整流程测试
1. 发送large_query请求（部分丢包）
2. 接收Error响应
3. 验证匹配成功
4. 验证事件上传
5. 验证统计数据正确

#### 场景2：性能回归测试
1. 正常场景性能基准测试
2. 丢包场景性能测试
3. 对比性能差异
4. 确保性能影响在可接受范围内

## 11. 监控和调试

### 11.1 新增统计指标
```cpp
typedef struct stats_postgre_packet_loss {
    volatile uint64_t cnt_packet_loss_detected;    // 检测到丢包的次数
    volatile uint64_t cnt_tolerant_match_success;  // 容错匹配成功次数
    volatile uint64_t cnt_tolerant_match_failed;   // 容错匹配失败次数
    volatile uint64_t cnt_incomplete_sql_created;  // 创建占位符SQL次数
} stats_postgre_packet_loss_t;
```

### 11.2 调试日志
```cpp
// 丢包检测日志
GWLOG_DEBUG(m_comm, "[PostgreSQL][PacketLoss] Request packet loss detected: seq=%u, ack=%u, sql_count=%d\n");

// 容错匹配日志
GWLOG_DEBUG(m_comm, "[PostgreSQL][PacketLoss] Allowing match despite missing SQL due to packet loss\n");

// 占位符创建日志
GWLOG_DEBUG(m_comm, "[PostgreSQL][PacketLoss] Created incomplete query marker for large query\n");
```

## 12. 部署和回滚策略

### 12.1 灰度部署
1. **阶段1**：在测试环境验证所有功能
2. **阶段2**：在少量生产节点部署，监控效果
3. **阶段3**：逐步扩展到所有节点

### 12.2 回滚准备
- 保留原有匹配逻辑代码
- 通过配置开关控制新功能启用
- 准备快速回滚脚本

### 12.3 配置参数
```ini
[parser]
# 是否启用丢包容错处理
postgre_enable_packet_loss_tolerance = 1

# 容错匹配的最大TCP序列号差异阈值
postgre_max_seq_diff_for_tolerance = 8192

# 是否为不完整查询创建占位符SQL
postgre_create_placeholder_sql = 1
```

---

**文档版本**：v1.0
**创建时间**：2025-01-06
**最后更新**：2025-01-06
**状态**：方案确认，待实施
**下一步**：开始第一阶段实施（基础设施建设）
