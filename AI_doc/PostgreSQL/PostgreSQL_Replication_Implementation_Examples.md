# PostgreSQL流复制协议实现示例

## 1. 头文件定义示例

### 1.1 postgre_parser_replication.h
```cpp
#ifndef POSTGRE_PARSER_REPLICATION_H
#define POSTGRE_PARSER_REPLICATION_H

#include "postgre_parser_common.h"

// 流复制连接类型
enum pgsql_connection_type {
    PGSQL_CONN_NORMAL = 0,
    PGSQL_CONN_PHYSICAL_REP = 1,
    PGSQL_CONN_LOGICAL_REP = 2
};

// 流复制状态
enum replication_state {
    REP_STATE_NONE = 0,
    REP_STATE_IDENTIFIED = 1,
    REP_STATE_SLOT_CREATED = 2,
    REP_STATE_STREAMING = 3,
    REP_STATE_COMPLETED = 4,
    REP_STATE_ERROR = 5
};

// 流复制消息类型
#define POSTGRE_MSG_XLOG_DATA              'w'
#define POSTGRE_MSG_PRIMARY_KEEPALIVE      'k'
#define POSTGRE_MSG_STANDBY_STATUS_UPDATE  'r'
#define POSTGRE_MSG_HOT_STANDBY_FEEDBACK   'h'

// 逻辑复制消息类型
#define LOGICAL_MSG_BEGIN                  'B'
#define LOGICAL_MSG_COMMIT                 'C'
#define LOGICAL_MSG_ORIGIN                 'O'
#define LOGICAL_MSG_RELATION               'R'
#define LOGICAL_MSG_TYPE                   'Y'
#define LOGICAL_MSG_INSERT                 'I'
#define LOGICAL_MSG_UPDATE                 'U'
#define LOGICAL_MSG_DELETE                 'D'
#define LOGICAL_MSG_TRUNCATE               'T'
#define LOGICAL_MSG_MESSAGE                'M'

// 流复制连接信息
typedef struct replication_connection {
    pgsql_connection_type conn_type;
    replication_state state;
    char *slot_name;
    char *publication_names;
    uint64_t start_lsn;
    uint64_t current_lsn;
    uint64_t flush_lsn;
    uint64_t apply_lsn;
    uint32_t timeline_id;
    char *system_id;
    bool is_logical;
    bool binary_mode;
    int proto_version;
    uint64_t start_time;
    uint64_t last_activity;
    uint32_t error_count;
} replication_connection_t;

// WAL数据记录
typedef struct wal_data_record {
    uint64_t start_lsn;
    uint64_t end_lsn;
    uint64_t timestamp;
    size_t data_len;
    uint32_t record_count;
    struct wal_data_record *next;
} wal_data_record_t;

// 逻辑复制事件
typedef struct logical_replication_event {
    char event_type;
    uint64_t lsn;
    uint64_t timestamp;
    uint32_t xid;
    uint32_t relation_id;
    char *relation_name;
    char *schema_name;
    void *event_data;
    size_t data_len;
    struct logical_replication_event *next;
} logical_replication_event_t;

// 函数声明
pgsql_connection_type detect_replication_connection(const char *data, int len);
bool init_replication_context(postgre_stream_t *pgs, pgsql_connection_type conn_type);
int parse_replication_message(postgre_stream_t *pgs, const char *data, int len, int direction);
void cleanup_replication_context(postgre_stream_t *pgs);

#endif // POSTGRE_PARSER_REPLICATION_H
```

## 2. 连接检测实现示例

### 2.1 流复制连接检测
```cpp
// 在postgre_parser_deal_probe.cpp中添加
pgsql_connection_type CPostgreParser::detect_replication_connection(const char *data, int len) {
    if (len < POSTGRE_STARTUP_MIN_LEN) {
        return PGSQL_CONN_NORMAL;
    }
    
    uint32_t msg_len = GET_MSG_LEN(data);
    if (msg_len > (uint32_t)len) {
        return PGSQL_CONN_NORMAL;
    }
    
    // 检查协议版本
    uint32_t protocol_version = GET_MSG_LEN(data + 4);
    if (protocol_version != POSTGRE_PROTOCOL_VERSION_3) {
        return PGSQL_CONN_NORMAL;
    }
    
    // 解析启动参数
    const char *param_start = data + 8;
    const char *param_end = data + msg_len;
    
    while (param_start < param_end) {
        const char *param_name = param_start;
        size_t param_name_len = strnlen(param_name, param_end - param_name);
        if (param_name_len == 0 || param_name_len == (size_t)(param_end - param_name)) {
            break;
        }
        param_start += param_name_len + 1;
        
        const char *param_value = param_start;
        size_t param_value_len = strnlen(param_value, param_end - param_value);
        if (param_value_len == (size_t)(param_end - param_value)) {
            break;
        }
        
        // 检查replication参数
        if (strncmp(param_name, "replication", param_name_len) == 0 && 
            param_name_len == 11) {
            
            if (strncmp(param_value, "true", param_value_len) == 0 ||
                strncmp(param_value, "on", param_value_len) == 0 ||
                strncmp(param_value, "yes", param_value_len) == 0 ||
                strncmp(param_value, "1", param_value_len) == 0) {
                return PGSQL_CONN_PHYSICAL_REP;
            } else if (strncmp(param_value, "database", param_value_len) == 0) {
                return PGSQL_CONN_LOGICAL_REP;
            }
        }
        
        param_start += param_value_len + 1;
    }
    
    return PGSQL_CONN_NORMAL;
}

// 在probe函数中集成检测
bool CPostgreParser::probe(CSessionMgt *psm, const app_stream *a_app, 
                          const struct conn *pcon, CSession *p_session) {
    // ... 现有代码 ...
    
    // 检测连接类型
    if (a_app->dir == PGSQL_REQUEST && is_valid_startup_msg(data, len)) {
        pgsql_connection_type conn_type = detect_replication_connection(data, len);
        
        // 根据连接类型设置解析器状态
        if (conn_type != PGSQL_CONN_NORMAL) {
            // 标记为流复制连接
            // 这里可以设置特殊标志或状态
        }
    }
    
    // ... 现有代码 ...
}
```

## 3. 消息解析实现示例

### 3.1 物理流复制消息解析
```cpp
// 在postgre_parser_replication_physical.cpp中实现
int CPostgreParser::parse_physical_replication_msg(postgre_stream_t *pgs, 
                                                   const char *data, int len, 
                                                   int direction) {
    if (!pgs->replication_conn || 
        pgs->replication_conn->conn_type != PGSQL_CONN_PHYSICAL_REP) {
        return PARSER_STATUS_DROP_DATA;
    }
    
    if (direction == PGSQL_RESPONSE) {
        // 处理服务器响应消息
        if (len < 1) return PARSER_STATUS_CONTINUE;
        
        char msg_type = data[0];
        switch (msg_type) {
            case POSTGRE_MSG_XLOG_DATA:
                return parse_xlog_data_msg(pgs, data, len);
            case POSTGRE_MSG_PRIMARY_KEEPALIVE:
                return parse_primary_keepalive_msg(pgs, data, len);
            default:
                // 其他消息类型
                break;
        }
    } else if (direction == PGSQL_REQUEST) {
        // 处理客户端请求消息
        char msg_type = data[0];
        switch (msg_type) {
            case POSTGRE_MSG_STANDBY_STATUS_UPDATE:
                return parse_standby_status_msg(pgs, data, len);
            case POSTGRE_MSG_HOT_STANDBY_FEEDBACK:
                return parse_hot_standby_feedback_msg(pgs, data, len);
            default:
                break;
        }
    }
    
    return PARSER_STATUS_FINISH;
}

int CPostgreParser::parse_xlog_data_msg(postgre_stream_t *pgs, 
                                        const char *data, int len) {
    if (len < 25) { // 最小XLogData消息长度
        return PARSER_STATUS_CONTINUE;
    }
    
    // 解析XLogData消息
    const char *pos = data + 1; // 跳过消息类型
    
    uint64_t start_lsn = read_uint64_be(pos);
    pos += 8;
    
    uint64_t end_lsn = read_uint64_be(pos);
    pos += 8;
    
    uint64_t timestamp = read_uint64_be(pos);
    pos += 8;
    
    size_t wal_data_len = len - 25;
    
    // 更新复制连接状态
    pgs->replication_conn->current_lsn = end_lsn;
    pgs->replication_conn->last_activity = get_current_time_ms();
    
    // 创建WAL数据记录
    wal_data_record_t *wal_record = new wal_data_record_t();
    if (wal_record) {
        wal_record->start_lsn = start_lsn;
        wal_record->end_lsn = end_lsn;
        wal_record->timestamp = timestamp;
        wal_record->data_len = wal_data_len;
        wal_record->record_count = estimate_wal_record_count(pos, wal_data_len);
        wal_record->next = NULL;
        
        // 添加到服务器半流的数据中
        add_wal_record_to_stream(pgs, wal_record);
    }
    
    return PARSER_STATUS_FINISH;
}
```

### 3.2 逻辑流复制消息解析
```cpp
// 在postgre_parser_replication_logical.cpp中实现
int CPostgreParser::parse_logical_replication_msg(postgre_stream_t *pgs, 
                                                  const char *data, int len) {
    if (!pgs->replication_conn || 
        pgs->replication_conn->conn_type != PGSQL_CONN_LOGICAL_REP) {
        return PARSER_STATUS_DROP_DATA;
    }
    
    if (len < 1) return PARSER_STATUS_CONTINUE;
    
    char msg_type = data[0];
    switch (msg_type) {
        case LOGICAL_MSG_BEGIN:
            return parse_logical_begin_msg(pgs, data, len);
        case LOGICAL_MSG_COMMIT:
            return parse_logical_commit_msg(pgs, data, len);
        case LOGICAL_MSG_RELATION:
            return parse_logical_relation_msg(pgs, data, len);
        case LOGICAL_MSG_INSERT:
            return parse_logical_insert_msg(pgs, data, len);
        case LOGICAL_MSG_UPDATE:
            return parse_logical_update_msg(pgs, data, len);
        case LOGICAL_MSG_DELETE:
            return parse_logical_delete_msg(pgs, data, len);
        default:
            // 未知消息类型，记录但继续处理
            GWLOG_WARN(m_comm, "[PostgreSQL][Logical] Unknown logical message type: %c\n", msg_type);
            break;
    }
    
    return PARSER_STATUS_FINISH;
}

int CPostgreParser::parse_logical_begin_msg(postgre_stream_t *pgs, 
                                           const char *data, int len) {
    if (len < 21) { // Begin消息最小长度
        return PARSER_STATUS_CONTINUE;
    }
    
    const char *pos = data + 1; // 跳过消息类型
    
    uint64_t final_lsn = read_uint64_be(pos);
    pos += 8;
    
    uint64_t timestamp = read_uint64_be(pos);
    pos += 8;
    
    uint32_t xid = read_uint32_be(pos);
    
    // 创建逻辑复制事件
    logical_replication_event_t *event = new logical_replication_event_t();
    if (event) {
        event->event_type = LOGICAL_MSG_BEGIN;
        event->lsn = final_lsn;
        event->timestamp = timestamp;
        event->xid = xid;
        event->relation_id = 0;
        event->relation_name = NULL;
        event->schema_name = NULL;
        event->event_data = NULL;
        event->data_len = 0;
        event->next = NULL;
        
        // 添加到事务上下文
        add_logical_event_to_transaction(pgs, event);
    }
    
    return PARSER_STATUS_FINISH;
}
```

## 4. 状态管理实现示例

### 4.1 流复制状态管理
```cpp
// 在postgre_parser_replication.cpp中实现
bool CPostgreParser::init_replication_context(postgre_stream_t *pgs, 
                                              pgsql_connection_type conn_type) {
    if (!pgs) return false;
    
    // 创建流复制连接上下文
    replication_connection_t *repl_conn = new replication_connection_t();
    if (!repl_conn) {
        return false;
    }
    
    memset(repl_conn, 0, sizeof(replication_connection_t));
    repl_conn->conn_type = conn_type;
    repl_conn->state = REP_STATE_NONE;
    repl_conn->start_time = get_current_time_ms();
    repl_conn->proto_version = 1; // 默认版本
    
    pgs->replication_conn = repl_conn;
    return true;
}

void CPostgreParser::update_replication_state(postgre_stream_t *pgs, 
                                              replication_state new_state) {
    if (!pgs || !pgs->replication_conn) {
        return;
    }
    
    replication_state old_state = pgs->replication_conn->state;
    pgs->replication_conn->state = new_state;
    
    // 记录状态转换
    GWLOG_DEBUG(m_comm, "[PostgreSQL][Replication] State transition: %d -> %d\n", 
                old_state, new_state);
    
    // 根据状态执行相应操作
    switch (new_state) {
        case REP_STATE_STREAMING:
            // 开始流复制，初始化相关资源
            init_streaming_resources(pgs);
            break;
        case REP_STATE_COMPLETED:
            // 流复制完成，清理资源
            cleanup_streaming_resources(pgs);
            break;
        case REP_STATE_ERROR:
            // 错误状态，记录错误信息
            handle_replication_error(pgs);
            break;
        default:
            break;
    }
}

void CPostgreParser::cleanup_replication_context(postgre_stream_t *pgs) {
    if (!pgs || !pgs->replication_conn) {
        return;
    }
    
    replication_connection_t *repl_conn = pgs->replication_conn;
    
    // 清理字符串资源
    if (repl_conn->slot_name) {
        free(repl_conn->slot_name);
    }
    if (repl_conn->publication_names) {
        free(repl_conn->publication_names);
    }
    if (repl_conn->system_id) {
        free(repl_conn->system_id);
    }
    
    // 清理事件缓存
    cleanup_logical_event_cache(pgs);
    
    // 清理WAL记录
    cleanup_wal_records(pgs);
    
    delete repl_conn;
    pgs->replication_conn = NULL;
}
```

## 5. 集成到现有解析器

### 5.1 在parse_query_msg中集成
```cpp
// 在postgre_parser_deal_parser.cpp的parse_query_msg函数中添加
int CPostgreParser::parse_query_msg(postgre_stream_t *pgs, const char *data, int len) {
    // 检查是否为流复制连接
    if (pgs->replication_conn) {
        // 处理流复制相关消息
        int repl_result = parse_replication_message(pgs, data, len, PGSQL_REQUEST);
        if (repl_result != PARSER_STATUS_CONTINUE) {
            return repl_result;
        }
    }
    
    // ... 现有的SQL消息处理代码 ...
}
```

### 5.2 在parse_response_msg中集成
```cpp
// 在postgre_parser_deal_parser.cpp的parse_response_msg函数中添加
int CPostgreParser::parse_response_msg(postgre_stream_t *pgs, const char *data, int len, 
                                      const struct conn *pcon, CSession *p_session) {
    // 检查是否为流复制连接
    if (pgs->replication_conn) {
        // 处理流复制相关消息
        int repl_result = parse_replication_message(pgs, data, len, PGSQL_RESPONSE);
        if (repl_result != PARSER_STATUS_CONTINUE) {
            return repl_result;
        }
    }
    
    // ... 现有的响应消息处理代码 ...
}
```
