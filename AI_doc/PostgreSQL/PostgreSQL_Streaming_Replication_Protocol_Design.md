# PostgreSQL流复制协议解析器设计方案

## 1. 项目概述

### 1.1 背景
当前PostgreSQL协议解析器插件支持标准SQL协议和COPY协议，但缺乏对PostgreSQL流复制协议的支持。流复制协议是PostgreSQL主备复制、逻辑复制和备份恢复的核心通信机制，对于数据库监控和审计具有重要意义。

### 1.2 目标
- 实现PostgreSQL物理流复制协议解析支持
- 实现PostgreSQL逻辑流复制协议解析支持
- 与现有PostgreSQL解析器架构无缝集成
- 保持高性能和低内存占用
- 提供完整的流复制事件监控和审计能力

### 1.3 范围
- 物理流复制协议消息解析
- 逻辑流复制协议消息解析
- 流复制连接识别和状态管理
- WAL数据流解析和事件提取
- 与现有COPY协议的协调处理

## 2. 协议分析

### 2.1 物理流复制协议

#### 2.1.1 连接建立
```
启动参数: replication=true (或 on/yes/1)
协议模式: 简单查询协议 (Simple Query Protocol)
认证方式: 标准PostgreSQL认证
```

#### 2.1.2 核心命令
- `IDENTIFY_SYSTEM`: 系统标识
- `TIMELINE_HISTORY`: 时间线历史
- `CREATE_REPLICATION_SLOT`: 创建复制槽
- `START_REPLICATION`: 开始流复制
- `BASE_BACKUP`: 基础备份

#### 2.1.3 流数据消息格式
```
XLogData (服务器->客户端):
- 消息标识: 'w'
- WAL起始位置: Int64
- WAL当前结束位置: Int64
- 服务器时间戳: Int64
- WAL数据: Byte[n]

Primary Keepalive (服务器->客户端):
- 消息标识: 'k'
- WAL结束位置: Int64
- 服务器时间戳: Int64
- 回复请求标志: Byte1

Standby Status Update (客户端->服务器):
- 消息标识: 'r'
- 接收位置: Int64
- 刷新位置: Int64
- 应用位置: Int64
- 客户端时间戳: Int64
- 回复请求标志: Byte1

Hot Standby Feedback (客户端->服务器):
- 消息标识: 'h'
- 客户端时间戳: Int64
- 全局xmin: Int32
- xmin epoch: Int32
- catalog_xmin: Int32
- catalog_xmin epoch: Int32
```

### 2.2 逻辑流复制协议

#### 2.2.1 连接建立
```
启动参数: replication=database
数据库连接: 指定目标数据库
协议模式: 简单查询协议 + SQL命令
```

#### 2.2.2 输出插件参数
```
pgoutput插件参数:
- proto_version: 协议版本 (1-4)
- publication_names: 发布名称列表
- binary: 二进制传输模式
- messages: 逻辑消息支持
- streaming: 流式事务支持
- two_phase: 两阶段提交支持
- origin: 源过滤选项
```

#### 2.2.3 逻辑复制消息类型
```
Begin: 事务开始
Commit: 事务提交
Origin: 源标识
Relation: 关系定义
Type: 类型定义
Insert: 插入操作
Update: 更新操作
Delete: 删除操作
Truncate: 截断操作
Message: 逻辑消息
Stream Start/Stop/Commit/Abort: 流式事务
```

## 3. 架构设计

### 3.1 整体架构

```
┌─────────────────────────────────────────────────────────────┐
│                PostgreSQL解析器插件                          │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │   探测模块   │  │  SQL协议    │  │   流复制协议模块     │  │
│  │             │  │   解析      │  │                     │  │
│  │ - 连接类型   │  │             │  │ - 物理流复制        │  │
│  │   识别      │  │ - 查询解析   │  │ - 逻辑流复制        │  │
│  │ - 参数检测   │  │ - COPY协议  │  │ - WAL数据解析       │  │
│  │             │  │             │  │ - 事件提取          │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
├─────────────────────────────────────────────────────────────┤
│                    共享基础设施                              │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │  状态管理   │  │  内存管理   │  │     事件上传        │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

### 3.2 模块设计

#### 3.2.1 连接类型识别
```cpp
enum pgsql_connection_type {
    PGSQL_CONN_NORMAL,      // 普通SQL连接
    PGSQL_CONN_PHYSICAL_REP, // 物理流复制连接
    PGSQL_CONN_LOGICAL_REP   // 逻辑流复制连接
};
```

#### 3.2.2 流复制状态管理
```cpp
enum replication_state {
    REP_STATE_NONE,
    REP_STATE_IDENTIFIED,    // 已识别系统
    REP_STATE_SLOT_CREATED,  // 复制槽已创建
    REP_STATE_STREAMING,     // 流复制进行中
    REP_STATE_COMPLETED,     // 流复制完成
    REP_STATE_ERROR          // 错误状态
};
```

#### 3.2.3 数据结构设计
```cpp
// 流复制连接信息
typedef struct replication_connection {
    pgsql_connection_type conn_type;
    replication_state state;
    char *slot_name;
    char *publication_names;
    uint64_t start_lsn;
    uint64_t current_lsn;
    uint64_t flush_lsn;
    uint64_t apply_lsn;
    uint32_t timeline_id;
    char *system_id;
    bool is_logical;
    bool binary_mode;
    int proto_version;
    uint64_t start_time;
    uint64_t last_activity;
} replication_connection_t;

// WAL数据记录
typedef struct wal_data_record {
    uint64_t start_lsn;
    uint64_t end_lsn;
    uint64_t timestamp;
    size_t data_len;
    char *data;
    struct wal_data_record *next;
} wal_data_record_t;

// 逻辑复制事件
typedef struct logical_replication_event {
    char event_type;  // B/C/R/I/U/D/T/M等
    uint64_t lsn;
    uint64_t timestamp;
    uint32_t xid;
    char *relation_name;
    char *schema_name;
    void *event_data;
    size_t data_len;
    struct logical_replication_event *next;
} logical_replication_event_t;
```

## 4. 实现方案

### 4.1 探测机制增强

#### 4.1.1 启动参数检测
在现有的`validate_startup_parameter_names`函数中添加replication参数检测：

```cpp
// 在postgre_parser_deal_probe.cpp中增加
bool CPostgreParser::detect_replication_connection(const char *data, int len) {
    // 解析启动消息中的replication参数
    // replication=true -> 物理流复制
    // replication=database -> 逻辑流复制
    // 返回连接类型
}
```

#### 4.1.2 连接状态跟踪
```cpp
// 在postgre_stream_t结构中添加
typedef struct postgre_stream {
    // ... 现有字段
    replication_connection_t *replication_conn;
    pgsql_connection_type conn_type;
} postgre_stream_t;
```

### 4.2 消息解析扩展

#### 4.2.1 新增消息类型定义
```cpp
// 在postgre_parser_common.h中添加
// 流复制专用消息类型
#define POSTGRE_MSG_XLOG_DATA              'w'
#define POSTGRE_MSG_PRIMARY_KEEPALIVE      'k'
#define POSTGRE_MSG_STANDBY_STATUS_UPDATE  'r'
#define POSTGRE_MSG_HOT_STANDBY_FEEDBACK   'h'

// 逻辑复制消息类型
#define LOGICAL_MSG_BEGIN                  'B'
#define LOGICAL_MSG_COMMIT                 'C'
#define LOGICAL_MSG_ORIGIN                 'O'
#define LOGICAL_MSG_RELATION               'R'
#define LOGICAL_MSG_TYPE                   'Y'
#define LOGICAL_MSG_INSERT                 'I'
#define LOGICAL_MSG_UPDATE                 'U'
#define LOGICAL_MSG_DELETE                 'D'
#define LOGICAL_MSG_TRUNCATE               'T'
#define LOGICAL_MSG_MESSAGE                'M'
#define LOGICAL_MSG_STREAM_START           'S'
#define LOGICAL_MSG_STREAM_STOP            'E'
#define LOGICAL_MSG_STREAM_COMMIT          'c'
#define LOGICAL_MSG_STREAM_ABORT           'A'
```

#### 4.2.2 解析函数扩展
```cpp
// 在parse_query_msg和parse_response_msg中添加流复制消息处理
int CPostgreParser::parse_replication_msg(postgre_stream_t *pgs, 
                                         const char *data, int len, 
                                         int direction);

int CPostgreParser::parse_xlog_data(postgre_stream_t *pgs, 
                                   const char *data, int len);

int CPostgreParser::parse_logical_message(postgre_stream_t *pgs, 
                                         const char *data, int len);
```

### 4.3 状态机管理

#### 4.3.1 流复制状态转换
```
NONE -> IDENTIFIED (收到IDENTIFY_SYSTEM响应)
IDENTIFIED -> SLOT_CREATED (收到CREATE_REPLICATION_SLOT响应)
SLOT_CREATED -> STREAMING (收到START_REPLICATION后的CopyBothResponse)
STREAMING -> COMPLETED (流复制结束)
任何状态 -> ERROR (发生错误)
```

#### 4.3.2 状态管理函数
```cpp
void init_replication_connection(postgre_stream_t *pgs, 
                                pgsql_connection_type type);
void update_replication_state(postgre_stream_t *pgs, 
                             replication_state new_state);
void cleanup_replication_connection(postgre_stream_t *pgs);
```

## 5. 集成策略

### 5.1 与现有架构的集成

#### 5.1.1 探测模块集成
- 在`probe()`函数中添加流复制连接检测
- 扩展连接类型枚举和状态管理
- 保持与现有SQL协议探测的兼容性

#### 5.1.2 解析模块集成
- 在现有的消息解析框架中添加流复制消息处理
- 复用现有的内存管理和错误处理机制
- 与COPY协议处理协调，避免冲突

#### 5.1.3 事件上传集成
- 扩展现有的事件结构，支持流复制事件
- 复用现有的protobuf序列化机制
- 保持事件格式的向后兼容性

### 5.2 性能考虑

#### 5.2.1 内存管理
- WAL数据流可能很大，需要流式处理而非缓存
- 逻辑复制事件需要适当的缓存和批处理
- 及时清理过期的复制状态和数据

#### 5.2.2 处理策略
- 对于大型WAL数据，只提取关键元数据
- 逻辑复制事件按事务边界进行批处理
- 使用异步处理避免阻塞主解析流程

### 5.3 兼容性保证

#### 5.3.1 向后兼容
- 现有SQL协议解析功能不受影响
- 配置选项控制流复制解析的启用/禁用
- 保持现有API和数据结构的稳定性

#### 5.3.2 版本支持
- 支持PostgreSQL 9.1+的物理流复制
- 支持PostgreSQL 10+的逻辑流复制
- 处理不同版本间的协议差异

## 6. 测试计划

### 6.1 单元测试
- 流复制连接识别测试
- 各类流复制消息解析测试
- 状态机转换测试
- 内存管理测试

### 6.2 集成测试
- 与现有PostgreSQL解析器的集成测试
- 多种连接类型并存测试
- 性能基准测试
- 内存泄漏测试

### 6.3 场景测试
- 主备复制场景测试
- 逻辑复制场景测试
- 备份恢复场景测试
- 异常情况处理测试

## 7. 实施计划

### 7.1 第一阶段：基础架构
- 扩展连接类型识别
- 添加基础数据结构
- 实现状态管理框架

### 7.2 第二阶段：物理流复制
- 实现物理流复制消息解析
- 添加WAL数据处理
- 集成测试和优化

### 7.3 第三阶段：逻辑流复制
- 实现逻辑流复制消息解析
- 添加事务和DML事件处理
- 完整性测试和性能优化

### 7.4 第四阶段：完善和优化
- 错误处理完善
- 性能优化
- 文档和测试完善

## 8. 详细技术设计

### 8.1 文件组织结构

```
src/hw/gw_parser/parser/postgre_parser/
├── postgre_parser_replication.h           # 流复制相关定义
├── postgre_parser_replication.cpp         # 流复制核心逻辑
├── postgre_parser_replication_physical.cpp # 物理流复制处理
├── postgre_parser_replication_logical.cpp  # 逻辑流复制处理
├── postgre_parser_wal_decoder.cpp         # WAL数据解码器
└── postgre_parser_logical_decoder.cpp     # 逻辑复制解码器
```

### 8.2 核心接口设计

#### 8.2.1 流复制检测接口
```cpp
class CPostgreParser {
private:
    // 检测流复制连接类型
    pgsql_connection_type detect_connection_type(const char *startup_data, int len);

    // 解析replication参数
    bool parse_replication_parameter(const char *param_value,
                                   pgsql_connection_type *conn_type,
                                   char **database_name);

    // 初始化流复制上下文
    bool init_replication_context(postgre_stream_t *pgs,
                                 pgsql_connection_type conn_type);
};
```

#### 8.2.2 消息处理接口
```cpp
// 流复制命令处理
int parse_replication_command(postgre_stream_t *pgs, const char *data, int len);

// 物理流复制消息处理
int parse_physical_replication_msg(postgre_stream_t *pgs,
                                  const char *data, int len, int direction);

// 逻辑流复制消息处理
int parse_logical_replication_msg(postgre_stream_t *pgs,
                                 const char *data, int len);

// WAL数据解析
int parse_wal_data(postgre_stream_t *pgs, const char *data, int len);

// 逻辑复制事件解析
int parse_logical_event(postgre_stream_t *pgs, const char *data, int len);
```

### 8.3 消息格式详细定义

#### 8.3.1 物理流复制消息格式
```cpp
// XLogData消息结构
typedef struct xlog_data_msg {
    char msg_type;        // 'w'
    uint64_t start_lsn;   // WAL起始位置
    uint64_t end_lsn;     // WAL结束位置
    uint64_t timestamp;   // 服务器时间戳
    uint32_t data_len;    // WAL数据长度
    char *wal_data;       // WAL数据内容
} xlog_data_msg_t;

// Primary Keepalive消息结构
typedef struct primary_keepalive_msg {
    char msg_type;        // 'k'
    uint64_t end_lsn;     // WAL结束位置
    uint64_t timestamp;   // 服务器时间戳
    uint8_t reply_flag;   // 回复请求标志
} primary_keepalive_msg_t;

// Standby Status Update消息结构
typedef struct standby_status_msg {
    char msg_type;        // 'r'
    uint64_t receive_lsn; // 接收位置
    uint64_t flush_lsn;   // 刷新位置
    uint64_t apply_lsn;   // 应用位置
    uint64_t timestamp;   // 客户端时间戳
    uint8_t reply_flag;   // 回复请求标志
} standby_status_msg_t;
```

#### 8.3.2 逻辑流复制消息格式
```cpp
// Begin消息结构
typedef struct logical_begin_msg {
    char msg_type;        // 'B'
    uint64_t final_lsn;   // 事务最终LSN
    uint64_t timestamp;   // 提交时间戳
    uint32_t xid;         // 事务ID
} logical_begin_msg_t;

// Commit消息结构
typedef struct logical_commit_msg {
    char msg_type;        // 'C'
    uint8_t flags;        // 标志位
    uint64_t commit_lsn;  // 提交LSN
    uint64_t end_lsn;     // 结束LSN
    uint64_t timestamp;   // 提交时间戳
} logical_commit_msg_t;

// Relation消息结构
typedef struct logical_relation_msg {
    char msg_type;        // 'R'
    uint32_t relation_id; // 关系ID
    char *namespace_name; // 命名空间
    char *relation_name;  // 关系名称
    uint8_t replica_identity; // 复制标识
    uint16_t column_count;    // 列数量
    logical_column_t *columns; // 列定义数组
} logical_relation_msg_t;

// Insert消息结构
typedef struct logical_insert_msg {
    char msg_type;        // 'I'
    uint32_t relation_id; // 关系ID
    uint8_t tuple_type;   // 元组类型 ('N' = new)
    logical_tuple_t *tuple; // 元组数据
} logical_insert_msg_t;
```

### 8.4 状态机详细设计

#### 8.4.1 物理流复制状态机
```
[NONE] --IDENTIFY_SYSTEM--> [IDENTIFIED]
[IDENTIFIED] --CREATE_REPLICATION_SLOT--> [SLOT_CREATED]
[IDENTIFIED] --START_REPLICATION--> [STREAMING]
[SLOT_CREATED] --START_REPLICATION--> [STREAMING]
[STREAMING] --XLogData/Keepalive--> [STREAMING]
[STREAMING] --CopyDone--> [COMPLETED]
[ANY] --Error--> [ERROR]
```

#### 8.4.2 逻辑流复制状态机
```
[NONE] --IDENTIFY_SYSTEM--> [IDENTIFIED]
[IDENTIFIED] --CREATE_REPLICATION_SLOT--> [SLOT_CREATED]
[SLOT_CREATED] --START_REPLICATION LOGICAL--> [STREAMING]
[STREAMING] --Begin--> [IN_TRANSACTION]
[IN_TRANSACTION] --DML Events--> [IN_TRANSACTION]
[IN_TRANSACTION] --Commit--> [STREAMING]
[IN_TRANSACTION] --Abort--> [STREAMING]
[STREAMING] --CopyDone--> [COMPLETED]
```

### 8.5 内存管理策略

#### 8.5.1 WAL数据处理
```cpp
// WAL数据流式处理，避免大量缓存
typedef struct wal_stream_processor {
    uint64_t current_lsn;
    uint64_t processed_bytes;
    uint32_t record_count;
    bool in_record;
    char *partial_record;
    size_t partial_size;
} wal_stream_processor_t;

// WAL记录解析（只提取元数据）
typedef struct wal_record_header {
    uint32_t xl_tot_len;    // 记录总长度
    uint32_t xl_xid;        // 事务ID
    uint64_t xl_prev;       // 前一记录LSN
    uint8_t xl_info;        // 信息标志
    uint8_t xl_rmid;        // 资源管理器ID
} wal_record_header_t;
```

#### 8.5.2 逻辑复制事件缓存
```cpp
// 事务级别的事件缓存
typedef struct logical_transaction {
    uint32_t xid;
    uint64_t begin_lsn;
    uint64_t commit_lsn;
    uint64_t timestamp;
    logical_replication_event_t *events;
    uint32_t event_count;
    size_t total_size;
    struct logical_transaction *next;
} logical_transaction_t;

// 事件缓存管理
typedef struct logical_event_cache {
    logical_transaction_t *active_transactions;
    uint32_t max_transactions;
    size_t max_memory_usage;
    size_t current_memory_usage;
} logical_event_cache_t;
```

### 8.6 错误处理机制

#### 8.6.1 错误类型定义
```cpp
enum replication_error_type {
    REP_ERROR_NONE = 0,
    REP_ERROR_INVALID_MESSAGE,      // 无效消息格式
    REP_ERROR_PROTOCOL_VIOLATION,   // 协议违规
    REP_ERROR_MEMORY_ALLOCATION,    // 内存分配失败
    REP_ERROR_STATE_TRANSITION,     // 状态转换错误
    REP_ERROR_WAL_DECODE_FAILED,    // WAL解码失败
    REP_ERROR_LOGICAL_DECODE_FAILED, // 逻辑解码失败
    REP_ERROR_TIMEOUT,              // 超时错误
    REP_ERROR_CONNECTION_LOST       // 连接丢失
};
```

#### 8.6.2 错误处理策略
```cpp
// 错误恢复策略
typedef struct replication_error_handler {
    replication_error_type error_type;
    bool is_recoverable;
    int max_retry_count;
    int retry_interval_ms;
    void (*recovery_action)(postgre_stream_t *pgs);
} replication_error_handler_t;

// 错误处理函数
void handle_replication_error(postgre_stream_t *pgs,
                             replication_error_type error_type,
                             const char *error_msg);
bool try_recover_replication_error(postgre_stream_t *pgs);
```

### 8.7 性能优化设计

#### 8.7.1 零拷贝优化
```cpp
// 使用指针引用避免数据拷贝
typedef struct zero_copy_buffer {
    const char *data;     // 指向原始数据
    size_t offset;        // 当前偏移
    size_t length;        // 数据长度
    bool owns_data;       // 是否拥有数据
} zero_copy_buffer_t;

// 流式解析接口
int parse_stream_data(zero_copy_buffer_t *buffer,
                     replication_message_handler_t *handler);
```

#### 8.7.2 批处理优化
```cpp
// 事件批处理
typedef struct event_batch {
    logical_replication_event_t **events;
    uint32_t count;
    uint32_t capacity;
    size_t total_size;
    uint64_t batch_timestamp;
} event_batch_t;

// 批处理配置
typedef struct batch_config {
    uint32_t max_events_per_batch;
    size_t max_batch_size;
    uint32_t max_batch_time_ms;
    bool enable_compression;
} batch_config_t;
```

### 8.8 监控和统计

#### 8.8.1 性能指标
```cpp
typedef struct replication_stats {
    // 连接统计
    uint32_t total_connections;
    uint32_t active_physical_connections;
    uint32_t active_logical_connections;

    // 数据量统计
    uint64_t total_wal_bytes;
    uint64_t total_logical_events;
    uint64_t total_transactions;

    // 性能统计
    uint64_t avg_processing_time_us;
    uint64_t max_processing_time_us;
    uint32_t parse_errors;
    uint32_t memory_allocation_failures;

    // 时间统计
    uint64_t start_time;
    uint64_t last_activity_time;
} replication_stats_t;
```

#### 8.8.2 监控接口
```cpp
// 统计更新函数
void update_replication_stats(replication_stats_t *stats,
                             replication_event_type event_type,
                             uint64_t processing_time,
                             size_t data_size);

// 统计报告函数
void report_replication_stats(replication_stats_t *stats);

// 性能监控回调
typedef void (*replication_monitor_callback_t)(replication_stats_t *stats);

## 9. 处理流程图

### 9.1 连接识别流程

```mermaid
flowchart TD
    A[接收启动消息] --> B{检查协议版本}
    B -->|版本3.0| C[解析启动参数]
    B -->|其他版本| Z[非PostgreSQL连接]

    C --> D{检查replication参数}
    D -->|replication=true| E[物理流复制连接]
    D -->|replication=database| F[逻辑流复制连接]
    D -->|无replication参数| G[普通SQL连接]

    E --> H[初始化物理流复制上下文]
    F --> I[初始化逻辑流复制上下文]
    G --> J[初始化SQL解析上下文]

    H --> K[设置物理流复制状态机]
    I --> L[设置逻辑流复制状态机]
    J --> M[设置SQL解析状态机]
```

### 9.2 物理流复制处理流程

```mermaid
flowchart TD
    A[物理流复制连接建立] --> B[等待IDENTIFY_SYSTEM]
    B --> C{收到IDENTIFY_SYSTEM响应}
    C -->|是| D[解析系统信息]
    C -->|否| B

    D --> E[状态转换为IDENTIFIED]
    E --> F[等待复制命令]

    F --> G{收到命令类型}
    G -->|CREATE_REPLICATION_SLOT| H[处理复制槽创建]
    G -->|START_REPLICATION| I[开始流复制]
    G -->|其他命令| J[处理其他复制命令]

    H --> K[状态转换为SLOT_CREATED]
    K --> F

    I --> L[状态转换为STREAMING]
    L --> M[进入CopyBoth模式]

    M --> N{接收流数据}
    N -->|XLogData| O[解析WAL数据]
    N -->|Primary Keepalive| P[处理心跳消息]
    N -->|Standby Status| Q[处理状态更新]
    N -->|CopyDone| R[流复制结束]

    O --> S[提取WAL元数据]
    P --> T[更新连接状态]
    Q --> U[记录备机状态]

    S --> N
    T --> N
    U --> N
    R --> V[状态转换为COMPLETED]
```

### 9.3 逻辑流复制处理流程

```mermaid
flowchart TD
    A[逻辑流复制连接建立] --> B[等待复制槽创建]
    B --> C{收到CREATE_REPLICATION_SLOT LOGICAL}
    C -->|是| D[创建逻辑复制槽]
    C -->|否| B

    D --> E[状态转换为SLOT_CREATED]
    E --> F[等待START_REPLICATION LOGICAL]

    F --> G{收到START_REPLICATION LOGICAL}
    G -->|是| H[开始逻辑流复制]
    G -->|否| F

    H --> I[状态转换为STREAMING]
    I --> J[进入CopyBoth模式]

    J --> K{接收逻辑复制消息}
    K -->|Begin| L[开始事务]
    K -->|Relation| M[处理关系定义]
    K -->|Type| N[处理类型定义]
    K -->|Insert/Update/Delete| O[处理DML操作]
    K -->|Commit| P[提交事务]
    K -->|Message| Q[处理逻辑消息]
    K -->|CopyDone| R[流复制结束]

    L --> S[创建事务上下文]
    M --> T[缓存关系元数据]
    N --> U[缓存类型信息]
    O --> V[记录DML事件]
    P --> W[完成事务处理]
    Q --> X[处理自定义消息]

    S --> K
    T --> K
    U --> K
    V --> K
    W --> Y[批量上传事务事件]
    X --> K
    Y --> K
    R --> Z[状态转换为COMPLETED]
```

### 9.4 错误处理流程

```mermaid
flowchart TD
    A[检测到错误] --> B{错误类型判断}
    B -->|协议错误| C[记录协议违规]
    B -->|解析错误| D[记录解析失败]
    B -->|内存错误| E[记录内存分配失败]
    B -->|状态错误| F[记录状态转换错误]

    C --> G{是否可恢复}
    D --> G
    E --> G
    F --> G

    G -->|可恢复| H[尝试错误恢复]
    G -->|不可恢复| I[标记连接为错误状态]

    H --> J{恢复成功}
    J -->|是| K[继续正常处理]
    J -->|否| L[增加重试计数]

    L --> M{重试次数检查}
    M -->|未超限| N[等待重试间隔]
    M -->|已超限| I

    N --> H
    I --> O[清理资源]
    O --> P[上报错误事件]
```

## 10. 配置管理

### 10.1 配置参数设计

```cpp
// 流复制解析配置
typedef struct replication_parser_config {
    // 基础开关
    bool enable_physical_replication;    // 启用物理流复制解析
    bool enable_logical_replication;     // 启用逻辑流复制解析
    bool enable_wal_decoding;           // 启用WAL解码
    bool enable_logical_decoding;       // 启用逻辑解码

    // 性能配置
    uint32_t max_wal_buffer_size;       // WAL缓冲区最大大小(MB)
    uint32_t max_logical_events_cache;  // 逻辑事件缓存最大数量
    uint32_t max_transaction_cache;     // 事务缓存最大数量
    uint32_t batch_size;                // 批处理大小
    uint32_t batch_timeout_ms;          // 批处理超时(毫秒)

    // 内存管理
    size_t max_memory_usage_mb;         // 最大内存使用(MB)
    uint32_t memory_cleanup_interval;   // 内存清理间隔(秒)
    bool enable_zero_copy;              // 启用零拷贝优化

    // 错误处理
    uint32_t max_parse_errors;          // 最大解析错误数
    uint32_t error_retry_count;         // 错误重试次数
    uint32_t error_retry_interval_ms;   // 错误重试间隔(毫秒)
    bool enable_error_recovery;         // 启用错误恢复

    // 监控配置
    bool enable_statistics;             // 启用统计
    uint32_t stats_report_interval;     // 统计报告间隔(秒)
    bool enable_performance_monitoring; // 启用性能监控

    // 调试配置
    bool enable_debug_logging;          // 启用调试日志
    uint32_t debug_log_level;           // 调试日志级别
    bool dump_raw_messages;             // 转储原始消息
} replication_parser_config_t;
```

### 10.2 配置文件格式

```ini
[postgresql_replication]
# 基础功能开关
enable_physical_replication = true
enable_logical_replication = true
enable_wal_decoding = false
enable_logical_decoding = true

# 性能配置
max_wal_buffer_size = 64
max_logical_events_cache = 10000
max_transaction_cache = 1000
batch_size = 100
batch_timeout_ms = 1000

# 内存管理
max_memory_usage_mb = 256
memory_cleanup_interval = 60
enable_zero_copy = true

# 错误处理
max_parse_errors = 100
error_retry_count = 3
error_retry_interval_ms = 1000
enable_error_recovery = true

# 监控配置
enable_statistics = true
stats_report_interval = 300
enable_performance_monitoring = true

# 调试配置
enable_debug_logging = false
debug_log_level = 2
dump_raw_messages = false
```

### 10.3 配置加载和验证

```cpp
// 配置加载函数
bool load_replication_config(const char *config_file,
                            replication_parser_config_t *config);

// 配置验证函数
bool validate_replication_config(const replication_parser_config_t *config);

// 配置应用函数
bool apply_replication_config(CPostgreParser *parser,
                             const replication_parser_config_t *config);

// 动态配置更新
bool update_replication_config(CPostgreParser *parser,
                              const char *param_name,
                              const char *param_value);
```

## 11. 事件格式设计

### 11.1 物理流复制事件

```protobuf
message PhysicalReplicationEvent {
    string event_type = 1;              // "physical_replication"
    uint64 timestamp = 2;               // 事件时间戳
    string system_id = 3;               // 系统标识符
    uint32 timeline_id = 4;             // 时间线ID
    string slot_name = 5;               // 复制槽名称

    oneof event_data {
        IdentifySystemEvent identify_system = 10;
        ReplicationSlotEvent slot_event = 11;
        WalDataEvent wal_data = 12;
        KeepaliveEvent keepalive = 13;
        StatusUpdateEvent status_update = 14;
    }
}

message IdentifySystemEvent {
    string system_id = 1;
    uint32 timeline_id = 2;
    string xlog_pos = 3;
    string database_name = 4;
}

message WalDataEvent {
    uint64 start_lsn = 1;
    uint64 end_lsn = 2;
    uint64 server_timestamp = 3;
    uint32 data_length = 4;
    // WAL数据内容通常不包含在事件中，只记录元数据
}
```

### 11.2 逻辑流复制事件

```protobuf
message LogicalReplicationEvent {
    string event_type = 1;              // "logical_replication"
    uint64 timestamp = 2;               // 事件时间戳
    string slot_name = 3;               // 复制槽名称
    string publication_name = 4;        // 发布名称
    uint32 transaction_id = 5;          // 事务ID
    uint64 lsn = 6;                     // LSN位置

    oneof event_data {
        TransactionBeginEvent begin = 10;
        TransactionCommitEvent commit = 11;
        RelationEvent relation = 12;
        InsertEvent insert = 13;
        UpdateEvent update = 14;
        DeleteEvent delete = 15;
        TruncateEvent truncate = 16;
        MessageEvent message = 17;
    }
}

message TransactionBeginEvent {
    uint64 final_lsn = 1;
    uint64 commit_timestamp = 2;
    uint32 transaction_id = 3;
}

message RelationEvent {
    uint32 relation_id = 1;
    string namespace_name = 2;
    string relation_name = 3;
    string replica_identity = 4;
    repeated ColumnDefinition columns = 5;
}

message InsertEvent {
    uint32 relation_id = 1;
    string relation_name = 2;
    repeated ColumnValue new_values = 3;
}

## 12. 风险评估与缓解策略

### 12.1 技术风险

#### 12.1.1 性能影响风险
**风险描述**: 流复制协议解析可能影响现有SQL解析性能
**影响程度**: 中等
**缓解策略**:
- 使用配置开关控制功能启用
- 实现零拷贝优化减少内存开销
- 采用异步处理避免阻塞主流程
- 设置内存使用上限和清理机制

#### 12.1.2 内存消耗风险
**风险描述**: WAL数据和逻辑事件可能消耗大量内存
**影响程度**: 高
**缓解策略**:
- 实现流式处理，避免大量缓存
- 设置严格的内存使用限制
- 实现智能的缓存淘汰策略
- 提供内存使用监控和告警

#### 12.1.3 协议兼容性风险
**风险描述**: 不同PostgreSQL版本的协议差异
**影响程度**: 中等
**缓解策略**:
- 支持多版本协议检测
- 实现向后兼容的解析逻辑
- 提供版本特定的处理分支
- 完善的错误处理和降级机制

### 12.2 集成风险

#### 12.2.1 现有功能影响风险
**风险描述**: 新功能可能影响现有SQL和COPY协议解析
**影响程度**: 高
**缓解策略**:
- 保持现有API和数据结构不变
- 使用独立的代码模块避免耦合
- 实现全面的回归测试
- 提供功能开关支持渐进式部署

#### 12.2.2 状态管理复杂性风险
**风险描述**: 多种协议并存增加状态管理复杂性
**影响程度**: 中等
**缓解策略**:
- 设计清晰的状态机和转换规则
- 实现状态隔离避免相互影响
- 提供状态监控和调试工具
- 完善的错误恢复机制

### 12.3 运维风险

#### 12.3.1 配置复杂性风险
**风险描述**: 新增配置参数增加运维复杂性
**影响程度**: 低
**缓解策略**:
- 提供合理的默认配置
- 实现配置验证和提示
- 提供配置模板和最佳实践
- 支持动态配置更新

#### 12.3.2 故障诊断风险
**风险描述**: 新功能可能增加故障诊断难度
**影响程度**: 中等
**缓解策略**:
- 提供详细的日志和监控
- 实现性能指标收集
- 提供调试工具和诊断接口
- 完善的错误码和错误信息

## 13. 开发里程碑

### 13.1 里程碑1: 基础架构 (2周)
**目标**: 完成基础架构设计和核心数据结构
**交付物**:
- 连接类型识别机制
- 基础数据结构定义
- 状态管理框架
- 配置管理系统
- 单元测试框架

**验收标准**:
- 能够正确识别流复制连接
- 状态机正常工作
- 配置系统功能完整
- 单元测试覆盖率>80%

### 13.2 里程碑2: 物理流复制 (3周)
**目标**: 实现完整的物理流复制协议解析
**交付物**:
- 物理流复制消息解析
- WAL数据处理逻辑
- 心跳和状态更新处理
- 错误处理机制
- 集成测试

**验收标准**:
- 支持所有物理流复制消息类型
- WAL数据解析正确
- 错误处理机制完善
- 集成测试通过

### 13.3 里程碑3: 逻辑流复制 (4周)
**目标**: 实现完整的逻辑流复制协议解析
**交付物**:
- 逻辑流复制消息解析
- 事务和DML事件处理
- 关系和类型元数据管理
- 事件批处理机制
- 性能优化

**验收标准**:
- 支持所有逻辑流复制消息类型
- 事务处理逻辑正确
- 性能满足要求
- 内存使用可控

### 13.4 里程碑4: 完善和优化 (2周)
**目标**: 完善功能并进行全面优化
**交付物**:
- 性能优化和调优
- 错误处理完善
- 监控和统计功能
- 文档和用户指南
- 全面测试

**验收标准**:
- 性能指标达标
- 错误处理覆盖全面
- 文档完整准确
- 测试覆盖率>90%

## 14. 总结

### 14.1 技术价值
本设计方案为PostgreSQL协议解析器添加了完整的流复制协议支持，具有以下技术价值：

1. **协议完整性**: 支持PostgreSQL的完整协议栈，包括SQL、COPY和流复制协议
2. **架构扩展性**: 采用模块化设计，易于扩展和维护
3. **性能优化**: 通过零拷贝、批处理等技术保证高性能
4. **兼容性保证**: 与现有功能完全兼容，支持渐进式部署

### 14.2 业务价值
1. **监控覆盖**: 提供PostgreSQL主备复制的完整监控能力
2. **审计完整**: 支持逻辑复制的数据变更审计
3. **故障诊断**: 增强数据库复制故障的诊断能力
4. **合规支持**: 满足数据库审计和合规要求

### 14.3 实施建议
1. **分阶段实施**: 按照里程碑计划分阶段实施，降低风险
2. **充分测试**: 在每个阶段进行充分的单元测试和集成测试
3. **性能监控**: 持续监控性能指标，及时优化
4. **文档完善**: 提供完整的技术文档和用户指南

### 14.4 后续扩展
1. **协议版本支持**: 支持更多PostgreSQL版本的协议特性
2. **高级功能**: 支持压缩、加密等高级特性
3. **可视化工具**: 开发流复制监控的可视化工具
4. **智能分析**: 基于流复制数据进行智能分析和预警

本设计方案提供了完整、可行的PostgreSQL流复制协议解析实现方案，能够满足当前和未来的业务需求，为PostgreSQL数据库的全面监控和审计提供强有力的技术支撑。
```
```
