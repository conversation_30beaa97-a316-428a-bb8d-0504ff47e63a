# PostgreSQL流复制协议技术参考

## 1. 协议规范详解

### 1.1 物理流复制协议规范

#### 1.1.1 连接建立过程
```
客户端 -> 服务器: StartupMessage (replication=true)
服务器 -> 客户端: AuthenticationOk
服务器 -> 客户端: ParameterStatus messages
服务器 -> 客户端: BackendKeyData
服务器 -> 客户端: ReadyForQuery
```

#### 1.1.2 IDENTIFY_SYSTEM命令
```sql
IDENTIFY_SYSTEM;
```
**响应格式**:
```
systemid        | timeline | xlogpos  | dbname
----------------+----------+----------+--------
6952492832045768| 1        | 0/1000000| 
```

#### 1.1.3 START_REPLICATION命令格式
```sql
-- 物理流复制
START_REPLICATION [SLOT slot_name] [PHYSICAL] XXX/XXX [TIMELINE tli]

-- 示例
START_REPLICATION SLOT my_slot 0/1000000 TIMELINE 1
```

#### 1.1.4 CopyBoth模式消息格式

**XLogData消息 (服务器->客户端)**:
```
Byte1('w')           - 消息类型标识
Int64                - WAL起始LSN
Int64                - WAL当前结束LSN  
Int64                - 服务器时间戳(微秒，从2000-01-01开始)
Byte[n]              - WAL数据内容
```

**Primary Keepalive消息 (服务器->客户端)**:
```
Byte1('k')           - 消息类型标识
Int64                - WAL结束LSN
Int64                - 服务器时间戳
Byte1                - 回复请求标志(1=需要回复, 0=不需要)
```

**Standby Status Update消息 (客户端->服务器)**:
```
Byte1('r')           - 消息类型标识
Int64                - 接收LSN (已接收并写入磁盘)
Int64                - 刷新LSN (已刷新到磁盘)
Int64                - 应用LSN (已应用到数据库)
Int64                - 客户端时间戳
Byte1                - 回复请求标志
```

### 1.2 逻辑流复制协议规范

#### 1.2.1 连接建立过程
```
客户端 -> 服务器: StartupMessage (replication=database, dbname=mydb)
服务器 -> 客户端: AuthenticationOk
服务器 -> 客户端: ParameterStatus messages
服务器 -> 客户端: BackendKeyData
服务器 -> 客户端: ReadyForQuery
```

#### 1.2.2 CREATE_REPLICATION_SLOT命令
```sql
CREATE_REPLICATION_SLOT slot_name LOGICAL output_plugin 
[EXPORT_SNAPSHOT | NOEXPORT_SNAPSHOT | USE_SNAPSHOT]
[TWO_PHASE]

-- 示例
CREATE_REPLICATION_SLOT my_logical_slot LOGICAL pgoutput EXPORT_SNAPSHOT;
```

#### 1.2.3 START_REPLICATION LOGICAL命令
```sql
START_REPLICATION SLOT slot_name LOGICAL XXX/XXX 
(option_name [option_value] [, ...])

-- 示例
START_REPLICATION SLOT my_logical_slot LOGICAL 0/1000000 
(proto_version '1', publication_names 'my_pub');
```

#### 1.2.4 逻辑复制消息格式

**Begin消息**:
```
Byte1('B')           - 消息类型
Int64                - 事务最终LSN
Int64                - 提交时间戳
Int32                - 事务ID
```

**Commit消息**:
```
Byte1('C')           - 消息类型
Int8                 - 标志位
Int64                - 提交LSN
Int64                - 事务结束LSN
Int64                - 提交时间戳
```

**Relation消息**:
```
Byte1('R')           - 消息类型
Int32                - 关系ID
String               - 命名空间名称
String               - 关系名称
Int8                 - 复制标识 (d=default, n=nothing, f=full, i=index)
Int16                - 列数量
```

对于每一列:
```
Int8                 - 标志位 (1=key part)
String               - 列名
Int32                - 数据类型OID
Int32                - 类型修饰符
```

**Insert消息**:
```
Byte1('I')           - 消息类型
Int32                - 关系ID
Byte1('N')           - 新元组标识
```
然后是元组数据。

**Update消息**:
```
Byte1('U')           - 消息类型
Int32                - 关系ID
Byte1('K'|'O')       - 键/旧元组标识 (可选)
[元组数据]           - 键/旧元组数据 (如果存在)
Byte1('N')           - 新元组标识
[元组数据]           - 新元组数据
```

**Delete消息**:
```
Byte1('D')           - 消息类型
Int32                - 关系ID
Byte1('K'|'O')       - 键/旧元组标识
[元组数据]           - 键/旧元组数据
```

## 2. WAL记录格式解析

### 2.1 WAL记录头部结构
```c
typedef struct XLogRecord {
    uint32      xl_tot_len;     // 记录总长度
    TransactionId xl_xid;       // 事务ID
    XLogRecPtr  xl_prev;        // 前一记录的LSN
    uint8       xl_info;        // 标志和信息
    RmgrId      xl_rmid;        // 资源管理器ID
    /* 2 bytes of padding here, initialize to zero */
    pg_crc32c   xl_crc;         // CRC校验
} XLogRecord;
```

### 2.2 资源管理器类型
```c
#define RM_XLOG_ID              0
#define RM_XACT_ID              1
#define RM_SMGR_ID              2
#define RM_CLOG_ID              3
#define RM_DBASE_ID             4
#define RM_TBLSPC_ID            5
#define RM_MULTIXACT_ID         6
#define RM_RELMAP_ID            7
#define RM_STANDBY_ID           8
#define RM_HEAP2_ID             9
#define RM_HEAP_ID              10
#define RM_BTREE_ID             11
#define RM_HASH_ID              12
#define RM_GIN_ID               13
#define RM_GIST_ID              14
#define RM_SEQ_ID               15
#define RM_SPGIST_ID            16
#define RM_BRIN_ID              17
#define RM_COMMIT_TS_ID         18
#define RM_REPLORIGIN_ID        19
#define RM_GENERIC_ID           20
#define RM_LOGICALMSG_ID        21
```

### 2.3 WAL记录解析策略
对于流复制协议解析器，我们主要关注以下信息：
- 记录总长度 (用于跳过记录内容)
- 事务ID (用于事务跟踪)
- 资源管理器ID (用于记录类型分类)
- LSN位置 (用于位置跟踪)

不需要完整解析WAL记录内容，只提取元数据即可。

## 3. 实现关键点

### 3.1 LSN处理
LSN (Log Sequence Number) 是64位整数，格式为 XXX/YYY：
```c
// LSN转换函数
uint64_t lsn_from_string(const char *lsn_str) {
    uint32_t hi, lo;
    sscanf(lsn_str, "%X/%X", &hi, &lo);
    return ((uint64_t)hi << 32) | lo;
}

char* lsn_to_string(uint64_t lsn) {
    char *result = malloc(32);
    snprintf(result, 32, "%X/%X", 
             (uint32_t)(lsn >> 32), 
             (uint32_t)(lsn & 0xFFFFFFFF));
    return result;
}
```

### 3.2 时间戳处理
PostgreSQL使用从2000-01-01开始的微秒数：
```c
// PostgreSQL时间戳转换
uint64_t pg_timestamp_to_unix_us(uint64_t pg_timestamp) {
    // PostgreSQL epoch: 2000-01-01 00:00:00 UTC
    // Unix epoch: 1970-01-01 00:00:00 UTC
    // 差值: 946684800 秒 = 946684800000000 微秒
    return pg_timestamp + 946684800000000ULL;
}

time_t pg_timestamp_to_time_t(uint64_t pg_timestamp) {
    return (time_t)((pg_timestamp + 946684800000000ULL) / 1000000);
}
```

### 3.3 网络字节序处理
所有多字节整数都使用网络字节序（大端序）：
```c
// 读取网络字节序的64位整数
uint64_t read_uint64_be(const char *data) {
    uint64_t result = 0;
    for (int i = 0; i < 8; i++) {
        result = (result << 8) | (unsigned char)data[i];
    }
    return result;
}

// 读取网络字节序的32位整数
uint32_t read_uint32_be(const char *data) {
    return ntohl(*(uint32_t*)data);
}
```

### 3.4 字符串处理
PostgreSQL协议中的字符串以null结尾：
```c
// 安全读取字符串
char* read_string_safe(const char *data, size_t max_len, size_t *consumed) {
    size_t len = strnlen(data, max_len);
    if (len == max_len) {
        return NULL; // 字符串未正确结尾
    }
    
    char *result = malloc(len + 1);
    if (result) {
        memcpy(result, data, len + 1);
        *consumed = len + 1;
    }
    return result;
}
```

## 4. 错误处理模式

### 4.1 协议错误分类
```c
enum protocol_error_severity {
    PROTOCOL_ERROR_RECOVERABLE,    // 可恢复错误
    PROTOCOL_ERROR_CONNECTION,     // 连接级错误
    PROTOCOL_ERROR_FATAL          // 致命错误
};

typedef struct protocol_error {
    protocol_error_severity severity;
    int error_code;
    char *error_message;
    const char *context;
    size_t data_offset;
} protocol_error_t;
```

### 4.2 错误恢复策略
```c
// 错误恢复处理
bool handle_protocol_error(postgre_stream_t *pgs, protocol_error_t *error) {
    switch (error->severity) {
        case PROTOCOL_ERROR_RECOVERABLE:
            // 记录错误，继续处理
            log_protocol_error(error);
            return true;
            
        case PROTOCOL_ERROR_CONNECTION:
            // 重置连接状态，尝试恢复
            reset_connection_state(pgs);
            return try_recover_connection(pgs);
            
        case PROTOCOL_ERROR_FATAL:
            // 标记连接为错误状态
            mark_connection_failed(pgs);
            return false;
    }
    return false;
}
```

### 4.3 数据完整性检查
```c
// 消息完整性验证
bool validate_message_integrity(const char *data, size_t len, char msg_type) {
    if (len < 5) return false; // 最小消息长度
    
    uint32_t declared_len = ntohl(*(uint32_t*)(data + 1));
    if (declared_len + 1 != len) return false; // 长度不匹配
    
    // 根据消息类型进行特定验证
    switch (msg_type) {
        case 'w': // XLogData
            return len >= 25; // 最小XLogData长度
        case 'k': // Primary Keepalive
            return len == 18; // 固定长度
        case 'r': // Standby Status Update
            return len == 34; // 固定长度
        default:
            return true; // 其他消息类型的基本验证
    }
}
```

## 5. 性能优化技巧

### 5.1 内存池管理
```c
// 内存池用于频繁的小对象分配
typedef struct memory_pool {
    char *buffer;
    size_t size;
    size_t used;
    size_t alignment;
} memory_pool_t;

void* pool_alloc(memory_pool_t *pool, size_t size) {
    size = (size + pool->alignment - 1) & ~(pool->alignment - 1);
    if (pool->used + size > pool->size) {
        return NULL; // 池已满
    }
    
    void *result = pool->buffer + pool->used;
    pool->used += size;
    return result;
}
```

### 5.2 批处理优化
```c
// 事件批处理结构
typedef struct event_batch {
    logical_replication_event_t **events;
    uint32_t count;
    uint32_t capacity;
    uint64_t batch_start_time;
    size_t total_size;
} event_batch_t;

// 批处理提交条件
bool should_commit_batch(event_batch_t *batch, 
                        const batch_config_t *config) {
    uint64_t current_time = get_current_time_us();
    
    return (batch->count >= config->max_events_per_batch) ||
           (batch->total_size >= config->max_batch_size) ||
           ((current_time - batch->batch_start_time) >= 
            config->max_batch_time_ms * 1000);
}
```

### 5.3 零拷贝实现
```c
// 零拷贝缓冲区
typedef struct zero_copy_buffer {
    const char *data;
    size_t length;
    size_t position;
    bool owns_data;
} zero_copy_buffer_t;

// 零拷贝读取函数
bool zcb_read_uint64(zero_copy_buffer_t *buf, uint64_t *value) {
    if (buf->position + 8 > buf->length) {
        return false;
    }
    
    *value = read_uint64_be(buf->data + buf->position);
    buf->position += 8;
    return true;
}
```
