# PostgreSQL流复制协议解析器测试计划

## 1. 测试概述

### 1.1 测试目标
- 验证流复制协议解析的正确性和完整性
- 确保与现有PostgreSQL解析器的兼容性
- 验证性能指标满足要求
- 确保错误处理机制的有效性

### 1.2 测试范围
- 物理流复制协议解析
- 逻辑流复制协议解析
- 连接类型识别
- 状态管理
- 错误处理
- 性能测试
- 集成测试

### 1.3 测试环境
- PostgreSQL版本：9.1, 10, 12, 14, 16
- 操作系统：Linux (CentOS 7/8, Ubuntu 18.04/20.04)
- 架构：x86_64, ARM64
- 网络环境：本地、跨网段、高延迟网络

## 2. 单元测试

### 2.1 连接检测测试

#### 2.1.1 测试用例：物理流复制连接检测
```cpp
TEST(ReplicationDetection, PhysicalReplication) {
    // 构造物理流复制启动消息
    const char startup_msg[] = {
        0x00, 0x00, 0x00, 0x3C,  // 消息长度
        0x00, 0x03, 0x00, 0x00,  // 协议版本3.0
        'u', 's', 'e', 'r', 0x00, 'p', 'o', 's', 't', 'g', 'r', 'e', 's', 0x00,
        'r', 'e', 'p', 'l', 'i', 'c', 'a', 't', 'i', 'o', 'n', 0x00,
        't', 'r', 'u', 'e', 0x00,
        0x00  // 结束标志
    };
    
    CPostgreParser parser;
    pgsql_connection_type conn_type = parser.detect_replication_connection(
        startup_msg, sizeof(startup_msg));
    
    EXPECT_EQ(conn_type, PGSQL_CONN_PHYSICAL_REP);
}
```

#### 2.1.2 测试用例：逻辑流复制连接检测
```cpp
TEST(ReplicationDetection, LogicalReplication) {
    // 构造逻辑流复制启动消息
    const char startup_msg[] = {
        0x00, 0x00, 0x00, 0x45,  // 消息长度
        0x00, 0x03, 0x00, 0x00,  // 协议版本3.0
        'u', 's', 'e', 'r', 0x00, 'p', 'o', 's', 't', 'g', 'r', 'e', 's', 0x00,
        'd', 'b', 'n', 'a', 'm', 'e', 0x00, 't', 'e', 's', 't', 'd', 'b', 0x00,
        'r', 'e', 'p', 'l', 'i', 'c', 'a', 't', 'i', 'o', 'n', 0x00,
        'd', 'a', 't', 'a', 'b', 'a', 's', 'e', 0x00,
        0x00  // 结束标志
    };
    
    CPostgreParser parser;
    pgsql_connection_type conn_type = parser.detect_replication_connection(
        startup_msg, sizeof(startup_msg));
    
    EXPECT_EQ(conn_type, PGSQL_CONN_LOGICAL_REP);
}
```

### 2.2 消息解析测试

#### 2.2.1 测试用例：XLogData消息解析
```cpp
TEST(MessageParsing, XLogDataMessage) {
    // 构造XLogData消息
    const char xlog_msg[] = {
        'w',                                    // 消息类型
        0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00,  // 起始LSN
        0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x10, 0x00,  // 结束LSN
        0x00, 0x00, 0x01, 0x7F, 0x12, 0x34, 0x56, 0x78,  // 时间戳
        // WAL数据...
        0x58, 0x4C, 0x4F, 0x47  // 示例WAL数据
    };
    
    CPostgreParser parser;
    postgre_stream_t pgs;
    init_test_replication_context(&pgs, PGSQL_CONN_PHYSICAL_REP);
    
    int result = parser.parse_xlog_data_msg(&pgs, xlog_msg, sizeof(xlog_msg));
    
    EXPECT_EQ(result, PARSER_STATUS_FINISH);
    EXPECT_EQ(pgs.replication_conn->current_lsn, 0x0100001000ULL);
}
```

#### 2.2.2 测试用例：逻辑Begin消息解析
```cpp
TEST(MessageParsing, LogicalBeginMessage) {
    const char begin_msg[] = {
        'B',                                    // 消息类型
        0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x20, 0x00,  // 最终LSN
        0x00, 0x00, 0x01, 0x7F, 0x12, 0x34, 0x56, 0x78,  // 时间戳
        0x00, 0x00, 0x12, 0x34                 // 事务ID
    };
    
    CPostgreParser parser;
    postgre_stream_t pgs;
    init_test_replication_context(&pgs, PGSQL_CONN_LOGICAL_REP);
    
    int result = parser.parse_logical_begin_msg(&pgs, begin_msg, sizeof(begin_msg));
    
    EXPECT_EQ(result, PARSER_STATUS_FINISH);
    // 验证事务上下文已创建
    EXPECT_NE(get_current_transaction(&pgs), nullptr);
}
```

### 2.3 状态管理测试

#### 2.3.1 测试用例：状态转换测试
```cpp
TEST(StateManagement, StateTransitions) {
    CPostgreParser parser;
    postgre_stream_t pgs;
    init_test_replication_context(&pgs, PGSQL_CONN_PHYSICAL_REP);
    
    // 测试状态转换序列
    parser.update_replication_state(&pgs, REP_STATE_IDENTIFIED);
    EXPECT_EQ(pgs.replication_conn->state, REP_STATE_IDENTIFIED);
    
    parser.update_replication_state(&pgs, REP_STATE_SLOT_CREATED);
    EXPECT_EQ(pgs.replication_conn->state, REP_STATE_SLOT_CREATED);
    
    parser.update_replication_state(&pgs, REP_STATE_STREAMING);
    EXPECT_EQ(pgs.replication_conn->state, REP_STATE_STREAMING);
    
    parser.update_replication_state(&pgs, REP_STATE_COMPLETED);
    EXPECT_EQ(pgs.replication_conn->state, REP_STATE_COMPLETED);
}
```

## 3. 集成测试

### 3.1 端到端测试场景

#### 3.1.1 物理流复制完整流程测试
```bash
#!/bin/bash
# 物理流复制集成测试脚本

# 1. 设置PostgreSQL主备环境
setup_master_slave_environment() {
    # 配置主服务器
    configure_master_server
    
    # 配置备服务器
    configure_slave_server
    
    # 启动服务
    start_postgresql_services
}

# 2. 启动流复制
start_physical_replication() {
    # 创建复制槽
    psql -h master -c "SELECT pg_create_physical_replication_slot('test_slot');"
    
    # 启动pg_receivewal模拟备机
    pg_receivewal -h master -D /tmp/wal_archive -S test_slot &
    
    # 记录进程ID用于后续清理
    echo $! > /tmp/pg_receivewal.pid
}

# 3. 生成测试数据
generate_test_data() {
    # 在主服务器上执行DML操作
    psql -h master -d testdb -c "
        CREATE TABLE test_replication (id SERIAL PRIMARY KEY, data TEXT);
        INSERT INTO test_replication (data) VALUES ('test1'), ('test2'), ('test3');
        UPDATE test_replication SET data = 'updated' WHERE id = 1;
        DELETE FROM test_replication WHERE id = 3;
    "
}

# 4. 验证解析结果
verify_parsing_results() {
    # 检查解析器输出
    check_parser_events
    
    # 验证事件完整性
    verify_event_completeness
    
    # 检查性能指标
    check_performance_metrics
}
```

#### 3.1.2 逻辑流复制完整流程测试
```bash
#!/bin/bash
# 逻辑流复制集成测试脚本

# 1. 设置逻辑复制环境
setup_logical_replication() {
    # 配置发布端
    psql -h publisher -d testdb -c "
        CREATE PUBLICATION test_pub FOR ALL TABLES;
        SELECT pg_create_logical_replication_slot('test_logical_slot', 'pgoutput');
    "
    
    # 启动pg_recvlogical模拟订阅端
    pg_recvlogical -h publisher -d testdb -S test_logical_slot \
        --start --create-slot --plugin=pgoutput \
        -o proto_version=1 -o publication_names=test_pub &
    
    echo $! > /tmp/pg_recvlogical.pid
}

# 2. 执行逻辑复制测试
run_logical_replication_test() {
    # 生成各种DML操作
    generate_dml_operations
    
    # 测试事务边界
    test_transaction_boundaries
    
    # 测试大事务处理
    test_large_transactions
}
```

### 3.2 兼容性测试

#### 3.2.1 多版本PostgreSQL测试
```yaml
# 测试矩阵配置
test_matrix:
  postgresql_versions:
    - "9.1"
    - "10.0"
    - "12.0"
    - "14.0"
    - "16.0"
  
  replication_types:
    - physical
    - logical
  
  test_scenarios:
    - basic_replication
    - slot_management
    - error_recovery
    - performance_test
```

#### 3.2.2 现有功能兼容性测试
```cpp
TEST(Compatibility, ExistingSQLParsing) {
    CPostgreParser parser;
    
    // 测试普通SQL连接不受影响
    test_normal_sql_connection(&parser);
    
    // 测试COPY协议不受影响
    test_copy_protocol(&parser);
    
    // 测试扩展查询协议不受影响
    test_extended_query_protocol(&parser);
}
```

## 4. 性能测试

### 4.1 性能基准测试

#### 4.1.1 吞吐量测试
```cpp
TEST(Performance, ThroughputTest) {
    CPostgreParser parser;
    
    // 测试配置
    const int message_count = 100000;
    const size_t message_size = 1024;
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    // 模拟大量消息处理
    for (int i = 0; i < message_count; i++) {
        char test_message[message_size];
        generate_test_xlog_message(test_message, message_size);
        
        parser.parse_xlog_data_msg(&test_pgs, test_message, message_size);
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
        end_time - start_time);
    
    // 计算吞吐量
    double throughput = (double)message_count / duration.count() * 1000;
    
    // 验证性能要求（例如：>10000 msg/s）
    EXPECT_GT(throughput, 10000.0);
}
```

#### 4.1.2 内存使用测试
```cpp
TEST(Performance, MemoryUsageTest) {
    CPostgreParser parser;
    
    size_t initial_memory = get_memory_usage();
    
    // 处理大量消息
    process_large_message_batch(&parser);
    
    size_t peak_memory = get_memory_usage();
    
    // 触发内存清理
    trigger_memory_cleanup(&parser);
    
    size_t final_memory = get_memory_usage();
    
    // 验证内存使用合理
    EXPECT_LT(peak_memory - initial_memory, MAX_MEMORY_INCREASE);
    EXPECT_LT(final_memory - initial_memory, MAX_MEMORY_RETENTION);
}
```

### 4.2 压力测试

#### 4.2.1 高并发连接测试
```bash
#!/bin/bash
# 高并发流复制连接测试

concurrent_connections=100
test_duration=300  # 5分钟

for i in $(seq 1 $concurrent_connections); do
    {
        # 启动并发的流复制连接
        start_replication_connection $i
        
        # 运行指定时间
        sleep $test_duration
        
        # 清理连接
        cleanup_connection $i
    } &
done

# 等待所有测试完成
wait

# 收集性能数据
collect_performance_data
```

## 5. 错误处理测试

### 5.1 异常场景测试

#### 5.1.1 网络中断测试
```cpp
TEST(ErrorHandling, NetworkInterruption) {
    CPostgreParser parser;
    
    // 模拟网络中断
    simulate_network_interruption();
    
    // 发送不完整消息
    char incomplete_msg[] = {0x77, 0x00, 0x00};  // 不完整的XLogData
    
    int result = parser.parse_xlog_data_msg(&test_pgs, incomplete_msg, 3);
    
    // 应该返回CONTINUE状态等待更多数据
    EXPECT_EQ(result, PARSER_STATUS_CONTINUE);
    
    // 验证错误计数增加
    EXPECT_GT(test_pgs.replication_conn->error_count, 0);
}
```

#### 5.1.2 协议违规测试
```cpp
TEST(ErrorHandling, ProtocolViolation) {
    CPostgreParser parser;
    
    // 发送无效的消息类型
    char invalid_msg[] = {0xFF, 0x00, 0x00, 0x00, 0x10};
    
    int result = parser.parse_replication_message(&test_pgs, invalid_msg, 5, PGSQL_RESPONSE);
    
    // 应该返回DROP_DATA状态
    EXPECT_EQ(result, PARSER_STATUS_DROP_DATA);
    
    // 验证错误状态
    EXPECT_EQ(test_pgs.replication_conn->state, REP_STATE_ERROR);
}
```

## 6. 测试自动化

### 6.1 持续集成配置
```yaml
# .github/workflows/replication_test.yml
name: PostgreSQL Replication Parser Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        postgres_version: [9.1, 10, 12, 14, 16]
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Setup PostgreSQL
      run: |
        sudo apt-get install postgresql-${{ matrix.postgres_version }}
        
    - name: Build Parser
      run: |
        make clean
        make BUILD_REPLICATION=1
        
    - name: Run Unit Tests
      run: |
        make test-replication
        
    - name: Run Integration Tests
      run: |
        ./scripts/run_integration_tests.sh ${{ matrix.postgres_version }}
        
    - name: Performance Tests
      run: |
        ./scripts/run_performance_tests.sh
```

### 6.2 测试报告生成
```bash
#!/bin/bash
# 生成测试报告

generate_test_report() {
    echo "PostgreSQL Replication Parser Test Report" > test_report.md
    echo "=========================================" >> test_report.md
    echo "" >> test_report.md
    
    # 单元测试结果
    echo "## Unit Test Results" >> test_report.md
    cat unit_test_results.xml | parse_test_results >> test_report.md
    
    # 集成测试结果
    echo "## Integration Test Results" >> test_report.md
    cat integration_test_results.xml | parse_test_results >> test_report.md
    
    # 性能测试结果
    echo "## Performance Test Results" >> test_report.md
    cat performance_test_results.json | format_performance_data >> test_report.md
    
    # 覆盖率报告
    echo "## Code Coverage" >> test_report.md
    gcov -r . | format_coverage_data >> test_report.md
}
```
