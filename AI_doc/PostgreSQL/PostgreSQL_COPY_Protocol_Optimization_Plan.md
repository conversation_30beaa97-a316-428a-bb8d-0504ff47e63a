# PostgreSQL COPY协议处理优化技术方案

## 1. 背景与问题分析

### 1.1 当前COPY协议处理的问题

#### COPY协议流程差异
```
COPY IN (客户端向服务器传输数据):
>Q (COPY FROM STDIN)     - 请求
<G (COPY_IN_RESPONSE)    - 响应，进入COPY模式
>d (COPY_DATA)           - 数据传输
>d (COPY_DATA)           - 数据传输
>c (COPY_DONE)           - 结束传输
<C (COMMAND_COMPLETE)    - 命令完成
<Z (READY_FOR_QUERY)     - 准备下一个查询

COPY OUT (服务器向客户端传输数据):
>Q (COPY TO STDOUT)      - 请求
<H (COPY_OUT_RESPONSE)   - 响应，进入COPY模式
<d (COPY_DATA)           - 数据传输
<d (COPY_DATA)           - 数据传输
<c (COPY_DONE)           - 结束传输
<C (COMMAND_COMPLETE)    - 命令完成
<Z (READY_FOR_QUERY)     - 准备下一个查询
```

#### 当前匹配模式的局限性
- **请求-响应匹配模式**：适合传统查询模式，但COPY协议是**状态驱动**的
- **COPY IN问题**：`>Q` 只能与 `<G` 匹配，后续的 `>d` 数据包无法与原始请求关联
- **active_copy_request临时方案**：破坏了统一的匹配逻辑，需要优化

### 1.2 优化目标
1. **统一COPY IN和COPY OUT的匹配上传处理逻辑**
2. **遵循一个请求-响应匹配模式**
3. **为COPY BOTH消息处理奠定基础**
4. **保持现有架构的完整性**

## 2. 技术方案设计

### 2.1 核心思路：状态驱动的延迟匹配

#### 基本原理
1. **延迟上传**：COPY请求在检测时创建但不立即上传
2. **状态跟踪**：使用状态机跟踪COPY操作进度
3. **节点复用**：COPY数据处理复用已创建的请求-响应节点
4. **批量匹配**：COPY完成时，将整个操作作为一个完整的事务进行匹配上传

#### 状态定义
```cpp
enum copy_processing_state {
    COPY_STATE_NONE,           // 非COPY状态
    COPY_STATE_COPY_IN,        // COPY IN处理中
    COPY_STATE_COPY_OUT,       // COPY OUT处理中
    COPY_STATE_COPY_BOTH,      // 流复制COPY BOTH处理中
    COPY_STATE_COMPLETING      // COPY完成中，等待COMMAND_COMPLETE
};
```

### 2.2 实现策略：消息特征解析阶段的状态判断

#### 核心改进点
在 `parse_client_message_by_signature` 和 `parse_server_message_by_signature` 的 `default` 分支中：
1. **检查COPY状态**：判断是否在COPY模式
2. **跳过节点创建**：COPY模式下不创建新节点
3. **复用解析函数**：直接调用 `parse_query_msg` 或 `parse_response_msg`

## 3. 详细实现方案

### 3.1 消息特征解析优化

#### 客户端消息处理
```cpp
int CPostgreParser::parse_client_message_by_signature(postgre_stream_t *pgs, const char *data, int len, 
                                                    const struct conn *pcon, CSession *p_session, half_stream* hlf)
{
    // ... 现有的启动消息处理逻辑 ...
    
    if (len >= 5) {
        char msg_type = data[0];
        
        switch (msg_type) {
            case POSTGRE_MSG_PASSWORD:
            case POSTGRE_MSG_SASL_INITIAL_RESPONSE:
            case POSTGRE_MSG_SASL_RESPONSE:
                return parse_client_auth_msg(pgs, data, len);
                
            default:
            {
                // ✨ 关键改进：在创建节点前检查COPY状态
                if (pgs->copy_state != COPY_STATE_NONE) {
                    // 在COPY模式下，直接处理消息，不创建新节点
                    return parse_query_msg(pgs, data, len, hlf);
                }
                
                // 非COPY模式：创建新的解析节点
                postgre_half_stream_t *p_new_stream = new postgre_half_stream_t();
                // ... 节点初始化逻辑 ...
                insert_into_parser_header(pgs, PGSQL_REQUEST, p_new_stream);
                
                return parse_query_msg(pgs, data, len, hlf);
            }
        }
    }
    
    return PARSER_STATUS_CONTINUE;
}
```

#### 服务端消息处理
```cpp
int CPostgreParser::parse_server_message_by_signature(postgre_stream_t *pgs, const char *data, int len, 
                                                     const struct conn *pcon, CSession *p_session, half_stream* hlf)
{
    if (len >= 5) {
        char msg_type = data[0];
        
        switch (msg_type) {
            case POSTGRE_MSG_AUTHENTICATION:
                return parse_auth_msg(pgs, data, len, pcon, p_session);
            // ... 其他特殊消息处理 ...
                
            default:
            {
                // ✨ 关键改进：在创建节点前检查COPY状态
                if (pgs->copy_state != COPY_STATE_NONE) {
                    // 在COPY模式下，直接处理消息，不创建新节点
                    return parse_response_msg(pgs, data, len, pcon, p_session);
                }
                
                // 非COPY模式：创建新的解析节点
                postgre_half_stream_t *p_new_stream = new postgre_half_stream_t();
                // ... 节点初始化逻辑 ...
                insert_into_parser_header(pgs, PGSQL_RESPONSE, p_new_stream);
                
                return parse_response_msg(pgs, data, len, pcon, p_session);
            }
        }
    }
    
    return PARSER_STATUS_CONTINUE;
}
```

### 3.2 COPY状态管理

#### 状态设置时机
```cpp
// 在parse_response_msg中设置COPY状态
switch (msg_type) {
    case POSTGRE_MSG_COPY_IN_RESPONSE:
        pgs->copy_state = COPY_STATE_COPY_IN;
        break;
        
    case POSTGRE_MSG_COPY_OUT_RESPONSE:
        pgs->copy_state = COPY_STATE_COPY_OUT;
        break;
        
    case POSTGRE_MSG_COPY_BOTH_RESPONSE:
        pgs->copy_state = COPY_STATE_COPY_BOTH;
        break;
        
    case POSTGRE_MSG_COMMAND_COMPLETE:
        if (pgs->copy_state != COPY_STATE_NONE) {
            pgs->copy_state = COPY_STATE_NONE; // 重置状态，允许匹配
        }
        break;
}
```

#### COPY数据处理
```cpp
// 在parse_query_msg和parse_response_msg中处理COPY DATA
case POSTGRE_MSG_COPY_DATA:
{
    if (pgs->copy_state != COPY_STATE_NONE) {
        bool is_client = (当前处理的是客户端消息);
        return process_copy_data_message(pgs, data, offset, msg_len, is_client, is_header);
    }
    break;
}

case POSTGRE_MSG_COPY_DONE:
{
    if (pgs->copy_state != COPY_STATE_NONE) {
        pgs->copy_state = COPY_STATE_COMPLETING;
    }
    break;
}
```

### 3.3 匹配逻辑优化

#### 延迟匹配控制
```cpp
bool CPostgreParser::postgre_parser_merge(postgre_stream_t *p_stream, const struct conn *pcon, int dir)
{
    // 简单的状态检查即可控制匹配时机
    if (p_stream->copy_state != COPY_STATE_NONE) {
        return false; // 延迟匹配，直到COPY完成
    }
    
    // 原有匹配逻辑...
    return perform_normal_matching(p_stream, pcon, dir);
}
```

### 3.4 数据存储统一策略

#### 统一存储位置
- **COPY IN**：客户端数据存储到响应端节点的 `rs_list`
- **COPY OUT**：服务端数据存储到响应端节点的 `rs_list`（保持不变）
- **COPY BOTH**：双向数据都存储到响应端节点的 `rs_list`

#### process_copy_data_message函数优化
已实现的统一COPY DATA处理函数，支持：
- 方向自动识别（is_client参数）
- 统一的数据存储策略
- 统一的二进制/文本格式处理
- 头部行处理（修复原始逻辑缺失）

## 4. 方案优势

### 4.1 架构优势
1. **最小侵入性**：只需在 `default` 分支添加状态检查
2. **完美复用**：直接使用现有的解析函数
3. **逻辑清晰**：状态驱动的控制流程直观
4. **易于维护**：COPY状态管理集中在关键点

### 4.2 功能优势
1. **统一处理**：COPY IN、COPY OUT、COPY BOTH使用相同逻辑
2. **保持匹配模式**：不破坏现有的请求-响应匹配架构
3. **完整性保证**：确保COPY操作的完整性
4. **扩展性强**：为流复制等高级功能奠定基础

### 4.3 性能优势
1. **节点复用**：避免重复创建节点的开销
2. **精确控制**：只在必要时进行状态检查
3. **批量处理**：减少匹配操作的频率

## 5. 实施计划

### 5.1 第一阶段：基础状态管理
1. 在 `postgre_stream_t` 中添加 `copy_state` 字段
2. 实现状态设置和重置逻辑
3. 修改消息特征解析函数的 `default` 分支

### 5.2 第二阶段：COPY数据处理优化
1. 完善 `process_copy_data_message` 函数
2. 统一COPY IN和COPY OUT的数据存储策略
3. 实现头部行处理逻辑

### 5.3 第三阶段：匹配逻辑优化
1. 修改 `postgre_parser_merge` 函数
2. 实现延迟匹配控制
3. 完善错误处理和状态重置

### 5.4 第四阶段：扩展支持
1. 添加COPY BOTH支持
2. 为流复制协议做准备
3. 性能优化和测试验证

## 6. 注意事项

### 6.1 状态同步
- 确保COPY状态在各个函数间正确传递
- 处理并发访问的线程安全问题

### 6.2 异常处理
- 网络中断时的状态清理
- 连接重置时的资源释放
- 错误消息的状态重置

### 6.3 兼容性
- 保持与现有功能的向后兼容
- 确保非COPY消息的处理不受影响

## 7. 总结

本方案通过状态驱动的延迟匹配策略，优雅地解决了PostgreSQL COPY协议处理中的核心问题。通过在消息特征解析阶段进行状态判断，既保持了现有架构的完整性，又实现了COPY协议的特殊处理需求。

该方案不仅解决了当前COPY IN和COPY OUT的匹配问题，还为未来的COPY BOTH和流复制协议提供了坚实的技术基础。
