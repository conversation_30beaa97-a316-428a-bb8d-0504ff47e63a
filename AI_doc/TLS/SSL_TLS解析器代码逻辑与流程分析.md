# SSL/TLS解析器代码逻辑与流程分析

## 概述

本文档详细梳理了gw-hw项目中SSL/TLS协议解析插件的完整代码逻辑和解析流程，包括协议探测、握手解析、密钥交换、数据解密等核心机制。

## 1. 整体架构

### 1.1 核心组件
- **协议探测模块**: 判断是否为SSL/TLS流量
- **握手解析模块**: 解析TLS握手消息
- **证书管理模块**: 加载和匹配私钥证书
- **密钥交换模块**: 处理RSA密钥交换
- **数据解密模块**: 解密应用层数据
- **会话缓存模块**: 管理Session ID/Session Ticket复用

### 1.2 主要文件结构
```
ssl_parser/
├── ssl_parser.h                    # 解析器类定义
├── ssl_parser.cpp                  # 解析器主要实现
├── ssl_parser_common.h             # 数据结构定义
├── ssl_parser_common.cpp           # 密钥套件映射表
├── ssl_parser_deal_parser.cpp      # 核心解析逻辑
├── ssl_parser_deal_probe.cpp       # 协议探测逻辑
├── ssl_parser_decrypt_task_worker.h # 异步解密任务
└── ssl_parser_data_msg_task_worker.h # 异步数据处理任务
```

## 2. 协议探测流程

### 2.1 探测机制
位于 `ssl_parser_deal_probe.cpp` 中，通过以下特征识别SSL/TLS流量：
- **TLS Record Header**: 检查记录层头部格式
- **Content Type**: 验证内容类型（Handshake=22, Application Data=23等）
- **Version Field**: 检查版本字段（SSL 3.0-TLS 1.3）
- **Length Field**: 验证长度字段合理性

### 2.2 支持的TLS版本
- **完全支持**: SSL 3.0, TLS 1.0, TLS 1.1, TLS 1.2
- **有限支持**: TLS 1.3（主要限制在密钥交换机制）

## 3. 证书加载与管理

### 3.1 证书加载流程
```cpp
// ssl_parser_deal_parser.cpp
void CSslParser::ssl_load_certificate(void)
{
    // 初始化RSA密钥缓存
    g_rsa_cache = malloc(sizeof(ssl_rsa_key_cache_t));
    memset(g_rsa_cache, 0, sizeof(ssl_rsa_key_cache_t));
    
    // 读取PEM文件目录
    read_pem_dir(m_conf_pem_dir.c_str());
}
```

### 3.2 PEM文件处理
支持多种PEM格式：
- **完整证书链**: 包含证书和私钥
- **纯私钥文件**: 仅包含RSA PRIVATE KEY
- **证书文件**: 仅包含证书信息

#### 关键处理逻辑
```cpp
// 读取PEM文件的关键逻辑
if (pkey != NULL)
{
    // 从完整密钥结构中提取RSA
    rsa = EVP_PKEY_get1_RSA(pkey);
}
else
{
    // 直接读取RSA私钥
    bio1 = fopen_bio(filename);
    rsa = PEM_read_bio_RSAPrivateKey(bio1, NULL, NULL, NULL);
}
```

### 3.3 密钥缓存机制
```cpp
// RSA密钥缓存结构
typedef struct ssl_rsa_key_cache
{
    int size;
    ssl_rsa_key_t *key_ptr;
} ssl_rsa_key_cache_t;

typedef struct ssl_rsa_key
{
    RSA *rsa_pub_key;  // 公钥（用于匹配）
    RSA *rsa_pri_key;  // 私钥（用于解密）
} ssl_rsa_key_t;
```

## 4. 握手解析流程

### 4.1 握手消息类型
- **Client Hello**: 客户端发起连接
- **Server Hello**: 服务器响应，选择密钥套件
- **Certificate**: 服务器证书链
- **Server Key Exchange**: 服务器密钥交换（DHE等）
- **Client Key Exchange**: 客户端密钥交换（包含加密的PreMaster Secret）
- **Change Cipher Spec**: 切换到加密通信
- **Finished**: 握手完成验证

### 4.2 证书匹配机制
```cpp
// 证书匹配逻辑（基于公钥参数n, e）
for (i = 0; i < rsa_cache->size; i++)
{
    RSA_get0_key(p[i].rsa_pri_key, &n, &e, &d);
    if (0 == BN_cmp(r_n, n) && 0 == BN_cmp(r_e, e))
    {
        // 找到匹配的私钥
        return &p[i];
    }
}
```

## 5. 密钥交换与解密流程

### 5.1 PreMaster Secret解密
```cpp
char *CSslParser::get_premaster_secret(int *buf_len, int tls_version, 
    RSA *rsaKey, uint8_t *pre_master_secret, int pre_master_secret_size)
{
    char *buf = (char *)malloc(RSA_size(rsaKey));
    
    // 使用RSA私钥解密PreMaster Secret
    ret = RSA_private_decrypt(RSA_size(rsaKey), pre_master_secret, 
        (unsigned char *)buf, rsaKey, RSA_SSLV23_PADDING);
    
    *buf_len = ret;
    return buf;
}
```

### 5.2 Master Secret计算
Master Secret通过TLS PRF（Pseudo-Random Function）计算：
```
Master Secret = PRF(PreMaster Secret, "master secret", 
                   Client Random + Server Random)
```

### 5.3 会话密钥派生
从Master Secret派生出各种密钥：
```cpp
// 密钥派生顺序
memcpy(p_ski->client_mac_key, data + 0, p_ski->mac_key_length);
memcpy(p_ski->server_mac_key, data + p_ski->mac_key_length, p_ski->mac_key_length);
memcpy(p_ski->client_key, data + p_ski->mac_key_length * 2, p_ski->cipher_key_length);
memcpy(p_ski->server_key, data + p_ski->mac_key_length * 2 + p_ski->cipher_key_length, p_ski->cipher_key_length);
memcpy(p_ski->client_iv, data + p_ski->mac_key_length * 2 + p_ski->cipher_key_length * 2, p_ski->iv_length);
memcpy(p_ski->server_iv, data + p_ski->mac_key_length * 2 + p_ski->cipher_key_length * 2 + p_ski->iv_length, p_ski->iv_length);
```

## 6. 数据解密机制

### 6.1 加密模式支持
- **CIPHERMODE_STREAM**: 流密码（如RC4）
- **CIPHERMODE_CBC**: 分组密码CBC模式
- **CIPHERMODE_EAEAD**: 认证加密模式（如GCM）
- **CIPHERMODE_IAEAD**: 隐式认证加密模式

### 6.2 解密流程
```cpp
ssl_record_payload_t *CSslParser::ssl_decrypt_payload(int dir, 
    ssl_half_stream_t *shs, ssl_record_type_t *srt, 
    const unsigned char *enc_data, int data_size)
{
    // 根据内容类型选择处理函数
    switch (srt->content_type)
    {
    case SSL_S_CTYPE_HANDSHAKE:
        func = &CSslParser::ssl_decrypted_TLSHandshakes;
        break;
    case SSL_S_CTYPE_APPLICATION_DATA:
        func = &CSslParser::ssl_decrypted_TLSPlaintext;
        break;
    }
    
    // 执行解密
    dec_data = ssl_decrypt_data(&srt->sdp, shs, enc_data, data_size);
    
    // 处理解密后的数据
    ret = (this->*func)(p_srp, dec_data, data_size);
}
```

## 7. 会话复用机制

### 7.1 Session ID复用
```cpp
// Session ID缓存结构
typedef struct ssl_session_it_cache
{
    int size;
    ssl_session_it_data_t **data;
    ssl_session_it_data_t *check_first; // 最早的节点（用于超时清理）
    ssl_session_it_data_t *check_last;  // 最新的节点
    int session_num;
} ssl_session_it_cache_t;
```

### 7.2 Session Ticket支持
支持TLS扩展中的Session Ticket机制，允许服务器将会话状态加密后发送给客户端。

### 7.3 超时管理
定期清理超时的会话缓存：
```cpp
void CSslParser::session_it_check_state(int timeout)
{
    time_t tm = m_comm->gw_time();
    
    // 清理超时的Session ID
    while ((data = g_session_it_cache_ptr->check_first) != NULL)
    {
        if (!(labs(data->last_update - tm) >= timeout))
            break;
        
        // 删除超时节点
        g_session_it_cache_ptr->session_num--;
        // ... 清理逻辑
    }
}
```

## 8. 异步处理机制

### 8.1 解密任务队列
```cpp
// SSL解密任务结构
typedef struct ssl_parser_decrypt_msg
{
    TaskWorkerData twd;
    ssl_session_param_t *pssp;
    int tls_version;
    RSA *rsaKey;
    uint8_t *pre_master_secret;
    int pre_master_secret_size;
} ssl_parser_decrypt_msg_t;
```

### 8.2 数据处理队列
```cpp
// SSL数据处理任务结构
typedef struct ssl_parser_data_msg
{
    TaskWorkerData twd;
    ssl_parser_data_queue_t spdq;
} ssl_parser_data_msg_t;
```

### 8.3 等待队列机制
对于会话复用场景，后续会话需要等待密钥解密完成：
```cpp
// 等待密钥解密的会话列表
struct waiting_it_session
{
    time_t wait_time;
    std::vector<ssl_parser_data_msg_t *> waiting_list;
};

static std::map<std::string, waiting_it_session> session_waiting_list;
```

## 9. 支持的密钥套件

### 9.1 RSA密钥交换套件
- TLS_RSA_WITH_AES_128_CBC_SHA (0x002f)
- TLS_RSA_WITH_AES_256_CBC_SHA (0x0035)
- TLS_RSA_WITH_AES_128_CBC_SHA256 (0x003c)
- TLS_RSA_WITH_AES_256_CBC_SHA256 (0x003d)
- TLS_RSA_WITH_AES_128_GCM_SHA256 (0x009c)
- TLS_RSA_WITH_AES_256_GCM_SHA384 (0x009d)

### 9.2 ECDHE_RSA套件（有限支持）
- ECDHE_RSA_WITH_AES_128_CBC_SHA (0xc013)
- ECDHE_RSA_WITH_AES_256_CBC_SHA (0xc014)
- ECDHE_RSA_WITH_AES_128_GCM_SHA256 (0xc02f)
- ECDHE_RSA_WITH_AES_256_GCM_SHA384 (0xc030)

## 10. 限制条件分析

### 10.1 密钥交换限制
- **仅支持RSA密钥交换**: 不支持DHE、ECDHE的密钥派生
- **TLS 1.3限制**: TLS 1.3采用不同的密钥交换机制，当前实现支持有限

### 10.2 加密算法限制
- **非对称加密**: 仅支持RSA，不支持DSA、ECDSA
- **对称加密**: 支持主流算法（AES、3DES、RC4等）
- **哈希算法**: 支持MD5、SHA-1、SHA-256、SHA-384

### 10.3 证书格式限制
- **支持格式**: PEM格式的RSA私钥和证书
- **不支持**: PKCS#12、DER格式等其他证书格式

## 11. 关键配置参数

### 11.1 证书配置
- `m_conf_pem_dir`: PEM文件目录路径
- `m_conf_ssl_session_cache_check_interval`: 会话缓存检查间隔

### 11.2 性能配置
- RSA密钥缓存大小：默认64个增量扩展
- Session缓存大小：可配置的哈希表大小
- 异步队列深度：可配置的任务队列大小

## 12. 仅有RSA私钥的匹配机制

### 12.1 匹配原理
RSA私钥包含完整的密钥对信息：
- **n**: 模数（公钥和私钥共享）
- **e**: 公钥指数
- **d**: 私钥指数
- **p, q**: 素数因子

### 12.2 匹配流程
1. **服务器证书解析**: 从握手消息中提取服务器证书
2. **公钥参数提取**: 从证书中提取RSA公钥的n和e参数
3. **缓存遍历匹配**: 遍历预加载的私钥缓存，比较n和e参数
4. **匹配成功**: 找到对应私钥，用于后续的PreMaster Secret解密

### 12.3 关键代码逻辑
```cpp
// 即使PEM文件中只有RSA PRIVATE KEY，也能成功匹配
if (pkey != NULL)
{
    rsa = EVP_PKEY_get1_RSA(pkey);  // 从完整结构提取
}
else
{
    bio1 = fopen_bio(filename);
    rsa = PEM_read_bio_RSAPrivateKey(bio1, NULL, NULL, NULL);  // 直接读取私钥
}

// 私钥中包含公钥参数，可以进行匹配
RSA_get0_key(rsa, &n, &e, &d);  // n, e用于匹配，d用于解密
```

## 13. 总结

SSL/TLS解析器实现了较为完整的TLS 1.2及以下版本的解析能力，核心特点：

1. **协议支持完整**: SSL 3.0到TLS 1.2的完整支持
2. **RSA密钥交换**: 专注于RSA密钥交换的深度支持
3. **证书灵活匹配**: 支持多种PEM格式，包括纯私钥文件
4. **会话复用优化**: 支持Session ID和Session Ticket
5. **异步处理架构**: 通过任务队列实现高性能解密
6. **缓存机制完善**: 多层缓存提升解析性能

**主要限制**：
- TLS 1.3支持有限（密钥交换机制差异）
- 仅支持RSA非对称加密（不支持ECDSA、DSA）
- 不支持完美前向保密的密钥交换（DHE、ECDHE的密钥派生）

这种设计在企业内网环境中能够有效解析大部分TLS流量，特别适合需要深度包检测和内容审计的应用场景。