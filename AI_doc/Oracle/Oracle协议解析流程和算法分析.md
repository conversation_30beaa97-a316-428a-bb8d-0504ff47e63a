# Oracle协议解析流程和算法分析

## 概述

本文档详细分析Oracle协议解析器的核心算法和解析流程，包括协议识别、数据解码、SQL分析等关键技术环节。

## 1. 协议探测算法

### 1.1 端口探测
```c++
bool COracleParser::probe() {
    // 首先检查Oracle标准端口
    if (pcon->client.port != 1521 && pcon->server.port != 1521) {
        return false;
    }
    
    // 检查数据包特征
    return validate_oracle_signature();
}
```

### 1.2 包特征识别
```mermaid
graph TD
    A[数据包到达] --> B{端口检查}
    B -->|1521| C[TNS包头验证]
    B -->|其他| D[返回false]
    
    C --> E{包长度检查}
    E -->|8-65535| F[包类型验证]
    E -->|异常| G[返回false]
    
    F --> H{TNS类型码}
    H -->|1-15| I[Oracle协议确认]
    H -->|其他| J[返回false]
```

## 2. TNS包解析算法

### 2.1 包头解析流程
```c++
int parse_tns_header(const char *data, size_t len, tns_header_t *header) {
    if (len < TNS_HEADER_SIZE) return TNS_PARSE_NEED_MORE_DATA;
    
    // 网络字节序转换
    header->length = ntohs(*(uint16_t*)data);
    header->checksum = ntohs(*(uint16_t*)(data + 2));
    header->type = data[4];
    header->flags = data[5];
    header->header_checksum = ntohs(*(uint16_t*)(data + 6));
    
    return validate_tns_header(header);
}
```

### 2.2 包类型处理分发
```mermaid
graph TD
    A[TNS包头解析] --> B{包类型}
    B -->|1-CONNECT| C[连接建立]
    B -->|2-ACCEPT| D[连接确认]
    B -->|6-DATA| E[数据包处理]
    B -->|4-REFUSE| F[连接拒绝]
    B -->|9-ABORT| G[连接中止]
    
    C --> H[协议版本协商]
    D --> I[能力参数确认]  
    E --> J[TTC层解析]
    F --> K[错误信息提取]
    G --> L[清理连接状态]
```

### 2.3 数据完整性验证
```c++
bool validate_tns_checksum(const tns_header_t *header, const char *data) {
    uint16_t calculated = 0;
    
    // 计算包体校验和
    for (size_t i = TNS_HEADER_SIZE; i < header->length; i++) {
        calculated += (uint8_t)data[i];
    }
    
    return calculated == header->checksum;
}
```

## 3. TTC消息解析算法

### 3.1 消息类型识别
```c++
int parse_ttc_message(const char *data, size_t len) {
    uint8_t msg_type = data[0];
    
    switch (msg_type) {
        case TTIPRO: return parse_protocol_negotiation(data, len);
        case TTIFUN: return parse_function_call(data, len);
        case TTIOER: return parse_error_message(data, len);
        case TTIRXH: return parse_resultset_header(data, len);
        case TTIRXD: return parse_resultset_data(data, len);
        default: return TTC_PARSE_UNSUPPORTED;
    }
}
```

### 3.2 协议协商处理
```mermaid
graph LR
    A[TTIPRO消息] --> B[版本提取]
    B --> C[字符集协商]
    C --> D[能力交换]
    D --> E[兼容性确定]
    E --> F[会话初始化]
```

### 3.3 函数调用解析
```c++
int parse_function_call(const char *data, size_t len) {
    // 提取函数码
    uint16_t function_code = read_uint16(data + 1);
    
    // 根据函数码分发处理
    switch (function_code) {
        case OALL7: return parse_all7_function(data, len);
        case OCOMMIT: return parse_commit_function(data, len);
        case OFETCH: return parse_fetch_function(data, len);
        default: return handle_unsupported_function(function_code);
    }
}
```

## 4. TTI层解析算法

### 4.1 Oracle函数码解析
```mermaid
graph TD
    A[TTI消息] --> B[函数码提取]
    B --> C{函数码类型}
    C -->|SQL相关| D[SQL解析流程]
    C -->|事务相关| E[事务控制流程]  
    C -->|认证相关| F[认证处理流程]
    C -->|游标相关| G[游标管理流程]
    
    D --> H[SQL文本提取]
    E --> I[事务状态更新]
    F --> J[认证信息解析] 
    G --> K[游标状态跟踪]
```

### 4.2 SQL文本提取算法
```c++
int extract_sql_text(const char *data, size_t len, b_string_t *sql) {
    size_t offset = 0;
    
    // 跳过函数头部
    offset += get_function_header_size(data);
    
    // 读取SQL长度（变长编码）
    uint32_t sql_len = 0;
    if (read_variable_length(data + offset, &sql_len, &offset) < 0) {
        return TTI_PARSE_INVALID_DATA;
    }
    
    // 验证长度合理性
    if (sql_len > MAX_SQL_LENGTH || offset + sql_len > len) {
        return TTI_PARSE_INVALID_DATA;
    }
    
    // 提取SQL文本
    sql->s = data + offset;
    sql->len = sql_len;
    
    return TTI_PARSE_SUCCESS;
}
```

### 4.3 绑定变量解析
```c++
int parse_bind_variables(const char *data, size_t len, size_t *offset) {
    // 读取绑定变量数量
    uint16_t bind_count = read_uint16(data + *offset);
    *offset += 2;
    
    for (uint16_t i = 0; i < bind_count; i++) {
        oracle_bind_variable_t bind_var = {0};
        
        // 解析单个绑定变量
        if (parse_single_bind_var(data, len, offset, &bind_var) < 0) {
            return TTI_PARSE_ERROR;
        }
        
        // 存储绑定变量
        store_bind_variable(&bind_var);
    }
    
    return TTI_PARSE_SUCCESS;
}
```

## 5. 数据类型解码算法

### 5.1 Oracle NUMBER解码
```c++
int decode_oracle_number(const uint8_t *data, size_t len) {
    if (len == 0) return NUMBER_NULL;
    
    uint8_t exp_byte = data[0];
    
    // 检查特殊值
    if (exp_byte == 0x80) return NUMBER_ZERO;
    if (exp_byte == 0x00) return NUMBER_NULL;
    
    // 判断正负号
    bool negative = (exp_byte & 0x80) == 0;
    
    // 计算指数
    int exponent;
    if (negative) {
        exponent = (~exp_byte & 0x7F) - 64;
    } else {
        exponent = (exp_byte & 0x7F) - 64;
    }
    
    // 解析尾数
    decode_mantissa(data + 1, len - 1, negative);
    
    return NUMBER_SUCCESS;
}
```

### 5.2 Oracle DATE解码
```c++
int decode_oracle_date(const uint8_t *data, size_t len) {
    if (len != 7) return DATE_INVALID;
    
    oracle_date_t date = {0};
    
    // Oracle内部格式：世纪、年、月、日、时、分、秒
    date.year = (data[0] - 100) * 100 + (data[1] - 100);
    date.month = data[2];
    date.day = data[3];
    date.hour = data[4] - 1;    // Oracle以1为基准
    date.minute = data[5] - 1;
    date.second = data[6] - 1;
    
    return validate_date(&date) ? DATE_SUCCESS : DATE_INVALID;
}
```

### 5.3 字符串类型解码
```c++
int decode_varchar2(const uint8_t *data, size_t len) {
    if (len == 0) return VARCHAR_NULL;
    
    // 第一字节是长度标识符
    uint8_t len_indicator = data[0];
    
    if (len_indicator == 0x00) {
        return VARCHAR_NULL;
    } else if (len_indicator <= 0xFA) {
        // 短字符串：长度直接编码
        return extract_string(data + 1, len_indicator);
    } else {
        // 长字符串：多字节长度编码
        return extract_long_string(data);
    }
}
```

## 6. SQL分析算法

### 6.1 SQL类型识别
```c++
oracle_sql_type_t identify_sql_type(const char *sql, size_t len) {
    // 跳过前导空白
    size_t start = skip_whitespace(sql, len);
    
    // 提取第一个关键字
    char keyword[32] = {0};
    extract_first_keyword(sql + start, len - start, keyword);
    
    // 关键字映射
    if (strcasecmp(keyword, "SELECT") == 0) return SQL_TYPE_SELECT;
    if (strcasecmp(keyword, "INSERT") == 0) return SQL_TYPE_INSERT;
    if (strcasecmp(keyword, "UPDATE") == 0) return SQL_TYPE_UPDATE;
    if (strcasecmp(keyword, "DELETE") == 0) return SQL_TYPE_DELETE;
    if (strcasecmp(keyword, "CREATE") == 0) return SQL_TYPE_CREATE;
    if (strcasecmp(keyword, "ALTER") == 0)  return SQL_TYPE_ALTER;
    if (strcasecmp(keyword, "DROP") == 0)   return SQL_TYPE_DROP;
    if (strcasecmp(keyword, "BEGIN") == 0)  return SQL_TYPE_PLSQL_BLOCK;
    
    return SQL_TYPE_UNKNOWN;
}
```

### 6.2 SQL标准化算法
```c++
int normalize_sql(const char *sql, size_t len, char **normalized) {
    char *result = malloc(len + 1);
    size_t out_pos = 0;
    bool in_string = false;
    bool in_comment = false;
    
    for (size_t i = 0; i < len; i++) {
        char c = sql[i];
        
        // 处理字符串字面量
        if (c == '\'' && !in_comment) {
            in_string = !in_string;
            result[out_pos++] = '?';  // 参数化
            continue;
        }
        
        if (in_string) continue;
        
        // 处理注释
        if (!in_comment && c == '/' && i + 1 < len && sql[i+1] == '*') {
            in_comment = true;
            i++; // 跳过 '*'
            continue;
        }
        
        if (in_comment && c == '*' && i + 1 < len && sql[i+1] == '/') {
            in_comment = false;
            i++; // 跳过 '/'
            continue;
        }
        
        if (in_comment) continue;
        
        // 标准化空白字符
        if (isspace(c)) {
            if (out_pos > 0 && result[out_pos-1] != ' ') {
                result[out_pos++] = ' ';
            }
        } else {
            result[out_pos++] = toupper(c);
        }
    }
    
    result[out_pos] = '\0';
    *normalized = result;
    return 0;
}
```

## 7. 性能优化算法

### 7.1 快速哈希算法
```c++
uint32_t fast_sql_hash(const char *sql, size_t len) {
    uint32_t hash = 5381;
    
    for (size_t i = 0; i < len; i++) {
        hash = ((hash << 5) + hash) + sql[i]; // hash * 33 + c
    }
    
    return hash;
}
```

### 7.2 内存池分配算法
```c++
void* pool_alloc(size_t size) {
    // 查找合适的池
    int pool_index = find_suitable_pool(size);
    if (pool_index < 0) {
        return malloc(size); // 后备分配
    }
    
    // 从池中分配
    memory_pool_t *pool = &pools[pool_index];
    if (pool->free_count > 0) {
        return pop_from_pool(pool);
    }
    
    // 池空，扩展池大小
    if (expand_pool(pool) == 0) {
        return pop_from_pool(pool);
    }
    
    return malloc(size); // 后备分配
}
```

## 8. 错误处理算法

### 8.1 错误检测策略
```mermaid
graph TD
    A[数据输入] --> B[格式验证]
    B --> C{格式正确?}
    C -->|否| D[记录错误]
    C -->|是| E[语义验证]
    E --> F{语义正确?}
    F -->|否| G[记录警告]
    F -->|是| H[正常处理]
    
    D --> I[错误恢复]
    G --> J[降级处理]
```

### 8.2 协议恢复算法
```c++
int protocol_recovery(parse_context_t *ctx) {
    // 1. 跳过当前错误包
    skip_current_packet(ctx);
    
    // 2. 查找下一个有效TNS包头
    while (ctx->offset < ctx->data_len - TNS_HEADER_SIZE) {
        if (is_valid_tns_header(ctx->data + ctx->offset)) {
            ctx->error_count++;
            return RECOVERY_SUCCESS;
        }
        ctx->offset++;
    }
    
    // 3. 未找到有效包头，等待更多数据
    return RECOVERY_NEED_MORE_DATA;
}
```

## 总结

Oracle协议解析器采用了多层次、模块化的解析算法，通过精确的协议识别、高效的数据解码和智能的错误处理，实现了对Oracle网络协议的完整解析。其算法设计充分考虑了性能优化和错误容错，为实际生产环境提供了可靠的协议分析能力。