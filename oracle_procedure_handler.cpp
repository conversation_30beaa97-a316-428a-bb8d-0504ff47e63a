/*
 * Oracle存储过程处理器实现
 * 处理Oracle存储过程和函数调用的解析
 * <AUTHOR> @date 2025
 */

#include "oracle_procedure_handler.h"
#include "oracle_parser_common.h"
#include "gw_logger.h"
#include <string.h>
#include <stdlib.h>
#include <ctype.h>

// 简化的日志宏定义
#define PROC_LOG_DEBUG(fmt, ...) printf("[PROC-DEBUG] " fmt "\n", ##__VA_ARGS__)
#define PROC_LOG_INFO(fmt, ...)  printf("[PROC-INFO] " fmt "\n", ##__VA_ARGS__)
#define PROC_LOG_WARN(fmt, ...)  printf("[PROC-WARN] " fmt "\n", ##__VA_ARGS__)
#define PROC_LOG_ERROR(fmt, ...) printf("[PROC-ERROR] " fmt "\n", ##__VA_ARGS__)

// 大端序读取函数
static uint16_t read_uint16_be(const char *data) {
    return (((uint8_t)data[0]) << 8) | ((uint8_t)data[1]);
}

static uint32_t read_uint32_be(const char *data) {
    return (((uint8_t)data[0]) << 24) | (((uint8_t)data[1]) << 16) | 
           (((uint8_t)data[2]) << 8) | ((uint8_t)data[3]);
}

OracleProcedureHandler::OracleProcedureHandler()
    : m_debug_enabled(false)
    , m_max_procedure_cache_size(1000)
    , m_procedure_timeout(300)
    , m_total_procedures_parsed(0)
    , m_total_functions_parsed(0)
    , m_total_plsql_blocks_parsed(0)
    , m_parse_errors(0)
{
    PROC_LOG_INFO("Oracle Procedure Handler initialized");
}

OracleProcedureHandler::~OracleProcedureHandler()
{
    // 清理缓存的存储过程信息
    for (auto& pair : m_procedure_cache) {
        free_procedure_info(pair.second);
    }
    m_procedure_cache.clear();
    
    PROC_LOG_INFO("Oracle Procedure Handler destroyed, parsed %lu procedures, %lu functions",
                 (unsigned long)m_total_procedures_parsed, (unsigned long)m_total_functions_parsed);
}

int OracleProcedureHandler::parse_procedure_call(const char *data, size_t data_len, 
                                               oracle_status_t *status, oracle_parsed_data_t *result)
{
    if (!data || data_len < 8 || !status || !result) {
        return PROCEDURE_PARSE_ERROR;
    }

    oracle_procedure_call_t *proc_call = (oracle_procedure_call_t*)calloc(1, sizeof(oracle_procedure_call_t));
    if (!proc_call) {
        return PROCEDURE_PARSE_ERROR;
    }

    size_t offset = 0;
    
    // 解析过程调用头部
    int ret = parse_procedure_header(data, data_len, &offset, proc_call);
    if (ret != PROCEDURE_PARSE_SUCCESS) {
        free_procedure_call(proc_call);
        return ret;
    }

    // 解析参数
    ret = parse_procedure_parameters(data, data_len, &offset, proc_call);
    if (ret != PROCEDURE_PARSE_SUCCESS) {
        free_procedure_call(proc_call);
        return ret;
    }

    // 设置结果
    result->procedure_call = proc_call;
    result->data_type = ORACLE_DATA_TYPE_PROCEDURE_CALL;
    
    m_total_procedures_parsed++;
    
    PROC_LOG_DEBUG("Procedure call parsed: %s", proc_call->procedure_name);
    
    return PROCEDURE_PARSE_SUCCESS;
}

int OracleProcedureHandler::parse_function_call(const char *data, size_t data_len,
                                              oracle_status_t *status, oracle_parsed_data_t *result)
{
    if (!data || data_len < 8 || !status || !result) {
        return PROCEDURE_PARSE_ERROR;
    }

    oracle_function_call_t *func_call = (oracle_function_call_t*)calloc(1, sizeof(oracle_function_call_t));
    if (!func_call) {
        return PROCEDURE_PARSE_ERROR;
    }

    size_t offset = 0;
    
    // 解析函数调用头部
    int ret = parse_function_header(data, data_len, &offset, func_call);
    if (ret != PROCEDURE_PARSE_SUCCESS) {
        free_function_call(func_call);
        return ret;
    }

    // 解析参数和返回值
    ret = parse_function_parameters(data, data_len, &offset, func_call);
    if (ret != PROCEDURE_PARSE_SUCCESS) {
        free_function_call(func_call);
        return ret;
    }

    // 设置结果
    result->function_call = func_call;
    result->data_type = ORACLE_DATA_TYPE_FUNCTION_CALL;
    
    m_total_functions_parsed++;
    
    PROC_LOG_DEBUG("Function call parsed: %s", func_call->function_name);
    
    return PROCEDURE_PARSE_SUCCESS;
}

int OracleProcedureHandler::parse_plsql_block(const char *data, size_t data_len,
                                            oracle_status_t *status, oracle_parsed_data_t *result)
{
    if (!data || data_len < 8 || !status || !result) {
        return PROCEDURE_PARSE_ERROR;
    }

    oracle_plsql_block_t *plsql_block = (oracle_plsql_block_t*)calloc(1, sizeof(oracle_plsql_block_t));
    if (!plsql_block) {
        return PROCEDURE_PARSE_ERROR;
    }

    size_t offset = 0;
    
    // 解析PL/SQL块头部
    int ret = parse_plsql_header(data, data_len, &offset, plsql_block);
    if (ret != PROCEDURE_PARSE_SUCCESS) {
        free_plsql_block(plsql_block);
        return ret;
    }

    // 解析PL/SQL代码
    ret = parse_plsql_code(data, data_len, &offset, plsql_block);
    if (ret != PROCEDURE_PARSE_SUCCESS) {
        free_plsql_block(plsql_block);
        return ret;
    }

    // 设置结果
    result->plsql_block = plsql_block;
    result->data_type = ORACLE_DATA_TYPE_PLSQL_BLOCK;
    
    m_total_plsql_blocks_parsed++;
    
    PROC_LOG_DEBUG("PL/SQL block parsed, size: %u bytes", plsql_block->block_size);
    
    return PROCEDURE_PARSE_SUCCESS;
}

// 私有方法的简化实现
int OracleProcedureHandler::parse_procedure_header(const char *data, size_t data_len, size_t *offset, oracle_procedure_call_t *proc_call)
{
    if (*offset + 8 > data_len) {
        return PROCEDURE_PARSE_NEED_MORE_DATA;
    }

    // 简化实现：读取基本信息
    proc_call->call_id = read_uint32_be(data + *offset);
    *offset += 4;
    
    uint16_t name_len = read_uint16_be(data + *offset);
    *offset += 2;
    
    if (*offset + name_len > data_len || name_len >= sizeof(proc_call->procedure_name)) {
        return PROCEDURE_PARSE_ERROR;
    }
    
    memcpy(proc_call->procedure_name, data + *offset, name_len);
    proc_call->procedure_name[name_len] = '\0';
    *offset += name_len;
    
    return PROCEDURE_PARSE_SUCCESS;
}

int OracleProcedureHandler::parse_procedure_parameters(const char *data, size_t data_len, size_t *offset, oracle_procedure_call_t *proc_call)
{
    if (*offset + 2 > data_len) {
        return PROCEDURE_PARSE_NEED_MORE_DATA;
    }

    proc_call->parameter_count = read_uint16_be(data + *offset);
    *offset += 2;
    
    // 简化实现：只记录参数数量
    PROC_LOG_DEBUG("Procedure %s has %u parameters", proc_call->procedure_name, proc_call->parameter_count);
    
    return PROCEDURE_PARSE_SUCCESS;
}

int OracleProcedureHandler::parse_function_header(const char *data, size_t data_len, size_t *offset, oracle_function_call_t *func_call)
{
    // 类似于存储过程头部解析
    return parse_procedure_header(data, data_len, offset, (oracle_procedure_call_t*)func_call);
}

int OracleProcedureHandler::parse_function_parameters(const char *data, size_t data_len, size_t *offset, oracle_function_call_t *func_call)
{
    // 类似于存储过程参数解析
    return parse_procedure_parameters(data, data_len, offset, (oracle_procedure_call_t*)func_call);
}

int OracleProcedureHandler::parse_plsql_header(const char *data, size_t data_len, size_t *offset, oracle_plsql_block_t *plsql_block)
{
    if (*offset + 8 > data_len) {
        return PROCEDURE_PARSE_NEED_MORE_DATA;
    }

    plsql_block->block_id = read_uint32_be(data + *offset);
    *offset += 4;
    
    plsql_block->block_size = read_uint32_be(data + *offset);
    *offset += 4;
    
    return PROCEDURE_PARSE_SUCCESS;
}

int OracleProcedureHandler::parse_plsql_code(const char *data, size_t data_len, size_t *offset, oracle_plsql_block_t *plsql_block)
{
    if (*offset + plsql_block->block_size > data_len) {
        return PROCEDURE_PARSE_NEED_MORE_DATA;
    }

    // 简化实现：只记录代码大小
    *offset += plsql_block->block_size;
    
    return PROCEDURE_PARSE_SUCCESS;
}

void OracleProcedureHandler::free_procedure_call(oracle_procedure_call_t *proc_call)
{
    if (proc_call) {
        free(proc_call);
    }
}

void OracleProcedureHandler::free_function_call(oracle_function_call_t *func_call)
{
    if (func_call) {
        free(func_call);
    }
}

void OracleProcedureHandler::free_plsql_block(oracle_plsql_block_t *plsql_block)
{
    if (plsql_block) {
        free(plsql_block);
    }
}

void OracleProcedureHandler::free_procedure_info(oracle_procedure_info_t *proc_info)
{
    if (proc_info) {
        free(proc_info);
    }
}
